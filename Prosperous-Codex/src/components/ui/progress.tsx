"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const progressVariants = cva(
  "relative h-2 w-full overflow-hidden rounded-full",
  {
    variants: {
      variant: {
        default: "bg-gray-200 dark:bg-neutral-800",
        brand: "bg-[#5E6AD2]/20 dark:bg-[#6E56CF]/20",
        success: "bg-emerald-200 dark:bg-emerald-900",
        warning: "bg-yellow-200 dark:bg-yellow-900",
        error: "bg-red-200 dark:bg-red-900",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const progressIndicatorVariants = cva(
  "h-full w-full flex-1 transition-all duration-300 ease-in-out",
  {
    variants: {
      variant: {
        default: "bg-primary",
        brand: "bg-[#5E6AD2] dark:bg-[#9E8CFC]",
        success: "bg-emerald-600 dark:bg-emerald-400",
        warning: "bg-yellow-600 dark:bg-yellow-400",
        error: "bg-red-600 dark:bg-red-400",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface ProgressProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>,
    VariantProps<typeof progressVariants> {
  value?: number
  showPercentage?: boolean
}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(({ className, value, variant, showPercentage = false, ...props }, ref) => (
  showPercentage ? (
    <div className="flex items-center gap-2 w-full">
      <ProgressPrimitive.Root
        ref={ref}
        className={cn(progressVariants({ variant }), className)}
        {...props}
      >
        <ProgressPrimitive.Indicator
          className={cn(progressIndicatorVariants({ variant }))}
          style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
        />
      </ProgressPrimitive.Root>
      <span className="text-xs font-medium text-muted-foreground min-w-[3rem] text-right">
        {value || 0}%
      </span>
    </div>
  ) : (
    <ProgressPrimitive.Root
      ref={ref}
      className={cn(progressVariants({ variant }), className)}
      {...props}
    >
      <ProgressPrimitive.Indicator
        className={cn(progressIndicatorVariants({ variant }))}
        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
      />
    </ProgressPrimitive.Root>
  )
))
Progress.displayName = ProgressPrimitive.Root.displayName

export { Progress }
