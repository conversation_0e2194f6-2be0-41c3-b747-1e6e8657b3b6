#!/usr/bin/env node

/**
 * <PERSON>ript to check access requests in the database
 * Usage: node scripts/check-access-requests.js
 */

const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');

function checkAccessRequests() {
  try {
    console.log('🔍 Checking Access Requests in Database...');
    console.log('');
    
    // Connect to database
    const dbPath = path.join(__dirname, '../data/prosperous-codex.db');
    if (!fs.existsSync(dbPath)) {
      console.error('❌ Database not found. Please run: npm run init-db');
      process.exit(1);
    }
    
    const db = new Database(dbPath);
    
    // Check if access_requests table exists
    const tableExists = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='access_requests'
    `).get();
    
    if (!tableExists) {
      console.log('❌ access_requests table does not exist');
      console.log('   The database schema might need to be updated');
      db.close();
      return;
    }
    
    console.log('✅ access_requests table exists');
    console.log('');
    
    // Get all access requests
    const requests = db.prepare(`
      SELECT 
        id, 
        email, 
        name, 
        reason, 
        status, 
        requested_at, 
        processed_at,
        processed_by
      FROM access_requests 
      ORDER BY requested_at DESC
    `).all();
    
    if (requests.length === 0) {
      console.log('📭 No access requests found in database');
      console.log('');
      console.log('💡 To test the system:');
      console.log('   1. Run: node scripts/test-access-request.js');
      console.log('   2. Or visit: http://localhost:3000/auth/request-access');
    } else {
      console.log(`📋 Found ${requests.length} access request(s):`);
      console.log('');
      
      requests.forEach((request, index) => {
        console.log(`${index + 1}. Request ID: ${request.id}`);
        console.log(`   Email: ${request.email}`);
        console.log(`   Name: ${request.name || 'Not provided'}`);
        console.log(`   Status: ${request.status}`);
        console.log(`   Requested: ${new Date(request.requested_at).toLocaleString()}`);
        if (request.processed_at) {
          console.log(`   Processed: ${new Date(request.processed_at).toLocaleString()}`);
        }
        if (request.reason) {
          console.log(`   Reason: ${request.reason}`);
        }
        console.log('');
      });
      
      const pendingCount = requests.filter(r => r.status === 'pending').length;
      const approvedCount = requests.filter(r => r.status === 'approved').length;
      const rejectedCount = requests.filter(r => r.status === 'rejected').length;
      
      console.log('📊 Summary:');
      console.log(`   Pending: ${pendingCount}`);
      console.log(`   Approved: ${approvedCount}`);
      console.log(`   Rejected: ${rejectedCount}`);
      
      if (pendingCount > 0) {
        console.log('');
        console.log('⚠️  You have pending access requests!');
        console.log('   Login to admin panel to review them:');
        console.log('   http://localhost:3000/admin (Access Requests tab)');
      }
    }
    
    db.close();
    console.log('');
    console.log('✅ Database check completed');
    
  } catch (error) {
    console.error('❌ Error checking access requests:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  checkAccessRequests();
}

module.exports = { checkAccessRequests };
