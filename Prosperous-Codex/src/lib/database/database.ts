import Database from 'better-sqlite3';
import { readFileSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { applyMigrations } from './migrations';
import { initializeApp } from './init';

// Database configuration
const DB_PATH = process.env.DATABASE_PATH || join(process.cwd(), 'data', 'prosperous-codex.db');
const SCHEMA_PATH = join(process.cwd(), 'src', 'lib', 'database', 'schema.sql');

// Database instance
let db: Database.Database | null = null;

/**
 * Initialize the database connection and create tables
 */
export function initializeDatabase(): Database.Database {
  if (db) {
    return db;
  }

  try {
    // Ensure data directory exists
    mkdirSync(dirname(DB_PATH), { recursive: true });

    // Create database connection
    db = new Database(DB_PATH);

    // Enable foreign keys
    db.pragma('foreign_keys = ON');

    // Set journal mode to WAL for better performance
    db.pragma('journal_mode = WAL');

    // Check if this is a fresh database or existing one
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all() as { name: string }[];
    const isNewDatabase = tables.length === 0;

    if (isNewDatabase) {
      // Fresh database - execute full schema
      console.log('🔧 Initializing fresh database...');
      const schema = readFileSync(SCHEMA_PATH, 'utf-8');
      db.exec(schema);
    } else {
      // Existing database - only apply migrations
      console.log('🔧 Existing database detected, applying migrations...');
    }

    // Apply any pending migrations
    try {
      applyMigrations(db);
    } catch (error) {
      console.warn('⚠️ Failed to apply migrations:', error);
    }

    return db;
  } catch (error) {
    throw error;
  }
}

/**
 * Get the database instance
 */
export function getDatabase(): Database.Database {
  if (!db) {
    const database = initializeDatabase();

    // Initialize default users in development if they don't exist
    if (process.env.NODE_ENV === 'development') {
      try {
        const userCount = database.prepare('SELECT COUNT(*) as count FROM users').get() as { count: number };
        if (userCount.count === 0) {
          console.log('🔧 No users found, initializing default users...');
          // Import and run initialization asynchronously
          setImmediate(async () => {
            try {
              // Use imported initializeApp function
              await initializeApp();
            } catch (error) {
              console.warn('⚠️ Failed to initialize default users:', error);
            }
          });
        }
      } catch (error) {
        console.warn('⚠️ Failed to check user count:', error);
      }
    }

    return database;
  }
  return db;
}

/**
 * Close the database connection
 */
export function closeDatabase(): void {
  if (db) {
    db.close();
    db = null;
  }
}

/**
 * Force close and reset database connection (for database reset operations)
 */
export function forceResetDatabase(): void {
  if (db) {
    try {
      db.close();
    } catch (error) {
      console.warn('⚠️ Error closing database connection:', error);
    }
    db = null;
  }
  console.log('🔄 Database connection reset');
}

/**
 * Execute a transaction
 */
export function executeTransaction<T>(callback: (db: Database.Database) => T): T {
  const database = getDatabase();
  const transaction = database.transaction(callback);
  return transaction(database);
}

/**
 * Database health check
 */
export function healthCheck(): boolean {
  try {
    const database = getDatabase();
    const result = database.prepare('SELECT 1 as health').get() as { health: number } | undefined;
    return result ? result.health === 1 : false;
  } catch {
    return false;
  }
}

/**
 * Get database statistics
 */
export function getDatabaseStats() {
  try {
    const database = getDatabase();
    
    const userCount = database.prepare('SELECT COUNT(*) as count FROM users').get() as { count: number };
    const projectCount = database.prepare('SELECT COUNT(*) as count FROM projects').get() as { count: number };
    const activeSessionCount = database.prepare("SELECT COUNT(*) as count FROM user_sessions WHERE expires_at > datetime('now')").get() as { count: number };
    
    return {
      users: userCount.count,
      projects: projectCount.count,
      activeSessions: activeSessionCount.count,
      healthy: true
    };
  } catch (error) {
    return {
      users: 0,
      projects: 0,
      activeSessions: 0,
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Initialize database on module load
initializeDatabase();

const databaseManager = {
  initialize: initializeDatabase,
  get: getDatabase,
  close: closeDatabase,
  forceReset: forceResetDatabase,
  transaction: executeTransaction,
  healthCheck,
  stats: getDatabaseStats
};

export default databaseManager;
