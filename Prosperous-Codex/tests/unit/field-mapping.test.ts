/**
 * Comprehensive Unit Tests for Field Mapping System
 *
 * Tests the enhanced field mapping system that fixes the team member display bug
 * by ensuring proper conversion between snake_case (database) and camelCase (API/frontend).
 *
 * This test suite validates:
 * - Core field mapping functionality
 * - DataMapper enhancements
 * - Error handling and edge cases
 * - Performance and consistency
 * - Integration with validation tools
 */

import { FieldMapper, FIELD_MAPPINGS, REVERSE_FIELD_MAPPINGS } from '@/lib/task-master/field-mapping';
import { DataMapper } from '@/lib/task-master/types';

describe('Field Mapping System', () => {
  describe('FIELD_MAPPINGS', () => {
    test('should include critical team member mappings', () => {
      expect(FIELD_MAPPINGS.teamMembers).toBe('team_members');
      expect(FIELD_MAPPINGS.userId).toBe('user_id');
      expect(FIELD_MAPPINGS.addedAt).toBe('added_at');
      expect(FIELD_MAPPINGS.addedBy).toBe('added_by');
      expect(FIELD_MAPPINGS.userEmail).toBe('user_email');
    });

    test('should include all required project mappings', () => {
      expect(FIELD_MAPPINGS.fullDescription).toBe('full_description');
      expect(FIELD_MAPPINGS.eventLog).toBe('event_log');
      expect(FIELD_MAPPINGS.dueDate).toBe('due_date');
      expect(FIELD_MAPPINGS.createdBy).toBe('created_by');
      expect(FIELD_MAPPINGS.assignedTo).toBe('assigned_to');
    });

    test('should include all required task mappings', () => {
      expect(FIELD_MAPPINGS.projectId).toBe('project_id');
      expect(FIELD_MAPPINGS.parentTaskId).toBe('parent_task_id');
    });
  });

  describe('REVERSE_FIELD_MAPPINGS', () => {
    test('should correctly reverse all mappings', () => {
      expect(REVERSE_FIELD_MAPPINGS.team_members).toBe('teamMembers');
      expect(REVERSE_FIELD_MAPPINGS.user_id).toBe('userId');
      expect(REVERSE_FIELD_MAPPINGS.added_at).toBe('addedAt');
      expect(REVERSE_FIELD_MAPPINGS.user_email).toBe('userEmail');
    });

    test('should be symmetric with FIELD_MAPPINGS', () => {
      for (const [apiField, dbField] of Object.entries(FIELD_MAPPINGS)) {
        expect(REVERSE_FIELD_MAPPINGS[dbField]).toBe(apiField);
      }
    });
  });

  describe('FieldMapper.apiToDb', () => {
    test('should convert team member API fields to database fields', () => {
      const apiData = {
        teamMembers: [],
        userId: 123,
        addedAt: '2024-01-01T00:00:00Z',
        addedBy: 456,
        userEmail: '<EMAIL>'
      };

      const dbData = FieldMapper.apiToDb(apiData);

      expect(dbData.team_members).toEqual([]);
      expect(dbData.user_id).toBe(123);
      expect(dbData.added_at).toBe('2024-01-01T00:00:00Z');
      expect(dbData.added_by).toBe(456);
      expect(dbData.user_email).toBe('<EMAIL>');
    });

    test('should convert project API fields to database fields', () => {
      const apiData = {
        fullDescription: 'Full description',
        eventLog: 'Event log',
        dueDate: '2024-12-31T23:59:59Z',
        createdBy: 123,
        assignedTo: 456
      };

      const dbData = FieldMapper.apiToDb(apiData);

      expect(dbData.full_description).toBe('Full description');
      expect(dbData.event_log).toBe('Event log');
      expect(dbData.due_date).toBe('2024-12-31T23:59:59Z');
      expect(dbData.created_by).toBe(123);
      expect(dbData.assigned_to).toBe(456);
    });

    test('should preserve unmapped fields', () => {
      const apiData = {
        id: 1,
        title: 'Test',
        unmappedField: 'value'
      };

      const dbData = FieldMapper.apiToDb(apiData);

      expect(dbData.id).toBe(1);
      expect(dbData.title).toBe('Test');
      expect(dbData.unmappedField).toBe('value');
    });
  });

  describe('FieldMapper.dbToApi', () => {
    test('should convert team member database fields to API fields', () => {
      const dbData = {
        team_members: [],
        user_id: 123,
        added_at: '2024-01-01T00:00:00Z',
        added_by: 456,
        user_email: '<EMAIL>'
      };

      const apiData = FieldMapper.dbToApi(dbData);

      expect(apiData.teamMembers).toEqual([]);
      expect(apiData.userId).toBe(123);
      expect(apiData.addedAt).toBe('2024-01-01T00:00:00Z');
      expect(apiData.addedBy).toBe(456);
      expect(apiData.userEmail).toBe('<EMAIL>');
    });

    test('should convert project database fields to API fields', () => {
      const dbData = {
        full_description: 'Full description',
        event_log: 'Event log',
        due_date: '2024-12-31T23:59:59Z',
        created_by: 123,
        assigned_to: 456
      };

      const apiData = FieldMapper.dbToApi(dbData);

      expect(apiData.fullDescription).toBe('Full description');
      expect(apiData.eventLog).toBe('Event log');
      expect(apiData.dueDate).toBe('2024-12-31T23:59:59Z');
      expect(apiData.createdBy).toBe(123);
      expect(apiData.assignedTo).toBe(456);
    });
  });

  describe('DataMapper.teamMemberFromDb', () => {
    test('should properly map team member with name field', () => {
      const dbRow = {
        id: 1,
        project_id: 100,
        user_id: 123,
        role: 'member',
        added_at: '2024-01-01T00:00:00Z',
        added_by: 456,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        username: 'testuser',
        email: '<EMAIL>'
      };

      const teamMember = DataMapper.teamMemberFromDb(dbRow);

      expect(teamMember.id).toBe(1);
      expect(teamMember.projectId).toBe(100);
      expect(teamMember.userId).toBe(123);
      expect(teamMember.role).toBe('member');
      expect(teamMember.addedAt).toBe('2024-01-01T00:00:00Z');
      expect(teamMember.addedBy).toBe(456);
      expect(teamMember.username).toBe('testuser');
      expect(teamMember.email).toBe('<EMAIL>');
      expect(teamMember.name).toBe('testuser'); // CRITICAL: This should be set
    });

    test('should use email as name when username is not available', () => {
      const dbRow = {
        id: 1,
        project_id: 100,
        user_id: 123,
        role: 'member',
        added_at: '2024-01-01T00:00:00Z',
        added_by: 456,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        username: null,
        email: '<EMAIL>'
      };

      const teamMember = DataMapper.teamMemberFromDb(dbRow);

      expect(teamMember.name).toBe('<EMAIL>');
    });

    test('should use fallback when both username and email are missing', () => {
      const dbRow = {
        id: 1,
        project_id: 100,
        user_id: 123,
        role: 'member',
        added_at: '2024-01-01T00:00:00Z',
        added_by: 456,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        username: null,
        email: null
      };

      const teamMember = DataMapper.teamMemberFromDb(dbRow);

      expect(teamMember.name).toBe('Unknown User');
    });
  });

  describe('DataMapper.safeTeamMemberFromDb', () => {
    test('should handle invalid data gracefully', () => {
      expect(DataMapper.safeTeamMemberFromDb(null)).toBeNull();
      expect(DataMapper.safeTeamMemberFromDb(undefined)).toBeNull();
      expect(DataMapper.safeTeamMemberFromDb('invalid')).toBeNull();
    });

    test('should handle missing required fields', () => {
      const invalidRow = {
        // Missing id, user_id, project_id
        role: 'member'
      };

      expect(DataMapper.safeTeamMemberFromDb(invalidRow)).toBeNull();
    });

    test('should return valid team member for good data', () => {
      const validRow = {
        id: 1,
        project_id: 100,
        user_id: 123,
        role: 'member',
        added_at: '2024-01-01T00:00:00Z',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        username: 'testuser',
        email: '<EMAIL>'
      };

      const result = DataMapper.safeTeamMemberFromDb(validRow);
      expect(result).not.toBeNull();
      expect(result?.name).toBe('testuser');
    });
  });

  describe('DataMapper.teamMembersFromDb', () => {
    test('should handle invalid input gracefully', () => {
      expect(DataMapper.teamMembersFromDb(null as any)).toEqual([]);
      expect(DataMapper.teamMembersFromDb(undefined as any)).toEqual([]);
      expect(DataMapper.teamMembersFromDb('invalid' as any)).toEqual([]);
    });

    test('should filter out invalid entries', () => {
      const mixedData = [
        {
          id: 1,
          project_id: 100,
          user_id: 123,
          role: 'member',
          added_at: '2024-01-01T00:00:00Z',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          username: 'validuser',
          email: '<EMAIL>'
        },
        {
          // Invalid - missing required fields
          role: 'member'
        },
        {
          id: 2,
          project_id: 100,
          user_id: 456,
          role: 'admin',
          added_at: '2024-01-01T00:00:00Z',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          username: 'validadmin',
          email: '<EMAIL>'
        }
      ];

      const result = DataMapper.teamMembersFromDb(mixedData);
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('validuser');
      expect(result[1].name).toBe('validadmin');
    });
  });

  describe('Bidirectional mapping consistency', () => {
    test('should maintain data integrity through round-trip conversion', () => {
      const originalApiData = {
        teamMembers: [],
        userId: 123,
        addedAt: '2024-01-01T00:00:00Z',
        userEmail: '<EMAIL>',
        fullDescription: 'Test description',
        projectId: 456
      };

      // API -> DB -> API
      const dbData = FieldMapper.apiToDb(originalApiData);
      const backToApiData = FieldMapper.dbToApi(dbData);

      expect(backToApiData.teamMembers).toEqual(originalApiData.teamMembers);
      expect(backToApiData.userId).toBe(originalApiData.userId);
      expect(backToApiData.addedAt).toBe(originalApiData.addedAt);
      expect(backToApiData.userEmail).toBe(originalApiData.userEmail);
      expect(backToApiData.fullDescription).toBe(originalApiData.fullDescription);
      expect(backToApiData.projectId).toBe(originalApiData.projectId);
    });

    test('should maintain data integrity through reverse round-trip conversion', () => {
      const originalDbData = {
        team_members: [],
        user_id: 123,
        added_at: '2024-01-01T00:00:00Z',
        user_email: '<EMAIL>',
        full_description: 'Test description',
        project_id: 456
      };

      // DB -> API -> DB
      const apiData = FieldMapper.dbToApi(originalDbData);
      const backToDbData = FieldMapper.apiToDb(apiData);

      expect(backToDbData.team_members).toEqual(originalDbData.team_members);
      expect(backToDbData.user_id).toBe(originalDbData.user_id);
      expect(backToDbData.added_at).toBe(originalDbData.added_at);
      expect(backToDbData.user_email).toBe(originalDbData.user_email);
      expect(backToDbData.full_description).toBe(originalDbData.full_description);
      expect(backToDbData.project_id).toBe(originalDbData.project_id);
    });
  });

  describe('Performance and Edge Cases', () => {
    test('should handle large datasets efficiently', () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        userId: i,
        projectId: i * 2,
        teamMembers: Array.from({ length: 10 }, (_, j) => ({
          userId: j,
          addedAt: '2024-01-01T00:00:00Z'
        }))
      }));

      const start = performance.now();
      const mapped = largeDataset.map(item => FieldMapper.apiToDb(item));
      const end = performance.now();

      expect(mapped).toHaveLength(1000);
      expect(end - start).toBeLessThan(100); // Should complete in under 100ms
    });

    test('should handle circular references gracefully', () => {
      const circularData: any = {
        userId: 123,
        projectId: 456
      };
      circularData.self = circularData;

      // Should not throw an error
      expect(() => FieldMapper.apiToDb(circularData)).not.toThrow();
    });

    test('should handle deeply nested objects', () => {
      const deepData = {
        userId: 123,
        nested: {
          level1: {
            level2: {
              level3: {
                teamMembers: [],
                addedAt: '2024-01-01T00:00:00Z'
              }
            }
          }
        }
      };

      const mapped = FieldMapper.apiToDb(deepData);
      expect(mapped.user_id).toBe(123);
      expect(mapped.nested.level1.level2.level3.team_members).toEqual([]);
    });
  });

  describe('Validation Integration', () => {
    test('should validate field mapping consistency', () => {
      // Test that all critical field mappings exist
      const criticalFields = ['userId', 'projectId', 'teamMembers', 'addedAt'];

      for (const field of criticalFields) {
        expect(FIELD_MAPPINGS).toHaveProperty(field);
        expect(REVERSE_FIELD_MAPPINGS).toHaveProperty(FIELD_MAPPINGS[field as keyof typeof FIELD_MAPPINGS]);
      }
    });

    test('should provide field mapping suggestions', () => {
      const validation = FieldMapper.validateConsistency(
        ['userId', 'projectId', 'unknownField'],
        ['user_id', 'project_id', 'unknown_field']
      );

      // The validation should fail because of unknown fields, but provide suggestions
      expect(validation.valid).toBe(false);
      expect(validation.errors).toHaveLength(2); // Two unknown fields
      expect(validation.suggestions).toHaveProperty('unknownField', 'unknown_field');
      expect(validation.suggestions).toHaveProperty('unknown_field', 'unknownField');
    });
  });

  describe('Error Recovery and Logging', () => {
    test('should log mapping errors without breaking execution', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      // Test with invalid data that should trigger error logging
      const invalidData = null;
      const result = DataMapper.safeTeamMemberFromDb(invalidData);

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    test('should provide fallback values for critical fields', () => {
      const incompleteData = {
        id: 1,
        project_id: 100,
        user_id: 123,
        role: 'member',
        added_at: '2024-01-01T00:00:00Z',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        // Missing username and email
        username: null,
        email: null
      };

      const result = DataMapper.teamMemberFromDb(incompleteData);

      expect(result.name).toBe('Unknown User'); // Fallback value
      expect(result.userId).toBe(123);
      expect(result.projectId).toBe(100);
    });
  });

  describe('Type Safety and Validation', () => {
    test('should maintain type safety through mapping operations', () => {
      const typedData = {
        userId: 123,
        projectId: 456,
        addedAt: '2024-01-01T00:00:00Z',
        isActive: true,
        progress: 75.5
      };

      const mapped = FieldMapper.apiToDb(typedData);

      // Verify types are preserved
      expect(typeof mapped.user_id).toBe('number');
      expect(typeof mapped.project_id).toBe('number');
      expect(typeof mapped.added_at).toBe('string');
      expect(typeof mapped.is_active).toBe('boolean');
      expect(typeof mapped.progress).toBe('number');
    });

    test('should handle null and undefined values correctly', () => {
      const dataWithNulls = {
        userId: 123,
        projectId: null,
        addedAt: undefined,
        teamMembers: null
      };

      const mapped = FieldMapper.apiToDb(dataWithNulls);

      expect(mapped.user_id).toBe(123);
      expect(mapped.project_id).toBeNull();
      expect(mapped.added_at).toBeUndefined();
      expect(mapped.team_members).toBeNull();
    });
  });
});
