/**
 * Authorization service for Task Master system
 * Implements role-based access control with resource-specific permissions
 * Based on Context7 validated patterns for RBAC
 */

import { User } from '@/lib/types/auth';
import { 
  Project, 
  Task, 
  ProjectTeamMember, 
  ProjectFile, 
  ProjectComment,
  TeamMemberRole 
} from './types';
import { AuthorizationError, ErrorFactory } from './errors';

/**
 * Available actions in the system
 */
export type Action = 
  | 'create' 
  | 'read' 
  | 'update' 
  | 'delete'
  | 'manage'  // Full control
  | 'assign'  // Assign tasks/projects to others
  | 'invite'; // Invite team members

/**
 * Available resources in the system
 */
export type Resource = 
  | 'project' 
  | 'task' 
  | 'comment' 
  | 'file' 
  | 'team_member'
  | 'activity_log';

/**
 * Permission scope - determines ownership context
 */
export type Scope = 'own' | 'any' | 'team';

/**
 * Permission definition
 */
export interface Permission {
  action: Action;
  resource: Resource;
  scope: Scope;
  granted: boolean;
  attributes?: string[]; // Which fields can be accessed/modified
}

/**
 * Role-based permission matrix
 * Based on Context7 validated RBAC patterns
 */
const ROLE_PERMISSIONS: Record<string, Permission[]> = {
  // System admin - full access to everything
  admin: [
    { action: 'manage', resource: 'project', scope: 'any', granted: true },
    { action: 'manage', resource: 'task', scope: 'any', granted: true },
    { action: 'manage', resource: 'comment', scope: 'any', granted: true },
    { action: 'manage', resource: 'file', scope: 'any', granted: true },
    { action: 'manage', resource: 'team_member', scope: 'any', granted: true },
    { action: 'read', resource: 'activity_log', scope: 'any', granted: true },
  ],

  // Moderator - can manage projects they're involved in
  moderator: [
    { action: 'create', resource: 'project', scope: 'any', granted: true },
    { action: 'read', resource: 'project', scope: 'team', granted: true },
    { action: 'update', resource: 'project', scope: 'team', granted: true },
    { action: 'delete', resource: 'project', scope: 'own', granted: true },
    { action: 'manage', resource: 'task', scope: 'team', granted: true },
    { action: 'manage', resource: 'comment', scope: 'team', granted: true },
    { action: 'manage', resource: 'file', scope: 'team', granted: true },
    { action: 'invite', resource: 'team_member', scope: 'team', granted: true },
    { action: 'read', resource: 'activity_log', scope: 'team', granted: true },
  ],

  // Regular user - limited access
  user: [
    { action: 'create', resource: 'project', scope: 'any', granted: true },
    { action: 'read', resource: 'project', scope: 'team', granted: true },
    { action: 'update', resource: 'project', scope: 'own', granted: true },
    { action: 'delete', resource: 'project', scope: 'own', granted: true },
    { action: 'create', resource: 'task', scope: 'team', granted: true },
    { action: 'read', resource: 'task', scope: 'team', granted: true },
    { action: 'update', resource: 'task', scope: 'own', granted: true },
    { action: 'delete', resource: 'task', scope: 'own', granted: true },
    { action: 'create', resource: 'comment', scope: 'team', granted: true },
    { action: 'read', resource: 'comment', scope: 'team', granted: true },
    { action: 'invite', resource: 'team_member', scope: 'team', granted: true }, // Allow users to invite team members
    { action: 'update', resource: 'comment', scope: 'own', granted: true },
    { action: 'delete', resource: 'comment', scope: 'own', granted: true },
    { action: 'create', resource: 'file', scope: 'team', granted: true },
    { action: 'read', resource: 'file', scope: 'team', granted: true },
    { action: 'delete', resource: 'file', scope: 'own', granted: true },
    { action: 'read', resource: 'activity_log', scope: 'team', granted: true },
  ],

  // Project owner - same as user but with broader permissions
  owner: [
    { action: 'create', resource: 'project', scope: 'any', granted: true },
    { action: 'read', resource: 'project', scope: 'team', granted: true },
    { action: 'update', resource: 'project', scope: 'own', granted: true },
    { action: 'delete', resource: 'project', scope: 'own', granted: true },
    { action: 'create', resource: 'task', scope: 'team', granted: true },
    { action: 'read', resource: 'task', scope: 'team', granted: true },
    { action: 'update', resource: 'task', scope: 'own', granted: true },
    { action: 'delete', resource: 'task', scope: 'own', granted: true },
    { action: 'create', resource: 'comment', scope: 'team', granted: true },
    { action: 'read', resource: 'comment', scope: 'team', granted: true },
    { action: 'invite', resource: 'team_member', scope: 'team', granted: true },
    { action: 'update', resource: 'comment', scope: 'own', granted: true },
    { action: 'delete', resource: 'comment', scope: 'own', granted: true },
    { action: 'create', resource: 'file', scope: 'team', granted: true },
    { action: 'read', resource: 'file', scope: 'team', granted: true },
    { action: 'delete', resource: 'file', scope: 'own', granted: true },
    { action: 'read', resource: 'activity_log', scope: 'team', granted: true },
  ],
};

/**
 * Team member role permissions within projects
 */
const TEAM_ROLE_PERMISSIONS: Record<TeamMemberRole, Permission[]> = {
  admin: [
    { action: 'manage', resource: 'project', scope: 'team', granted: true },
    { action: 'manage', resource: 'task', scope: 'team', granted: true },
    { action: 'manage', resource: 'comment', scope: 'team', granted: true },
    { action: 'manage', resource: 'file', scope: 'team', granted: true },
    { action: 'manage', resource: 'team_member', scope: 'team', granted: true },
    { action: 'read', resource: 'activity_log', scope: 'team', granted: true },
  ],

  member: [
    { action: 'read', resource: 'project', scope: 'team', granted: true },
    { action: 'update', resource: 'project', scope: 'team', granted: true, attributes: ['event_log', 'progress'] },
    { action: 'create', resource: 'task', scope: 'team', granted: true },
    { action: 'read', resource: 'task', scope: 'team', granted: true },
    { action: 'update', resource: 'task', scope: 'team', granted: true },
    { action: 'delete', resource: 'task', scope: 'own', granted: true },
    { action: 'manage', resource: 'comment', scope: 'team', granted: true },
    { action: 'manage', resource: 'file', scope: 'team', granted: true },
    { action: 'read', resource: 'activity_log', scope: 'team', granted: true },
  ],

  viewer: [
    { action: 'read', resource: 'project', scope: 'team', granted: true },
    { action: 'read', resource: 'task', scope: 'team', granted: true },
    { action: 'read', resource: 'comment', scope: 'team', granted: true },
    { action: 'read', resource: 'file', scope: 'team', granted: true },
    { action: 'read', resource: 'activity_log', scope: 'team', granted: true },
    { action: 'create', resource: 'comment', scope: 'team', granted: true },
  ],
};

/**
 * Authorization context for permission checks
 */
export interface AuthContext {
  user: User;
  project?: Project;
  task?: Task;
  teamMembership?: ProjectTeamMember;
  resource?: any; // The specific resource being accessed
}

/**
 * Authorization service class
 */
export class AuthorizationService {
  private db: any;

  constructor(db: any) {
    this.db = db;
  }
  /**
   * Check if user has permission to perform action on resource
   */
  static can(
    context: AuthContext,
    action: Action,
    resource: Resource,
    scope?: Scope
  ): Permission {
    const { user, project, teamMembership } = context;

    // Get base permissions from user role
    const rolePermissions = ROLE_PERMISSIONS[user.role] || [];
    
    // Get team-specific permissions if user is a team member
    const teamPermissions = teamMembership 
      ? TEAM_ROLE_PERMISSIONS[teamMembership.role] || []
      : [];

    // Combine permissions (team permissions take precedence)
    const allPermissions = [...teamPermissions, ...rolePermissions];

    // Find matching permission
    const permission = this.findMatchingPermission(allPermissions, action, resource, scope);

    if (!permission) {
      return { action, resource, scope: scope || 'own', granted: false };
    }

    // Additional scope validation
    if (permission.scope === 'own' && context.resource) {
      const isOwner = this.isResourceOwner(context.resource, user);
      if (!isOwner) {
        return { ...permission, granted: false };
      }
    }

    if (permission.scope === 'team' && project) {
      const hasTeamAccess = this.hasTeamAccess(user, project);
      if (!hasTeamAccess) {
        return { ...permission, granted: false };
      }
    }

    return permission;
  }

  /**
   * Assert that user has permission (throws if not)
   */
  static assertCan(
    context: AuthContext,
    action: Action,
    resource: Resource,
    scope?: Scope
  ): void {
    const permission = this.can(context, action, resource, scope);

    if (!permission.granted) {
      // Determine specific error message based on context
      const errorScenario = this.getErrorScenario(action, resource, scope, context);
      throw errorScenario.scenario
        ? ErrorFactory.permissionDenied(errorScenario.scenario, errorScenario.requiredRole)
        : ErrorFactory.unauthorized(resource, `${action} ${scope ? `(${scope})` : ''}`);
    }
  }

  /**
   * Determine the appropriate error scenario for specific user-friendly messages
   */
  private static getErrorScenario(
    action: string,
    resource: string,
    scope: string | undefined,
    context: AuthorizationContext
  ): { scenario?: 'project_access' | 'task_edit' | 'team_management' | 'file_upload' | 'role_required', requiredRole?: string } {

    // Project access scenarios (but not for comments - they have universal access)
    if (resource === 'project' && (action === 'read' || action === 'view')) {
      return { scenario: 'project_access' };
    }

    // Task editing scenarios
    if (resource === 'task' && (action === 'update' || action === 'edit' || action === 'delete')) {
      return { scenario: 'task_edit' };
    }

    // Team management scenarios
    if ((resource === 'team' || resource === 'teamMember') &&
        (action === 'create' || action === 'update' || action === 'delete' || action === 'manage')) {
      return { scenario: 'team_management' };
    }

    // File upload scenarios
    if (resource === 'file' && (action === 'create' || action === 'upload')) {
      return { scenario: 'file_upload' };
    }

    // Role-based restrictions (system-level permissions)
    // Note: Comment creation is now universal, so exclude it from role restrictions
    if (context.user.role === 'user' && (action === 'create' || action === 'manage') && resource !== 'comment') {
      return { scenario: 'role_required', requiredRole: 'Moderator' };
    }

    if (context.user.role !== 'admin' && scope === 'system') {
      return { scenario: 'role_required', requiredRole: 'Admin' };
    }

    // Default to generic error
    return {};
  }

  /**
   * Check if user is owner of a resource
   */
  private static isResourceOwner(resource: any, user: User): boolean {
    if (!resource || !user) return false;

    // Check common ownership patterns
    const userId = parseInt(user.id);
    
    return (
      resource.createdBy === userId ||
      resource.created_by === userId ||
      resource.authorId === userId ||
      resource.author_id === userId ||
      resource.uploadedBy === userId ||
      resource.uploaded_by === userId ||
      resource.userId === userId ||
      resource.user_id === userId
    );
  }

  /**
   * Check if user has team access to a project
   */
  private static hasTeamAccess(user: User, project: Project): boolean {
    if (!project || !user) return false;

    const userId = parseInt(user.id);

    // Check if user is project creator
    if (project.createdBy === userId) return true;

    // Check if user is assigned to project
    if (project.assignedTo === userId) return true;

    // Check if user is a team member
    if (project.teamMembers) {
      return project.teamMembers.some(member => member.userId === userId);
    }

    return false;
  }

  /**
   * Find matching permission from list
   */
  private static findMatchingPermission(
    permissions: Permission[],
    action: Action,
    resource: Resource,
    scope?: Scope
  ): Permission | null {
    // First, look for exact match
    let match = permissions.find(p =>
      p.action === action &&
      p.resource === resource &&
      this.scopeMatches(p.scope, scope)
    );

    if (match) return match;

    // Look for 'manage' permission which grants all actions
    match = permissions.find(p =>
      p.action === 'manage' &&
      p.resource === resource &&
      this.scopeMatches(p.scope, scope)
    );

    if (match) {
      return { ...match, action }; // Return with requested action
    }

    // Look for broader scope permissions
    if (scope === 'own') {
      match = permissions.find(p => 
        p.action === action && 
        p.resource === resource && 
        (p.scope === 'team' || p.scope === 'any')
      );
    } else if (scope === 'team') {
      match = permissions.find(p => 
        p.action === action && 
        p.resource === resource && 
        p.scope === 'any'
      );
    }

    return match || null;
  }

  /**
   * Check if permission scope matches or is broader than requested scope
   */
  private static scopeMatches(permissionScope: Scope, requestedScope?: Scope): boolean {
    if (!requestedScope) return true; // No scope requirement

    // Exact match
    if (permissionScope === requestedScope) return true;

    // 'any' scope grants access to everything
    if (permissionScope === 'any') return true;

    // 'team' scope grants access to 'own' scope
    if (permissionScope === 'team' && requestedScope === 'own') return true;

    return false;
  }

  /**
   * Get user's team membership for a project
   */
  static getTeamMembership(user: User, project: Project): ProjectTeamMember | null {
    if (!project.teamMembers || !user) return null;

    const userId = parseInt(user.id);
    return project.teamMembers.find(member => member.userId === userId) || null;
  }

  /**
   * Create authorization context
   */
  static createContext(
    user: User,
    options: {
      project?: Project;
      task?: Task;
      resource?: any;
    } = {}
  ): AuthContext {
    const context: AuthContext = { user };

    if (options.project) {
      context.project = options.project;
      context.teamMembership = this.getTeamMembership(user, options.project);
    }

    if (options.task) {
      context.task = options.task;
    }

    if (options.resource) {
      context.resource = options.resource;
    }

    return context;
  }

  /**
   * Check if user can access project (static method for backward compatibility)
   */
  static canAccessProject(user: User, project: Project): boolean {
    const context = this.createContext(user, { project });
    const permission = this.can(context, 'read', 'project');
    return permission.granted;
  }

  /**
   * Check if user can modify project (static method for backward compatibility)
   */
  static canModifyProject(user: User, project: Project): boolean {
    const context = this.createContext(user, { project });

    // For project modification, only the project creator/owner should be allowed
    // Team members can only update specific attributes, not the whole project
    const userId = parseInt(user.id);

    // Check if user is project creator (owner)
    if (project.createdBy === userId) {
      return true;
    }

    // Admin users can modify any project
    if (user.role === 'admin') {
      return true;
    }

    // Moderators can modify projects they have team access to
    if (user.role === 'moderator' && this.hasTeamAccess(user, project)) {
      return true;
    }

    // Regular users can only modify their own projects
    return false;
  }

  /**
   * Check if user can delete project (static method for backward compatibility)
   */
  static canDeleteProject(user: User, project: Project): boolean {
    const context = this.createContext(user, { project, resource: project });
    const permission = this.can(context, 'delete', 'project', 'own');
    return permission.granted;
  }

  // Instance methods for database-driven authorization (used by tests)

  /**
   * Check if user can access project by IDs
   */
  async canAccessProject(projectId: number, userId: number): Promise<boolean> {
    if (!projectId || !userId || projectId <= 0 || userId <= 0) {
      return false;
    }

    let project: any = null;
    let user: any = null;

    try {
      project = await this.getProjectWithTeamMembers(projectId);
    } catch (error) {
      if (error && error.message && error.message.includes('The database connection is not open')) {
        throw error;
      }
      console.error('Error fetching project:', error);
      return false;
    }

    try {
      user = await this.getUserById(userId);
    } catch (error) {
      if (error && error.message && error.message.includes('The database connection is not open')) {
        throw error;
      }
      console.error('Error fetching user:', error);
      return false;
    }

    if (!project || !user) {
      return false;
    }

    return AuthorizationService.canAccessProject(user, project);
  }

  /**
   * Check if user can modify project by IDs
   */
  async canModifyProject(projectId: number, userId: number): Promise<boolean> {
    try {
      if (!projectId || !userId || projectId <= 0 || userId <= 0) {
        return false;
      }

      const project = await this.getProjectWithTeamMembers(projectId);
      const user = await this.getUserById(userId);

      if (!project || !user) {
        return false;
      }

      return AuthorizationService.canModifyProject(user, project);
    } catch (error) {
      console.error('Error checking project modify permission:', error);
      return false;
    }
  }

  /**
   * Check if user can delete project by IDs
   */
  async canDeleteProject(projectId: number, userId: number): Promise<boolean> {
    try {
      if (!projectId || !userId || projectId <= 0 || userId <= 0) {
        return false;
      }

      const project = await this.getProjectWithTeamMembers(projectId);
      const user = await this.getUserById(userId);

      if (!project || !user) {
        return false;
      }

      return AuthorizationService.canDeleteProject(user, project);
    } catch (error) {
      console.error('Error checking project delete permission:', error);
      return false;
    }
  }

  /**
   * Require project access (throws if denied)
   */
  async requireProjectAccess(projectId: number, userId: number): Promise<void> {
    // Validate input parameters
    if (!projectId || !userId || projectId <= 0 || userId <= 0) {
      throw new AuthorizationError(
        `Access denied to project ${projectId}`,
        {
          context: {
            projectId,
            userId,
            action: 'read',
            resource: 'project'
          }
        }
      );
    }

    const canAccess = await this.canAccessProject(projectId, userId);

    if (!canAccess) {
      throw new AuthorizationError(
        `Access denied to project ${projectId}`,
        {
          context: {
            projectId,
            userId,
            action: 'read',
            resource: 'project'
          }
        }
      );
    }
  }

  // Task authorization methods

  /**
   * Check if user can access task by IDs
   */
  async canAccessTask(taskId: number, userId: number): Promise<boolean> {
    try {
      if (!taskId || !userId || taskId <= 0 || userId <= 0) {
        return false;
      }

      const task = await this.getTaskWithProject(taskId);
      const user = await this.getUserById(userId);

      if (!task || !user) {
        return false;
      }

      // Check if user is task creator
      if (task.createdBy === parseInt(user.id)) {
        return true;
      }

      // Check if user is task assignee
      if (task.assignedTo === parseInt(user.id)) {
        return true;
      }

      // Check if user has access to the project
      return await this.canAccessProject(task.projectId, userId);
    } catch (error) {
      console.error('Error checking task access:', error);
      return false;
    }
  }

  /**
   * Check if user can modify task by IDs
   */
  async canModifyTask(taskId: number, userId: number): Promise<boolean> {
    try {
      if (!taskId || !userId || taskId <= 0 || userId <= 0) {
        return false;
      }

      const task = await this.getTaskWithProject(taskId);
      const user = await this.getUserById(userId);

      if (!task || !user) {
        return false;
      }

      // Check if user is task creator
      if (task.createdBy === parseInt(user.id)) {
        return true;
      }

      // Check if user is task assignee
      if (task.assignedTo === parseInt(user.id)) {
        return true;
      }

      // Check if user can modify the project (project owner)
      return await this.canModifyProject(task.projectId, userId);
    } catch (error) {
      console.error('Error checking task modify permission:', error);
      return false;
    }
  }

  /**
   * Check if user can delete task by IDs
   */
  async canDeleteTask(taskId: number, userId: number): Promise<boolean> {
    try {
      if (!taskId || !userId || taskId <= 0 || userId <= 0) {
        return false;
      }

      const task = await this.getTaskWithProject(taskId);
      const user = await this.getUserById(userId);

      if (!task || !user) {
        return false;
      }

      // Check if user is task creator
      if (task.createdBy === parseInt(user.id)) {
        return true;
      }

      // Check if user is project owner (can delete any task in their project)
      const project = await this.getProjectWithTeamMembers(task.projectId);
      if (project && project.createdBy === parseInt(user.id)) {
        return true;
      }

      // Admin users can delete any task
      if (user.role === 'admin') {
        return true;
      }

      // Task assignees cannot delete tasks they didn't create
      return false;
    } catch (error) {
      console.error('Error checking task delete permission:', error);
      return false;
    }
  }

  // Comment authorization methods

  /**
   * Check if user can access comment by IDs
   */
  async canAccessComment(commentId: number, userId: number): Promise<boolean> {
    try {
      if (!commentId || !userId || commentId <= 0 || userId <= 0) {
        return false;
      }

      const comment = await this.getCommentWithTask(commentId);
      const user = await this.getUserById(userId);

      if (!comment || !user) {
        return false;
      }

      // Check if user is comment creator
      if (comment.createdBy === parseInt(user.id)) {
        return true;
      }

      // Check if user has access to the task (and thus the project)
      return await this.canAccessTask(comment.taskId, userId);
    } catch (error) {
      console.error('Error checking comment access:', error);
      return false;
    }
  }

  /**
   * Check if user can modify comment by IDs
   */
  async canModifyComment(commentId: number, userId: number): Promise<boolean> {
    try {
      if (!commentId || !userId || commentId <= 0 || userId <= 0) {
        return false;
      }

      const comment = await this.getCommentWithTask(commentId);
      const user = await this.getUserById(userId);

      if (!comment || !user) {
        return false;
      }

      // Only comment creator can modify comments
      return comment.createdBy === parseInt(user.id);
    } catch (error) {
      console.error('Error checking comment modify permission:', error);
      return false;
    }
  }

  /**
   * Check if user can delete comment by IDs
   */
  async canDeleteComment(commentId: number, userId: number): Promise<boolean> {
    try {
      if (!commentId || !userId || commentId <= 0 || userId <= 0) {
        return false;
      }

      const comment = await this.getCommentWithTask(commentId);
      const user = await this.getUserById(userId);

      if (!comment || !user) {
        return false;
      }

      // Check if user is comment creator
      if (comment.createdBy === parseInt(user.id)) {
        return true;
      }

      // Get the task to find the project
      const task = await this.getTaskWithProject(comment.taskId);
      if (!task) {
        return false;
      }

      // Check if user is project owner (can delete any comment in their project)
      const project = await this.getProjectWithTeamMembers(task.projectId);
      if (project && project.createdBy === parseInt(user.id)) {
        return true;
      }

      // Admin users can delete any comment
      if (user.role === 'admin') {
        return true;
      }

      // Regular team members cannot delete comments they didn't create
      return false;
    } catch (error) {
      console.error('Error checking comment delete permission:', error);
      return false;
    }
  }

  // Team management methods

  /**
   * Check if user can manage team for a project
   */
  async canManageTeam(projectId: number, userId: number): Promise<boolean> {
    try {
      if (!projectId || !userId || projectId <= 0 || userId <= 0) {
        return false;
      }

      const project = await this.getProjectWithTeamMembers(projectId);
      const user = await this.getUserById(userId);

      if (!project || !user) {
        return false;
      }

      // Only project creator/owner can manage team
      return project.createdBy === parseInt(user.id);
    } catch (error) {
      console.error('Error checking team management permission:', error);
      return false;
    }
  }

  /**
   * Get user's role in a project
   */
  async getUserProjectRole(projectId: number, userId: number): Promise<string | null> {
    try {
      if (!projectId || !userId || projectId <= 0 || userId <= 0) {
        return null;
      }

      const project = await this.getProjectWithTeamMembers(projectId);
      const user = await this.getUserById(userId);

      if (!project || !user) {
        return null;
      }

      // Check if user is project creator
      if (project.createdBy === parseInt(user.id)) {
        return 'owner';
      }

      // Check team membership
      const teamMember = project.teamMembers?.find(member => member.userId === parseInt(user.id));
      if (teamMember) {
        return teamMember.role;
      }

      return null;
    } catch (error) {
      console.error('Error getting user project role:', error);
      return null;
    }
  }

  // Helper methods for database operations

  /**
   * Get user by ID from database
   */
  private async getUserById(userId: number): Promise<User | null> {
    const stmt = this.db.prepare('SELECT * FROM users WHERE id = ?');
    const row = stmt.get(userId);

    if (!row) {
      return null;
    }

    return {
      id: row.id.toString(),
      email: row.email,
      username: row.username,
      role: row.role || 'user'
    };
  }

  /**
   * Get project with team members from database
   */
  private async getProjectWithTeamMembers(projectId: number): Promise<Project | null> {
    // Get project data
    const projectStmt = this.db.prepare('SELECT * FROM projects WHERE id = ?');
    const projectRow = projectStmt.get(projectId);

    if (!projectRow) {
      return null;
    }

    // Get team members
    const teamStmt = this.db.prepare(`
      SELECT ptm.*, u.username, u.email
      FROM project_team_members ptm
      JOIN users u ON ptm.user_id = u.id
      WHERE ptm.project_id = ?
    `);
    const teamRows = teamStmt.all(projectId);

    const teamMembers = teamRows.map((row: any) => ({
      id: row.id,
      projectId: row.project_id,
      userId: row.user_id,
      role: row.role,
      name: row.username || row.email,
      email: row.email,
      addedAt: row.joined_at || row.added_at
    }));

    return {
      id: projectRow.id,
      title: projectRow.title,
      description: projectRow.description,
      status: projectRow.status,
      priority: projectRow.priority,
      progress: projectRow.progress,
      createdBy: projectRow.created_by,
      assignedTo: projectRow.assigned_to,
      dueDate: projectRow.due_date,
      visibility: projectRow.visibility,
      teamMembers
    };
  }

  /**
   * Get task with project information from database
   */
  private async getTaskWithProject(taskId: number): Promise<(Task & { projectId: number }) | null> {
    try {
      const stmt = this.db.prepare('SELECT * FROM tasks WHERE id = ?');
      const row = stmt.get(taskId);

      if (!row) {
        return null;
      }

      return {
        id: row.id,
        title: row.title,
        description: row.description,
        status: row.status,
        priority: row.priority,
        progress: row.progress || 0,
        createdBy: row.created_by,
        assignedTo: row.assigned_to,
        projectId: row.project_id,
        parentTaskId: row.parent_task_id,
        dueDate: row.due_date,
        completedDate: row.completed_date
      };
    } catch (error) {
      console.error('Error fetching task:', error);
      return null;
    }
  }

  /**
   * Get comment with task information from database
   */
  private async getCommentWithTask(commentId: number): Promise<(ProjectComment & { taskId: number }) | null> {
    try {
      const stmt = this.db.prepare('SELECT * FROM comments WHERE id = ?');
      const row = stmt.get(commentId);

      if (!row) {
        return null;
      }

      return {
        id: row.id,
        content: row.content,
        createdBy: row.created_by,
        taskId: row.task_id,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      };
    } catch (error) {
      console.error('Error fetching comment:', error);
      return null;
    }
  }
}
