import { TaskMasterColumn } from '@/lib/types/task-master';

export const mockTaskMasterData: TaskMasterColumn[] = [
  {
    id: 'todo',
    title: 'To Do',
    icon: 'Inbox',
    variant: 'brand',
    count: 0, // Will be calculated dynamically
    tasks: [
      {
        id: '1',
        title: 'Website Redesign',
        description: 'Update company website with new branding',
        status: 'todo',
        priority: 'high',
        progress: 65,
        dueDate: '2024-12-15',
        assignee: {
          name: '<PERSON>',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
        },
        details: 'Develop comprehensive website redesign incorporating new brand guidelines and improved user experience.',
        projectDetails: {
          fullDescription: 'Complete overhaul of the company website to align with new brand identity. This includes redesigning all pages, improving user experience, implementing responsive design, and optimizing for performance and SEO.',
          tags: ['Design', 'Frontend', 'UX/UI', 'Branding'],
          teamMembers: [
            {
              id: '1',
              name: '<PERSON>',
              avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
              role: 'Project Lead',
              email: '<EMAIL>'
            },
            {
              id: '2',
              name: 'Sarah Wilson',
              avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
              role: 'UI Designer',
              email: '<EMAIL>'
            }
          ],
          files: [
            {
              id: '1',
              name: 'website-mockup-v2.fig',
              type: 'Figma Design',
              size: '2.4 MB',
              uploadedBy: 'Sarah Wilson',
              uploadedAt: '2024-12-01T10:30:00Z',
              thumbnail: 'https://res.cloudinary.com/subframe/image/upload/v1723780835/uploads/302/kr9usrdgbwp9cge3ab1f.png'
            },
            {
              id: '2',
              name: 'brand-guidelines.pdf',
              type: 'PDF Document',
              size: '1.8 MB',
              uploadedBy: 'John Doe',
              uploadedAt: '2024-11-28T14:15:00Z'
            }
          ],
          comments: [
            {
              id: '1',
              author: {
                id: '1',
                name: 'John Doe',
                avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
                role: 'Project Lead'
              },
              content: 'Great progress on the design! The new layout looks much more modern and user-friendly.',
              createdAt: '2024-12-01T09:15:00Z'
            }
          ],
          eventLog: [
            {
              id: '1',
              type: 'upload',
              description: 'uploaded website-mockup-v2.fig',
              author: {
                id: '2',
                name: 'Sarah Wilson',
                avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
                role: 'UI Designer'
              },
              timestamp: '2024-12-01T10:30:00Z'
            },
            {
              id: '2',
              type: 'comment',
              description: 'added a comment',
              author: {
                id: '1',
                name: 'John Doe',
                avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
                role: 'Project Lead'
              },
              timestamp: '2024-12-01T09:15:00Z'
            }
          ]
        }
      },
      {
        id: '2',
        title: 'Database Migration',
        description: 'Migrate legacy database to new infrastructure',
        status: 'todo',
        priority: 'medium',
        progress: 25,
        dueDate: '2024-12-20',
        assignee: {
          name: 'Jane Smith',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
        },
        details: 'Complete migration of customer data and ensure zero downtime during transition.',
        projectDetails: {
          fullDescription: 'Migrate the legacy database system to a modern cloud-based infrastructure with improved performance and scalability.',
          tags: ['Database', 'Migration', 'Infrastructure'],
          teamMembers: [
            {
              id: '3',
              name: 'Jane Smith',
              avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
              role: 'Database Administrator',
              email: '<EMAIL>'
            }
          ],
          files: [],
          comments: [
            {
              id: '2',
              author: {
                id: '3',
                name: 'Jane Smith',
                avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
                role: 'Database Administrator'
              },
              content: 'Starting the data backup process before migration begins.',
              createdAt: '2024-12-02T08:30:00Z'
            }
          ],
          eventLog: []
        }
      },
      {
        id: '3',
        title: 'API Documentation',
        description: 'Create comprehensive API documentation',
        status: 'todo',
        priority: 'low',
        progress: 10,
        dueDate: '2024-12-25',
        assignee: {
          name: 'Mike Johnson',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
        },
        details: 'Document all API endpoints with examples and integration guides.'
      }
    ]
  },
  {
    id: 'inProgress',
    title: 'In Progress',
    icon: 'Clock',
    variant: 'warning',
    count: 0, // Will be calculated dynamically
    tasks: [
      {
        id: '4',
        title: 'Mobile App Development',
        description: 'Build iOS and Android applications',
        status: 'inProgress',
        priority: 'medium',
        progress: 40,
        dueDate: '2025-01-30',
        assignee: {
          name: 'Sarah Wilson',
          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face'
        },
        details: 'Create native mobile applications for both iOS and Android platforms with cross-platform functionality.'
      },
      {
        id: '5',
        title: 'Security Audit',
        description: 'Comprehensive security review and testing',
        status: 'inProgress',
        priority: 'high',
        progress: 75,
        dueDate: '2024-12-10',
        assignee: {
          name: 'David Brown',
          avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face'
        },
        details: 'Perform penetration testing and vulnerability assessment across all systems.'
      },
      {
        id: '6',
        title: 'Performance Optimization',
        description: 'Optimize application performance and speed',
        status: 'inProgress',
        priority: 'medium',
        progress: 55,
        dueDate: '2024-12-18',
        assignee: {
          name: 'Emily Davis',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
        },
        details: 'Improve page load times and optimize database queries for better user experience.'
      }
    ]
  },
  {
    id: 'completed',
    title: 'Completed',
    icon: 'CheckCircle',
    variant: 'success',
    count: 0, // Will be calculated dynamically
    tasks: [
      {
        id: '7',
        title: 'Brand Guidelines',
        description: 'Company brand identity documentation',
        status: 'completed',
        priority: 'high',
        progress: 100,
        completedDate: '2024-11-28',
        assignee: {
          name: 'Lisa Anderson',
          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face'
        },
        details: 'Comprehensive brand guidelines including logo usage, typography, and color palette.'
      },
      {
        id: '8',
        title: 'User Authentication',
        description: 'Implement secure user login system',
        status: 'completed',
        priority: 'high',
        progress: 100,
        completedDate: '2024-11-25',
        assignee: {
          name: 'Tom Wilson',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
        },
        details: 'Secure authentication system with multi-factor authentication and password recovery.'
      },
      {
        id: '9',
        title: 'Payment Integration',
        description: 'Integrate payment processing system',
        status: 'completed',
        priority: 'medium',
        progress: 100,
        completedDate: '2024-11-20',
        assignee: {
          name: 'Anna Taylor',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
        },
        details: 'Integrated Stripe payment processing with support for multiple currencies.'
      },
      {
        id: '10',
        title: 'Email Templates',
        description: 'Design and implement email templates',
        status: 'completed',
        priority: 'low',
        progress: 100,
        completedDate: '2024-11-15',
        assignee: {
          name: 'Chris Martin',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
        },
        details: 'Responsive email templates for notifications, newsletters, and transactional emails.'
      }
    ]
  }
];
