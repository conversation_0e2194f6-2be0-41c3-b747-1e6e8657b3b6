import OpenAI from 'openai';

export interface ParsedContent {
  title: string;
  eventLog: string;
  description: string;
  tasks: ParsedMainTask[];
}

export interface ParsedMainTask {
  title: string;
  description: string;
  subtasks: ParsedSubtask[];
}

export interface ParsedSubtask {
  id: string;
  description: string;
}

export class AIParsingService {
  private client: OpenAI | null = null;
  private isConfigured: boolean = false;
  private primaryModel: string = 'google/gemini-2.5-flash-lite-preview-06-17';
  private fallbackModel: string = 'deepseek/deepseek-chat-v3-0324';

  constructor() {
    this.initialize();
  }

  private initialize() {
    try {
      const openRouterKey = process.env.OPENROUTER_API_KEY;
      const openAiKey = process.env.OPENAI_API_KEY;

      if (!openRouterKey && !openAiKey) {
        return;
      }

      // Configure for OpenRouter or OpenAI
      if (openRouterKey) {
        this.client = new OpenAI({
          apiKey: openRouterKey,
          baseURL: 'https://openrouter.ai/api/v1',
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2'),
          defaultHeaders: {
            'HTTP-Referer': process.env.OPENROUTER_REFERER || 'http://localhost:3000',
            'X-Title': process.env.OPENROUTER_APP_NAME || 'Prosperous Codex'
          }
        });
      } else {
        this.client = new OpenAI({
          apiKey: openAiKey,
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2')
        });
      }

      this.isConfigured = true;

    } catch (error) {
      this.client = null;
      this.isConfigured = false;
    }
  }

  /**
   * Check if the service is available and properly configured
   */
  public isAvailable(): boolean {
    return this.client !== null && this.isConfigured;
  }

  /**
   * Check if a model supports reasoning configuration
   * For parsing service, we disable reasoning/thinking mode for more consistent JSON output
   */
  private supportsReasoning(model: string): boolean {
    // Enable reasoning config for models that support it, but we'll set thinking_budget to 0
    const reasoningModels = [
      'google/gemini-2.5-flash-lite-preview-06-17',
    ];
    return reasoningModels.includes(model);
  }

  /**
   * Get reasoning configuration for models that support it
   * For parsing service, we disable reasoning/thinking mode by setting thinking_budget to 0
   */
  private getReasoningConfig(model: string) {
    if (this.supportsReasoning(model)) {
      return {
        thinking_budget: 0 // Set to 0 to disable thinking entirely
      };
    }
    return undefined;
  }

  /**
   * Parse structured content using the draft parsing prompt
   */
  public async parseStructuredContent(inputText: string): Promise<ParsedContent> {
    if (!this.isAvailable()) {
      throw new Error('AI Parsing Service is not available. Please check API key configuration.');
    }

    // Use the updated parsing prompt
    const systemPrompt = `Prompt: Intelligent Text-to-JSON Parsing Service

Role: You are a specialized, automated data transformation service. Your primary role is to intelligently parse a block of semi-structured text and convert it into a single, valid JSON object.

Core Function:

Receive a text input that may contain sections like "Event Log", "Description", and "Tasks".

Intelligently identify these sections based on CamelCase fixed markers (e.g., ---EventLog---, ---Description---, ---TaskAssignment---) or common written heading.

Extract the content from each identified section according to the specific rules below.

Construct a single, valid JSON object based on the provided schema.

Critical Output Requirements:

JSON Only: Your entire output must be only the raw JSON object. Do not include any explanatory text, greetings, or markdown code fences like \`\`\`json ... \`\`\`.

Adhere to Schema: The generated JSON must strictly follow the keys and data types defined in the JSON Schema section.

Handle Missing Sections: If a section is not present in the input text, its corresponding key in the JSON object should have a null or empty value (e.g., "eventLog": "" or "tasks": []).

Handle Ambiguity: If the input is too unstructured to confidently map to the schema, return a JSON object with an error key: {"error": "Input text is too unstructured to parse."}.

Parsing Rules & Logic:

1. title Field:

Extract the project title from the "Subject:" line at the beginning of the content.

Look for a line that starts with "Subject:" and extract everything after "Subject:" as the title.

If no "Subject:" line is found, set the title field to an empty string.

2. eventLog Field:

Extract only the content between ---EventLog--- and ---EndOfEventLog--- markers.

Do NOT include the start marker (---EventLog---) or end marker (---EndOfEventLog---) in the extracted content.

The eventLog field should contain only the pure content within these boundaries.

3. description Field:

Extract only the content between ---Description--- and ---EndOfDescription--- markers.

Do NOT include the start marker (---Description---) or end marker (---EndOfDescription---) in the extracted content.

The description field should contain only the pure content within these boundaries.

4. tasks Field:

Extract only the content between ---TaskAssignment--- and ---EndOfTaskAssignment--- markers.

Do NOT include the start marker (---TaskAssignment---) or end marker (---EndOfTaskAssignment---) in the extracted content.

When parsing the "TaskAssignment" section, you must create a hierarchical structure with main tasks and subtasks:

MAIN TASKS: Lines that start with major indicators like "»", "Task", or numbered items without decimal points (1, 2, 3, etc.)
- The title field should capture the entire line including any leading characters and numbers
- The description field should contain a brief summary or the same as title if no separate description exists
- Each main task should have a subtasks array (can be empty if no subtasks)

SUBTASKS: Lines that are indented or have decimal numbering (1.1, 1.2, 2.1, etc.) under a main task
- These should be placed in the subtasks array of their parent main task
- The id field should capture the number/identifier (e.g., "1.1", "1.2")
- The description field should capture the text that follows the identifier

HIERARCHICAL STRUCTURE: Group subtasks under their corresponding main tasks based on numbering or indentation.

IMPORTANT: Each main task MUST have its own object in the tasks array, and subtasks MUST be nested within their parent main task's subtasks array. Do NOT create separate main task entries for subtasks.

JSON Schema:

{
  "title": "string",
  "eventLog": "string",
  "description": "string",
  "tasks": [
    {
      "title": "string",
      "description": "string",
      "subtasks": [
        {
          "id": "string",
          "description": "string"
        }
      ]
    }
  ]
}

Example Output for Task Structure:

Input:
---TaskAssignment---
» Task 1: Setup Development Environment
  1.1 Install Node.js and npm
  1.2 Configure development database
  1.3 Setup testing framework

» Task 2: Design User Interface
  2.1 Create wireframes
  2.2 Design mockups
---EndOfTaskAssignment---

Expected JSON:
{
  "tasks": [
    {
      "title": "» Task 1: Setup Development Environment",
      "description": "Setup Development Environment",
      "subtasks": [
        {
          "id": "1.1",
          "description": "Install Node.js and npm"
        },
        {
          "id": "1.2",
          "description": "Configure development database"
        },
        {
          "id": "1.3",
          "description": "Setup testing framework"
        }
      ]
    },
    {
      "title": "» Task 2: Design User Interface",
      "description": "Design User Interface",
      "subtasks": [
        {
          "id": "2.1",
          "description": "Create wireframes"
        },
        {
          "id": "2.2",
          "description": "Design mockups"
        }
      ]
    }
  ]
}

Input Text to be Parsed:`;

    const userPrompt = inputText;

    // Try primary model first, then fallback
    let completion;

    try {
      // Build request configuration
      const requestConfig: any = {
        model: this.primaryModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: 16384,
        temperature: 0.2,
        top_p: 0.1,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      };

      // Add reasoning configuration for models that support it
      const reasoningConfig = this.getReasoningConfig(this.primaryModel);
      if (reasoningConfig) {
        requestConfig.reasoning = reasoningConfig;
      }

      completion = await this.client!.chat.completions.create(requestConfig);

    } catch (error) {

      try {
        // Build fallback request configuration
        const fallbackRequestConfig: any = {
          model: this.fallbackModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: 16384,
          temperature: 0.2,
          top_p: 0.1,
          frequency_penalty: 0.1,
          presence_penalty: 0.1
        };

        // Add reasoning configuration for fallback model if it supports it
        const fallbackReasoningConfig = this.getReasoningConfig(this.fallbackModel);
        if (fallbackReasoningConfig) {
          fallbackRequestConfig.reasoning = fallbackReasoningConfig;
        }

        completion = await this.client!.chat.completions.create(fallbackRequestConfig);

      } catch (fallbackError) {
        throw new Error('All AI models failed to process the request');
      }
    }

    try {
      const responseText = completion.choices[0]?.message?.content;

      if (!responseText) {
        throw new Error('No response received from AI service');
      }

      // Parse and validate JSON
      return this.validateParsedContent(responseText);

    } catch (error) {

      if (error instanceof OpenAI.APIError) {
        if (error.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        if (error.status === 401) {
          throw new Error('API key authentication failed');
        }
        if (error.status === 400 && error.message.includes('content_filter')) {
          throw new Error('Content cannot be processed due to safety guidelines');
        }
      }

      throw new Error(`AI parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private validateParsedContent(responseText: string): ParsedContent {
    try {
      // Clean response text (remove any markdown formatting if present)
      const cleanedText = responseText.trim().replace(/^```json\s*|\s*```$/g, '');
      
      const parsed = JSON.parse(cleanedText);
      
      // Check for error response
      if (parsed.error) {
        throw new Error(`Parsing failed: ${parsed.error}`);
      }

      // Validate and sanitize structure
      const result: ParsedContent = {
        title: typeof parsed.title === 'string' ? parsed.title : '',
        eventLog: typeof parsed.eventLog === 'string' ? parsed.eventLog : '',
        description: typeof parsed.description === 'string' ? parsed.description : '',
        tasks: Array.isArray(parsed.tasks) ? parsed.tasks.map((task: any) => ({
          title: typeof task.title === 'string' ? task.title : '',
          description: typeof task.description === 'string' ? task.description : '',
          subtasks: Array.isArray(task.subtasks) ? task.subtasks.map((subtask: any) => ({
            id: typeof subtask.id === 'string' ? subtask.id : '',
            description: typeof subtask.description === 'string' ? subtask.description : ''
          })) : []
        })) : []
      };

      return result;

    } catch (error) {
      throw new Error('Failed to parse AI response as valid JSON');
    }
  }
}

// Export singleton instance
export const aiParsingService = new AIParsingService();
