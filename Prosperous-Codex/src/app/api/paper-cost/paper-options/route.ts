import { NextRequest, NextResponse } from 'next/server';
import { PaperCostService } from '@/lib/database/paper-cost-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    
    const paperCostService = new PaperCostService();
    
    // Get user's paper options
    const paperOptions = paperCostService.getUserPaperOptions(parseInt(user.id), category || undefined);
    
    return NextResponse.json({ paperOptions });
    
  } catch (error) {
    console.error('Get paper options error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const paperData = await request.json();
    
    // Validate required fields
    const requiredFields = ['category', 'name', 'source', 'caliper', 'gsm'];

    // Check basic required fields
    for (const field of requiredFields) {
      if (!paperData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Check paper ID (either camelCase or snake_case)
    if (!paperData.paperId && !paperData.paper_id) {
      return NextResponse.json(
        { error: 'Missing required field: paperId or paper_id' },
        { status: 400 }
      );
    }

    // Check sheet width (either camelCase or snake_case)
    if (!paperData.sheetWidth && !paperData.sheet_width) {
      return NextResponse.json(
        { error: 'Missing required field: sheetWidth or sheet_width' },
        { status: 400 }
      );
    }

    // Check grain direction (either camelCase or snake_case)
    if (!paperData.grainDirection && !paperData.grain_direction) {
      return NextResponse.json(
        { error: 'Missing required field: grainDirection or grain_direction' },
        { status: 400 }
      );
    }
    
    // Validate category
    if (!['Inner Text', 'Cover', 'Endpapers'].includes(paperData.category)) {
      return NextResponse.json(
        { error: 'Invalid category. Must be "Inner Text", "Cover", or "Endpapers"' },
        { status: 400 }
      );
    }
    
    // Validate numeric fields - support both camelCase and snake_case
    const sheetWidth = paperData.sheetWidth || paperData.sheet_width;
    const caliper = paperData.caliper;
    const gsm = paperData.gsm;

    if (isNaN(parseFloat(sheetWidth))) {
      return NextResponse.json(
        { error: 'Invalid sheetWidth/sheet_width. Must be a number' },
        { status: 400 }
      );
    }

    if (isNaN(parseFloat(caliper))) {
      return NextResponse.json(
        { error: 'Invalid caliper. Must be a number' },
        { status: 400 }
      );
    }

    if (isNaN(parseFloat(gsm))) {
      return NextResponse.json(
        { error: 'Invalid gsm. Must be a number' },
        { status: 400 }
      );
    }

    // Validate optional numeric fields - support both camelCase and snake_case
    const sheetHeight = paperData.sheetHeight || paperData.sheet_height;
    if (sheetHeight !== undefined && sheetHeight !== null && isNaN(parseFloat(sheetHeight))) {
      return NextResponse.json(
        { error: 'Invalid sheetHeight/sheet_height. Must be a number' },
        { status: 400 }
      );
    }
    
    if (paperData.cost_per_ream !== undefined && paperData.cost_per_ream !== null && isNaN(parseFloat(paperData.cost_per_ream))) {
      return NextResponse.json(
        { error: 'Invalid cost_per_ream. Must be a number' },
        { status: 400 }
      );
    }
    
    if (paperData.cost_per_ton !== undefined && paperData.cost_per_ton !== null && isNaN(parseFloat(paperData.cost_per_ton))) {
      return NextResponse.json(
        { error: 'Invalid cost_per_ton. Must be a number' },
        { status: 400 }
      );
    }
    
    const paperCostService = new PaperCostService();
    
    // Add paper option - normalize field names to snake_case for database
    const normalizedData = {
      category: paperData.category,
      paper_id: paperData.paperId || paperData.paper_id,
      name: paperData.name,
      source: paperData.source,
      sheet_height: sheetHeight ? parseFloat(sheetHeight) : undefined,
      sheet_width: parseFloat(sheetWidth),
      grain_direction: paperData.grainDirection || paperData.grain_direction,
      caliper: parseInt(caliper),
      cost_per_ream: (paperData.costPerReam || paperData.cost_per_ream) ? parseFloat(paperData.costPerReam || paperData.cost_per_ream) : undefined,
      gsm: parseInt(gsm),
      cost_per_ton: (paperData.costPerTon || paperData.cost_per_ton) ? parseFloat(paperData.costPerTon || paperData.cost_per_ton) : undefined,
      is_custom: paperData.isCustom || paperData.is_custom || true
    };

    const paperOption = paperCostService.addPaperOption(parseInt(user.id), normalizedData);
    
    if (paperOption) {
      return NextResponse.json({ 
        message: 'Paper option added successfully',
        paperOption 
      }, { status: 201 });
    } else {
      return NextResponse.json(
        { error: 'Failed to add paper option' },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Add paper option error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { searchParams } = new URL(request.url);
    const paperOptionId = searchParams.get('id');
    
    // Validate required fields
    if (!paperOptionId) {
      return NextResponse.json(
        { error: 'Missing required query parameter: id' },
        { status: 400 }
      );
    }
    
    const paperOptionIdNum = parseInt(paperOptionId);
    if (isNaN(paperOptionIdNum)) {
      return NextResponse.json(
        { error: 'Invalid paper option ID' },
        { status: 400 }
      );
    }
    
    const paperCostService = new PaperCostService();
    
    // Delete paper option (only custom ones)
    const success = paperCostService.deletePaperOption(
      parseInt(user.id),
      paperOptionIdNum
    );
    
    if (success) {
      return NextResponse.json({ 
        message: 'Paper option deleted successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Paper option not found, not custom, or failed to delete' },
        { status: 404 }
      );
    }
    
  } catch (error) {
    console.error('Delete paper option error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
