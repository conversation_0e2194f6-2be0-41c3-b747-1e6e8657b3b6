import { Database } from 'better-sqlite3';

export interface Migration {
  version: number;
  description: string;
  up: (db: Database) => void;
  down?: (db: Database) => void;
}

/**
 * Database migrations to handle schema changes without data loss
 */
export const migrations: Migration[] = [
  {
    version: 1,
    description: 'Initial schema',
    up: (_db: Database) => {
      // This is handled by the main schema.sql file
      console.log('✅ Initial schema applied');
    }
  },
  {
    version: 2,
    description: 'Add Task Master tables',
    up: (db: Database) => {
      // Check if tables already exist
      const tablesExist = db.prepare(`
        SELECT COUNT(*) as count 
        FROM sqlite_master 
        WHERE type='table' AND name IN ('projects', 'tasks', 'project_team_members', 'project_files', 'project_comments', 'activity_log')
      `).get() as { count: number };

      if (tablesExist.count === 0) {
        console.log('🔧 Adding Task Master tables...');
        
        // Add Task Master tables if they don't exist
        db.exec(`
          CREATE TABLE IF NOT EXISTS projects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            full_description TEXT,
            status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'inProgress', 'completed')),
            priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
            progress INTEGER NOT NULL DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
            due_date TEXT,
            created_by INTEGER NOT NULL,
            assigned_to INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
          );

          CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            parent_task_id INTEGER,
            title TEXT NOT NULL,
            description TEXT,
            status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'inProgress', 'completed')),
            priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
            due_date TEXT,
            assigned_to INTEGER,
            created_by INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            FOREIGN KEY (parent_task_id) REFERENCES tasks(id) ON DELETE CASCADE,
            FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
          );

          CREATE TABLE IF NOT EXISTS project_team_members (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
            added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            added_by INTEGER NOT NULL,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE(project_id, user_id)
          );

          CREATE TABLE IF NOT EXISTS project_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            filename TEXT NOT NULL,
            original_filename TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_size INTEGER NOT NULL,
            mime_type TEXT,
            uploaded_by INTEGER NOT NULL,
            uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE
          );

          CREATE TABLE IF NOT EXISTS project_comments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            parent_comment_id INTEGER,
            author_id INTEGER NOT NULL,
            content TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            FOREIGN KEY (parent_comment_id) REFERENCES project_comments(id) ON DELETE CASCADE,
            FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
          );

          CREATE TABLE IF NOT EXISTS activity_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER,
            task_id INTEGER,
            user_id INTEGER NOT NULL,
            activity_type TEXT NOT NULL,
            description TEXT NOT NULL,
            metadata TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
          );
        `);
        
        console.log('✅ Task Master tables added successfully');
      } else {
        console.log('✅ Task Master tables already exist');
      }
    }
  },
  {
    version: 3,
    description: 'Add comment edit history tracking',
    up: (db: Database) => {
      console.log('🔧 Adding comment edit history table...');

      // Create comment edit history table
      db.exec(`
        CREATE TABLE IF NOT EXISTS comment_edit_history (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          comment_id INTEGER NOT NULL,
          version_number INTEGER NOT NULL,
          previous_content TEXT NOT NULL,
          edited_by INTEGER NOT NULL,
          edited_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (comment_id) REFERENCES project_comments(id) ON DELETE CASCADE,
          FOREIGN KEY (edited_by) REFERENCES users(id) ON DELETE CASCADE,
          UNIQUE(comment_id, version_number)
        );
      `);

      // Add indexes for efficient querying
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_comment_edit_history_comment_id ON comment_edit_history(comment_id);
        CREATE INDEX IF NOT EXISTS idx_comment_edit_history_edited_at ON comment_edit_history(edited_at);
        CREATE INDEX IF NOT EXISTS idx_comment_edit_history_edited_by ON comment_edit_history(edited_by);
      `);

      // Add fields to project_comments table to track edit status
      db.exec(`
        ALTER TABLE project_comments ADD COLUMN is_edited BOOLEAN DEFAULT FALSE;
        ALTER TABLE project_comments ADD COLUMN edit_count INTEGER DEFAULT 0;
        ALTER TABLE project_comments ADD COLUMN last_edited_at DATETIME;
        ALTER TABLE project_comments ADD COLUMN last_edited_by INTEGER REFERENCES users(id);
      `);

      // Update activity_log to include comment edit and delete types
      db.exec(`
        UPDATE activity_log SET activity_type = 'comment_edit'
        WHERE activity_type = 'comment' AND description LIKE '%Updated comment%';

        UPDATE activity_log SET activity_type = 'comment_delete'
        WHERE activity_type = 'comment' AND description LIKE '%Deleted comment%';
      `);

      console.log('✅ Comment edit history table added successfully');
    }
  },
  {
    version: 4,
    description: 'Add project field edit history tracking',
    up: (db: Database) => {
      console.log('🔧 Adding project field edit history table...');

      // Create project edit history table
      db.exec(`
        CREATE TABLE IF NOT EXISTS project_edit_history (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          project_id INTEGER NOT NULL,
          field_name TEXT NOT NULL CHECK (field_name IN ('full_description', 'event_log')),
          version_number INTEGER NOT NULL,
          previous_content TEXT NOT NULL,
          edited_by INTEGER NOT NULL,
          edited_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
          FOREIGN KEY (edited_by) REFERENCES users(id) ON DELETE CASCADE,
          UNIQUE(project_id, field_name, version_number)
        );
      `);

      // Add indexes for efficient querying
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_project_edit_history_project_id ON project_edit_history(project_id);
        CREATE INDEX IF NOT EXISTS idx_project_edit_history_field_name ON project_edit_history(field_name);
        CREATE INDEX IF NOT EXISTS idx_project_edit_history_edited_at ON project_edit_history(edited_at);
        CREATE INDEX IF NOT EXISTS idx_project_edit_history_edited_by ON project_edit_history(edited_by);
      `);

      // Add fields to projects table to track edit status for specific fields
      db.exec(`
        ALTER TABLE projects ADD COLUMN full_description_edit_count INTEGER DEFAULT 0;
        ALTER TABLE projects ADD COLUMN full_description_last_edited_at DATETIME;
        ALTER TABLE projects ADD COLUMN full_description_last_edited_by INTEGER REFERENCES users(id);
        ALTER TABLE projects ADD COLUMN event_log_edit_count INTEGER DEFAULT 0;
        ALTER TABLE projects ADD COLUMN event_log_last_edited_at DATETIME;
        ALTER TABLE projects ADD COLUMN event_log_last_edited_by INTEGER REFERENCES users(id);
      `);

      console.log('✅ Project field edit history table added successfully');
    }
  },
  {
    version: 5,
    description: 'Add project visibility for collaborative workspace',
    up: (db: Database) => {
      console.log('🔧 Adding project visibility field...');

      // Check if visibility column already exists
      const columnExists = db.prepare(`
        SELECT COUNT(*) as count
        FROM pragma_table_info('projects')
        WHERE name = 'visibility'
      `).get() as { count: number };

      if (columnExists.count === 0) {
        // Add visibility field to projects table with default public visibility
        db.exec(`
          ALTER TABLE projects ADD COLUMN visibility TEXT DEFAULT 'public' CHECK (visibility IN ('public', 'private'));
        `);
        console.log('✅ Visibility column added successfully');
      } else {
        console.log('ℹ️ Visibility column already exists, skipping...');
      }

      // Update all existing projects to be public (collaborative by default)
      db.exec(`
        UPDATE projects SET visibility = 'public' WHERE visibility IS NULL;
      `);

      // Add index for efficient visibility filtering
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_projects_visibility ON projects(visibility);
      `);

      // Add composite index for common query patterns (visibility + status)
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_projects_visibility_status ON projects(visibility, status);
      `);

      console.log('✅ Project visibility field added successfully');
    }
  },
  {
    version: 6,
    description: 'Fix event log edit history initial state registration',
    up: (db: Database) => {
      console.log('🔧 Fixing event log edit history initial state registration...');

      try {
        // Get all projects that have content but no edit history baseline
        const projectsWithContentStmt = db.prepare(`
          SELECT p.id, p.full_description, p.event_log, p.created_by, p.created_at
          FROM projects p
          WHERE (p.full_description IS NOT NULL AND p.full_description != '')
             OR (p.event_log IS NOT NULL AND p.event_log != '')
        `);

        const projectsWithContent = projectsWithContentStmt.all() as Array<{
          id: number;
          full_description: string | null;
          event_log: string | null;
          created_by: number;
          created_at: string;
        }>;

        console.log(`Found ${projectsWithContent.length} projects with content to process...`);

        // For each project, check if it has version 0 baseline in edit history
        const checkBaselineStmt = db.prepare(`
          SELECT COUNT(*) as count
          FROM project_edit_history
          WHERE project_id = ? AND field_name = ? AND version_number = 0
        `);

        const insertBaselineStmt = db.prepare(`
          INSERT INTO project_edit_history (project_id, field_name, version_number, previous_content, edited_by, edited_at)
          VALUES (?, ?, 0, '', ?, ?)
        `);

        let fixedCount = 0;

        for (const project of projectsWithContent) {
          // Check and fix full_description baseline
          if (project.full_description && project.full_description.trim()) {
            const hasBaseline = checkBaselineStmt.get(project.id, 'full_description') as { count: number };
            if (hasBaseline.count === 0) {
              insertBaselineStmt.run(project.id, 'full_description', project.created_by, project.created_at);
              fixedCount++;
            }
          }

          // Check and fix event_log baseline
          if (project.event_log && project.event_log.trim()) {
            const hasBaseline = checkBaselineStmt.get(project.id, 'event_log') as { count: number };
            if (hasBaseline.count === 0) {
              insertBaselineStmt.run(project.id, 'event_log', project.created_by, project.created_at);
              fixedCount++;
            }
          }
        }

        console.log(`✅ Fixed ${fixedCount} missing baseline entries for event log edit history`);
      } catch (error) {
        console.error('❌ Error fixing event log edit history:', error);
        throw error;
      }
    }
  }
];

/**
 * Get current database version
 */
export function getCurrentVersion(db: Database): number {
  try {
    // Check if migrations table exists
    const tableExists = db.prepare(`
      SELECT COUNT(*) as count 
      FROM sqlite_master 
      WHERE type='table' AND name='migrations'
    `).get() as { count: number };

    if (tableExists.count === 0) {
      // Create migrations table
      db.exec(`
        CREATE TABLE migrations (
          version INTEGER PRIMARY KEY,
          description TEXT NOT NULL,
          applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      return 0;
    }

    // Get latest migration version
    const result = db.prepare('SELECT MAX(version) as version FROM migrations').get() as { version: number | null };
    return result.version || 0;
  } catch (error) {
    console.warn('Failed to get current migration version:', error);
    return 0;
  }
}

/**
 * Apply pending migrations
 */
export function applyMigrations(db: Database): void {
  const currentVersion = getCurrentVersion(db);
  const pendingMigrations = migrations.filter(m => m.version > currentVersion);

  if (pendingMigrations.length === 0) {
    console.log('✅ Database is up to date');
    return;
  }

  console.log(`🔧 Applying ${pendingMigrations.length} pending migrations...`);

  for (const migration of pendingMigrations) {
    try {
      console.log(`📦 Applying migration ${migration.version}: ${migration.description}`);
      
      // Apply migration in transaction
      const transaction = db.transaction(() => {
        migration.up(db);
        
        // Record migration
        db.prepare('INSERT INTO migrations (version, description) VALUES (?, ?)').run(
          migration.version,
          migration.description
        );
      });
      
      transaction();
      
      console.log(`✅ Migration ${migration.version} applied successfully`);
    } catch (error) {
      console.error(`❌ Failed to apply migration ${migration.version}:`, error);
      throw error;
    }
  }

  console.log('✅ All migrations applied successfully');
}
