import { NextRequest, NextResponse } from 'next/server';
import { PaperCostService } from '@/lib/database/paper-cost-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'saved' or 'history'
    const limit = searchParams.get('limit');
    
    const paperCostService = new PaperCostService();
    
    if (type === 'history') {
      // Get calculation history
      const limitNum = limit ? parseInt(limit) : 50;
      const history = paperCostService.getCalculationHistory(parseInt(user.id), limitNum);
      
      return NextResponse.json({ history });
    } else {
      // Get saved calculations (default)
      const calculations = paperCostService.getSavedCalculations(parseInt(user.id));
      
      return NextResponse.json({ calculations });
    }
    
  } catch (error) {
    console.error('Get calculations error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const requestData = await request.json();
    const { type, ...data } = requestData;
    
    const paperCostService = new PaperCostService();
    
    if (type === 'save') {
      // Save calculation
      const { project_name, tab_id, job_inputs, selected_paper_id, calculation_results } = data;
      
      // Validate required fields
      if (!project_name || !tab_id || !job_inputs) {
        return NextResponse.json(
          { error: 'Missing required fields: project_name, tab_id, job_inputs' },
          { status: 400 }
        );
      }
      
      // Validate tab_id
      if (!['innerText', 'cover', 'endpapers'].includes(tab_id)) {
        return NextResponse.json(
          { error: 'Invalid tab_id. Must be innerText, cover, or endpapers' },
          { status: 400 }
        );
      }
      
      const calculation = paperCostService.saveCalculation(parseInt(user.id), {
        project_name,
        tab_id,
        job_inputs: typeof job_inputs === 'string' ? job_inputs : JSON.stringify(job_inputs),
        selected_paper_id,
        calculation_results: calculation_results ? 
          (typeof calculation_results === 'string' ? calculation_results : JSON.stringify(calculation_results)) 
          : undefined
      });
      
      if (calculation) {
        return NextResponse.json({ 
          message: 'Calculation saved successfully',
          calculation 
        }, { status: 201 });
      } else {
        return NextResponse.json(
          { error: 'Failed to save calculation' },
          { status: 500 }
        );
      }
      
    } else if (type === 'history') {
      // Add to calculation history
      const { tab_id, job_inputs, paper_options, results } = data;
      
      // Validate required fields
      if (!tab_id || !job_inputs || !paper_options || !results) {
        return NextResponse.json(
          { error: 'Missing required fields: tab_id, job_inputs, paper_options, results' },
          { status: 400 }
        );
      }
      
      const success = paperCostService.addCalculationHistory(parseInt(user.id), {
        tab_id,
        job_inputs: typeof job_inputs === 'string' ? job_inputs : JSON.stringify(job_inputs),
        paper_options: typeof paper_options === 'string' ? paper_options : JSON.stringify(paper_options),
        results: typeof results === 'string' ? results : JSON.stringify(results)
      });
      
      if (success) {
        return NextResponse.json({ 
          message: 'Added to calculation history'
        }, { status: 201 });
      } else {
        return NextResponse.json(
          { error: 'Failed to add to calculation history' },
          { status: 500 }
        );
      }
      
    } else {
      return NextResponse.json(
        { error: 'Invalid type. Must be "save" or "history"' },
        { status: 400 }
      );
    }
    
  } catch (error) {
    console.error('Save calculation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { searchParams } = new URL(request.url);
    const calculationId = searchParams.get('id');
    
    // Validate required fields
    if (!calculationId) {
      return NextResponse.json(
        { error: 'Missing required query parameter: id' },
        { status: 400 }
      );
    }
    
    const calculationIdNum = parseInt(calculationId);
    if (isNaN(calculationIdNum)) {
      return NextResponse.json(
        { error: 'Invalid calculation ID' },
        { status: 400 }
      );
    }
    
    const paperCostService = new PaperCostService();
    
    // Delete calculation
    const success = paperCostService.deleteCalculation(
      parseInt(user.id),
      calculationIdNum
    );
    
    if (success) {
      return NextResponse.json({ 
        message: 'Calculation deleted successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Calculation not found or failed to delete' },
        { status: 404 }
      );
    }
    
  } catch (error) {
    console.error('Delete calculation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
