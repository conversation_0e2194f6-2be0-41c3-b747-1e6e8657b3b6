/**
 * Database Types (snake_case)
 * 
 * These types represent the raw database structure with snake_case field names.
 * They should only be used in database operations and service layers.
 * 
 * For API and frontend usage, use types from api.ts and frontend.ts instead.
 */

/**
 * Base database entity structure
 */
export interface BaseDbEntity {
  id: number;
  created_at: string;
  updated_at: string;
}

/**
 * Task status and priority enums
 */
export type TaskStatus = 'todo' | 'inProgress' | 'completed';
export type TaskPriority = 'low' | 'medium' | 'high';
export type ProjectVisibility = 'public' | 'private';
export type TeamMemberRole = 'owner' | 'admin' | 'member' | 'viewer';
export type ActivityType = 'upload' | 'comment' | 'comment_edit' | 'comment_delete' | 
  'status_change' | 'assignment' | 'due_date' | 'completion' | 'creation' | 'update' |
  'task_creation' | 'task_completion';

/**
 * Raw database project structure (snake_case)
 */
export interface ProjectDbRow extends BaseDbEntity {
  title: string;
  description?: string;
  full_description?: string;
  event_log?: string;
  status: TaskStatus;
  priority: TaskPriority;
  progress: number;
  due_date?: string;
  completed_date?: string;
  visibility: ProjectVisibility;
  created_by: number;
  assigned_to?: number;
  
  // Edit tracking fields
  full_description_edit_count?: number;
  full_description_last_edited_at?: string;
  full_description_last_edited_by?: number;
  event_log_edit_count?: number;
  event_log_last_edited_at?: string;
  event_log_last_edited_by?: number;
  
  // Joined data from queries
  created_by_username?: string;
  assigned_to_username?: string;
  full_description_last_edited_by_username?: string;
  event_log_last_edited_by_username?: string;
}

/**
 * Raw database task structure (snake_case)
 */
export interface TaskDbRow extends BaseDbEntity {
  project_id: number;
  parent_task_id?: number;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  due_date?: string;
  completed_date?: string;
  assigned_to?: number;
  created_by: number;
  
  // Joined data from queries
  assigned_to_username?: string;
  created_by_username?: string;
}

/**
 * Raw database team member structure (snake_case)
 */
export interface ProjectTeamMemberDbRow extends BaseDbEntity {
  project_id: number;
  user_id: number;
  role: TeamMemberRole;
  added_at: string;
  added_by?: number;
  
  // Joined user data from queries
  username?: string;
  email?: string;
  avatar?: string;
}

/**
 * Raw database file structure (snake_case)
 */
export interface ProjectFileDbRow extends BaseDbEntity {
  project_id: number;
  file_name: string;
  file_type?: string;
  file_size?: number;
  file_path: string;
  thumbnail_path?: string;
  uploaded_by: number;
  uploaded_at: string;
  
  // Joined data from queries
  uploaded_by_username?: string;
}

/**
 * Raw database comment structure (snake_case)
 */
export interface ProjectCommentDbRow extends BaseDbEntity {
  project_id: number;
  parent_comment_id?: number;
  author_id: number;
  content: string;
  
  // Edit tracking fields
  is_edited?: boolean;
  edit_count?: number;
  last_edited_at?: string;
  last_edited_by?: number;
  
  // Joined data from queries
  author_username?: string;
  author_email?: string;
  last_edited_by_username?: string;
}

/**
 * Raw database comment edit history structure (snake_case)
 */
export interface CommentEditHistoryDbRow extends BaseDbEntity {
  comment_id: number;
  version_number: number;
  previous_content: string;
  edited_by: number;
  edited_at: string;
  
  // Joined data from queries
  edited_by_username?: string;
}

/**
 * Raw database project edit history structure (snake_case)
 */
export interface ProjectEditHistoryDbRow extends BaseDbEntity {
  project_id: number;
  field_name: 'full_description' | 'event_log';
  version_number: number;
  previous_content: string;
  edited_by: number;
  edited_at: string;
  
  // Joined data from queries
  edited_by_username?: string;
}

/**
 * Raw database activity log structure (snake_case)
 */
export interface ActivityLogEntryDbRow extends BaseDbEntity {
  project_id?: number;
  task_id?: number;
  user_id: number;
  activity_type: ActivityType;
  description: string;
  metadata?: string;
  
  // Joined data from queries
  username?: string;
}

/**
 * Raw database user structure (snake_case)
 */
export interface UserDbRow extends BaseDbEntity {
  email: string;
  username?: string;
  password_hash: string;
  role: string;
  is_active: boolean;
  last_login?: string;
  avatar?: string;
  language?: string;
}

/**
 * Raw database paper option structure (snake_case)
 */
export interface PaperOptionDbRow extends BaseDbEntity {
  user_id: number;
  category: string;
  paper_id: string;
  name: string;
  source: string;
  sheet_height?: number;
  sheet_width: number;
  grain_direction: string;
  caliper: number;
  cost_per_ream?: number;
  gsm: number;
  cost_per_ton?: number;
  is_custom: boolean;
}

/**
 * Raw database calculation result structure (snake_case)
 */
export interface CalculationResultDbRow extends BaseDbEntity {
  user_id: number;
  tab_id: string;
  job_inputs: string; // JSON string
  paper_options: string; // JSON string
  calculation_results: string; // JSON string
  selected_paper_id?: string;
}

/**
 * Database query result types
 */
export interface QueryResult {
  changes: number;
  lastInsertRowid?: number;
}

export interface DatabaseInfo {
  version: string;
  tables: string[];
  indexes: string[];
}

/**
 * Database transaction options
 */
export interface TransactionOptions {
  immediate?: boolean;
  deferred?: boolean;
  exclusive?: boolean;
}
