"use client";

import { useSession } from 'next-auth/react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Bell, Settings, CalendarIcon } from 'lucide-react';
import { useState } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';

export function DashboardHeader() {
  const { data: session } = useSession();
  const currentUser = session?.user;
  const router = useRouter();
  const t = useTranslations('dashboard.header');
  const tNav = useTranslations('navigation');
  const [date, setDate] = useState<Date | undefined>(new Date());

  const getInitials = (email: string, name?: string) => {
    if (name) {
      return name.charAt(0).toUpperCase();
    }
    return email.charAt(0).toUpperCase();
  };

  const getDisplayName = (email: string, name?: string) => {
    if (name) {
      return name;
    }
    return email.split('@')[0];
  };

  if (!currentUser) return null;

  return (
    <div className="flex w-full items-start gap-4">
      <div className="flex grow shrink-0 basis-0 flex-col items-start justify-center gap-1">
        <div className="flex items-center gap-3">
          <Avatar size="large" variant="brand">
            <AvatarImage src={null} alt={getDisplayName(currentUser.email || '', currentUser.name)} />
            <AvatarFallback size="large">
              {getInitials(currentUser.email || '', currentUser.name)}
            </AvatarFallback>
          </Avatar>
          <span className="text-xl font-semibold text-foreground">
            {t('welcome', { name: getDisplayName(currentUser.email || '', currentUser.name) })}
          </span>
        </div>
        <span className="text-sm text-muted-foreground">
          {t('subtitle')}
        </span>
      </div>
      <div className="flex items-center gap-2">
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="brand-secondary">
              <CalendarIcon className="h-4 w-4" />
              Calendar
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="center">
            <Calendar
              mode="single"
              selected={date}
              onSelect={setDate}
              initialFocus
            />
          </PopoverContent>
        </Popover>
        <Button variant="neutral-secondary">
          <Bell className="h-4 w-4" />
          {tNav('notifications')}
        </Button>
        <Button
          variant="default"
          onClick={() => router.push('/settings')}
        >
          <Settings className="h-4 w-4" />
          {tNav('settings')}
        </Button>
      </div>
    </div>
  );
}
