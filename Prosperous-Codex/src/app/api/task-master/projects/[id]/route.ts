import { NextRequest } from 'next/server';
import { z } from 'zod';
import { TaskMasterService } from '@/lib/database/task-master-service';
import {
  parseFields,
  getLoadingOptions,
  withPartialResponse,
  validateFields
} from '@/lib/task-master/partial-response';
import {
  withTaskMasterMiddleware,
  createApiResponse,
  createErrorResponse,
  ApiContext
} from '@/lib/task-master/api-middleware';
import { ProjectSchemas } from '@/lib/task-master/schemas';
import { FieldMapper } from '@/lib/task-master/field-mapping';

export const GET = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    try {
      const { user, params, query } = context;

      // Params are already validated by middleware using ProjectSchemas.idParam
      const projectId = params!.id as number;

      // Parse query parameters for selective loading and partial response
      const fieldsParam = query?.fields as string;
      const requestedFields = parseFields(fieldsParam, 'project');
      const loadingOptions = getLoadingOptions(requestedFields);

      // Validate fields if provided
      if (fieldsParam && !fieldsParam.includes(',') && !['basic', 'summary', 'full', 'withRelations'].includes(fieldsParam)) {
        const validation = validateFields(fieldsParam, 'project');
        if (!validation.valid) {
          return createApiResponse(undefined, {
            error: 'Invalid fields parameter',
            status: 400,
            requestId: context.requestId,
            details: {
              invalidFields: validation.invalidFields,
              suggestions: validation.suggestions
            }
          });
        }
      }



      // Legacy support for explicit include parameters (override fields-based loading)
      const includeTasks = query?.includeTasks === 'true' || loadingOptions.includeTasks;
      const includeActivity = query?.includeActivity === 'true' || loadingOptions.includeActivity;
      const includeFiles = query?.includeFiles === 'true' || loadingOptions.includeFiles;
      const includeComments = query?.includeComments === 'true' || loadingOptions.includeComments;
      const includeTeamMembers = query?.includeTeamMembers === 'true' || loadingOptions.includeTeamMembers;

      // Pagination parameters
      const activityLimit = parseInt(query?.activityLimit as string || '50');
      const activityOffset = parseInt(query?.activityOffset as string || '0');
      const filesLimit = parseInt(query?.filesLimit as string || '20');
      const filesOffset = parseInt(query?.filesOffset as string || '0');
      const commentsLimit = parseInt(query?.commentsLimit as string || '20');
      const commentsOffset = parseInt(query?.commentsOffset as string || '0');

      const taskMasterService = new TaskMasterService();



      // Get project with selective loading
      const project = taskMasterService.getProjectById(projectId, {
        includeTasks,
        includeActivity: false, // We'll load this separately with pagination
        includeFiles: false,    // We'll load this separately with pagination
        includeComments: false, // We'll load this separately with pagination
        includeTeamMembers
      });



      if (!project) {
        return createApiResponse(undefined, {
          error: 'Project not found',
          status: 404,
          requestId: context.requestId
        });
      }

      // Check if user has access to this project
      const userProjects = taskMasterService.getUserProjects(parseInt(user.id));
      const hasAccess = userProjects.some(p => p.id === projectId);

      if (!hasAccess) {
        return createApiResponse(undefined, {
          error: 'Access denied',
          status: 403,
          requestId: context.requestId
        });
      }

      // Load additional data with pagination if requested
      const result: any = { project };

      if (includeActivity) {
        result.activity = await taskMasterService.getProjectActivity(projectId, {
          limit: activityLimit,
          offset: activityOffset
        });
      }

      if (includeFiles) {
        result.files = await taskMasterService.getProjectFiles(projectId, {
          limit: filesLimit,
          offset: filesOffset
        });
      }

      if (includeComments) {
        result.comments = await taskMasterService.getProjectComments(projectId, {
          limit: commentsLimit,
          offset: commentsOffset
        });
      }

      // Apply partial response filtering if fields parameter was provided
      if (fieldsParam) {
        const { data: filteredProject, meta } = withPartialResponse(result.project, 'project', fieldsParam);
        result.project = filteredProject;

        // Add metadata about field filtering
        result.meta = meta;
      }

      return createApiResponse(result);

    } catch (error) {
      console.error('Get project error:', error);
      return createErrorResponse(error, context.requestId);
    }
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateParams: ProjectSchemas.idParam,
    validateQuery: z.object({
      // Include options
      includeTasks: z.string().optional(),
      includeActivity: z.string().optional(),
      includeFiles: z.string().optional(),
      includeComments: z.string().optional(),
      includeTeamMembers: z.string().optional(),
      // Pagination options
      activityLimit: z.string().optional(),
      activityOffset: z.string().optional(),
      filesLimit: z.string().optional(),
      filesOffset: z.string().optional(),
      commentsLimit: z.string().optional(),
      commentsOffset: z.string().optional(),
      // Partial response
      fields: z.string().optional()
    }).optional()
  }
);

export const PUT = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    try {
      const { user, params, body } = context;

      // Params and body are already validated by middleware
      const projectId = params!.id as number;

      // Apply field mapping: camelCase (API) -> snake_case (Database)
      const updateData = FieldMapper.apiToDb(body);
    
      // Set completed_date if status is being changed to completed (using snake_case after mapping)
      if (updateData.status === 'completed' && !updateData.completed_date) {
        updateData.completed_date = new Date().toISOString();
        // Note: Progress should be calculated based on actual task completion, not status
      }

      // Clear completed_date if status is changed from completed (using snake_case after mapping)
      if (updateData.status && updateData.status !== 'completed') {
        updateData.completed_date = null;
      }

      const taskMasterService = new TaskMasterService();

      // Check if user has access to this project
      const userProjects = taskMasterService.getUserProjects(parseInt(user.id));
      const hasAccess = userProjects.some(p => p.id === projectId);

      if (!hasAccess) {
        return createApiResponse(undefined, {
          error: 'Access denied',
          status: 403,
          requestId: context.requestId
        });
      }

      // Update project (new signature expects user object)
      await taskMasterService.updateProject(projectId, user, updateData);

      // Get updated project
      const updatedProject = taskMasterService.getProjectById(projectId);

      return createApiResponse({
        message: 'Project updated successfully',
        project: updatedProject
      });

    } catch (error) {
      console.error('Update project error:', error);
      return createErrorResponse(error, context.requestId);
    }
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateParams: ProjectSchemas.idParam,
    validateBody: ProjectSchemas.update
  }
);

export const DELETE = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    try {
      const { user, params } = context;

      // Params are already validated by middleware
      const projectId = params!.id as number;

      const taskMasterService = new TaskMasterService();

      // Check if user has access to this project
      const userProjects = taskMasterService.getUserProjects(parseInt(user.id));
      const hasAccess = userProjects.some(p => p.id === projectId);

      if (!hasAccess) {
        return createApiResponse(undefined, {
          error: 'Access denied',
          status: 403,
          requestId: context.requestId
        });
      }

      // Delete project (new signature expects user object)
      await taskMasterService.deleteProject(projectId, user);

      return createApiResponse({ message: 'Project deleted successfully' });

    } catch (error) {
      console.error('Delete project error:', error);
      return createErrorResponse(error, context.requestId);
    }
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateParams: ProjectSchemas.idParam
  }
);
