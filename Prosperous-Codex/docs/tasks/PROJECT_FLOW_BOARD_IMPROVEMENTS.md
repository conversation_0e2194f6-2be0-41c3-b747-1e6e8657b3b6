# Task Master Project Flow Board Improvements

## Overview
This document outlines the detailed task list for implementing improvements to the Task Master Project Flow Board drawer component. These changes should be implemented in a separate session following the line break preservation fixes.

## Context from Previous Session
- ✅ Line break preservation issues have been fixed
- ✅ Optimistic UI updates implemented for Event Log field
- ✅ API middleware field mapping corrected (snake_case/camelCase)
- ✅ Text formatting utilities enhanced

## Implementation Requirements

### 1. Extend Optimistic UI Updates
**Goal**: Apply the "magical" instant update pattern to all editable project fields

**Current State**: Only Event Log has optimistic updates
**Target State**: All editable fields update instantly without loading delays

**Pattern to Follow** (from `saveEventLog()` function):
```typescript
const saveField = async () => {
  // 1. Optimistic update: immediate UI change
  const previousValue = currentValue;
  updateState(newValue);
  closeEditMode();

  // 2. Background API call
  try {
    const response = await fetch('/api/endpoint', { /* ... */ });
    if (!response.ok) {
      // 3. Silent revert on failure
      updateState(previousValue);
    }
  } catch (error) {
    // 4. Silent revert on error
    updateState(previousValue);
  }
};
```

### 2. Event Log Section UI Improvements
**Goal**: Clean up Event Log section appearance

**Changes Required**:
- Header: "Event log & Activity" → "Current Event Log"
- Remove: "Project Notes (Markdown supported)" label/text
- Layout: Input field directly under header

### 3. Comment Section Functionality Enhancements
**Goal**: Complete comment functionality with deletion, proper keyboard behavior, and text wrapping

**Features to Implement**:
- Comment deletion with permissions
- Shift+Enter for line breaks (not sending)
- Fix text overflow issues

## Key Files to Modify

| File | Purpose | Changes |
|------|---------|---------|
| `src/components/task-master/project-flow-board.tsx` | Main component | Optimistic updates, UI changes, comment features |
| `src/app/api/task-master/comments/[id]/route.ts` | Comment deletion API | Create DELETE endpoint |
| `src/lib/database/task-master-service.ts` | Database operations | Add comment deletion methods |

## Success Criteria

### User Experience
- ✅ All field edits appear instantly (no loading delays)
- ✅ Failed API calls revert silently (no error messages)
- ✅ Event Log section has clean, minimal appearance
- ✅ Comments support proper line breaks and deletion
- ✅ Long comment text wraps properly and remains selectable

### Technical
- ✅ All optimistic updates follow consistent pattern
- ✅ Error handling is silent and graceful
- ✅ Comment permissions enforced correctly
- ✅ No breaking changes to existing functionality

## Testing Strategy

### Optimistic Updates Testing
1. Edit each field and verify instant UI updates
2. Test with slow/disabled network
3. Verify silent error handling
4. Test rapid successive edits

### Comment Features Testing
1. Test comment deletion permissions
2. Verify Shift+Enter vs Enter behavior
3. Test text wrapping with long content
4. Integration testing with other features

## Implementation Notes

### Optimistic Update Pattern
- Always save previous state before updating
- Update UI immediately, close edit modes instantly
- Handle API calls in background
- Revert silently on any failure
- No loading states or error messages visible to user

### Comment Deletion Permissions
- Comment author: Can delete own comments
- Moderator/Admin: Can delete any comments
- Others: No delete option visible

### Text Wrapping CSS Classes
```css
.comment-content {
  @apply break-words overflow-wrap-anywhere whitespace-pre-wrap max-w-full;
}
```

## Dependencies
- Existing line break preservation fixes
- Current optimistic update implementation for Event Log
- FormattedText component functionality
- API middleware sanitization system

---

*This task list is designed for implementation in a separate session. Each task includes specific file paths, function names, code snippets, and testing requirements for precise execution.*
