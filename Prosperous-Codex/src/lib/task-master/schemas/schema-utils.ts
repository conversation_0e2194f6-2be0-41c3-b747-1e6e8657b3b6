/**
 * Schema Utilities
 * 
 * Common utilities for working with schemas
 */

import { z } from 'zod';

/**
 * Schema composition utilities
 */
export class SchemaComposer {
  /**
   * Merge multiple schemas into one
   */
  static merge<T extends z.ZodRawShape, U extends z.ZodRawShape>(
    schema1: z.ZodObject<T>,
    schema2: z.ZodObject<U>
  ): z.ZodObject<T & U> {
    return schema1.merge(schema2);
  }

  /**
   * Make all fields optional
   */
  static partial<T extends z.ZodRawShape>(
    schema: z.ZodObject<T>
  ): z.ZodObject<{ [K in keyof T]: z.ZodOptional<T[K]> }> {
    return schema.partial();
  }

  /**
   * Pick specific fields from schema
   */
  static pick<T extends z.ZodRawShape, K extends keyof T>(
    schema: z.ZodObject<T>,
    keys: K[]
  ): z.ZodObject<Pick<T, K>> {
    return schema.pick(Object.fromEntries(keys.map(k => [k, true])) as Record<K, true>);
  }

  /**
   * Omit specific fields from schema
   */
  static omit<T extends z.ZodRawShape, K extends keyof T>(
    schema: z.ZodObject<T>,
    keys: K[]
  ): z.ZodObject<Omit<T, K>> {
    return schema.omit(Object.fromEntries(keys.map(k => [k, true])) as Record<K, true>);
  }

  /**
   * Extend schema with additional fields
   */
  static extend<T extends z.ZodRawShape, U extends z.ZodRawShape>(
    schema: z.ZodObject<T>,
    extension: U
  ): z.ZodObject<T & U> {
    return schema.extend(extension);
  }
}

/**
 * Schema validation utilities
 */
export class SchemaValidator {
  /**
   * Validate data against schema with detailed error reporting
   */
  static validate<T>(schema: z.ZodSchema<T>, data: unknown): {
    success: boolean;
    data?: T;
    errors?: Array<{
      path: string;
      message: string;
      code: string;
    }>;
  } {
    const result = schema.safeParse(data);
    
    if (result.success) {
      return { success: true, data: result.data };
    }

    const errors = result.error.errors.map(err => ({
      path: err.path.join('.') || 'root',
      message: err.message,
      code: err.code,
    }));

    return { success: false, errors };
  }

  /**
   * Validate and transform data
   */
  static validateAndTransform<T, U>(
    schema: z.ZodSchema<T>,
    data: unknown,
    transformer: (data: T) => U
  ): { success: boolean; data?: U; errors?: any[] } {
    const validation = this.validate(schema, data);
    
    if (!validation.success) {
      return validation;
    }

    try {
      const transformed = transformer(validation.data!);
      return { success: true, data: transformed };
    } catch (error) {
      return {
        success: false,
        errors: [{
          path: 'transformation',
          message: error instanceof Error ? error.message : 'Transformation failed',
          code: 'transformation_error',
        }],
      };
    }
  }

  /**
   * Batch validate multiple items
   */
  static validateBatch<T>(
    schema: z.ZodSchema<T>,
    items: unknown[]
  ): {
    success: boolean;
    validItems: T[];
    invalidItems: Array<{ index: number; errors: any[] }>;
  } {
    const validItems: T[] = [];
    const invalidItems: Array<{ index: number; errors: any[] }> = [];

    items.forEach((item, index) => {
      const result = this.validate(schema, item);
      if (result.success) {
        validItems.push(result.data!);
      } else {
        invalidItems.push({ index, errors: result.errors || [] });
      }
    });

    return {
      success: invalidItems.length === 0,
      validItems,
      invalidItems,
    };
  }
}

/**
 * Schema transformation utilities
 */
export class SchemaTransformer {
  /**
   * Transform schema for API responses
   */
  static forApiResponse<T extends z.ZodRawShape>(
    schema: z.ZodObject<T>
  ): z.ZodObject<T & {
    success: z.ZodBoolean;
    timestamp: z.ZodString;
  }> {
    return schema.extend({
      success: z.boolean().default(true),
      timestamp: z.string().datetime().default(() => new Date().toISOString()),
    });
  }

  /**
   * Transform schema for database operations
   */
  static forDatabase<T extends z.ZodRawShape>(
    schema: z.ZodObject<T>
  ): z.ZodObject<T & {
    created_at: z.ZodString;
    updated_at: z.ZodString;
  }> {
    const now = new Date().toISOString();
    return schema.extend({
      created_at: z.string().datetime().default(now),
      updated_at: z.string().datetime().default(now),
    });
  }

  /**
   * Transform schema for pagination
   */
  static withPagination<T extends z.ZodRawShape>(
    schema: z.ZodObject<T>
  ): z.ZodObject<{
    data: z.ZodArray<z.ZodObject<T>>;
    pagination: z.ZodObject<{
      page: z.ZodNumber;
      limit: z.ZodNumber;
      total: z.ZodNumber;
      totalPages: z.ZodNumber;
      hasNext: z.ZodBoolean;
      hasPrev: z.ZodBoolean;
    }>;
  }> {
    return z.object({
      data: z.array(schema),
      pagination: z.object({
        page: z.number(),
        limit: z.number(),
        total: z.number(),
        totalPages: z.number(),
        hasNext: z.boolean(),
        hasPrev: z.boolean(),
      }),
    });
  }
}

/**
 * Schema caching for performance
 */
export class SchemaCache {
  private static cache = new Map<string, z.ZodSchema>();

  static set(key: string, schema: z.ZodSchema): void {
    this.cache.set(key, schema);
  }

  static get<T extends z.ZodSchema>(key: string): T | undefined {
    return this.cache.get(key) as T | undefined;
  }

  static has(key: string): boolean {
    return this.cache.has(key);
  }

  static clear(): void {
    this.cache.clear();
  }

  static getOrCreate<T extends z.ZodSchema>(
    key: string,
    factory: () => T
  ): T {
    if (this.has(key)) {
      return this.get<T>(key)!;
    }

    const schema = factory();
    this.set(key, schema);
    return schema;
  }
}

/**
 * Common schema patterns
 */
export const CommonPatterns = {
  /**
   * ID field pattern
   */
  id: z.number().int().positive(),

  /**
   * Timestamp pattern
   */
  timestamp: z.string().datetime(),

  /**
   * Email pattern
   */
  email: z.string().email(),

  /**
   * URL pattern
   */
  url: z.string().url(),

  /**
   * UUID pattern
   */
  uuid: z.string().uuid(),

  /**
   * Slug pattern
   */
  slug: z.string().regex(/^[a-z0-9-]+$/),

  /**
   * Phone number pattern
   */
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/),

  /**
   * Color hex pattern
   */
  hexColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/),

  /**
   * Semantic version pattern
   */
  semver: z.string().regex(/^\d+\.\d+\.\d+$/),
};
