"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Calendar, Flag, UserPlus, Upload, FileText, MessageSquare, Clock, ChevronDown, Edit2, Send, User, CheckCircle, Plus, Trash2, Settings, BookOpen, Eye, EyeOff, Crown, Shield } from 'lucide-react';
import TaskManagement from './task-management';
import FileUpload from './file-upload';
import TeamManagement from './team-management';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ProjectDescriptionText, CommentText, FormattedText } from '@/components/ui/formatted-text';
import { ProjectFlowBoardProps, TaskMaster, TeamMember, ProjectFile, ProjectComment, EventLogEntry } from '@/lib/types/task-master';
import { Project, ProjectTeamMember } from '@/lib/task-master/types';
import { ParsedContent, ParsedMainTask } from '@/lib/types/ai-parsing';
import { toast } from '@/hooks/use-toast';

// Temporary team member interface for optimistic UI updates
interface TemporaryTeamMember extends Omit<ProjectTeamMember, 'id' | 'addedAt' | 'createdAt' | 'updatedAt'> {
  tempId: string;
  userEmail: string;
  isTemporary: true;
}

// Activity log entry interface
interface ActivityLogEntry {
  id: string | number;
  activityType?: string;
  activity_type?: string;
  description: string;
  metadata?: string | Record<string, unknown>;
  username: string;
  createdAt?: string;
  created_at?: string;
}

// Floating drawer overlay component with clean animations
const DrawerOverlay = ({ children, open, onOpenChange }: {
  children: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) => {
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    if (open) {
      setShouldRender(true);
    } else {
      // Wait for exit animation to complete
      const timer = setTimeout(() => setShouldRender(false), 300);
      return () => clearTimeout(timer);
    }
  }, [open]);

  if (!shouldRender) return null;

  return (
    <div className="fixed inset-0 z-[300] flex items-center justify-end">
      {/* Backdrop */}
      <div 
        className={`absolute inset-0 transition-opacity duration-300 ${
          open ? 'opacity-100' : 'opacity-0'
        }`}
        onClick={() => onOpenChange(false)}
      />
      
      {/* Drawer Panel */}
      <div
        className={`relative h-full bg-white dark:bg-[#1A1A1A] shadow-2xl transition-transform duration-300 ease-out ${
          open ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{
          width: '460px',
          maxWidth: '460px'
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default function ProjectFlowBoard({
  open,
  onOpenChange,
  task,
  onProjectDetailsModalOpen,
  projectDescription: externalProjectDescription,
  onProjectDescriptionChange,
  onCreateTask,
  onUpdateTask,
  onDeleteTask,
  onFileUploaded,
  onFileDeleted,
  onTeamUpdated,
  onCommentAdded,
  onOptimisticDescriptionUpdate,
  onOptimisticStatusUpdate,
  currentUserId,
  onParseContent,
  isParsing: externalIsParsing,
  parseProgress: externalParseProgress,
  onWorkspaceRefresh
}: ProjectFlowBoardProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [commentText, setCommentText] = useState('');
  const [fullProjectData, setFullProjectData] = useState<Project | null>(null);
  const [isLoadingProject, setIsLoadingProject] = useState(false);
  const [eventLogText, setEventLogText] = useState('');
  const [isEditingEventLog, setIsEditingEventLog] = useState(false);

  // Parsing state
  const [isParsing, setIsParsing] = useState(false);
  const [parseProgress, setParseProgress] = useState<string>('');

  // Track if content has been parsed/edited and needs confirmation before closing
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showCloseConfirmation, setShowCloseConfirmation] = useState(false);
  const [originalProjectData, setOriginalProjectData] = useState<Project | null>(null);

  // Track task completion changes for batched submission
  const [pendingTaskUpdates, setPendingTaskUpdates] = useState<Map<number, { status: 'todo' | 'inProgress' | 'completed' }>>(new Map());

  // Track temporary team members for batched submission
  const [pendingTeamMembers, setPendingTeamMembers] = useState<Map<string, TemporaryTeamMember>>(new Map());

  // Track deleted tasks for batched submission
  const [pendingTaskDeletions, setPendingTaskDeletions] = useState<Set<number | string>>(new Set());

  // Track initial loading state to prevent interactions before data is ready
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [initialLoadingProgress, setInitialLoadingProgress] = useState('Loading project data...');

  // Track workspace refresh state
  const [isRefreshingWorkspace, setIsRefreshingWorkspace] = useState(false);

  // REMOVED: optimisticTasks state - using temporary state management instead

  const [projectTitleText, setProjectTitleText] = useState('');
  const [isEditingProjectTitle, setIsEditingProjectTitle] = useState(false);

  // Visibility state
  const [projectVisibility, setProjectVisibility] = useState<'public' | 'private'>('public');
  const [isTogglingVisibility, setIsTogglingVisibility] = useState(false);

  // Status state for cycling through statuses
  const [projectStatus, setProjectStatus] = useState<'todo' | 'inProgress' | 'completed'>('todo');
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  // Priority state for cycling through priorities
  const [projectPriority, setProjectPriority] = useState<'low' | 'medium' | 'high'>('low');
  const [isUpdatingPriority, setIsUpdatingPriority] = useState(false);

  // Due date state for date selection
  const [projectDueDate, setProjectDueDate] = useState<string | undefined>(undefined);
  const [isUpdatingDueDate, setIsUpdatingDueDate] = useState(false);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  const [comments, setComments] = useState<any[]>([]);
  const [isLoadingComments, setIsLoadingComments] = useState(false);
  const [isSendingComment, setIsSendingComment] = useState(false);
  const [activityLog, setActivityLog] = useState<any[]>([]);
  const [isLoadingActivity, setIsLoadingActivity] = useState(false);

  // Comment editing state
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editingCommentText, setEditingCommentText] = useState('');
  const [commentEditHistory, setCommentEditHistory] = useState<Record<string, any[]>>({});
  const [showEditHistory, setShowEditHistory] = useState<string | null>(null);
  const [isLoadingEditHistory, setIsLoadingEditHistory] = useState(false);

  // Project field edit history state
  const [projectFieldEditHistory, setProjectFieldEditHistory] = useState<Record<string, any[]>>({});
  const [showProjectFieldHistory, setShowProjectFieldHistory] = useState<string | null>(null);
  const [isLoadingProjectFieldHistory, setIsLoadingProjectFieldHistory] = useState(false);
  const [loadedProjectFieldVersions, setLoadedProjectFieldVersions] = useState<Record<string, number>>({});

  // Helper functions for project-scoped cache keys
  const getProjectFieldCacheKey = (projectId: number | string, fieldName: string) => `${projectId}-${fieldName}`;
  const getCommentCacheKey = (projectId: number | string, commentId: string) => `${projectId}-${commentId}`;

  // Helper functions for cleaner cache access
  const getProjectFieldHistory = (fieldName: 'fullDescription' | 'eventLog') => {
    if (!task?.id) return [];
    const cacheKey = getProjectFieldCacheKey(task.id, fieldName);
    return projectFieldEditHistory[cacheKey] || [];
  };

  const getProjectFieldVersionCount = (fieldName: 'fullDescription' | 'eventLog') => {
    if (!task?.id) return 0;
    const versionKey = getProjectFieldCacheKey(task.id, fieldName);
    return loadedProjectFieldVersions[versionKey] || 0;
  };

  const getCommentHistory = (commentId: string) => {
    if (!task?.id) return [];
    const cacheKey = getCommentCacheKey(task.id, commentId);
    return commentEditHistory[cacheKey] || [];
  };

  const updateProjectFieldHistory = (fieldName: 'fullDescription' | 'eventLog', history: any[]) => {
    if (!task?.id) return;
    const cacheKey = getProjectFieldCacheKey(task.id, fieldName);
    setProjectFieldEditHistory(prev => ({
      ...prev,
      [cacheKey]: history
    }));
  };

  const updateProjectFieldVersionCount = (fieldName: 'fullDescription' | 'eventLog', count: number) => {
    if (!task?.id) return;
    const versionKey = getProjectFieldCacheKey(task.id, fieldName);
    setLoadedProjectFieldVersions(prev => ({
      ...prev,
      [versionKey]: count
    }));
  };

  // Auto-collapse edit history when tab changes
  useEffect(() => {
    // Collapse all edit history sections when switching tabs
    setShowProjectFieldHistory(null);
    setShowEditHistory(null);
  }, [activeTab]);

  // Auto-collapse edit history and clear ALL cached data when drawer/task changes
  useEffect(() => {
    // Collapse all edit history sections when switching to a different task/project
    setShowProjectFieldHistory(null);
    setShowEditHistory(null);

    // CRITICAL FIX: Clear ALL cached edit history data to prevent cross-project contamination
    setCommentEditHistory({});
    setProjectFieldEditHistory({});
    setLoadedProjectFieldVersions({});

    console.log('🧹 Cleared all edit history cache for project isolation');
  }, [task?.id]);

  // Auto-collapse edit history when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;

      // Check if click is inside any edit history section
      const isInsideEditHistory = target.closest('[data-edit-history-section]');

      // If click is outside edit history sections, collapse them
      if (!isInsideEditHistory) {
        setShowProjectFieldHistory(null);
        setShowEditHistory(null);
      }
    };

    // Only add listener when drawer is open and edit history is shown
    if (open && (showProjectFieldHistory || showEditHistory)) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [open, showProjectFieldHistory, showEditHistory]);

  // beforeunload warning - Prevent accidental data loss when user navigates away or closes browser
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = 'You have unsaved changes that will be lost. Are you sure you want to leave?';
        return 'You have unsaved changes that will be lost. Are you sure you want to leave?';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  // Component cleanup - Clear all temporary state when component unmounts
  useEffect(() => {
    return () => {
      // Clear all temporary state on component unmount
      setPendingTeamMembers(new Map());
      setPendingTaskUpdates(new Map());
      setPendingTaskDeletions(new Set());
      setHasUnsavedChanges(false);
      console.log('🧹 All temporary state cleared on component unmount');
    };
  }, []);

  const projectDescription = externalProjectDescription || task?.projectDetails?.fullDescription || task?.details || '';

  // Fetch comments for the project
  const fetchComments = useCallback(async () => {
    if (!task?.id) return;

    setIsLoadingComments(true);
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(`/api/task-master/projects/${task.id}/comments?limit=20`, {
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      if (data.success) {
        setComments(data.data.comments || []);
      } else {
        console.error('Failed to fetch comments:', data.error);
        setComments([]); // Set empty array on error
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
      setComments([]); // Set empty array on error
    } finally {
      setIsLoadingComments(false);
    }
  }, [task?.id]);

  // Fetch activity log for the project
  const fetchActivityLog = useCallback(async () => {
    if (!task?.id) return;

    setIsLoadingActivity(true);
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(`/api/task-master/projects/${task.id}/activity?limit=30`, {
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      if (data.activity) {
        setActivityLog(data.activity);
      } else {
        console.error('Failed to fetch activity log:', data.error);
        setActivityLog([]);
      }
    } catch (error) {
      console.error('Error fetching activity log:', error);
      setActivityLog([]);
    } finally {
      setIsLoadingActivity(false);
    }
  }, [task?.id]);

  // Fetch full project details function - declared before useEffect that uses it
  const fetchFullProjectDetails = useCallback(async () => {
    if (!task) {
      console.log('⚠️ fetchFullProjectDetails: No task provided');
      return;
    }

    // Note: Guards removed to prevent dependency loops
    // Protection against overwrites will be handled at the call site level

    console.log('🔄 fetchFullProjectDetails: Starting refresh for project', task.id);
    const beforeState = fullProjectData;

    setIsLoadingProject(true);
    try {
      const url = `/api/task-master/projects/${task.id}?includeActivity=true&includeTasks=true&includeTeamMembers=true`;
      console.log('📤 fetchFullProjectDetails: API request to', url);

      const response = await fetch(url);
      const data = await response.json();

      console.log('📥 fetchFullProjectDetails: Response status', response.status);
      console.log('📦 fetchFullProjectDetails: Response data structure:', {
        hasData: !!data.data,
        hasProject: !!data.data?.project,
        projectId: data.data?.project?.id,
        tasksCount: data.data?.project?.tasks?.length || 0,
        progress: data.data?.project?.progress,
        hasActivity: !!data.data?.activity
      });

      if (response.ok && data.data) {
        const newProjectData = {
          ...data.data.project,
          activity: data.data.activity || data.data.project.activity || []
        };

        console.log('✅ fetchFullProjectDetails: Setting new project data:', {
          id: newProjectData.id,
          title: newProjectData.title?.substring(0, 50) + '...',
          tasksCount: newProjectData.tasks?.length || 0,
          progress: newProjectData.progress,
          hasFullDescription: !!newProjectData.fullDescription,
          hasEventLog: !!newProjectData.eventLog
        });

        // Set project data with state change logging
        setFullProjectData(newProjectData);
        setEventLogText(newProjectData.eventLog || '');

        // Manual state change logging (logStateChange not available in this scope)
        console.log('🔄 STATE CHANGE [fetchFullProjectDetails]:');
        console.log('  📊 Before:', beforeState ? {
          id: beforeState.id,
          title: beforeState.title?.substring(0, 50) + '...',
          tasksCount: beforeState.tasks?.length || 0,
          hasFullDescription: !!beforeState.fullDescription
        } : 'NULL');
        console.log('  📊 After:', {
          id: newProjectData.id,
          title: newProjectData.title?.substring(0, 50) + '...',
          tasksCount: newProjectData.tasks?.length || 0,
          hasFullDescription: !!newProjectData.fullDescription
        });

        console.log('✅ fetchFullProjectDetails: Project data refresh completed successfully');

        // DEBUG: Log task structure for comparison
        if (newProjectData.tasks && newProjectData.tasks.length > 0) {
          console.log('🔍 TASK STRUCTURE DEBUG:');
          console.log('📋 Total tasks found:', newProjectData.tasks.length);
          console.log('📋 First task structure:', newProjectData.tasks[0]);
          console.log('📋 All task IDs and titles:', newProjectData.tasks.map(t => ({
            id: t.id,
            title: t.title?.substring(0, 30),
            status: t.status,
            parentTaskId: t.parentTaskId,
            hasCheckboxHandler: typeof t.onUpdate === 'function'
          })));
        } else {
          console.log('⚠️ No tasks found in refreshed data');
        }
      } else {
        console.error('❌ fetchFullProjectDetails: Failed to fetch project details:', {
          status: response.status,
          error: data.error,
          data: data
        });
        throw new Error(`API request failed: ${response.status} ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('❌ fetchFullProjectDetails: Error fetching project details:', error);
      throw error; // Re-throw to allow caller to handle
    } finally {
      setIsLoadingProject(false);
      console.log('🏁 fetchFullProjectDetails: Loading state cleared');
    }
  }, [task]);

  // Initialize state values when task changes
  useEffect(() => {
    if (task) {
      setProjectVisibility(task.visibility || 'public');
      setProjectStatus(task.status || 'todo');
      setProjectPriority(task.priority || 'low');
      setProjectDueDate(task.dueDate);
    }
  }, [task]);

  // Initialize project data immediately when drawer opens, only fetch missing details
  useEffect(() => {
    if (open && task) {
      console.log('🔄 Drawer opening - task data available:', {
        id: task.id,
        title: task.title,
        hasDescription: !!task.fullDescription,
        hasEventLog: !!task.eventLog,
        tasksCount: task.tasks?.length || 0,
        hasTeamMembers: !!task.teamMembers?.length,
        hasFiles: !!task.files?.length
      });

      // Start initial loading state
      setIsInitialLoading(true);
      setInitialLoadingProgress('Initializing project data...');

      // Only initialize if fullProjectData is null or if switching to a different task
      // This preserves optimistic updates for the same task
      if (!fullProjectData || fullProjectData.id !== task.id) {
        console.log('🔄 Initializing fullProjectData from task prop...');

        setInitialLoadingProgress('Loading project details...');

        // Initialize project data from the task prop
        setFullProjectData({
          ...task,
          // Initialize missing fields that might not be in the basic task data
          teamMembers: task.teamMembers || [],
          files: task.files || [],
          comments: task.comments || [],
          tasks: task.tasks || [],
          activity: task.activity || []
        });
        setEventLogText(task.eventLog || '');

        // Only fetch additional data that's not already available
        // Most basic project data is already in the task prop
        setInitialLoadingProgress('Loading comments and activity...');

        // Load data sequentially to reduce server load and race conditions
        (async () => {
          try {
            await fetchComments();
            setInitialLoadingProgress('Loading activity log...');
            await fetchActivityLog();
            setInitialLoadingProgress('Finalizing...');
          } catch (error) {
            console.error('Error loading project data:', error);
            setInitialLoadingProgress('Error loading data...');
          }
        })();
      } else {
        console.log('🔄 Preserving existing fullProjectData (same task, potential optimistic updates)');
        // Still need to complete loading state even if preserving data
        setInitialLoadingProgress('Preparing interface...');
        setTimeout(() => {
          setIsInitialLoading(false);
        }, 500); // Small delay to prevent flashing
      }

      // Fetch full details only if we're missing critical data
      // Simple condition to avoid loops - only check for missing description or event log
      if (!task.fullDescription || !task.eventLog) {
        console.log('🔄 Initial load: fetching missing project details...', {
          missingDescription: !task.fullDescription,
          missingEventLog: !task.eventLog
        });
        setInitialLoadingProgress('Fetching complete project data...');
        fetchFullProjectDetails().then(() => {
          setInitialLoadingProgress('Data loaded successfully');
          setTimeout(() => {
            setIsInitialLoading(false);
          }, 300);
        }).catch(() => {
          setInitialLoadingProgress('Loading completed');
          setTimeout(() => {
            setIsInitialLoading(false);
          }, 300);
        });
      } else {
        // All data is already available, complete loading
        setTimeout(() => {
          setIsInitialLoading(false);
        }, 800); // Slight delay to show loading state briefly
      }
    }
  }, [open, task, fetchActivityLog, fetchComments]);

  // Reset project data when drawer closes and store original data when opening
  useEffect(() => {
    if (!open) {
      // FIXED: Only reset data after successful close, not on cancel
      // The cancel logic (handleCancelClose) handles reverting to original state
      // This cleanup only happens after successful submission or normal close
      if (!hasUnsavedChanges) {
        setFullProjectData(null);
        setOriginalProjectData(null);
        setPendingTaskUpdates(new Map());
        setPendingTeamMembers(new Map());
        setIsInitialLoading(true); // Reset loading state for next open
        setInitialLoadingProgress('Loading project data...');
      }
      // Always reset the unsaved changes flag when drawer closes
      setHasUnsavedChanges(false);
    } else if (open && task) {
      // Store original project data when drawer opens for potential revert
      setOriginalProjectData({
        ...task,
        tasks: task.tasks || [],
        activity: task.activity || []
      });
    }
  }, [open, task, hasUnsavedChanges]);

  // Sync event log text when fullProjectData changes
  useEffect(() => {
    if (fullProjectData) {
      setEventLogText(fullProjectData.eventLog || '');
    }
  }, [fullProjectData]);

  // CRITICAL FIX: Update originalProjectData baseline when fullProjectData first loads
  // This prevents false positive change detection when task.eventLog differs from fullProjectData.eventLog
  useEffect(() => {
    if (fullProjectData && originalProjectData && !hasUnsavedChanges) {
      // Only update if the eventLog values are different (avoid unnecessary updates)
      const currentOriginal = originalProjectData.eventLog || '';
      const loadedEventLog = fullProjectData.eventLog || '';

      if (currentOriginal !== loadedEventLog) {
        setOriginalProjectData(prev => prev ? {
          ...prev,
          eventLog: loadedEventLog
        } : null);
        console.log('🔄 Updated originalProjectData.eventLog baseline to match loaded data:', {
          from: currentOriginal,
          to: loadedEventLog
        });
      }
    }
  }, [fullProjectData?.eventLog, originalProjectData?.eventLog, hasUnsavedChanges]);

  // Refresh drawer data when task prop changes (e.g., after modal save)
  // DISABLED: This was causing automatic refreshes that interrupted user interactions
  // useEffect(() => {
  //   if (open && task && fullProjectData && task.id === fullProjectData.id) {
  //     // Check if the task data has been updated (e.g., description changed)
  //     // If so, refresh the full project details to get the latest data
  //     fetchFullProjectDetails();
  //   }
  // }, [task, open, fullProjectData, fetchFullProjectDetails]);

  // Expose parsing function to parent component
  useEffect(() => {
    if (onParseContent) {
      onParseContent.current = handleParseContent;
    }
  }, [onParseContent]);

  // Optimistic update function for project description
  const handleOptimisticDescriptionUpdate = useCallback((newDescription: string) => {
    setFullProjectData(prev => prev ? {
      ...prev,
      fullDescription: newDescription
    } : null);

    // FIXED: Trigger change detection when description is updated via modal
    // Check if the new description is different from the original
    if (originalProjectData && newDescription !== originalProjectData.fullDescription) {
      setHasUnsavedChanges(true);
      console.log('✅ Description change detected - marked as unsaved');
    }
  }, [originalProjectData]);

  // Expose the update function to parent via ref
  useEffect(() => {
    if (onOptimisticDescriptionUpdate && typeof onOptimisticDescriptionUpdate === 'object' && 'current' in onOptimisticDescriptionUpdate) {
      onOptimisticDescriptionUpdate.current = handleOptimisticDescriptionUpdate;
    }
  }, [onOptimisticDescriptionUpdate, handleOptimisticDescriptionUpdate]);



  // Sync project title text when task changes (only when switching to different task)
  useEffect(() => {
    if (task && (!fullProjectData || fullProjectData.id !== task.id)) {
      setProjectTitleText(task.title || '');
      setProjectVisibility(task.visibility || 'public');
      setProjectStatus(task.status || 'todo');
      setProjectPriority(task.priority || 'low');
      setProjectDueDate(task.dueDate);
    }
  }, [task, fullProjectData]);

  if (!task) return null;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    const year = date.getFullYear();
    return `${day} ${month}, ${year}`;
  };



  // Toggle project visibility with optimistic UI updates
  const toggleProjectVisibility = async () => {
    if (!task?.id || isTogglingVisibility) return;

    const previousVisibility = projectVisibility;
    const newVisibility = projectVisibility === 'public' ? 'private' : 'public';

    // Optimistic update: immediately update the UI
    setProjectVisibility(newVisibility);
    setIsTogglingVisibility(true);

    try {
      const response = await fetch(`/api/task-master/projects/${task.id}/visibility`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        // Revert on failure
        setProjectVisibility(previousVisibility);
        console.error('Failed to toggle project visibility');
      } else {
        // Notify parent component to refresh project data
        if (onCommentAdded) {
          onCommentAdded();
        }
      }
    } catch (error) {
      // Revert on error
      setProjectVisibility(previousVisibility);
      console.error('Error toggling project visibility:', error);
    } finally {
      setIsTogglingVisibility(false);
    }
  };

  // Cycle through project status with optimistic UI updates only
  const cycleProjectStatus = () => {
    if (!task?.id) return;

    const statusOrder: ('todo' | 'inProgress' | 'completed')[] = ['todo', 'inProgress', 'completed'];
    const currentIndex = statusOrder.indexOf(projectStatus);
    const nextIndex = (currentIndex + 1) % statusOrder.length;
    const newStatus = statusOrder[nextIndex];

    // Optimistic update: immediately update the UI only
    // API call will be made when drawer closes via handleDrawerClose
    setProjectStatus(newStatus);
  };

  // Cycle through project priority with optimistic UI updates only
  const cycleProjectPriority = () => {
    if (!task?.id) return;

    const priorityOrder: ('low' | 'medium' | 'high')[] = ['low', 'medium', 'high'];
    const currentIndex = priorityOrder.indexOf(projectPriority);
    const nextIndex = (currentIndex + 1) % priorityOrder.length;
    const newPriority = priorityOrder[nextIndex];

    // Optimistic update: immediately update the UI only
    // API call will be made when drawer closes via handleDrawerClose
    setProjectPriority(newPriority);

    // FIXED: Add change detection for priority changes
    if (originalProjectData && newPriority !== originalProjectData.priority) {
      setHasUnsavedChanges(true);
      console.log('✅ Priority change detected - marked as unsaved');
    }
  };

  // Handle due date selection with optimistic UI updates
  const handleDueDateSelect = (date: Date | undefined) => {
    if (!task?.id) return;

    const newDueDate = date ? date.toISOString() : undefined;

    // Optimistic update: immediately update the UI only
    // API call will be made when drawer closes via handleDrawerClose
    setProjectDueDate(newDueDate);
    setIsCalendarOpen(false);

    // FIXED: Add change detection for due date changes
    if (originalProjectData && newDueDate !== originalProjectData.dueDate) {
      setHasUnsavedChanges(true);
      console.log('✅ Due date change detected - marked as unsaved');
    }
  };

  // Clear due date
  const clearDueDate = () => {
    if (!task?.id) return;

    // Optimistic update: immediately update the UI only
    setProjectDueDate(undefined);
    setIsCalendarOpen(false);

    // FIXED: Add change detection for due date clearing
    if (originalProjectData && originalProjectData.dueDate) {
      setHasUnsavedChanges(true);
      console.log('✅ Due date cleared - marked as unsaved');
    }
  };

  // Handle adding temporary team members for optimistic UI updates
  const handleAddTemporaryMember = useCallback((userEmail: string, role: string, userData: { username?: string; email: string; id: string }) => {
    if (!task?.id) return;

    // Generate temporary ID
    const tempId = `temp-member-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Create temporary team member object
    const temporaryMember: TemporaryTeamMember = {
      tempId,
      projectId: task.id,
      userId: parseInt(userData.id),
      role: role as any,
      addedBy: currentUserId ? parseInt(currentUserId) : undefined,
      username: userData.username,
      email: userData.email,
      name: userData.username || userData.email,
      userEmail: userEmail,
      isTemporary: true
    };

    // Add to pending team members
    setPendingTeamMembers(prev => new Map(prev.set(tempId, temporaryMember)));

    // Mark that changes have been made
    setHasUnsavedChanges(true);

    console.log('✅ Temporary team member added:', tempId, userEmail);
  }, [task?.id, currentUserId]);

  // Enhanced drawer close handler with confirmation for unsaved changes
  const handleDrawerClose = async () => {
    // Check if there are unsaved changes from parsing or other edits
    if (hasUnsavedChanges) {
      setShowCloseConfirmation(true);
      return; // Don't close yet, wait for user confirmation
    }

    // Check if status, priority, or due date has changed from the original task
    const statusChanged = task && projectStatus !== task.status;
    const priorityChanged = task && projectPriority !== task.priority;
    const dueDateChanged = task && projectDueDate !== task.dueDate;

    // Store original values for potential rollback
    const originalStatus = task?.status;

    // Close the drawer first for immediate UI feedback
    onOpenChange(false);

    if (statusChanged || priorityChanged || dueDateChanged) {
      // Optimistic update: trigger immediate visual changes
      if (statusChanged && onOptimisticStatusUpdate && task) {
        await onOptimisticStatusUpdate(task.id.toString(), projectStatus);
      }

      // Background API call: update the project
      try {
        const updateData: Record<string, unknown> = {};
        if (statusChanged) updateData.status = projectStatus;
        if (priorityChanged) updateData.priority = projectPriority;
        if (dueDateChanged) updateData.dueDate = projectDueDate;

        const response = await fetch(`/api/task-master/projects/${task.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updateData)
        });

        if (response.ok) {
          // Success: Delay refresh to give server time to process the update
          // This prevents race conditions where we fetch before server has updated
          if (onCommentAdded) {
            setTimeout(() => {
              onCommentAdded();
            }, 2500);
          }
        } else {
          // Rollback on actual server errors (4xx, 5xx)
          if (statusChanged && onOptimisticStatusUpdate && task && originalStatus) {
            await onOptimisticStatusUpdate(task.id.toString(), originalStatus);
          }
          console.error(`Failed to update project status: Server returned ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        // Rollback on network/server errors
        if (statusChanged && onOptimisticStatusUpdate && task && originalStatus) {
          await onOptimisticStatusUpdate(task.id.toString(), originalStatus);
        }
        console.error('Network error updating project on close:', error);
      }
    }
  };



  // Delete comment function
  const deleteComment = async (commentId: string) => {
    if (!task?.id || !commentId) return;

    try {
      const response = await fetch(`/api/task-master/projects/${task.id}/comments/${commentId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        // Optimistically remove comment from UI
        setComments(prev => prev.filter(comment => comment.id !== commentId));
        // Notify parent component to refresh main task data
        if (onCommentAdded) {
          onCommentAdded();
        }
      } else {
        console.error('Failed to delete comment');
        // Refresh comments to restore state
        await fetchComments();
      }
    } catch (error) {
      console.error('Error deleting comment:', error);
      // Refresh comments to restore state
      await fetchComments();
    }
  };

  // Check if user can delete comment (author or moderator+)
  const canDeleteComment = (comment: ProjectComment) => {
    if (!currentUserId) return false;
    // User can delete their own comments
    if (comment.authorId?.toString() === currentUserId) return true;
    // Project owner can delete any comment
    if (task.createdBy?.toString() === currentUserId) return true;
    // Add more role-based checks here if needed
    return false;
  };

  // Check if user can edit comment (same logic as delete)
  const canEditComment = (comment: ProjectComment) => {
    if (!currentUserId) return false;
    // User can edit their own comments
    if (comment.authorId?.toString() === currentUserId) return true;
    // Project owner can edit any comment
    if (task.createdBy?.toString() === currentUserId) return true;
    // Add more role-based checks here if needed
    return false;
  };

  // Start editing a comment
  const startEditingComment = (comment: ProjectComment) => {
    setEditingCommentId(comment.id);
    setEditingCommentText(comment.content);
  };

  // Cancel editing a comment
  const cancelEditingComment = () => {
    setEditingCommentId(null);
    setEditingCommentText('');
  };

  // Save comment edit with optimistic UI updates
  const saveCommentEdit = async () => {
    if (!task?.id || !editingCommentId || !editingCommentText.trim()) return;

    const commentId = editingCommentId;
    const newContent = editingCommentText.trim();

    // Optimistic update: immediately update the UI
    const previousComments = [...comments];
    setComments(prev => prev.map(comment =>
      comment.id === commentId
        ? {
            ...comment,
            content: newContent,
            editedAt: new Date().toISOString(),
            isEdited: true
          }
        : comment
    ));
    setEditingCommentId(null);
    setEditingCommentText('');

    // Background API call
    try {
      const response = await fetch(`/api/task-master/projects/${task.id}/comments/${commentId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: newContent
        })
      });

      if (!response.ok) {
        // Silently revert on failure
        setComments(previousComments);
      } else {
        // Refresh comments to get updated data including edit history
        await fetchComments();
        // Notify parent component
        if (onCommentAdded) {
          onCommentAdded();
        }
      }
    } catch (error) {
      // Silently revert on error
      setComments(previousComments);
    }
  };

  // Fetch edit history for a comment with project-scoped caching
  const fetchCommentEditHistory = async (commentId: string) => {
    if (!task?.id) return;

    const currentProjectId = task.id;
    const cacheKey = getCommentCacheKey(currentProjectId, commentId);

    // Check if we already have this data cached
    if (commentEditHistory[cacheKey]) return;

    setIsLoadingEditHistory(true);
    try {
      const response = await fetch(`/api/task-master/projects/${currentProjectId}/comments/${commentId}/history`);
      const data = await response.json();

      // RACE CONDITION PROTECTION: Only update state if we're still on the same project
      if (task?.id !== currentProjectId) {
        console.log('🚫 Ignoring stale comment edit history response for project', currentProjectId);
        return;
      }

      if (response.ok && data.success) {
        setCommentEditHistory(prev => ({
          ...prev,
          [cacheKey]: data.data.history || []
        }));
        console.log('✅ Loaded comment edit history for project', currentProjectId, 'comment', commentId);
      } else {
        console.error('Failed to fetch comment edit history:', data.error);
      }
    } catch (error) {
      console.error('Error fetching comment edit history:', error);
    } finally {
      setIsLoadingEditHistory(false);
    }
  };

  // Fetch edit history for a project field with project-scoped caching
  const fetchProjectFieldEditHistory = async (fieldName: 'fullDescription' | 'eventLog', loadAll = false) => {
    if (!task?.id) return;

    const currentProjectId = task.id;
    const cacheKey = getProjectFieldCacheKey(currentProjectId, fieldName);
    const versionKey = getProjectFieldCacheKey(currentProjectId, fieldName);

    // Convert camelCase to snake_case for API parameter
    const apiFieldName = fieldName === 'fullDescription' ? 'full_description' : 'event_log';

    // If we already have data and not loading all, don't fetch again
    if (!loadAll && projectFieldEditHistory[cacheKey]) return;

    setIsLoadingProjectFieldHistory(true);
    try {
      const response = await fetch(`/api/task-master/projects/${currentProjectId}/field-history?field=${apiFieldName}`);
      const data = await response.json();

      // RACE CONDITION PROTECTION: Only update state if we're still on the same project
      if (task?.id !== currentProjectId) {
        console.log('🚫 Ignoring stale project field edit history response for project', currentProjectId);
        return;
      }

      if (response.ok && data.success) {
        const history = data.data.history || [];

        if (loadAll) {
          // Load ALL edit history versions
          setProjectFieldEditHistory(prev => ({
            ...prev,
            [cacheKey]: history
          }));

          setLoadedProjectFieldVersions(prev => ({
            ...prev,
            [versionKey]: history.length
          }));
        } else {
          // Initial load - show first version only
          setProjectFieldEditHistory(prev => ({
            ...prev,
            [cacheKey]: history.slice(0, 1)
          }));

          setLoadedProjectFieldVersions(prev => ({
            ...prev,
            [versionKey]: Math.min(1, history.length)
          }));
        }
        console.log('✅ Loaded project field edit history for project', currentProjectId, 'field', fieldName);
      } else {
        console.error('Failed to fetch project field edit history:', data.error);
      }
    } catch (error) {
      console.error('Error fetching project field edit history:', error);
    } finally {
      setIsLoadingProjectFieldHistory(false);
    }
  };

  // Handle key events for comment editing textarea
  const handleCommentEditKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift+Enter: Allow line break (default behavior)
        return;
      } else {
        // Enter: Save comment
        e.preventDefault();
        if (editingCommentText.trim()) {
          saveCommentEdit();
        }
      }
    } else if (e.key === 'Escape') {
      // Escape: Cancel editing
      e.preventDefault();
      cancelEditingComment();
    }
  };

  /**
   * Determines if the current user is a team member of the project.
   * This includes both project owners and explicit team members.
   * Used for conditional UI rendering and permission checks.
   * Memoized for performance optimization.
   *
   * @returns {boolean} True if user is project owner or team member, false otherwise
   */
  const isCurrentUserTeamMember = useMemo(() => {
    if (!currentUserId || !fullProjectData?.teamMembers) return false;

    // Check if user is project owner
    if (task.createdBy?.toString() === currentUserId) return true;

    // Check if user is in team members list
    return fullProjectData.teamMembers.some(member =>
      member.userId?.toString() === currentUserId
    );
  }, [currentUserId, fullProjectData?.teamMembers, task.createdBy]);

  /**
   * Returns appropriate placeholder text for the comment input field.
   * Provides contextual messaging to encourage universal participation
   * while maintaining clear expectations for different user types.
   * Memoized for performance optimization.
   *
   * @returns {string} Placeholder text for comment input
   */
  const getCommentPlaceholder = useMemo(() => {
    if (isCurrentUserTeamMember) {
      return "Add a comment... (Shift+Enter for line breaks)";
    } else {
      return "Add a comment or request access to this project... (Shift+Enter for line breaks)";
    }
  }, [isCurrentUserTeamMember]);

  // Send comment with Shift+Enter support
  const sendComment = async () => {
    if (!task?.id || !commentText.trim() || isSendingComment) return;

    setIsSendingComment(true);
    try {
      const response = await fetch(`/api/task-master/projects/${task.id}/comments`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: commentText.trim()
        })
      });

      if (response.ok) {
        setCommentText('');
        await fetchComments();
        // Notify parent component to refresh main task data for hover functionality
        if (onCommentAdded) {
          onCommentAdded();
        }
      } else {
        console.error('Failed to send comment');
      }
    } catch (error) {
      console.error('Error sending comment:', error);
    } finally {
      setIsSendingComment(false);
    }
  };

  // Handle key events for comment textarea
  const handleCommentKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift+Enter: Allow line break (default behavior)
        return;
      } else {
        // Enter: Send comment
        e.preventDefault();
        if (!isSendingComment && commentText.trim()) {
          sendComment();
        }
      }
    }
  };

  // Save event log with optimistic UI updates
  const saveEventLog = async () => {
    if (!task?.id) return;

    // Update local state only
    setFullProjectData(prev => prev ? {
      ...prev,
      eventLog: eventLogText
    } : null);
    setIsEditingEventLog(false); // Exit edit mode immediately

    // FIXED: Only mark as unsaved if content actually changed from original
    // Handle undefined eventLog by treating it as empty string for comparison
    const originalEventLog = originalProjectData?.eventLog || '';
    if (originalProjectData && eventLogText !== originalEventLog) {
      setHasUnsavedChanges(true);
      console.log('✅ Event log change detected - marked as unsaved', {
        current: eventLogText.substring(0, 50) + '...',
        original: originalEventLog.substring(0, 50) + '...'
      });
    } else {
      console.log('ℹ️ Event log unchanged - no unsaved changes marked', {
        current: eventLogText.substring(0, 50) + '...',
        original: originalEventLog.substring(0, 50) + '...'
      });
    }
  };

  // REMOVED: saveProjectTitleWithContent and saveEventLogWithContent functions
  // These immediate save functions have been removed to ensure all changes use temporary state
  // and are batch-submitted through handleConfirmClose for consistent UX

  // REMOVED: createTasksSequentially function
  // This function made immediate API calls during task creation, bypassing the intended
  // temporary state management workflow. All task creation should now only update
  // temporary state until user confirmation via the drawer close dialog.

  // State debugging helper (reduced verbosity for performance)
  const logStateChange = (operation: string, before: any, after: any) => {
    // Only log critical state changes to avoid performance issues
    if (!before && after) {
      console.log(`✅ STATE RESTORED [${operation}]: null -> valid state`);
    } else if (before && !after) {
      console.warn(`⚠️ STATE TRANSITION [${operation}]: valid state -> null (may be temporary)`);
    }
  };

  // Main parsing handler with comprehensive error handling and state protection
  const handleParseContent = async (content: string) => {
    if (!content.trim()) return;

    console.log('🚀 Starting content parsing...');
    console.log('📝 Content length:', content.length);

    // Store initial state for recovery
    const initialProjectData = fullProjectData;

    setIsParsing(true);
    setParseProgress('Parsing content...');

    let parsedContent: any = null;
    let parseSuccess = false;
    let titleSuccess = false;
    let eventLogSuccess = false;
    let descriptionSuccess = false;
    let tasksSuccess = false;
    const errors: string[] = [];

    try {
      // 1. Parse the content via API
      console.log('📤 Sending parse request to API...');
      const response = await fetch('/api/task-master/parse-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ inputText: content })
      });

      console.log(`📥 Parse API response: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Parse API failed:', errorText);
        throw new Error(`Parse API failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const responseData = await response.json();
      parsedContent = responseData.data;
      parseSuccess = true;

      console.log('✅ Content parsed successfully:', {
        title: parsedContent.title?.length || 0,
        eventLog: parsedContent.eventLog?.length || 0,
        description: parsedContent.description?.length || 0,
        tasks: parsedContent.tasks?.length || 0
      });

      // 2. Handle Title - Update in drawer (temporary preview)
      if (parsedContent.title && parsedContent.title.trim()) {
        try {
          console.log('🏷️ Updating project title in drawer...');
          setParseProgress('Updating project title...');
          setProjectTitleText(parsedContent.title);
          // Update fullProjectData for immediate display
          setFullProjectData(prev => prev ? {
            ...prev,
            title: parsedContent.title
          } : null);
          titleSuccess = true;
          console.log('✅ Title updated in drawer (temporary)');
        } catch (error) {
          const errorMsg = `Failed to update title: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error('❌', errorMsg);
          errors.push(errorMsg);
        }
      } else {
        titleSuccess = true; // Mark as successful if no title to process
        console.log('ℹ️ No title found in parsed content');
      }

      // 3. Handle Event Log - Update in drawer (temporary preview)
      if (parsedContent.eventLog && parsedContent.eventLog.trim()) {
        try {
          console.log('📋 Updating event log in drawer...');
          setParseProgress('Updating event log...');
          setEventLogText(parsedContent.eventLog || '');
          // Update fullProjectData for immediate display
          setFullProjectData(prev => prev ? {
            ...prev,
            eventLog: parsedContent.eventLog
          } : null);
          eventLogSuccess = true;
          console.log('✅ Event log updated in drawer (temporary)');
        } catch (error) {
          const errorMsg = `Failed to update event log: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error('❌', errorMsg);
          errors.push(errorMsg);
        }
      }

      // 4. Handle Description - Update in drawer (temporary preview)
      if (parsedContent.description && parsedContent.description.trim()) {
        try {
          console.log('📝 Updating description in drawer...');
          setParseProgress('Updating description...');

          // SAFE state update with protection against null states
          setFullProjectData(prev => {
            if (!prev) {
              console.warn('⚠️ WARNING: fullProjectData is null during description update, using initial state');
              return initialProjectData ? {
                ...initialProjectData,
                fullDescription: parsedContent.description
              } : null;
            }

            const newState = {
              ...prev,
              fullDescription: parsedContent.description
            };

            logStateChange('Description Update (Temporary)', prev, newState);
            return newState;
          });

          // Also trigger the optimistic update callback (it's a ref)
          if (onOptimisticDescriptionUpdate && onOptimisticDescriptionUpdate.current) {
            onOptimisticDescriptionUpdate.current(parsedContent.description);
          }

          // CRITICAL: Update the modal's tempProjectDescription so user sees parsed content
          // This ensures the modal shows the parsed description, not the original input
          if (typeof window !== 'undefined') {
            // Dispatch a custom event to update the modal's description
            window.dispatchEvent(new CustomEvent('updateModalDescription', {
              detail: { description: parsedContent.description }
            }));
          }
          descriptionSuccess = true;
          console.log('✅ Description updated in drawer (temporary)');
        } catch (error) {
          const errorMsg = `Failed to update description: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error('❌', errorMsg);
          errors.push(errorMsg);
        }
      }

      // 5. Handle Tasks - Add to drawer state (temporary preview)
      if (parsedContent.tasks && parsedContent.tasks.length > 0) {
        try {
          console.log('📋 Adding tasks to drawer...');
          setParseProgress('Adding tasks to drawer...');

          // Convert parsed tasks to temporary task objects for display
          const tempTasks = parsedContent.tasks.reduce((acc, parsedTask, index) => {
            // Create main task
            const mainTask = {
              id: `temp-main-${Date.now()}-${index}`,
              title: parsedTask.title,
              description: parsedTask.description,
              status: 'todo' as const,
              priority: 'medium' as const,
              projectId: fullProjectData?.id || task?.id,
              parentTaskId: null,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              isTemporary: true // Mark as temporary for identification
            };

            acc.push(mainTask);

            // Create subtasks
            if (parsedTask.subtasks && parsedTask.subtasks.length > 0) {
              const subtasks = parsedTask.subtasks.map((subtask, subIndex) => ({
                id: `temp-sub-${Date.now()}-${index}-${subIndex}`,
                title: `${subtask.id}: ${subtask.description}`,
                description: subtask.description,
                status: 'todo' as const,
                priority: 'medium' as const,
                projectId: fullProjectData?.id || task?.id,
                parentTaskId: mainTask.id,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                isTemporary: true // Mark as temporary for identification
              }));
              acc.push(...subtasks);
            }

            return acc;
          }, [] as any[]);

          // Add temporary tasks to fullProjectData (keep flat array format)
          setFullProjectData(prev => {
            if (!prev) return prev;

            return {
              ...prev,
              tasks: [...(prev.tasks || []), ...tempTasks]
            };
          });

          tasksSuccess = true;
          console.log('✅ Tasks added to drawer (temporary)');
        } catch (error) {
          const errorMsg = `Failed to add tasks to drawer: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error('❌', errorMsg);
          errors.push(errorMsg);
        }
      }

      // 6. DO NOT switch tabs - stay on current tab (Overview)
      // User should manually navigate to see results
      console.log('ℹ️ Parse completed - staying on current tab for user control');

      // 7. Provide detailed feedback based on what succeeded/failed
      const successCount = [titleSuccess, eventLogSuccess, descriptionSuccess, tasksSuccess].filter(Boolean).length;
      const totalOperations = [
        parsedContent.title?.trim(),
        parsedContent.eventLog?.trim(),
        parsedContent.description?.trim(),
        parsedContent.tasks?.length > 0
      ].filter(Boolean).length;

      console.log(`📊 Parse Summary: ${successCount}/${totalOperations} operations successful`);

      // Mark that changes have been made
      if (successCount > 0) {
        setHasUnsavedChanges(true);
      }

      if (errors.length === 0) {
        setParseProgress('Parse completed - content updated in drawer');
        // Success feedback is provided by the visual updates to the content
        // No toast notification needed as users can see the parsing results
      } else {
        setParseProgress(`Parse completed with ${errors.length} error(s) - review changes`);
        toast({
          title: "Parse Partially Completed",
          description: `${successCount}/${totalOperations} sections successful. ${errors.length} error(s): ${errors.join('; ')}. Changes are temporary until you close the drawer.`,
          variant: "destructive"
        });
      }

    } catch (error) {
      console.warn('⚠️ Parse error:', error);
      setParseProgress('Parse failed');

      // Critical: Restore initial state if current state is corrupted
      const currentState = fullProjectData;
      if (!currentState && initialProjectData) {
        console.log('🔄 Restoring initial state due to parse failure');
        setFullProjectData(initialProjectData);
        logStateChange('Error Recovery', currentState, initialProjectData);
      }

      // Only show toast for actual failures, not temporary state issues
      if (error instanceof Error && !error.message.includes('temporary')) {
        toast({
          title: "Parse Failed",
          description: error.message,
          variant: "destructive"
        });
      }
    } finally {
      setIsParsing(false);

      // Final state validation
      const finalState = fullProjectData;
      console.log('🏁 Final state check:', finalState ? {
        id: finalState.id,
        title: finalState.title?.substring(0, 50) + '...',
        tasksCount: finalState.tasks?.length || 0,
        progress: finalState.progress,
        isValid: !!finalState.id
      } : 'NULL - DRAWER WILL BE BLANK');

      if (!finalState) {
        console.warn('⚠️ WARNING: Final state is null - attempting recovery');
        if (initialProjectData) {
          console.log('🔄 Emergency state restoration');
          setFullProjectData(initialProjectData);
        }
      }

      // Keep progress message visible for a few seconds
      setTimeout(() => setParseProgress(''), 3000);
    }
  };

  // Handle confirmation dialog for closing with unsaved changes
  const handleConfirmClose = async () => {
    setShowCloseConfirmation(false);

    if (!task?.id || !fullProjectData) {
      onOpenChange(false);
      return;
    }

    try {
      // Save all parsed changes to the server
      const updateData: Record<string, unknown> = {};

      // Check what needs to be saved
      if (fullProjectData.title !== originalProjectData?.title) {
        updateData.title = fullProjectData.title;
      }

      if (fullProjectData.eventLog !== originalProjectData?.eventLog) {
        updateData.eventLog = fullProjectData.eventLog;
      }

      if (fullProjectData.fullDescription !== originalProjectData?.fullDescription) {
        updateData.fullDescription = fullProjectData.fullDescription;
      }

      // FIXED: Add due date and priority to batch submission
      if (projectDueDate !== originalProjectData?.dueDate) {
        updateData.dueDate = projectDueDate;
      }

      if (projectPriority !== originalProjectData?.priority) {
        updateData.priority = projectPriority;
      }

      if (projectStatus !== originalProjectData?.status) {
        updateData.status = projectStatus;
      }

      if (projectVisibility !== originalProjectData?.visibility) {
        updateData.visibility = projectVisibility;
      }

      // Save project updates if any
      if (Object.keys(updateData).length > 0) {
        const response = await fetch(`/api/task-master/projects/${task.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updateData)
        });

        if (!response.ok) {
          throw new Error('Failed to save project changes');
        }
      }

      // Save pending task status updates (only for real tasks, not temporary ones)
      for (const [taskId, taskUpdate] of pendingTaskUpdates.entries()) {
        // Skip temporary tasks - they should only be created as new tasks, not updated
        if (typeof taskId === 'string' && taskId.startsWith('temp-')) {
          console.log('⏭️ Skipping temporary task update:', taskId);
          continue;
        }

        const response = await fetch(`/api/task-master/tasks/${taskId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(taskUpdate)
        });

        if (!response.ok) {
          throw new Error(`Failed to save task status update for task ${taskId}`);
        }
      }

      // Save temporary tasks to server with proper hierarchy handling
      const tempTasks = fullProjectData.tasks?.filter(t => t.isTemporary) || [];
      const tempIdMapping = new Map<string | number, number>(); // Map temporary IDs to real database IDs

      console.log('🔄 Saving temporary tasks:', tempTasks.length);

      // First pass: Create all main tasks (no parentTaskId or parentTaskId is null)
      const mainTasks = tempTasks.filter(t => !t.parentTaskId || t.parentTaskId === null);
      for (const tempTask of mainTasks) {
        console.log('📝 Creating main task:', tempTask.title);

        const taskData = {
          projectId: parseInt(task.id),
          title: tempTask.title,
          description: tempTask.description,
          status: tempTask.status,
          priority: tempTask.priority
        };

        const response = await fetch('/api/task-master/tasks', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(taskData)
        });

        if (!response.ok) {
          throw new Error(`Failed to save main task: ${tempTask.title}`);
        }

        const result = await response.json();
        const createdTaskId = result.data?.task?.id;

        if (createdTaskId) {
          tempIdMapping.set(tempTask.id, createdTaskId);
          console.log(`✅ Main task created: ${tempTask.id} → ${createdTaskId}`);
        }
      }

      // Second pass: Create all subtasks with proper parent references
      const subtasks = tempTasks.filter(t => t.parentTaskId && t.parentTaskId !== null);
      for (const subtask of subtasks) {
        console.log('📝 Creating subtask:', subtask.title, 'under parent:', subtask.parentTaskId);

        // Get the real parent ID from our mapping
        const realParentId = tempIdMapping.get(subtask.parentTaskId);

        if (!realParentId) {
          console.error('❌ Could not find real parent ID for:', subtask.parentTaskId);
          continue;
        }

        const subtaskData = {
          projectId: parseInt(task.id),
          title: subtask.title,
          description: subtask.description,
          status: subtask.status,
          priority: subtask.priority,
          parentTaskId: realParentId
        };

        const response = await fetch('/api/task-master/tasks', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(subtaskData)
        });

        if (!response.ok) {
          console.error('❌ Failed to save subtask:', subtask.title);
          continue;
        }

        const result = await response.json();
        const createdSubtaskId = result.data?.task?.id;

        if (createdSubtaskId) {
          tempIdMapping.set(subtask.id, createdSubtaskId);
          console.log(`✅ Subtask created: ${subtask.id} → ${createdSubtaskId}`);
        }
      }

      // Delete committed tasks that were marked for deletion
      for (const taskId of pendingTaskDeletions) {
        // Only delete committed tasks (numeric IDs), skip temporary tasks (string IDs starting with 'temp-')
        if (typeof taskId === 'number' || (typeof taskId === 'string' && !taskId.startsWith('temp-'))) {
          console.log('🗑️ Deleting committed task:', taskId);

          const response = await fetch(`/api/task-master/tasks/${taskId}`, {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' }
          });

          if (!response.ok) {
            console.error('❌ Failed to delete task:', taskId);
            // Continue with other deletions even if one fails
          } else {
            console.log('✅ Task deleted successfully:', taskId);
          }
        } else {
          console.log('ℹ️ Skipping temporary task deletion (already removed from UI):', taskId);
        }
      }

      // Save pending team members
      for (const tempMember of pendingTeamMembers.values()) {
        console.log('👥 Adding team member:', tempMember.userEmail);

        const response = await fetch(`/api/task-master/projects/${task.id}/team`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userEmail: tempMember.userEmail,
            role: tempMember.role
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Failed to add team member ${tempMember.userEmail}: ${errorData.error || 'Unknown error'}`);
        }

        console.log('✅ Team member added successfully:', tempMember.userEmail);
      }

      console.log('✅ All changes saved successfully');

      // Clear pending updates after successful save
      setPendingTaskUpdates(new Map());
      setPendingTaskDeletions(new Set());
      setPendingTeamMembers(new Map());

      // Trigger workspace refresh to show updated data
      if (onWorkspaceRefresh) {
        console.log('🔄 Triggering workspace refresh after successful submission...');
        setIsRefreshingWorkspace(true);
        try {
          // Force refresh to bypass cache and get latest data
          await onWorkspaceRefresh();
          console.log('✅ Workspace refresh completed - updated data should now be visible');

          // REMOVED: Clear optimistic updates - using temporary state management instead
          console.log('✅ Workspace refresh completed');
        } catch (error) {
          console.error('❌ Workspace refresh failed:', error);
          // Don't prevent drawer from closing if refresh fails
          // User can manually refresh if needed
        } finally {
          setIsRefreshingWorkspace(false);
        }
      } else {
        // REMOVED: Clear optimistic updates - using temporary state management instead
        console.log('✅ No refresh function provided');
      }

      onOpenChange(false);
    } catch (error) {
      console.error('❌ Failed to save changes:', error);
      toast({
        title: "Save Failed",
        description: error instanceof Error ? error.message : "Failed to save changes",
        variant: "destructive"
      });
    }
  };

  // Handle canceling the close (revert changes)
  const handleCancelClose = () => {
    setShowCloseConfirmation(false);

    // FIXED: Properly revert all changes to original state
    if (originalProjectData) {
      setFullProjectData(originalProjectData);
      setProjectTitleText(originalProjectData.title || '');
      setEventLogText(originalProjectData.eventLog || '');

      // Revert due date and priority fields
      setProjectDueDate(originalProjectData.dueDate);
      setProjectPriority(originalProjectData.priority || 'low');
      setProjectStatus(originalProjectData.status || 'todo');
      setProjectVisibility(originalProjectData.visibility || 'public');

      // Trigger optimistic update to revert description
      if (onOptimisticDescriptionUpdate && onOptimisticDescriptionUpdate.current) {
        onOptimisticDescriptionUpdate.current(originalProjectData.fullDescription || '');
      }

      // Update the parent component's project description state
      if (onProjectDescriptionChange) {
        onProjectDescriptionChange(originalProjectData.fullDescription || '');
      }
    }

    // Clear pending task updates and deletions
    setPendingTaskUpdates(new Map());
    setPendingTaskDeletions(new Set());
    setPendingTeamMembers(new Map());

    // REMOVED: Clear optimistic task updates - using temporary state management instead

    setHasUnsavedChanges(false);
    console.log('🔄 All changes reverted to original state');

    // Now allow the drawer to close normally
    onOpenChange(false);
  };

  // Save project title with temporary state (no server save)
  const saveProjectTitle = async () => {
    if (!task?.id) return;

    // Update local state only
    setFullProjectData(prev => prev ? {
      ...prev,
      title: projectTitleText
    } : null);
    setIsEditingProjectTitle(false); // Exit edit mode immediately

    // FIXED: Only mark as unsaved if content actually changed from original
    // Handle undefined title by treating it as empty string for comparison
    const originalTitle = originalProjectData?.title || '';
    if (originalProjectData && projectTitleText !== originalTitle) {
      setHasUnsavedChanges(true);
      console.log('✅ Project title change detected - marked as unsaved', {
        current: projectTitleText,
        original: originalTitle
      });
    } else {
      console.log('ℹ️ Project title unchanged - no unsaved changes marked', {
        current: projectTitleText,
        original: originalTitle
      });
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-neutral-800 dark:text-neutral-300';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-400';
      case 'inProgress':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'todo':
        return 'bg-[#5E6AD2]/10 text-[#5E6AD2] dark:bg-[#6E56CF]/10 dark:text-[#6E56CF]';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-neutral-800 dark:text-neutral-300';
    }
  };

  // Role helper functions for team member badges
  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return <Crown className="h-3 w-3" />;
      case 'admin':
        return <Shield className="h-3 w-3" />;
      default:
        return <User className="h-3 w-3" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'admin':
        return 'bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  // Optimistic update functions for instant visual feedback
  const applyOptimisticTaskUpdate = useCallback((taskId: string | number, updateData: any) => {
    console.log('🔄 applyOptimisticTaskUpdate called:', { taskId, updateData });

    const currentTasks = fullProjectData?.tasks || [];
    console.log('📋 Current tasks before update:', currentTasks.length);

    // CRITICAL FIX: Handle both main tasks and subtasks
    const updatedTasks = currentTasks.map(task => {
      // Check if this is the main task being updated
      if (task.id === taskId) {
        const updatedTask = { ...task, ...updateData };
        console.log('✅ Main task updated optimistically:', { id: taskId, before: task.status, after: updatedTask.status });
        return updatedTask;
      }

      // Check if this task has subtasks and one of them is being updated
      if (task.subtasks && task.subtasks.length > 0) {
        const updatedSubtasks = task.subtasks.map(subtask => {
          if (subtask.id === taskId) {
            const updatedSubtask = { ...subtask, ...updateData };
            console.log('✅ Subtask updated optimistically:', { id: taskId, before: subtask.status, after: updatedSubtask.status });
            return updatedSubtask;
          }
          return subtask;
        });

        // Return task with updated subtasks if any subtask was updated
        if (updatedSubtasks.some((subtask, index) => subtask !== task.subtasks![index])) {
          return { ...task, subtasks: updatedSubtasks };
        }
      }

      return task;
    });

    // Count all tasks (main + subtasks) for debugging
    const allTasks: any[] = [];
    updatedTasks.forEach(mainTask => {
      allTasks.push(mainTask);
      if (mainTask.subtasks) allTasks.push(...mainTask.subtasks);
    });

    const completedCount = allTasks.filter(t => t.status === 'completed').length;
    const updatedTask = allTasks.find(t => t.id === taskId);

    console.log('📋 Updated tasks after optimistic update:', {
      totalMainTasks: updatedTasks.length,
      totalAllTasks: allTasks.length,
      updatedTask: updatedTask ? { id: updatedTask.id, status: updatedTask.status, isSubtask: !!updatedTask.parentTaskId } : 'NOT FOUND',
      completedCount
    });

    // REMOVED: setOptimisticTasks - using temporary state management instead

    // Also update fullProjectData to ensure drawer state consistency
    setFullProjectData(prev => prev ? {
      ...prev,
      tasks: updatedTasks
    } : null);

    console.log('✅ Task update applied to temporary state');
  }, [fullProjectData?.tasks, task]);

  // REMOVED: applyOptimisticTaskCreation, applyOptimisticTaskDeletion, clearOptimisticUpdates
  // These functions are no longer needed since we're using temporary state management
  // instead of optimistic UI updates. All changes remain in temporary state until
  // user confirmation via the drawer close dialog.

  // REMOVED: organizeTaskHierarchy function
  // Task hierarchy organization is now handled by TaskHierarchyTransformer utility
  // in TaskManagement component for display while keeping the underlying data structure
  // flat for proper data management and API compatibility.

  // Frontend-only progress calculation with comprehensive debugging and subtask support
  const calculateProgress = useCallback(() => {
    // Use temporary state tasks for instant updates
    const mainTasks = fullProjectData?.tasks || [];

    // CRITICAL FIX: Flatten tasks to include both main tasks and subtasks
    const allTasks: any[] = [];
    mainTasks.forEach(mainTask => {
      allTasks.push(mainTask);
      if (mainTask.subtasks && mainTask.subtasks.length > 0) {
        allTasks.push(...mainTask.subtasks);
      }
    });

    console.log('📊 PROGRESS CALCULATION (with subtasks):', {
      mainTasksCount: mainTasks.length,
      allTasksCount: allTasks.length,
      usingTemporaryState: true,
      subtaskBreakdown: mainTasks.map(t => ({ id: t.id, subtaskCount: t.subtasks?.length || 0 }))
    });

    if (allTasks.length === 0) {
      console.log('📊 No tasks found for progress calculation');
      return 0;
    }

    const completedTasks = allTasks.filter(task => task.status === 'completed').length;
    const totalTasks = allTasks.length;
    const progress = Math.round((completedTasks / totalTasks) * 100);

    console.log('📊 Progress result:', {
      completed: completedTasks,
      total: totalTasks,
      progress: `${progress}%`,
      mainCompleted: mainTasks.filter(t => t.status === 'completed').length,
      subtasksCompleted: allTasks.filter(t => t.parentTaskId && t.status === 'completed').length
    });

    return progress;
  }, [fullProjectData?.tasks]);

  const getProgressColor = (progress: number) => {
    // Color interpolation: 0% = red, 50% = yellow/orange, 100% = green
    if (progress <= 50) {
      // Interpolate from red to yellow/orange (0% to 50%)
      const ratio = progress / 50;
      const red = Math.round(239 - (239 - 245) * ratio); // From #EF4444 to #F59E0B
      const green = Math.round(68 + (158 - 68) * ratio);
      const blue = Math.round(68 - (68 - 11) * ratio);

      return {
        light: `rgb(${red}, ${green}, ${blue})`,
        dark: `rgb(${Math.round(red * 0.8)}, ${Math.round(green * 0.8)}, ${Math.round(blue * 0.8)})`
      };
    } else {
      // Interpolate from yellow/orange to green (50% to 100%)
      const ratio = (progress - 50) / 50;
      const red = Math.round(245 - (245 - 16) * ratio); // From #F59E0B to #10B981
      const green = Math.round(158 + (185 - 158) * ratio);
      const blue = Math.round(11 + (129 - 11) * ratio);

      return {
        light: `rgb(${red}, ${green}, ${blue})`,
        dark: `rgb(${Math.round(red * 0.8)}, ${Math.round(green * 0.8)}, ${Math.round(blue * 0.8)})`
      };
    }
  };

  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case 'upload':
        return { icon: Upload, color: 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400' };
      case 'comment':
        return { icon: MessageSquare, color: 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' };
      case 'status_change':
        return { icon: Settings, color: 'bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400' };
      case 'assignment':
        return { icon: UserPlus, color: 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400' };
      case 'dueDate':
        return { icon: Calendar, color: 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400' };
      case 'completion':
        return { icon: CheckCircle, color: 'bg-emerald-100 dark:bg-emerald-900/20 text-emerald-600 dark:text-emerald-400' };
      case 'creation':
        return { icon: Plus, color: 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' };
      case 'update':
        return { icon: Edit2, color: 'bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400' };
      case 'task_creation':
        return { icon: Plus, color: 'bg-cyan-100 dark:bg-cyan-900/20 text-cyan-600 dark:text-cyan-400' };
      case 'task_completion':
        return { icon: CheckCircle, color: 'bg-emerald-100 dark:bg-emerald-900/20 text-emerald-600 dark:text-emerald-400' };
      case 'comment_edit':
        return { icon: Edit2, color: 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' };
      case 'project_details_edit':
        return { icon: FileText, color: 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400' };
      case 'event_log_edit':
        return { icon: BookOpen, color: 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400' };
      default:
        return { icon: Clock, color: 'bg-gray-100 dark:bg-gray-900/20 text-gray-600 dark:text-gray-400' };
    }
  };

  // Format activity description based on activity type and metadata
  const formatActivityDescription = (activity: ActivityLogEntry) => {
    const { activityType, description, metadata, username } = activity;

    // For activities with metadata, parse and create user-friendly descriptions
    if (metadata) {
      try {
        const parsedMetadata = typeof metadata === 'string' ? JSON.parse(metadata) : metadata;
        const versionNumber = parsedMetadata.versionNumber || 'unknown';

        switch (activityType) {
          case 'comment_edit':
            return `edited comment (V${versionNumber})`;
          case 'project_details_edit':
            return `edited Project Details (V${versionNumber})`;
          case 'event_log_edit':
            return `edited Event Log (V${versionNumber})`;
          default:
            return description;
        }
      } catch (error) {
        console.warn('Failed to parse activity metadata:', error);
        return description || 'performed an action';
      }
    }

    // For other activity types, return the original description
    return description;
  };

  return (
    <DrawerOverlay open={open} onOpenChange={handleDrawerClose}>
      <div className={`flex h-full flex-col ${isInitialLoading ? 'pointer-events-none opacity-50' : ''}`}>
        {/* Header */}
        <div className="flex items-center border-b border-gray-200 dark:border-[#2A2A2A] px-6 py-4 bg-white dark:bg-[#1A1A1A]">
          {/* Left side - Project Title */}
          <div className="flex-1 min-w-0 mr-4">
            {isEditingProjectTitle ? (
              <input
                type="text"
                value={projectTitleText}
                onChange={(e) => setProjectTitleText(e.target.value)}
                onBlur={() => {
                  // Immediate optimistic save on blur
                  saveProjectTitle();
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Escape') {
                    setProjectTitleText(fullProjectData?.title || task.title || '');
                    setIsEditingProjectTitle(false);
                  } else if (e.key === 'Enter') {
                    e.preventDefault();
                    saveProjectTitle();
                  }
                }}
                className="text-lg font-semibold text-black dark:text-white bg-transparent border-none outline-none focus:outline-none w-full"
                autoFocus
              />
            ) : (
              <span
                className="text-lg font-semibold text-black dark:text-white cursor-pointer hover:text-[#5E6AD2] dark:hover:text-[#6E56CF] transition-colors duration-200 block truncate"
                onClick={() => setIsEditingProjectTitle(true)}
                title={`${fullProjectData?.title || task.title} - Click to edit title`}
              >
                {fullProjectData?.title || task.title}
              </span>
            )}
          </div>

          {/* Right side - Controls */}
          <div className="flex items-center gap-3">
            {/* Visibility Toggle Button - Icon Only */}
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleProjectVisibility}
              disabled={isTogglingVisibility}
              className={`h-8 w-8 p-0 ${
                projectVisibility === 'private'
                  ? 'text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20'
                  : 'text-purple-600 hover:text-purple-700 hover:bg-purple-50 dark:text-purple-400 dark:hover:text-purple-300 dark:hover:bg-purple-900/20'
              }`}
              title={`Project is ${projectVisibility}. Click to make ${projectVisibility === 'public' ? 'private' : 'public'}.`}
            >
              {projectVisibility === 'public' ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4" />
              )}
            </Button>

            {/* Status Badge - Clickable to cycle through statuses */}
            <Button
              variant="ghost"
              size="sm"
              onClick={cycleProjectStatus}
              className="h-8 p-0 hover:bg-gray-50 dark:hover:bg-gray-800/50"
              title="Click to cycle through status: To Do → In Progress → Completed"
            >
              <Badge
                variant="secondary"
                className={getStatusColor(projectStatus)}
              >
                {projectStatus === 'inProgress' ? 'In Progress' :
                 projectStatus === 'completed' ? 'Completed' : 'To Do'}
              </Badge>
            </Button>
          </div>
        </div>

        {/* Tabs - Subframe Style */}
        <div className="flex w-full items-end bg-white dark:bg-[#1A1A1A]">
          <div className="flex items-start">
            <div
              className={`flex h-10 cursor-pointer items-center justify-center gap-2 px-2.5 py-0.5 border-b border-solid ${
                activeTab === 'overview'
                  ? 'border-b-2 border-[#5E6AD2] dark:border-[#6E56CF] pb-px'
                  : 'border-gray-200 dark:border-[#2A2A2A]'
              } ${isInitialLoading ? 'pointer-events-none opacity-50' : ''}`}
              onClick={() => !isInitialLoading && setActiveTab('overview')}
            >
              <span className={`text-sm font-medium ${
                activeTab === 'overview'
                  ? 'text-[#5E6AD2] dark:text-[#6E56CF]'
                  : 'text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white'
              }`}>
                Overview
              </span>
            </div>
            <div
              className={`flex h-10 cursor-pointer items-center justify-center gap-2 px-2.5 py-0.5 border-b border-solid ${
                activeTab === 'eventlog'
                  ? 'border-b-2 border-[#5E6AD2] dark:border-[#6E56CF] pb-px'
                  : 'border-gray-200 dark:border-[#2A2A2A]'
              } ${isInitialLoading ? 'pointer-events-none opacity-50' : ''}`}
              onClick={() => !isInitialLoading && setActiveTab('eventlog')}
            >
              <span className={`text-sm font-medium ${
                activeTab === 'eventlog'
                  ? 'text-[#5E6AD2] dark:text-[#6E56CF]'
                  : 'text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white'
              }`}>
                Event Log
              </span>
            </div>
            <div
              className={`flex h-10 cursor-pointer items-center justify-center gap-2 px-2.5 py-0.5 border-b border-solid ${
                activeTab === 'files'
                  ? 'border-b-2 border-[#5E6AD2] dark:border-[#6E56CF] pb-px'
                  : 'border-gray-200 dark:border-[#2A2A2A]'
              } ${isInitialLoading ? 'pointer-events-none opacity-50' : ''}`}
              onClick={() => !isInitialLoading && setActiveTab('files')}
            >
              <span className={`text-sm font-medium ${
                activeTab === 'files'
                  ? 'text-[#5E6AD2] dark:text-[#6E56CF]'
                  : 'text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white'
              }`}>
                Files
              </span>
            </div>
            <div
              className={`flex h-10 cursor-pointer items-center justify-center gap-2 px-2.5 py-0.5 border-b border-solid ${
                activeTab === 'team'
                  ? 'border-b-2 border-[#5E6AD2] dark:border-[#6E56CF] pb-px'
                  : 'border-gray-200 dark:border-[#2A2A2A]'
              } ${isInitialLoading ? 'pointer-events-none opacity-50' : ''}`}
              onClick={() => !isInitialLoading && setActiveTab('team')}
            >
              <span className={`text-sm font-medium ${
                activeTab === 'team'
                  ? 'text-[#5E6AD2] dark:text-[#6E56CF]'
                  : 'text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white'
              }`}>
                Team
              </span>
            </div>
            <div
              className={`flex h-10 cursor-pointer items-center justify-center gap-2 px-2.5 py-0.5 border-b border-solid ${
                activeTab === 'comments'
                  ? 'border-b-2 border-[#5E6AD2] dark:border-[#6E56CF] pb-px'
                  : 'border-gray-200 dark:border-[#2A2A2A]'
              }`}
              onClick={() => setActiveTab('comments')}
            >
              <span className={`text-sm font-medium ${
                activeTab === 'comments'
                  ? 'text-[#5E6AD2] dark:text-[#6E56CF]'
                  : 'text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white'
              }`}>
                Comments
              </span>
            </div>
          </div>
          <div className="flex grow shrink-0 basis-0 border-b border-gray-200 dark:border-[#2A2A2A]" />
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto">
          {activeTab === 'overview' && (
            <div className="p-6 space-y-6">
              {/* Project Details */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-base font-semibold text-black dark:text-white">
                    Project Details
                  </h3>
                  {/* Project Owner Badge */}
                  {currentUserId && task.createdBy?.toString() === currentUserId && (
                    <Badge
                      variant="secondary"
                      className="bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400"
                    >
                      <Crown className="h-3 w-3 mr-1" />
                      Owner
                    </Badge>
                  )}
                </div>

                <div className="space-y-3">
                  <div
                    onClick={() => {
                      if (onProjectDetailsModalOpen) {
                        onProjectDetailsModalOpen(fullProjectData?.fullDescription || '');
                      }
                    }}
                    className="min-h-[150px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200 overflow-hidden"
                  >
                    {fullProjectData?.fullDescription ? (
                      <div className="w-full break-words overflow-wrap-anywhere">
                        <ProjectDescriptionText
                          text={fullProjectData.fullDescription}
                          className="text-xs text-gray-700 dark:text-gray-300 leading-relaxed break-words whitespace-pre-wrap word-wrap-break-word"
                        />
                      </div>
                    ) : (
                      <p className="text-xs text-gray-400 dark:text-gray-500 italic">
                        Click to add project description, goals, and requirements...
                      </p>
                    )}
                  </div>



                  {/* Edited indicator for Project Details */}
                  {fullProjectData?.fullDescriptionEditCount && fullProjectData.fullDescriptionEditCount > 0 && (
                    <div className="flex items-center gap-2">
                      <span
                        className="text-xs text-gray-500 dark:text-gray-400 cursor-pointer hover:text-[#5E6AD2] dark:hover:text-[#6E56CF] transition-colors duration-200"
                        data-edit-history-section
                        onClick={() => {
                          const fieldKey = 'fullDescription';

                          if (showProjectFieldHistory === fieldKey) {
                            setShowProjectFieldHistory(null);
                          } else {
                            setShowProjectFieldHistory(fieldKey);
                            // Reset to show only first version when opening
                            const history = getProjectFieldHistory(fieldKey);
                            if (history.length > 0) {
                              updateProjectFieldHistory(fieldKey, history.slice(0, 1));
                              updateProjectFieldVersionCount(fieldKey, 1);
                            } else {
                              fetchProjectFieldEditHistory('fullDescription');
                            }
                          }
                        }}
                        title="Click to view edit history"
                      >
                        Edited {fullProjectData.fullDescriptionEditCount} time{fullProjectData.fullDescriptionEditCount > 1 ? 's' : ''}
                      </span>
                    </div>
                  )}

                  {/* Edit History for Project Details */}
                  {showProjectFieldHistory === 'fullDescription' && (
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-3" data-edit-history-section>
                      {isLoadingProjectFieldHistory ? (
                        <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 py-2">
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-[#5E6AD2] dark:border-[#6E56CF]"></div>
                          Loading edit history...
                        </div>
                      ) : getProjectFieldHistory('fullDescription').length > 0 ? (
                        <div className="space-y-0">
                          {/* Previous versions */}
                          {getProjectFieldHistory('fullDescription').map((edit: { versionNumber: number; previousContent: string; editedAt: string; editedBy: string; editedByUsername?: string }, index: number) => (
                            <div key={index}>
                              {index > 0 && <hr className="border-gray-200 dark:border-gray-700 my-3" />}
                              <div className="pb-3">
                                {/* Timestamp above content with version label */}
                                <div className="text-xs text-gray-500 dark:text-gray-500 mb-2">
                                  {edit.versionNumber === 1 ? (
                                    <>
                                      <span className="font-medium text-blue-600 dark:text-blue-400">Original</span>
                                      {' • '}
                                      {new Date(edit.editedAt).toLocaleString()}
                                      {edit.editedByUsername && ` • Created by ${edit.editedByUsername}`}
                                    </>
                                  ) : (
                                    <>
                                      <span className="font-medium text-purple-600 dark:text-purple-400">Version {edit.versionNumber}</span>
                                      {' • '}
                                      {new Date(edit.editedAt).toLocaleString()}
                                      {edit.editedByUsername && ` • Edited by ${edit.editedByUsername}`}
                                    </>
                                  )}
                                </div>
                                {/* Content - show the content that was replaced (previous content) */}
                                <div className="text-xs text-gray-600 dark:text-gray-400 break-words whitespace-pre-wrap">
                                  {edit.previousContent || '(Empty)'}
                                </div>
                              </div>
                            </div>
                          ))}

                          {/* Show Older Revisions button */}
                          {getProjectFieldHistory('fullDescription').length > 0 &&
                           getProjectFieldVersionCount('fullDescription') < (fullProjectData?.fullDescriptionEditCount || 0) && (
                            <div className="text-center pt-2">
                              <hr className="border-gray-200 dark:border-gray-700 mb-3" />
                              <button
                                onClick={() => fetchProjectFieldEditHistory('fullDescription', true)}
                                className="text-xs text-[#5E6AD2] dark:text-[#6E56CF] hover:underline"
                                disabled={isLoadingProjectFieldHistory}
                                data-edit-history-section
                              >
                                {isLoadingProjectFieldHistory ? 'Loading...' : 'Show Older Revisions'}
                              </button>
                            </div>
                          )}

                          {/* Recent Edit button - shown when all history is loaded */}
                          {getProjectFieldHistory('fullDescription').length > 0 &&
                           getProjectFieldVersionCount('fullDescription') >= (fullProjectData?.fullDescriptionEditCount || 0) &&
                           getProjectFieldHistory('fullDescription').length > 1 && (
                            <div className="text-center pt-2">
                              <hr className="border-gray-200 dark:border-gray-700 mb-3" />
                              <button
                                onClick={() => {
                                  // Reset to show only first version
                                  const fieldKey = 'fullDescription';
                                  const history = getProjectFieldHistory(fieldKey);
                                  updateProjectFieldHistory(fieldKey, history.slice(0, 1));
                                  updateProjectFieldVersionCount(fieldKey, 1);
                                }}
                                className="text-xs text-[#5E6AD2] dark:text-[#6E56CF] hover:underline"
                                data-edit-history-section
                              >
                                Recent Edit
                              </button>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="text-xs text-gray-500 dark:text-gray-400 text-center py-4">
                          No edit history available
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Badges */}
              <div className="space-y-4">
                <div className="flex items-center justify-end gap-2">
                    {/* Due Date - Clickable to open calendar */}
                    <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 p-0 hover:bg-gray-50 dark:hover:bg-gray-800/50"
                          title="Click to set due date"
                        >
                          <Badge variant="secondary" className="bg-sky-100 text-sky-700 dark:bg-sky-900/20 dark:text-sky-400">
                            <Calendar className="h-3 w-3 mr-1" />
                            {projectDueDate ? `Due ${formatDate(projectDueDate)}` : 'Set Due Date'}
                          </Badge>
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0 z-[400]" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={projectDueDate ? new Date(projectDueDate) : undefined}
                          onSelect={handleDueDateSelect}
                          initialFocus
                        />
                        {projectDueDate && (
                          <div className="p-3 border-t">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={clearDueDate}
                              className="w-full"
                            >
                              Clear Due Date
                            </Button>
                          </div>
                        )}
                      </PopoverContent>
                    </Popover>
                    {/* Priority Badge - Clickable to cycle through priorities */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={cycleProjectPriority}
                      className="h-8 p-0 hover:bg-gray-50 dark:hover:bg-gray-800/50"
                      title="Click to cycle through priority: Low → Medium → High"
                    >
                      <Badge variant="secondary" className={getPriorityColor(projectPriority)}>
                        <Flag className="h-3 w-3 mr-1" />
                        {projectPriority === 'medium' ? 'Medium' : projectPriority.charAt(0).toUpperCase() + projectPriority.slice(1)} Priority
                      </Badge>
                    </Button>
                  </div>
                </div>

              {/* Team Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-base font-semibold text-black dark:text-white">
                    Team
                  </h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setActiveTab('team')}
                  >
                    <UserPlus className="h-4 w-4 mr-2" />
                    Add Member
                  </Button>
                </div>

                {/* Team Members Display */}
                <div className="space-y-2">
                  {(() => {
                    const allMembers = [];

                    // Add project owner/assignee if available
                    if (task.assignee) {
                      allMembers.push({
                        id: `owner-${task.assignee.id || 'unknown'}`,
                        name: task.assignee.name,
                        email: task.assignee.email,
                        avatar: task.assignee.avatar,
                        role: 'owner',
                        isOwner: true,
                        isTemporary: false
                      });
                    }

                    // Add team members from fullProjectData
                    if (fullProjectData?.teamMembers) {
                      fullProjectData.teamMembers.forEach(member => {
                        // Don't duplicate the owner if they're also in team members
                        const isOwnerDuplicate = task.assignee &&
                          (member.email === task.assignee.email || member.userId === task.assignee.id);

                        if (!isOwnerDuplicate) {
                          allMembers.push({
                            id: member.id,
                            name: member.name || member.username || member.email || 'Unknown User',
                            email: member.email,
                            avatar: member.avatar,
                            role: member.role,
                            isOwner: false,
                            isTemporary: false
                          });
                        }
                      });
                    }

                    // Add temporary team members
                    Array.from(pendingTeamMembers.values()).forEach(tempMember => {
                      // Don't duplicate existing members
                      const isDuplicate = allMembers.some(member =>
                        member.email === tempMember.email ||
                        (task.assignee && tempMember.email === task.assignee.email)
                      );

                      if (!isDuplicate) {
                        allMembers.push({
                          id: tempMember.tempId,
                          name: tempMember.name || tempMember.username || tempMember.email || 'Unknown User',
                          email: tempMember.email,
                          avatar: tempMember.avatar,
                          role: tempMember.role,
                          isOwner: false,
                          isTemporary: true
                        });
                      }
                    });

                    if (allMembers.length === 0) {
                      return (
                        <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                          <User className="h-6 w-6 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No team members yet</p>
                          <p className="text-xs">Click &quot;Add Member&quot; to invite team members</p>
                        </div>
                      );
                    }

                    return allMembers.map((member) => (
                      <div
                        key={member.id}
                        className={`flex items-center gap-3 p-3 rounded-md border bg-white dark:bg-[#1A1A1A] ${
                          member.isTemporary
                            ? 'border-dashed border-[#5E6AD2] dark:border-[#6E56CF] bg-[#5E6AD2]/5 dark:bg-[#6E56CF]/5'
                            : 'border-gray-200 dark:border-[#2A2A2A]'
                        }`}
                      >
                        <Avatar className="h-8 w-8">
                          {member.avatar ? (
                            <Image
                              src={member.avatar}
                              alt={member.name}
                              width={32}
                              height={32}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <div className="h-full w-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                              <User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            </div>
                          )}
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-black dark:text-white truncate">
                            {member.name}
                          </p>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {member.isOwner ? 'Project Owner' : `Team ${member.role.charAt(0).toUpperCase() + member.role.slice(1)}`}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          {member.isTemporary && (
                            <Badge
                              variant="outline"
                              className="text-xs border-[#5E6AD2] dark:border-[#6E56CF] text-[#5E6AD2] dark:text-[#6E56CF]"
                            >
                              Pending
                            </Badge>
                          )}
                          <Badge
                            variant="secondary"
                            className={`text-xs ${getRoleColor(member.isOwner ? 'owner' : member.role)}`}
                          >
                            {getRoleIcon(member.isOwner ? 'owner' : member.role)}
                            <span className="ml-1 capitalize">{member.isOwner ? 'Owner' : member.role}</span>
                          </Badge>
                        </div>
                      </div>
                    ));
                  })()}
                </div>
              </div>

              {/* Task Management Section */}
              {onCreateTask && onUpdateTask && onDeleteTask && (
                <TaskManagement
                  projectId={task.id}
                  tasks={fullProjectData?.tasks || []}
                  onCreateTask={async (projectId, taskData) => {
                    console.log('🔧 MANUAL TASK CREATION START (TEMPORARY STATE ONLY):', {
                      projectId,
                      taskData,
                      hasParentTaskId: !!taskData.parentTaskId,
                      parentTaskId: taskData.parentTaskId,
                      parentTaskIdType: typeof taskData.parentTaskId
                    });

                    // Create task in temporary state only - no API calls
                    const currentTasks = fullProjectData?.tasks || [];

                    // Generate temporary ID
                    const tempId = taskData.parentTaskId
                      ? `temp-sub-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
                      : `temp-main-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

                    const newTask = {
                      id: tempId,
                      ...taskData,
                      status: taskData.status || 'todo',
                      priority: taskData.priority || 'medium',
                      createdAt: new Date().toISOString(),
                      updatedAt: new Date().toISOString(),
                      isTemporary: true // Mark as temporary for batch submission
                    };

                    console.log('📝 Created task object:', {
                      id: newTask.id,
                      title: newTask.title,
                      parentTaskId: newTask.parentTaskId,
                      isSubtask: !!newTask.parentTaskId
                    });

                    // Update temporary state (keep flat array format)
                    const updatedTasks = [...currentTasks, newTask];
                    setFullProjectData(prev => prev ? { ...prev, tasks: updatedTasks } : null);

                    // Mark that changes have been made
                    setHasUnsavedChanges(true);

                    console.log('✅ Task created in temporary state:', tempId);
                    return { success: true, isTemporary: true };
                  }}
                  onUpdateTask={async (taskId, updateData) => {
                    const isTemporary = typeof taskId === 'string' && taskId.startsWith('temp-');
                    console.log('🔧 TASK UPDATE START:', { taskId, updateData, isTemporary });

                    // Apply optimistic update immediately to drawer state
                    applyOptimisticTaskUpdate(taskId, updateData);

                    // Track pending task updates for batch submission (only for real tasks)
                    if (updateData.status && !isTemporary) {
                      setPendingTaskUpdates(prev => {
                        const newMap = new Map(prev);
                        newMap.set(taskId, { status: updateData.status! });
                        return newMap;
                      });
                    }

                    // Mark that changes have been made
                    setHasUnsavedChanges(true);

                    console.log('✅ Task update applied to drawer:', isTemporary ? '(temporary state only)' : '(tracked for batch submission)');
                  }}
                  onDeleteTask={async (taskId) => {
                    console.log('🔧 TASK DELETION START (TEMPORARY UI ONLY):', { taskId });

                    // Find the task and its subtasks
                    const currentTasks = fullProjectData?.tasks || [];
                    const taskToDelete = currentTasks.find(task => task.id === taskId);

                    if (!taskToDelete) {
                      console.warn('⚠️ Task not found:', taskId);
                      return;
                    }

                    // Find all tasks that will be affected (task + its subtasks)
                    const tasksToDelete = currentTasks.filter(task =>
                      task.id === taskId || task.parentTaskId === taskId
                    );

                    // Track deletions for batch submission (only for committed tasks)
                    tasksToDelete.forEach(task => {
                      if (!task.isTemporary) {
                        // This is a committed task - track for deletion during batch submission
                        setPendingTaskDeletions(prev => new Set([...prev, task.id]));
                        console.log('📝 Tracking committed task for deletion:', task.id);
                      } else {
                        console.log('📝 Temporary task will be removed from UI only:', task.id);
                      }
                    });

                    // Remove from temporary UI state (both temporary and committed tasks)
                    let updatedTasks = currentTasks.filter(task => {
                      // Remove the task if it matches the ID
                      if (task.id === taskId) return false;
                      // Remove any subtasks under this task
                      if (task.parentTaskId === taskId) return false;
                      return true;
                    });

                    // Update temporary state (keep flat array format)
                    setFullProjectData(prev => prev ? { ...prev, tasks: updatedTasks } : null);

                    // Mark that changes have been made
                    setHasUnsavedChanges(true);

                    console.log('✅ Task removed from UI (temporary state):', taskId);
                    console.log('📋 Tasks affected:', tasksToDelete.map(t => ({ id: t.id, isTemporary: t.isTemporary })));
                  }}
                />
              )}

              {/* Task Progress Bar - Frontend Calculated */}
              <div className="space-y-4">
                <div className="w-full space-y-2">
                  <Progress
                    value={calculateProgress()}
                    variant="brand"
                    className="w-full"
                    key={`progress-${fullProjectData?.tasks?.length || 0}`}
                  />
                  <div className="flex w-full items-center justify-between">
                    <span className="text-xs text-muted-foreground">
                      Task Completion
                    </span>
                    <span className="text-xs font-semibold text-[#5E6AD2] dark:text-[#6E56CF]">
                      {calculateProgress()}% {calculateProgress() === 100 ? 'Complete' : 'Progress'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Recent Activity Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-base font-semibold text-black dark:text-white">
                    Recent Activity
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setActiveTab('eventlog')}
                    className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  >
                    View all
                    <ChevronDown className="h-3 w-3 ml-1 rotate-[-90deg]" />
                  </Button>
                </div>

                {activityLog.length > 0 ? (
                  <div className="space-y-3">
                    {activityLog.slice(0, 3).map((activity: ActivityLogEntry, index: number) => {
                      const { icon: IconComponent, color } = getActivityIcon(activity.activityType || activity.activity_type);
                      return (
                        <div key={activity.id || index} className="flex items-start gap-3">
                          <div className={`h-8 w-8 rounded-full flex items-center justify-center ${color}`}>
                            <IconComponent className="h-4 w-4" />
                          </div>
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium text-black dark:text-white">
                                {activity.username || 'Unknown User'}
                              </span>
                              <span className="text-xs text-gray-600 dark:text-gray-400">
                                {formatActivityDescription(activity)}
                              </span>
                            </div>
                            <span className="text-xs text-gray-500 dark:text-gray-500">
                              {activity.createdAt
                                ? new Date(activity.createdAt).toLocaleString()
                                : 'Unknown time'
                              }
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                    <Clock className="h-6 w-6 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No recent activity</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'eventlog' && (
            <div className="p-6 space-y-6">
              <h3 className="text-base font-semibold text-black dark:text-white">
                Event Log & Activity
              </h3>

              <div className="space-y-6">
                {/* Manual Event Log with inline editing */}
                <div className="space-y-3">
                  {isEditingEventLog ? (
                    <div className="space-y-2">
                      <Textarea
                        value={eventLogText}
                        onChange={(e) => setEventLogText(e.target.value)}
                        onBlur={() => {
                          // Immediate optimistic save on blur
                          saveEventLog();
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Escape') {
                            setEventLogText(fullProjectData?.eventLog || '');
                            setIsEditingEventLog(false);
                          } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                            e.preventDefault();
                            saveEventLog();
                          }
                        }}
                        placeholder="Add project notes, milestones, or important events...

Examples:
• Project kickoff meeting completed
• Design phase started
• **Important:** Client feedback received
• Next milestone: Development phase"
                        className="min-h-[200px] text-xs font-mono border-none bg-transparent focus:ring-0 focus:border-none p-0"
                        autoFocus
                      />
                      <div className="flex gap-2">
                        <button
                          onClick={saveEventLog}
                          className="px-3 py-1 text-xs bg-[#5E6AD2] hover:bg-[#5E6AD2]/90 dark:bg-[#6E56CF] dark:hover:bg-[#6E56CF]/90 text-white rounded-md"
                        >
                          Save
                        </button>
                        <button
                          onClick={() => {
                            setEventLogText(fullProjectData?.eventLog || '');
                            setIsEditingEventLog(false);
                          }}
                          className="px-3 py-1 text-xs bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div
                      onClick={() => setIsEditingEventLog(true)}
                      className="min-h-[200px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                    >
                      {fullProjectData?.eventLog ? (
                        <FormattedText
                          text={fullProjectData.eventLog}
                          className="text-xs text-gray-700 dark:text-gray-300"
                          variant="muted"
                        />
                      ) : (
                        <p className="text-xs text-gray-400 dark:text-gray-500 italic">
                          Click to add project notes, milestones, or important events...
                        </p>
                      )}
                    </div>
                  )}

                  {/* Edited indicator for Event Log */}
                  {!isEditingEventLog && fullProjectData?.eventLogEditCount && fullProjectData.eventLogEditCount > 0 && (
                    <div className="flex items-center gap-2">
                      <span
                        className="text-xs text-gray-500 dark:text-gray-400 cursor-pointer hover:text-[#5E6AD2] dark:hover:text-[#6E56CF] transition-colors duration-200"
                        data-edit-history-section
                        onClick={() => {
                          const fieldKey = 'eventLog';

                          if (showProjectFieldHistory === fieldKey) {
                            setShowProjectFieldHistory(null);
                          } else {
                            setShowProjectFieldHistory(fieldKey);
                            // Reset to show only first version when opening
                            const history = getProjectFieldHistory(fieldKey);
                            if (history.length > 0) {
                              updateProjectFieldHistory(fieldKey, history.slice(0, 1));
                              updateProjectFieldVersionCount(fieldKey, 1);
                            } else {
                              fetchProjectFieldEditHistory('eventLog');
                            }
                          }
                        }}
                        title="Click to view edit history"
                      >
                        Edited {fullProjectData.eventLogEditCount} time{fullProjectData.eventLogEditCount > 1 ? 's' : ''}
                      </span>
                    </div>
                  )}

                  {/* Edit History for Event Log */}
                  {showProjectFieldHistory === 'eventLog' && (
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-3" data-edit-history-section>
                      {isLoadingProjectFieldHistory ? (
                        <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 py-2">
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-[#5E6AD2] dark:border-[#6E56CF]"></div>
                          Loading edit history...
                        </div>
                      ) : getProjectFieldHistory('eventLog').length > 0 ? (
                        <div className="space-y-0">
                          {/* Previous versions */}
                          {getProjectFieldHistory('eventLog').map((edit: { versionNumber: number; previousContent: string; editedAt: string; editedBy: string; editedByUsername?: string }, index: number) => (
                            <div key={index}>
                              {index > 0 && <hr className="border-gray-200 dark:border-gray-700 my-3" />}
                              <div className="pb-3">
                                {/* Timestamp above content with version label */}
                                <div className="text-xs text-gray-500 dark:text-gray-500 mb-2">
                                  {edit.versionNumber === 1 ? (
                                    <>
                                      <span className="font-medium text-blue-600 dark:text-blue-400">Original</span>
                                      {' • '}
                                      {new Date(edit.editedAt).toLocaleString()}
                                      {edit.editedByUsername && ` • Created by ${edit.editedByUsername}`}
                                    </>
                                  ) : (
                                    <>
                                      <span className="font-medium text-purple-600 dark:text-purple-400">Version {edit.versionNumber}</span>
                                      {' • '}
                                      {new Date(edit.editedAt).toLocaleString()}
                                      {edit.editedByUsername && ` • Edited by ${edit.editedByUsername}`}
                                    </>
                                  )}
                                </div>
                                {/* Content - show the content that was replaced (previous content) */}
                                <div className="text-xs text-gray-600 dark:text-gray-400 break-words whitespace-pre-wrap">
                                  {edit.previousContent || '(Empty)'}
                                </div>
                              </div>
                            </div>
                          ))}

                          {/* Show Older Revisions button */}
                          {getProjectFieldHistory('eventLog').length > 0 &&
                           getProjectFieldVersionCount('eventLog') < (fullProjectData?.eventLogEditCount || 0) && (
                            <div className="text-center pt-2">
                              <hr className="border-gray-200 dark:border-gray-700 mb-3" />
                              <button
                                onClick={() => fetchProjectFieldEditHistory('eventLog', true)}
                                className="text-xs text-[#5E6AD2] dark:text-[#6E56CF] hover:underline"
                                disabled={isLoadingProjectFieldHistory}
                                data-edit-history-section
                              >
                                {isLoadingProjectFieldHistory ? 'Loading...' : 'Show Older Revisions'}
                              </button>
                            </div>
                          )}

                          {/* Recent Edit button - shown when all history is loaded */}
                          {getProjectFieldHistory('eventLog').length > 0 &&
                           getProjectFieldVersionCount('eventLog') >= (fullProjectData?.eventLogEditCount || 0) &&
                           getProjectFieldHistory('eventLog').length > 1 && (
                            <div className="text-center pt-2">
                              <hr className="border-gray-200 dark:border-gray-700 mb-3" />
                              <button
                                onClick={() => {
                                  // Reset to show only first version
                                  const fieldKey = 'eventLog';
                                  const history = getProjectFieldHistory(fieldKey);
                                  updateProjectFieldHistory(fieldKey, history.slice(0, 1));
                                  updateProjectFieldVersionCount(fieldKey, 1);
                                }}
                                className="text-xs text-[#5E6AD2] dark:text-[#6E56CF] hover:underline"
                                data-edit-history-section
                              >
                                Recent Edit
                              </button>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="text-xs text-gray-500 dark:text-gray-400 text-center py-4">
                          No edit history available
                        </div>
                      )}
                    </div>
                  )}


                </div>

                {/* Comprehensive Activity Log */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-4">
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Activity Timeline
                    </h4>
                    {isLoadingActivity && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">Loading...</div>
                    )}
                  </div>

                  {activityLog.length > 0 ? (
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {activityLog.map((activity: ActivityLogEntry, index: number) => {
                        const { icon: IconComponent, color } = getActivityIcon(activity.activityType || activity.activity_type);
                        return (
                          <div key={activity.id || index} className="flex items-start gap-3">
                            <div className={`h-8 w-8 rounded-full flex items-center justify-center ${color}`}>
                              <IconComponent className="h-4 w-4" />
                            </div>
                            <div className="flex-1 space-y-1">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <p className="text-sm text-gray-700 dark:text-gray-300">
                                    <span className="font-medium">{activity.username || 'Unknown User'}</span>
                                    <span className="ml-1">{formatActivityDescription(activity)}</span>
                                  </p>
                                  <p className="text-xs text-gray-500 dark:text-gray-400">
                                    {activity.createdAt
                                      ? new Date(activity.createdAt).toLocaleString()
                                      : 'Unknown time'
                                    }
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : !isLoadingActivity ? (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No activity recorded yet.</p>
                      <p className="text-xs">Activity will appear here as team members interact with the project.</p>
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'files' && (
            <div className="p-6">
              {onFileUploaded && onFileDeleted && (
                <FileUpload
                  projectId={task.id}
                  files={task.projectDetails?.files || []}
                  onFileUploaded={onFileUploaded}
                  onFileDeleted={onFileDeleted}
                />
              )}
            </div>
          )}

          {activeTab === 'team' && (
            <div className="p-6">
              {onTeamUpdated && currentUserId && (
                <TeamManagement
                  projectId={task.id}
                  teamMembers={fullProjectData?.teamMembers || []}
                  pendingTeamMembers={Array.from(pendingTeamMembers.values())}
                  isProjectOwner={task.createdBy?.toString() === currentUserId}
                  onTeamUpdated={async () => {
                    await onTeamUpdated();
                    // DISABLED: Don't refresh immediately - preserve optimistic state
                    // Refresh project data after team update
                    // await fetchFullProjectDetails();
                  }}
                  onAddTemporaryMember={handleAddTemporaryMember}
                />
              )}
            </div>
          )}

          {activeTab === 'comments' && (
            <div className="p-6 space-y-4">
              <h3 className="text-base font-semibold text-black dark:text-white">
                Comments
              </h3>

              {isLoadingComments ? (
                <div className="flex justify-center py-8">
                  <div className="text-sm text-gray-500 dark:text-gray-400">Loading comments...</div>
                </div>
              ) : (
                <div className="space-y-4">
                  {comments.length === 0 ? (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      <p className="text-sm">
                        {isCurrentUserTeamMember
                          ? "No comments yet. Be the first to add one!"
                          : "No comments yet. Start a conversation or request access to this project!"
                        }
                      </p>
                    </div>
                  ) : (
                    comments.map((comment: ProjectComment) => (
                      <div key={comment.id} className="group relative">
                        {/* Comment Container with proper text wrapping constraints */}
                        <div className="flex items-start gap-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg p-3 transition-colors duration-200 max-w-full">
                          <Avatar className="h-8 w-8 flex-shrink-0">
                            <div className="h-full w-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                              <User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            </div>
                          </Avatar>

                          {/* Content area with proper width constraints */}
                          <div className="flex-1 min-w-0 space-y-2">
                            {/* Header with user info and timestamp */}
                            <div className="flex items-center gap-2 flex-wrap">
                              <span className="text-sm font-medium text-black dark:text-white">
                                {comment.authorUsername || 'Unknown User'}
                              </span>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-gray-500 dark:text-gray-500">
                                  {comment.createdAt ? new Date(comment.createdAt).toLocaleString() : 'Invalid Date'}
                                </span>
                                {comment.isEdited && comment.editCount && comment.editCount > 0 && (
                                  <span
                                    className="text-xs text-gray-500 dark:text-gray-400 cursor-pointer hover:text-[#5E6AD2] dark:hover:text-[#6E56CF] transition-colors duration-200"
                                    data-edit-history-section
                                    onClick={() => {
                                      setShowEditHistory(showEditHistory === comment.id ? null : comment.id);
                                      if (showEditHistory !== comment.id) {
                                        fetchCommentEditHistory(comment.id);
                                      }
                                    }}
                                    title="Click to view edit history"
                                  >
                                    Edited {comment.editCount} time{comment.editCount > 1 ? 's' : ''}
                                  </span>
                                )}
                              </div>
                            </div>

                            {/* Comment content with edit mode */}
                            {editingCommentId === comment.id ? (
                              <div className="space-y-2">
                                <Textarea
                                  value={editingCommentText}
                                  onChange={(e) => setEditingCommentText(e.target.value)}
                                  onKeyDown={handleCommentEditKeyDown}
                                  className="w-full text-xs border border-gray-200 dark:border-[#2A2A2A] rounded-md bg-white dark:bg-[#1A1A1A] text-black dark:text-white resize-none min-h-[60px] max-h-[200px] break-words"
                                  autoFocus
                                  rows={3}
                                />
                                <div className="flex gap-2">
                                  <button
                                    onClick={saveCommentEdit}
                                    className="px-3 py-1 text-xs bg-[#5E6AD2] hover:bg-[#5E6AD2]/90 dark:bg-[#6E56CF] dark:hover:bg-[#6E56CF]/90 text-white rounded-md"
                                  >
                                    Save
                                  </button>
                                  <button
                                    onClick={cancelEditingComment}
                                    className="px-3 py-1 text-xs bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md"
                                  >
                                    Cancel
                                  </button>
                                </div>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  Press Shift+Enter for line breaks, Enter to save, Escape to cancel
                                </p>
                              </div>
                            ) : (
                              <div className="w-full break-words overflow-wrap-anywhere p-2 -m-2">
                                <CommentText
                                  text={comment.content}
                                  className="text-xs text-gray-700 dark:text-gray-300 break-words whitespace-pre-wrap"
                                />
                              </div>
                            )}

                            {/* Edit History Dropdown */}
                            {showEditHistory === comment.id && (
                              <div className="mt-3 border-t border-gray-200 dark:border-gray-700 pt-3" data-edit-history-section>
                                {isLoadingEditHistory ? (
                                  <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 py-2">
                                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-[#5E6AD2] dark:border-[#6E56CF]"></div>
                                    Loading edit history...
                                  </div>
                                ) : getCommentHistory(comment.id).length > 0 ? (
                                  <div className="space-y-0 max-h-48 overflow-y-auto">
                                    {/* Previous versions only (show up to 2 previous versions) */}
                                    {getCommentHistory(comment.id).slice(0, 2).map((edit: { versionNumber: number; previousContent: string; editedAt: string; editedBy: string; editedByUsername?: string }, index: number) => (
                                      <div key={index}>
                                        {index > 0 && <hr className="border-gray-200 dark:border-gray-700 my-3" />}
                                        <div className="pb-3">
                                          {/* Timestamp above content with version label */}
                                          <div className="text-xs text-gray-500 dark:text-gray-500 mb-2">
                                            {edit.versionNumber === 1 ? (
                                              <>
                                                <span className="font-medium text-blue-600 dark:text-blue-400">Original</span>
                                                {' • '}
                                                {new Date(edit.editedAt).toLocaleString()}
                                              </>
                                            ) : (
                                              <>
                                                <span className="font-medium text-purple-600 dark:text-purple-400">Version {edit.versionNumber}</span>
                                                {' • '}
                                                {new Date(edit.editedAt).toLocaleString()}
                                              </>
                                            )}
                                          </div>
                                          {/* Previous content */}
                                          <div className="text-xs text-gray-600 dark:text-gray-400 break-words whitespace-pre-wrap">
                                            {edit.previousContent}
                                          </div>
                                        </div>
                                      </div>
                                    ))}

                                    {getCommentHistory(comment.id).length > 2 && (
                                      <div className="text-center pt-2">
                                        <hr className="border-gray-200 dark:border-gray-700 mb-3" />
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                          {getCommentHistory(comment.id).length - 2} older version{getCommentHistory(comment.id).length > 3 ? 's' : ''} not shown
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                ) : (
                                  <div className="text-xs text-gray-500 dark:text-gray-400 text-center py-4">
                                    No edit history available
                                  </div>
                                )}
                              </div>
                            )}
                          </div>

                          {/* Action buttons positioned in top-right */}
                          <div className="flex-shrink-0 flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            {canEditComment(comment) && editingCommentId !== comment.id && (
                              <button
                                onClick={() => startEditingComment(comment)}
                                className="p-1 text-gray-400 hover:text-[#5E6AD2] dark:text-gray-500 dark:hover:text-[#6E56CF] transition-colors duration-200"
                                title="Edit comment"
                              >
                                <Edit2 className="h-4 w-4" />
                              </button>
                            )}
                            {canDeleteComment(comment) && (
                              <button
                                onClick={() => deleteComment(comment.id)}
                                className="p-1 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 transition-colors duration-200"
                                title="Delete comment"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}

              {/* Add comment with Shift+Enter support */}
              <div className="border-t border-gray-200 dark:border-[#2A2A2A] pt-4">
                <div className="flex w-full gap-2">
                  <Textarea
                    value={commentText}
                    onChange={(e) => setCommentText(e.target.value)}
                    onKeyDown={handleCommentKeyDown}
                    placeholder={getCommentPlaceholder}
                    className="flex-1 px-3 py-2 text-xs border border-gray-200 dark:border-[#2A2A2A] rounded-md bg-white dark:bg-[#1A1A1A] text-black dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:outline-none focus:border-[#5E6AD2] dark:focus:border-[#6E56CF] transition-colors duration-200 resize-none min-h-[40px] max-h-[120px]"
                    disabled={isSendingComment}
                    rows={1}
                  />
                  <Button
                    size="sm"
                    onClick={sendComment}
                    disabled={!commentText.trim() || isSendingComment}
                    className="bg-[#5E6AD2] hover:bg-[#5E6AD2]/90 dark:bg-[#6E56CF] dark:hover:bg-[#6E56CF]/90 self-end"
                  >
                    <Send className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Initial Loading Overlay */}
        {isInitialLoading && (
          <div className="absolute inset-0 z-[350] bg-white/80 dark:bg-[#1A1A1A]/80 backdrop-blur-sm flex items-center justify-center">
            <div className="bg-white dark:bg-[#1A1A1A] rounded-lg shadow-xl p-8 max-w-sm mx-4 border border-gray-200 dark:border-gray-700">
              <div className="flex flex-col items-center space-y-4">
                {/* Loading Spinner */}
                <div className="relative">
                  <div className="w-12 h-12 border-4 border-gray-200 dark:border-gray-700 rounded-full"></div>
                  <div className="absolute top-0 left-0 w-12 h-12 border-4 border-[#5E6AD2] border-t-transparent rounded-full animate-spin"></div>
                </div>

                {/* Loading Text */}
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-black dark:text-white mb-2">
                    Loading Project
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {initialLoadingProgress}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Confirmation Dialog for Unsaved Changes */}
        {showCloseConfirmation && (
          <div className="fixed inset-0 z-[400] flex items-center justify-center">
            {/* Backdrop */}
            <div className="absolute inset-0 bg-black/50" />

            {/* Dialog */}
            <div className="relative bg-white dark:bg-[#1A1A1A] rounded-lg shadow-xl p-6 max-w-md mx-4">
              <h3 className="text-lg font-semibold text-black dark:text-white mb-4">
                {isRefreshingWorkspace ? 'Saving Changes...' : 'Edits have been made'}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                {isRefreshingWorkspace
                  ? 'Saving your changes and updating the workspace...'
                  : 'Are you sure you\'d like to close and submit?'
                }
              </p>

              {isRefreshingWorkspace && (
                <div className="flex items-center justify-center mb-4">
                  <div className="w-6 h-6 border-2 border-[#5E6AD2] border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}

              <div className="flex gap-3 justify-end">
                <Button
                  variant="outline"
                  onClick={handleCancelClose}
                  className="text-sm"
                  disabled={isRefreshingWorkspace}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleConfirmClose}
                  className="text-sm bg-[#5E6AD2] hover:bg-[#4E5BC2] text-white"
                  disabled={isRefreshingWorkspace}
                >
                  {isRefreshingWorkspace ? 'Saving...' : 'Confirm'}
                </Button>
              </div>
            </div>
          </div>
        )}

      </div>
    </DrawerOverlay>
  );
}
