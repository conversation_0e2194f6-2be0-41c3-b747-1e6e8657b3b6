"use client";

import { useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import { useSession, signOut } from 'next-auth/react';
import { usePathname } from '@/i18n/navigation';
import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import {
  Zap,
  ChevronDown,
  Home,
  Calculator,
  CheckSquare,
  FolderOpen,
  MessageSquare,
  Bell,
  Settings,
  LogOut,
  Check,
  Briefcase,
  Rocket,
  Star,
  Plus,
  X,
  Menu,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  className?: string;
}

export function Sidebar({ isOpen, onClose, isCollapsed = false, onToggleCollapse, className = '' }: SidebarProps) {
  const { resolvedTheme } = useTheme();
  const { data: session } = useSession();
  const currentUser = session?.user;
  const pathname = usePathname();
  const t = useTranslations('navigation');
  const [workspaceMenuOpen, setWorkspaceMenuOpen] = useState(false);
  const [projectsOpen, setProjectsOpen] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const isDark = resolvedTheme === 'dark';

  const navigationItems = [
    {
      href: '/dashboard',
      icon: Home,
      label: t('dashboard'),
      active: pathname === '/dashboard'
    },
    {
      href: '/calculator',
      icon: Calculator,
      label: t('calculator'),
      active: pathname === '/calculator'
    },
    {
      href: '/task-master',
      icon: CheckSquare,
      label: t('taskMaster'),
      active: pathname === '/task-master'
    },
    {
      href: '#',
      icon: FolderOpen,
      label: t('projectHistory'),
      active: false,
      disabled: true
    },
    {
      href: '#',
      icon: MessageSquare,
      label: t('messages'),
      active: false,
      disabled: true,
      notification: true
    },
    {
      href: '#',
      icon: Bell,
      label: t('notifications'),
      active: false,
      disabled: true,
      notification: true
    }
  ];



  const projects = [
    {
      id: 'paper-estimator',
      name: 'Paper Cost Estimator',
      status: 'active',
      color: 'bg-gradient-to-tl from-teal-400 to-blue-500',
      starred: true,
      subItems: [
        'Calculator Engine',
        'Paper Database',
        'Cost Analysis'
      ]
    }
  ];

  const sidebarClasses = `
    sidebar overflow-hidden transition-all duration-300
    ${isCollapsed ? 'sidebar-collapsed' : ''}
    ${isDark
      ? 'beautiful-shadow-dark bg-neutral-800 border-neutral-700'
      : 'beautiful-shadow bg-white border-gray-200'
    }
    rounded-2xl
    ${className}
  `;

  const isMobileView = typeof window !== 'undefined' && window.innerWidth < 768;

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && isMobileView && (
        <div
          className="sidebar-overlay"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside className={`${sidebarClasses} ${isMobileView ? (isOpen ? 'sidebar-mobile open' : 'sidebar-mobile') : 'sticky top-0'}`}>
        {/* Collapsed state - completely hidden */}
        {isCollapsed && !isMobileView && null}

        {/* Full sidebar content - only show when not collapsed or on mobile */}
        {(!isCollapsed || isMobileView) && (
          <>
        {/* Mobile close button */}
        {isMobileView && (
          <div className="flex justify-end p-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className={isDark ? 'text-white hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        )}

        {/* Workspace header */}
        <div className={`flex items-center justify-between border-b pt-5 pr-5 pb-5 pl-5 ${isDark ? 'border-gray-700' : 'border-gray-100'}`}>
          <button 
            onClick={() => setWorkspaceMenuOpen(!workspaceMenuOpen)}
            className={`flex items-center gap-2 transition-all text-sm font-semibold rounded-xl pt-2.5 pr-4 pb-2.5 pl-4 ${
              isDark 
                ? 'beautiful-shadow-dark bg-gray-700 border-gray-600 border hover:bg-gray-600' 
                : 'beautiful-shadow hover:shadow-md bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200 border'
            }`}
          >
            <Zap className={`w-4 h-4 ${isDark ? 'text-cyan-400' : 'text-indigo-600'}`} />
            <span className={`${isDark ? 'text-white' : 'text-gray-800'} truncate max-w-[120px]`}>Codex</span>
            <ChevronDown className={`w-4 h-4 ${isDark ? 'text-gray-400' : 'text-gray-500'}`} />
          </button>
          <div className="flex items-center gap-2">
            {/* Hamburger menu button for desktop */}
            {!isMobileView && onToggleCollapse && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleCollapse}
                className={`p-2 ${isDark ? 'text-white hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'}`}
              >
                <Menu className="w-4 h-4" />
              </Button>
            )}
            <div className="relative">
              <div className={`w-10 h-10 rounded-xl object-cover border-2 beautiful-shadow flex items-center justify-center text-sm font-semibold ${
                isDark 
                  ? 'border-gray-600 beautiful-shadow-dark bg-gray-700 text-white' 
                  : 'border-gray-200 bg-gradient-to-br from-blue-500 to-purple-600 text-white'
              }`}>
                {currentUser?.name?.[0]?.toUpperCase() || currentUser?.email?.[0]?.toUpperCase() || 'U'}
              </div>
              <div className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 ${
                isDark ? 'bg-green-400 border-gray-800' : 'bg-emerald-400 border-white'
              }`}></div>
            </div>
          </div>
        </div>

        {/* Workspace dropdown */}
        {workspaceMenuOpen && (
          <div className={`mx-5 mt-2 rounded-xl border p-5 text-sm ${
            isDark 
              ? 'bg-gray-700 border-gray-600 beautiful-shadow-dark' 
              : 'bg-gradient-to-br from-gray-50 to-indigo-50 beautiful-shadow border-gray-200'
          }`}>
            <div className={`mb-4 pb-4 border-b ${isDark ? 'border-gray-600' : 'border-gray-200'}`}>
              <p className={`font-semibold ${isDark ? 'text-white' : 'text-gray-800'} truncate`}>
                {currentUser?.name || 'User'}
              </p>
              <p className={`text-xs mt-1 ${isDark ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                {currentUser?.email}
              </p>
              <div className="flex items-center gap-2 mt-2">
                <div className={`w-2 h-2 rounded-full ${isDark ? 'bg-green-400' : 'bg-emerald-400'}`}></div>
                <span className={`text-xs ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>Online</span>
              </div>
            </div>
            
            <button className={`flex items-center gap-3 w-full py-2.5 px-2 rounded-lg transition-colors ${
              isDark ? 'hover:bg-gray-600' : 'hover:bg-white/60'
            }`}>
              <Zap className={`w-4 h-4 ${isDark ? 'text-cyan-400' : 'text-indigo-600'}`} />
              <span className="font-medium truncate">Codex</span>
              <Check className={`w-4 h-4 ml-auto ${isDark ? 'text-green-400' : 'text-emerald-600'}`} />
            </button>
            
            <button className={`flex items-center gap-3 w-full py-2.5 px-2 rounded-lg transition-colors ${
              isDark ? 'hover:bg-gray-600 text-gray-400' : 'hover:bg-white/60 text-gray-600'
            }`}>
              <Briefcase className="w-4 h-4 text-blue-600" />
              <span>Future Workspace</span>
            </button>
            
            <button className={`flex items-center gap-3 w-full py-2.5 px-2 rounded-lg transition-colors ${
              isDark ? 'hover:bg-gray-600 text-gray-400' : 'hover:bg-white/60 text-gray-600'
            }`}>
              <Rocket className="w-4 h-4 text-purple-600" />
              <span>Enterprise Hub</span>
            </button>
            
            <hr className={`my-4 ${isDark ? 'border-gray-600' : 'border-gray-200'}`} />
            
            <Link
              href="/settings"
              onClick={onClose}
              className={`flex items-center gap-3 w-full py-2.5 px-2 rounded-lg transition-colors ${
                isDark ? 'hover:bg-gray-600 text-gray-300' : 'hover:bg-white/60 text-gray-700'
              }`}
            >
              <Settings className="w-4 h-4" />
              {t('settings')}
            </Link>

            <button
              onClick={() => signOut({ callbackUrl: "/auth/login" })}
              className={`flex items-center gap-3 w-full py-2.5 px-2 rounded-lg transition-colors ${
                isDark ? 'hover:bg-gray-600 text-red-400' : 'hover:bg-white/60 text-red-600'
              }`}
            >
              <LogOut className="w-4 h-4" />
              Sign Out
            </button>
          </div>
        )}

        {/* Navigation */}
        <nav className={`select-none text-sm pt-6 pr-2 pl-2 ${isDark ? 'text-white' : ''}`}>
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = item.active;
            
            if (item.disabled) {
              return (
                <div
                  key={item.label}
                  className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-colors opacity-50 cursor-not-allowed ${
                    isDark ? 'text-gray-400' : 'text-gray-500'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  {item.label}
                  {item.badge && (
                    <span className={`ml-auto text-xs px-2 py-1 rounded-full font-medium ${
                      isDark ? 'bg-gray-700 text-gray-300' : 'bg-indigo-100 text-indigo-800'
                    }`}>
                      {item.badge}
                    </span>
                  )}
                  {/* Notification indicator hidden for disabled items until functionality is implemented */}
                  {/* {item.notification && (
                    <span className={`absolute right-6 top-1/2 -translate-y-1/2 w-2 h-2 rounded-full ${
                      isDark ? 'bg-cyan-400' : 'bg-red-500'
                    }`}></span>
                  )} */}
                </div>
              );
            }

            return (
              <Link
                key={item.href}
                href={item.href}
                onClick={onClose}
                className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-colors relative ${
                  isActive
                    ? isDark
                      ? 'bg-gradient-to-r from-cyan-600 to-blue-600 beautiful-shadow-dark text-white'
                      : 'bg-gradient-to-r from-blue-500 to-purple-600 beautiful-shadow text-white'
                    : isDark
                      ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                      : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className={isActive ? 'font-medium' : ''}>{item.label}</span>
                {item.badge && (
                  <span className={`ml-auto text-xs px-2 py-1 rounded-full font-medium ${
                    isActive
                      ? 'bg-white/20 text-white'
                      : isDark
                        ? 'bg-gray-700 text-gray-300'
                        : 'bg-indigo-100 text-indigo-800'
                  }`}>
                    {item.badge}
                  </span>
                )}
                {/* Notification indicator hidden until functionality is implemented */}
                {/* {item.notification && (
                  <span className={`absolute right-6 top-1/2 -translate-y-1/2 w-2 h-2 rounded-full ${
                    isDark ? 'bg-cyan-400' : 'bg-red-500'
                  }`}></span>
                )} */}
              </Link>
            );
          })}
          {/* Projects Section */}
          <div className="px-4 mt-6 pb-6">
            <button
              onClick={() => setProjectsOpen(!projectsOpen)}
              className={`flex items-center gap-2 w-full uppercase text-xs tracking-wider font-semibold mb-3 ${
                isDark ? 'text-slate-400' : 'text-slate-500'
              }`}
            >
              <ChevronDown className={`w-4 h-4 transition-transform ${projectsOpen ? '' : '-rotate-90'}`} />
              Active Projects
              <Plus className={`w-4 h-4 ml-auto rounded p-0.5 transition-colors ${
                isDark ? 'hover:bg-slate-700' : 'hover:bg-slate-200'
              }`} />
            </button>

            {projectsOpen && (
              <div className="space-y-1">
                {projects.map((project) => (
                  <div key={project.id}>
                    <button className={`flex items-center gap-3 w-full px-4 py-3 rounded-xl transition-colors ${
                      project.status === 'active'
                        ? `${project.color} text-white beautiful-shadow${isDark ? '-dark' : ''}`
                        : isDark
                          ? 'hover:bg-slate-800 text-slate-300'
                          : 'hover:bg-slate-100 text-slate-700'
                    }`}>
                      <span className={`w-2 h-2 rounded-full ${
                        project.status === 'active'
                          ? 'bg-white'
                          : project.color.replace('bg-gradient-to-tl from-teal-400 to-blue-500', project.color)
                      }`}></span>
                      <span className={project.status === 'active' ? 'font-medium' : ''}>{project.name}</span>
                      {project.starred && (
                        <Star className="w-4 h-4 ml-auto" />
                      )}
                      {project.badge && (
                        <div className="ml-auto flex items-center gap-1">
                          <span className={`w-1.5 h-1.5 rounded-full ${project.color}`}></span>
                          <span className={`text-xs ${isDark ? 'text-slate-400' : 'text-slate-500'}`}>{project.badge}</span>
                        </div>
                      )}
                      {project.statusLabel && (
                        <span className={`ml-auto text-xs px-2 py-1 rounded-full ${
                          project.status === 'review'
                            ? isDark ? 'bg-amber-900 text-amber-300' : 'bg-amber-100 text-amber-800'
                            : project.status === 'urgent'
                              ? isDark ? 'bg-rose-900 text-rose-300' : 'bg-rose-100 text-rose-800'
                              : isDark ? 'bg-slate-700 text-slate-300' : 'bg-slate-100 text-slate-800'
                        }`}>
                          {project.statusLabel}
                        </span>
                      )}
                    </button>

                    {project.subItems && project.status === 'active' && (
                      <div className="ml-9 space-y-1 mt-1">
                        {project.subItems.map((subItem) => (
                          <a
                            key={subItem}
                            href="#"
                            className={`block py-2 rounded-lg transition-colors ${
                              isDark
                                ? 'text-slate-400 hover:text-white hover:bg-slate-800'
                                : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
                            }`}
                          >
                            {subItem}
                          </a>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>


        </nav>
          </>
        )}
      </aside>
    </>
  );
}
