<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Design System Cards</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

    :root {
      --card-size: clamp(260px, 22vw, 400px);
      --card-ar: 4 / 3;
      --x: calc(50vw * 1);
      --y: calc(50vh * 1);
      --spotlight: 50vmin;
      --backdrop: rgba(11, 13, 17, 0.7);
      --radius: 12;
      --border: 1;
      --backup-border: rgba(39, 46, 63, 0.5);
      --size: 200;
      --bg-spot-opacity: 0.1;
      --border-light-opacity: 0.4;
      --border-spot-opacity: 0.35;
      --gap: 2rem;
      --background: #090A0C;
      --surface: #101219;
      --surface-elevated: #161A23;
      --surface-bright: #21263a;
      --surface-hover: #2a3152;
      --border-color: #272E3F;
      --text-primary: rgba(235, 236, 240, 0.9);
      --text-primary-bright: #ffffff;
      --text-secondary: rgba(235, 236, 240, 0.5);
      --text-tertiary: rgba(235, 236, 240, 0.3);
      --accent-purple: #5E6AD2;
      --accent-blue: #2E80F2;
      --accent-green: #25B888;
      --accent-yellow: #D9A71F;
    }

    *,
    *:after,
    *:before {
      box-sizing: border-box;
    }

    body {
      display: grid;
      place-items: center;
      min-height: 100vh;
      overflow: hidden;
      background: var(--background);
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      font-weight: 400;
      color: var(--text-primary);
      margin: 0;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    article {
      aspect-ratio: var(--card-ar);
      border-radius: calc(var(--radius) * 1px);
      display: flex;
      flex-direction: column;
      position: relative;
      width: var(--card-size);
      height: 100%;
      background-color: var(--surface);
      backdrop-filter: blur(8px);
      overflow: hidden;
      transition: all 0.3s cubic-bezier(.4,0,.2,1);
    }

    /* Article layout */
    .card-header {
      padding: 1.25rem 1.25rem 0.75rem;
      display: flex;
      align-items: center;
      gap: 0.75rem;
      transition: transform 0.3s ease;
    }

    .card-header .icon {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 10px;
      background-color: var(--surface-elevated);
      flex-shrink: 0;
      transition: all 0.3s ease;
    }

    .card-header svg {
      width: 22px;
      height: 22px;
      color: hsl(var(--hue) calc(var(--sat, 60) * 1%) 70%);
      transition: all 0.3s ease;
    }

    .card-header-text {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      transition: color 0.2s;
    }

    .card-content {
      flex: 1;
      padding: 0.25rem 1.25rem 1.25rem;
      display: flex;
      flex-direction: column;
      transition: transform 0.3s ease;
    }

    .divider {
      width: 100%;
      height: 1px;
      background-color: var(--border-color);
      margin: 0.75rem 0;
      transition: background-color 0.3s ease;
    }

    .card-description {
      color: var(--text-secondary);
      font-size: 0.875rem;
      line-height: 1.4;
      margin-bottom: 1rem;
      transition: color 0.3s ease;
    }

    .stats {
      display: flex;
      gap: 1rem;
      margin-top: auto;
    }

    .stat {
      flex: 1;
    }

    .stat-label {
      font-size: 0.75rem;
      color: var(--text-tertiary);
      margin-bottom: 0.25rem;
      transition: color 0.3s ease;
    }

    .stat-value {
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--text-primary);
      transition: color 0.3s ease;
    }

    .tag {
      display: inline-flex;
      align-items: center;
      height: 22px;
      border-radius: 4px;
      padding: 0 8px;
      font-size: 0.75rem;
      font-weight: 500;
      margin-top: 1rem;
      color: var(--text-primary);
      background-color: var(--surface-elevated);
      transition: color 0.2s;
    }

    h2 {
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0;
      color: var(--text-primary);
      transition: color 0.3s ease;
    }

    .company {
      font-size: 0.85rem;
      color: var(--text-secondary);
      transition: color 0.3s ease;
    }

    .grid-container {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: var(--gap);
      width: calc((var(--card-size) * 2) + var(--gap));
      max-height: calc((var(--card-size) * (4/3) * 2) + var(--gap));
    }

    .card-wrapper {
      display: grid;
      place-items: center;
      position: relative;
    }

    section {
      width: 100vw;
      display: grid;
      place-items: center;
    }

    /* Glow specific styles */
    [data-glow] {
      --border-size: calc(var(--border, 1) * 1px);
      --spotlight-size: calc(var(--card-size) * 1.2);
      --hue: calc(var(--base) + (var(--xp, 0) * var(--spread, 0)));
      background-image: radial-gradient(
        var(--spotlight-size) var(--spotlight-size) at
        var(--x)
        var(--y),
        hsl(var(--hue, 210) calc(var(--saturation, 100) * 1%) calc(var(--lightness, 70) * 1%) / var(--bg-spot-opacity, 0.1)), transparent
      );
      background-size: calc(100% + (2 * var(--border-size))) calc(100% + (2 * var(--border-size)));
      background-position: 50% 50%;
      background-attachment: fixed;
      border: var(--border-size) solid var(--backup-border);
      position: relative;
      touch-action: none;
      transition: all 0.3s cubic-bezier(.4,0,.2,1);
    }

    [data-glow]::before,
    [data-glow]::after {
      pointer-events: none;
      content: "";
      position: absolute;
      inset: calc(var(--border-size) * -1);
      border: var(--border-size) solid transparent;
      border-radius: calc(var(--radius) * 1px);
      background-attachment: fixed;
      background-size: calc(100% + (2 * var(--border-size))) calc(100% + (2 * var(--border-size)));
      background-repeat: no-repeat;
      background-position: 50% 50%;
      mask:
        linear-gradient(transparent, transparent),
        linear-gradient(white, white);
      mask-clip: padding-box, border-box;
      mask-composite: intersect;
      transition: opacity 0.3s ease;
    }

    [data-glow]::before {
      background-image: radial-gradient(
        calc(var(--spotlight-size) * 0.75) calc(var(--spotlight-size) * 0.75) at
        var(--x)
        var(--y),
        hsl(var(--hue, 210) calc(var(--saturation, 100) * 1%) calc(var(--lightness, 50) * 1%) / var(--border-spot-opacity, 0.35)), transparent 100%
      );
      filter: brightness(1.5);
    }
    
    [data-glow]::after {
      background-image: radial-gradient(
        calc(var(--spotlight-size) * 0.4) calc(var(--spotlight-size) * 0.4) at
        var(--x)
        var(--y),
        hsl(0 100% 100% / var(--border-light-opacity, 0.4)), transparent 100%
      );
    }

    /* Enhanced hover effect */
    [data-glow]:hover {
      background-color: var(--surface-hover);
      transform: translateY(-4px) scale(1.02);
      box-shadow: 
        0 20px 35px rgba(0, 0, 0, 0.3),
        0 8px 40px 0 rgba(85, 110, 255, 0.25), 
        0 0 0 1px rgba(94, 106, 210, 0.6);
      border-color: rgba(94, 106, 210, 0.7);
      z-index: 10;
    }
    
    [data-glow]:hover::before {
      opacity: 1.6;
    }
    
    [data-glow]:hover::after {
      opacity: 1.2;
    }
    
    /* Brighten content on hover */
    [data-glow]:hover h2,
    [data-glow]:hover .stat-value {
      color: var(--text-primary-bright);
    }
    
    [data-glow]:hover .company,
    [data-glow]:hover .card-description {
      color: rgba(255, 255, 255, 0.8);
    }
    
    [data-glow]:hover .stat-label {
      color: rgba(255, 255, 255, 0.6);
    }
    
    [data-glow]:hover .divider {
      background-color: rgba(94, 106, 210, 0.5);
    }
    
    [data-glow]:hover .icon {
      background-color: rgba(94, 106, 210, 0.2);
      box-shadow: 0 0 16px rgba(94, 106, 210, 0.3);
    }
    
    [data-glow]:hover svg {
      transform: scale(1.15);
    }

    .card-wrapper:nth-child(1) [data-glow] {
      --base: 260;
      --sat: 80;
    }
    
    .card-wrapper:nth-child(1) [data-glow]:hover .icon svg {
      color: #8a94ff;
    }
    
    .card-wrapper:nth-child(2) [data-glow] {
      --base: 200;
      --sat: 80;
    }
    
    .card-wrapper:nth-child(2) [data-glow]:hover .icon svg {
      color: #69c3ff;
    }
    
    .card-wrapper:nth-child(3) [data-glow] {
      --base: 150;
      --sat: 80;
    }
    
    .card-wrapper:nth-child(3) [data-glow]:hover .icon svg {
      color: #5de4a9;
    }
    
    .card-wrapper:nth-child(4) [data-glow] {
      --base: 40;
      --sat: 80;
    }
    
    .card-wrapper:nth-child(4) [data-glow]:hover .icon svg {
      color: #ffd56a;
    }
  </style>
<script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
  <section>
    <div class="grid-container">
      <div class="card-wrapper">
        <article data-glow>
          <div class="card-header">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
              </svg>
            </div>
            <div class="card-header-text">
              <h2>Component Library</h2>
              <div class="company">Design System</div>
            </div>
          </div>
          <div class="card-content">
            <div class="divider"></div>
            <div class="card-description">
              Reusable UI components built with accessibility and customization in mind, supporting light and dark mode.
            </div>
            <div class="stats">
              <div class="stat">
                <div class="stat-label">Components</div>
                <div class="stat-value">187</div>
              </div>
              <div class="stat">
                <div class="stat-label">Version</div>
                <div class="stat-value">v2.4.0</div>
              </div>
            </div>
          </div>
        </article>
      </div>
      <div class="card-wrapper">
        <article data-glow>
          <div class="card-header">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7 14c-1.66 0-3 1.34-3 3 0 1.31-1.16 2-2 2 .92 1.22 2.49 2 4 2 2.21 0 4-1.79 4-4 0-1.66-1.34-3-3-3zm13.71-9.37l-1.34-1.34a.996.996 0 00-1.41 0L9 12.25 11.75 15l8.96-8.96a.996.996 0 000-1.41z"/>
              </svg>
            </div>
            <div class="card-header-text">
              <h2>Color System</h2>
              <div class="company">Design Tokens</div>
            </div>
          </div>
          <div class="card-content">
            <div class="divider"></div>
            <div class="card-description">
              Comprehensive color palette with semantic tokens for UI states, supporting WCAG AA accessibility standards.
            </div>
            <div class="stats">
              <div class="stat">
                <div class="stat-label">Colors</div>
                <div class="stat-value">64</div>
              </div>
              <div class="stat">
                <div class="stat-label">Themes</div>
                <div class="stat-value">3</div>
              </div>
            </div>
          </div>
        </article>
      </div>
      <div class="card-wrapper">
        <article data-glow>
          <div class="card-header">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4zm2.5 2.1h-15V5h15v14.1zm0-16.1h-15c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h15c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
              </svg>
            </div>
            <div class="card-header-text">
              <h2>Typography</h2>
              <div class="company">Font System</div>
            </div>
          </div>
          <div class="card-content">
            <div class="divider"></div>
            <div class="card-description">
              Modular type scale with responsive text styles. Optimized for readability across desktop and mobile interfaces.
            </div>
            <div class="stats">
              <div class="stat">
                <div class="stat-label">Font Families</div>
                <div class="stat-value">2</div>
              </div>
              <div class="stat">
                <div class="stat-label">Text Styles</div>
                <div class="stat-value">12</div>
              </div>
            </div>
          </div>
        </article>
      </div>
      <div class="card-wrapper">
        <article data-glow>
          <div class="card-header">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm6 6h4v-4h-4v4zm0 6h4v-4h-4v4z"/>
              </svg>
            </div>
            <div class="card-header-text">
              <h2>Spacing System</h2>
              <div class="company">Layout Grid</div>
            </div>
          </div>
          <div class="card-content">
            <div class="divider"></div>
            <div class="card-description">
              Consistent spacing scale with predefined values for margins, padding, and layout. Based on 4px increments.
            </div>
            <div class="stats">
              <div class="stat">
                <div class="stat-label">Space Units</div>
                <div class="stat-value">16</div>
              </div>
              <div class="stat">
                <div class="stat-label">Grid Columns</div>
                <div class="stat-value">12</div>
              </div>
            </div>
          </div>
        </article>
      </div>
    </div>
  </section>

  <script>
    const syncPointer = ({ x, y }) => {
      document.documentElement.style.setProperty('--px', x.toFixed(2));
      document.documentElement.style.setProperty('--py', y.toFixed(2));
      const posX = x;
      const posY = y;
      document.documentElement.style.setProperty('--x', `${posX}px`);
      document.documentElement.style.setProperty('--y', `${posY}px`);
    };
    document.body.addEventListener('pointermove', syncPointer);
  </script>
</body>
</html>



