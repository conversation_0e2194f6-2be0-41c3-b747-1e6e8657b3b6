/**
 * Standardized API middleware for Task Master endpoints
 * Provides consistent authentication, authorization, validation, and error handling
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth, requireRole } from '@/lib/auth/middleware';
import { isTaskMasterError } from './errors';
import { InputSanitizer } from './sanitization';

/**
 * Standard API response format
 */
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
  requestId?: string;
}

/**
 * API handler function type
 */
export type ApiHandler<T = any> = (
  request: NextRequest,
  context: ApiContext
) => Promise<NextResponse<ApiResponse<T>>>;

/**
 * API context passed to handlers
 */
export interface ApiContext {
  user: {
    id: string;
    email: string;
    username: string;
    role: string;
  };
  params?: Record<string, string | number>;
  query?: Record<string, unknown>;
  body?: Record<string, unknown>;
  requestId: string;
}

/**
 * Middleware options
 */
export interface MiddlewareOptions {
  requireAuth?: boolean;
  requireRole?: 'user' | 'moderator' | 'admin';
  validateInput?: boolean;
  sanitizeInput?: boolean;
  validateParams?: z.ZodSchema;
  validateQuery?: z.ZodSchema;
  validateBody?: z.ZodSchema;
  rateLimit?: {
    requests: number;
    windowMs: number;
  };
}

/**
 * Create standardized API response
 */
export function createApiResponse<T>(
  data?: T,
  options?: {
    message?: string;
    error?: string;
    status?: number;
    requestId?: string;
  }
): NextResponse<ApiResponse<T>> {
  const response: ApiResponse<T> = {
    timestamp: new Date().toISOString(),
    requestId: options?.requestId || generateRequestId(),
  };

  if (data !== undefined) {
    response.data = data;
  }

  if (options?.message) {
    response.message = options.message;
  }

  if (options?.error) {
    response.error = options.error;
  }

  return NextResponse.json(response, {
    status: options?.status || (options?.error ? 400 : 200),
    headers: {
      'X-Request-ID': response.requestId,
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
    },
  });
}

/**
 * Create error response from TaskMaster error
 */
export function createErrorResponse(
  error: unknown,
  requestId?: string
): NextResponse<ApiResponse> {
  if (isTaskMasterError(error)) {
    const statusCode = getStatusCodeForError(error);

    // For authorization errors, use the specific user-friendly message
    const errorMessage = error.type === 'authorization-error'
      ? error.message
      : error.message;

    return createApiResponse(undefined, {
      error: errorMessage,
      status: statusCode,
      requestId,
    });
  }

  // Log unexpected errors
  console.error('Unexpected API error:', error);

  return createApiResponse(undefined, {
    error: 'Internal server error',
    status: 500,
    requestId,
  });
}

/**
 * Map TaskMaster error types to HTTP status codes
 */
function getStatusCodeForError(error: { type?: string; [key: string]: unknown }): number {
  switch (error.type) {
    case 'validation-error':
      return 400;
    case 'authorization-error':
      return 403;
    case 'not-found-error':
      return 404;
    case 'business-logic-error':
      return 422;
    case 'file-operation-error':
      return 400;
    case 'database-error':
    default:
      return 500;
  }
}

/**
 * Generate unique request ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Sanitize request body based on content type
 */
function sanitizeRequestBody(body: unknown): unknown {
  if (!body || typeof body !== 'object') {
    return body;
  }

  const sanitized = { ...body };

  // Define field types for common Task Master fields
  const fieldTypes: Record<string, 'richText' | 'plainText' | 'markdown' | 'fileName' | 'email' | 'url'> = {
    title: 'plainText',
    name: 'plainText',
    description: 'richText',
    fullDescription: 'richText',
    full_description: 'richText', // snake_case variant
    eventLog: 'markdown',
    event_log: 'markdown', // snake_case variant
    content: 'richText',
    comment: 'richText',
    fileName: 'fileName',
    file_name: 'fileName',
    email: 'email',
    userEmail: 'email',
    user_email: 'email',
    url: 'url',
    href: 'url',
  };

  // Sanitize string fields
  for (const [key, value] of Object.entries(sanitized)) {
    if (typeof value === 'string') {
      const fieldType = fieldTypes[key] || 'plainText';
      sanitized[key] = InputSanitizer.sanitizeByType(value, fieldType);
    } else if (Array.isArray(value)) {
      // Sanitize array of strings (like tags)
      sanitized[key] = value.map(item => 
        typeof item === 'string' ? InputSanitizer.sanitizePlainText(item) : item
      );
    }
  }

  return sanitized;
}

/**
 * Validate request parameters
 */
function validateParams(params: Record<string, string>): Record<string, string | number> {
  const validated: Record<string, string | number> = {};

  for (const [key, value] of Object.entries(params)) {
    // Convert ID parameters to numbers
    if (key.endsWith('Id') || key === 'id') {
      const numValue = parseInt(value);
      if (isNaN(numValue) || numValue <= 0) {
        throw new Error(`Invalid ${key}: must be a positive number`);
      }
      validated[key] = numValue;
    } else {
      // Sanitize string parameters
      validated[key] = InputSanitizer.sanitizePlainText(value);
    }
  }

  return validated;
}

/**
 * Validate data using Zod schema with enhanced error handling
 */
function validateWithSchema<T>(schema: z.ZodSchema<T>, data: unknown, context: string): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => {
        const path = err.path.length > 0 ? ` at ${err.path.join('.')}` : '';
        return `${err.message}${path}`;
      }).join(', ');
      throw new Error(`${context} validation failed: ${errorMessages}`);
    }
    throw new Error(`${context} validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Parse and validate query parameters from URL
 */
function parseQueryParams(request: NextRequest): Record<string, any> {
  const { searchParams } = new URL(request.url);
  const query: Record<string, any> = {};

  for (const [key, value] of searchParams.entries()) {
    // Handle multiple values for the same key
    if (query[key]) {
      if (Array.isArray(query[key])) {
        query[key].push(value);
      } else {
        query[key] = [query[key], value];
      }
    } else {
      query[key] = value;
    }
  }

  return query;
}

/**
 * Main middleware wrapper for Task Master API endpoints
 */
export function withTaskMasterMiddleware(
  handler: ApiHandler,
  options: MiddlewareOptions = {}
) {
  return async (
    request: NextRequest,
    context?: { params?: Record<string, string> | Promise<Record<string, string>> }
  ): Promise<NextResponse> => {
    const requestId = generateRequestId();

    try {
      // Authentication
      if (options.requireAuth !== false) {
        const authResult = options.requireRole
          ? await requireRole(request, options.requireRole)
          : await requireAuth(request);

        if ('error' in authResult) {
          return createApiResponse(undefined, {
            error: authResult.error,
            status: authResult.status,
            requestId,
          });
        }

        // Validate and sanitize parameters
        let validatedParams: Record<string, string | number> = {};
        if (context?.params) {
          try {
            // Handle both Promise and direct params (Next.js 15 compatibility)
            const resolvedParams = context.params instanceof Promise
              ? await context.params
              : context.params;

            if (options.validateParams) {
              validatedParams = validateWithSchema(options.validateParams, resolvedParams, 'Parameters');
            } else {
              validatedParams = validateParams(resolvedParams);
            }
          } catch (error) {
            return createApiResponse(undefined, {
              error: error instanceof Error ? error.message : 'Invalid parameters',
              status: 400,
              requestId,
            });
          }
        }

        // Parse and validate query parameters
        let validatedQuery: Record<string, any> = {};
        if (options.validateQuery) {
          try {
            const queryParams = parseQueryParams(request);
            validatedQuery = validateWithSchema(options.validateQuery, queryParams, 'Query parameters');
          } catch (error) {
            return createApiResponse(undefined, {
              error: error instanceof Error ? error.message : 'Invalid query parameters',
              status: 400,
              requestId,
            });
          }
        }

        // Parse and validate request body
        let validatedBody: Record<string, unknown> | undefined = undefined;
        if (options.validateBody && request.method !== 'GET' && request.method !== 'DELETE') {
          try {
            const body = await request.json();
            validatedBody = validateWithSchema(options.validateBody, body, 'Request body');

            // Apply sanitization if requested
            if (options.sanitizeInput) {
              validatedBody = sanitizeRequestBody(validatedBody);
            }
          } catch (error) {
            if (error instanceof SyntaxError) {
              return createApiResponse(undefined, {
                error: 'Invalid JSON in request body',
                status: 400,
                requestId,
              });
            }
            return createApiResponse(undefined, {
              error: error instanceof Error ? error.message : 'Invalid request body',
              status: 400,
              requestId,
            });
          }
        }

        // Create API context with validated data
        const apiContext: ApiContext = {
          user: authResult.user,
          params: validatedParams,
          query: validatedQuery,
          body: validatedBody,
          requestId,
        };

        // Call the actual handler
        return await handler(request, apiContext);
      } else {
        // No authentication required - still validate params/query/body if schemas provided
        let validatedParams: Record<string, string | number> = {};
        let validatedQuery: Record<string, unknown> = {};
        let validatedBody: Record<string, unknown> | undefined = undefined;

        // Validate parameters
        if (context?.params) {
          try {
            // Handle both Promise and direct params (Next.js 15 compatibility)
            const resolvedParams = context.params instanceof Promise
              ? await context.params
              : context.params;

            if (options.validateParams) {
              validatedParams = validateWithSchema(options.validateParams, resolvedParams, 'Parameters');
            } else {
              validatedParams = validateParams(resolvedParams);
            }
          } catch (error) {
            return createApiResponse(undefined, {
              error: error instanceof Error ? error.message : 'Invalid parameters',
              status: 400,
              requestId,
            });
          }
        }

        // Validate query parameters
        if (options.validateQuery) {
          try {
            const queryParams = parseQueryParams(request);
            validatedQuery = validateWithSchema(options.validateQuery, queryParams, 'Query parameters');
          } catch (error) {
            return createApiResponse(undefined, {
              error: error instanceof Error ? error.message : 'Invalid query parameters',
              status: 400,
              requestId,
            });
          }
        }

        // Validate request body
        if (options.validateBody && request.method !== 'GET' && request.method !== 'DELETE') {
          try {
            const body = await request.json();
            validatedBody = validateWithSchema(options.validateBody, body, 'Request body');

            if (options.sanitizeInput) {
              validatedBody = sanitizeRequestBody(validatedBody);
            }
          } catch (error) {
            if (error instanceof SyntaxError) {
              return createApiResponse(undefined, {
                error: 'Invalid JSON in request body',
                status: 400,
                requestId,
              });
            }
            return createApiResponse(undefined, {
              error: error instanceof Error ? error.message : 'Invalid request body',
              status: 400,
              requestId,
            });
          }
        }

        // Create minimal context for non-authenticated requests
        const apiContext: ApiContext = {
          user: {
            id: '',
            email: '',
            username: '',
            role: 'anonymous',
          },
          params: validatedParams,
          query: validatedQuery,
          body: validatedBody,
          requestId,
        };

        return await handler(request, apiContext);
      }
    } catch (error) {
      return createErrorResponse(error, requestId);
    }
  };
}

/**
 * Utility to get validated body from API context
 * @deprecated Use context.body instead
 */
export function getSanitizedBody(request: NextRequest): unknown {
  console.warn('getSanitizedBody is deprecated. Use context.body from the middleware instead.');
  return (request as { _sanitizedBody?: unknown })._sanitizedBody;
}

/**
 * Rate limiting store (in-memory for simplicity)
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

/**
 * Simple rate limiting middleware
 */
export function rateLimit(options: { requests: number; windowMs: number }) {
  return (request: NextRequest): boolean => {
    const clientId = request.ip || 'unknown';
    const now = Date.now();
    const windowStart = now - options.windowMs;

    // Clean up old entries
    for (const [key, value] of rateLimitStore.entries()) {
      if (value.resetTime < now) {
        rateLimitStore.delete(key);
      }
    }

    const current = rateLimitStore.get(clientId);
    
    if (!current || current.resetTime < now) {
      // First request or window expired
      rateLimitStore.set(clientId, {
        count: 1,
        resetTime: now + options.windowMs,
      });
      return true;
    }

    if (current.count >= options.requests) {
      return false; // Rate limit exceeded
    }

    current.count++;
    return true;
  };
}
