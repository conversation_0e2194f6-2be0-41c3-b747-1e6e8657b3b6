/**
 * Service Types
 * 
 * Type definitions for service layer operations
 */

/**
 * Service response wrapper
 */
export interface ServiceResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: {
    timestamp: string;
    duration?: number;
    cached?: boolean;
  };
}

/**
 * Service error types
 */
export interface ServiceError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  stack?: string;
}

/**
 * Service configuration
 */
export interface ServiceConfig {
  timeout?: number;
  retries?: number;
  cache?: {
    enabled: boolean;
    ttl: number;
  };
  logging?: {
    enabled: boolean;
    level: 'debug' | 'info' | 'warn' | 'error';
  };
}

/**
 * Service context
 */
export interface ServiceContext {
  userId?: number;
  userRole?: string;
  requestId: string;
  timestamp: string;
  metadata?: Record<string, unknown>;
}

/**
 * Service method options
 */
export interface ServiceMethodOptions {
  context?: ServiceContext;
  timeout?: number;
  skipCache?: boolean;
  skipValidation?: boolean;
}

/**
 * Pagination options for services
 */
export interface ServicePaginationOptions {
  page: number;
  limit: number;
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  };
}

/**
 * Service filter options
 */
export interface ServiceFilterOptions {
  search?: string;
  filters?: Record<string, unknown>;
  dateRange?: {
    start: string;
    end: string;
  };
}

/**
 * Service operation result
 */
export interface ServiceOperationResult<T = unknown> {
  success: boolean;
  data?: T;
  affected?: number;
  errors?: ServiceError[];
  warnings?: string[];
}

/**
 * Bulk service operation
 */
export interface BulkServiceOperation<T = unknown> {
  items: T[];
  operation: 'create' | 'update' | 'delete';
  options?: {
    continueOnError?: boolean;
    batchSize?: number;
  };
}

/**
 * Service cache entry
 */
export interface ServiceCacheEntry<T = unknown> {
  key: string;
  data: T;
  timestamp: number;
  ttl: number;
  hits: number;
}

/**
 * Service metrics
 */
export interface ServiceMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  cacheHitRatio: number;
  errorRate: number;
  lastError?: ServiceError;
}

/**
 * Service health status
 */
export interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime: number;
  version: string;
  dependencies: Record<string, {
    status: 'up' | 'down';
    responseTime?: number;
    error?: string;
  }>;
  metrics: ServiceMetrics;
}

/**
 * Service event types
 */
export type ServiceEvent = 
  | { type: 'method_called'; method: string; timestamp: string; context?: ServiceContext }
  | { type: 'method_completed'; method: string; duration: number; timestamp: string }
  | { type: 'method_failed'; method: string; error: ServiceError; timestamp: string }
  | { type: 'cache_hit'; key: string; timestamp: string }
  | { type: 'cache_miss'; key: string; timestamp: string }
  | { type: 'validation_failed'; errors: unknown[]; timestamp: string };

/**
 * Service event listener
 */
export interface ServiceEventListener {
  (event: ServiceEvent): void;
}

/**
 * Service middleware
 */
export interface ServiceMiddleware<T = any, R = any> {
  name: string;
  before?: (args: T, context: ServiceContext) => Promise<T> | T;
  after?: (result: R, context: ServiceContext) => Promise<R> | R;
  error?: (error: Error, context: ServiceContext) => Promise<void> | void;
}

/**
 * Service registry entry
 */
export interface ServiceRegistryEntry {
  name: string;
  version: string;
  instance: unknown;
  config: ServiceConfig;
  health: ServiceHealth;
  dependencies: string[];
}

/**
 * Service dependency injection
 */
export interface ServiceContainer {
  register<T>(name: string, factory: () => T, config?: ServiceConfig): void;
  resolve<T>(name: string): T;
  has(name: string): boolean;
  remove(name: string): boolean;
  clear(): void;
}

/**
 * Service authorization context
 */
export interface ServiceAuthContext {
  userId: number;
  userRole: string;
  permissions: string[];
  scopes: string[];
  sessionId?: string;
}

/**
 * Service rate limiting
 */
export interface ServiceRateLimit {
  key: string;
  limit: number;
  window: number; // in seconds
  current: number;
  resetTime: number;
}

/**
 * Service audit log
 */
export interface ServiceAuditLog {
  id: string;
  service: string;
  method: string;
  userId?: number;
  action: string;
  resource?: string;
  resourceId?: number;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  timestamp: string;
  ip?: string;
  userAgent?: string;
}
