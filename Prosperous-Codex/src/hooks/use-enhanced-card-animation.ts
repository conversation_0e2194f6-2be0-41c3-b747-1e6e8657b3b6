"use client";

import { useState, useCallback, useRef, useEffect } from 'react';
import { TaskMaster } from '@/lib/types/task-master';

interface EnhancedAnimationData {
  cardId: string;
  cardData: TaskMaster;
  sourcePosition: DOMRect;
  destinationPosition: DOMRect;
  fromColumnId: string;
  toColumnId: string;
}

interface UseEnhancedCardAnimationOptions {
  duration?: number;
  easing?: string;
}

export function useEnhancedCardAnimation(options: UseEnhancedCardAnimationOptions = {}) {
  const { duration = 400, easing = 'cubic-bezier(0.4, 0.0, 0.2, 1)' } = options;

  const [animatingCards, setAnimatingCards] = useState<Set<string>>(new Set());
  const [currentAnimation, setCurrentAnimation] = useState<EnhancedAnimationData | null>(null);
  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const animatingCardsRef = useRef<Set<string>>(new Set());

  /**
   * Enhanced animation that takes actual source and destination DOMRect positions
   * This allows animation to the exact final position after system positioning logic
   */
  const startAnimation = useCallback((
    cardId: string,
    cardData: TaskMaster,
    sourcePosition: DOMRect,
    destinationPosition: DOMRect,
    fromColumnId: string,
    toColumnId: string,
    onComplete: () => void
  ) => {
    // Check if card is already animating using ref
    if (animatingCardsRef.current.has(cardId)) {
      onComplete();
      return;
    }

    try {
      const animationData: EnhancedAnimationData = {
        cardId,
        cardData,
        sourcePosition,
        destinationPosition,
        fromColumnId,
        toColumnId
      };

      // Update ref and state
      animatingCardsRef.current.add(cardId);
      setAnimatingCards(new Set(animatingCardsRef.current));
      setCurrentAnimation(animationData);

      // Clear any existing timeout
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }

      // Set timeout to complete animation
      animationTimeoutRef.current = setTimeout(() => {
        animatingCardsRef.current.delete(cardId);
        setAnimatingCards(new Set(animatingCardsRef.current));
        setCurrentAnimation(null);
        onComplete();
      }, duration);

    } catch (error) {
      console.warn('Enhanced animation failed, falling back to immediate completion:', error);
      onComplete();
    }
  }, [duration]);

  /**
   * Legacy animation method for backward compatibility
   * Converts old API to new enhanced API by calculating destination position
   */
  const startLegacyAnimation = useCallback(async (
    cardId: string,
    cardData: TaskMaster,
    sourceElement: HTMLElement,
    destinationColumnElement: HTMLElement,
    fromColumnId: string,
    toColumnId: string,
    onComplete: () => void
  ) => {
    try {
      const sourceRect = sourceElement.getBoundingClientRect();
      
      // Calculate destination position at bottom of column (legacy behavior)
      const cardsContainer = destinationColumnElement.querySelector('[data-cards-container]') as HTMLElement;
      let destinationRect: DOMRect;
      
      if (cardsContainer) {
        const cardsRect = cardsContainer.getBoundingClientRect();
        const existingCards = cardsContainer.querySelectorAll('[data-task-card]');
        
        let targetY = cardsRect.top;
        if (existingCards.length > 0) {
          const lastCard = existingCards[existingCards.length - 1];
          const lastCardRect = lastCard.getBoundingClientRect();
          targetY = lastCardRect.bottom + 16; // 16px gap
        }
        
        // Create a DOMRect-like object for destination
        destinationRect = {
          left: cardsRect.left,
          top: targetY,
          right: cardsRect.right,
          bottom: targetY + sourceRect.height,
          width: cardsRect.width,
          height: sourceRect.height,
          x: cardsRect.left,
          y: targetY
        } as DOMRect;
      } else {
        // Fallback to column position
        const columnRect = destinationColumnElement.getBoundingClientRect();
        destinationRect = {
          left: columnRect.left + 16,
          top: columnRect.top + 80,
          right: columnRect.right - 16,
          bottom: columnRect.top + 80 + sourceRect.height,
          width: columnRect.width - 32,
          height: sourceRect.height,
          x: columnRect.left + 16,
          y: columnRect.top + 80
        } as DOMRect;
      }

      // Use enhanced animation with calculated destination
      startAnimation(
        cardId,
        cardData,
        sourceRect,
        destinationRect,
        fromColumnId,
        toColumnId,
        onComplete
      );

    } catch (error) {
      console.warn('Legacy animation failed, falling back to immediate completion:', error);
      onComplete();
    }
  }, [startAnimation]);

  // Check if a card is currently animating
  const isCardAnimating = useCallback((cardId: string) => {
    return animatingCardsRef.current.has(cardId);
  }, []);

  // Cancel all animations (useful for cleanup)
  const cancelAnimations = useCallback(() => {
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }
    animatingCardsRef.current.clear();
    setAnimatingCards(new Set());
    setCurrentAnimation(null);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, []);

  return {
    startAnimation,
    startLegacyAnimation,
    isCardAnimating,
    cancelAnimations,
    currentAnimation,
    animationConfig: {
      duration,
      easing
    }
  };
}
