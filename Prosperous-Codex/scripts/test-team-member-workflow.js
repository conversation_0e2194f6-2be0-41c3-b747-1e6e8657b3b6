#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Field Naming Standardization
 *
 * This script runs the complete validation and testing infrastructure to verify:
 * - Field mapping bug fixes are working correctly
 * - Naming conventions are consistent across all layers
 * - Documentation is up-to-date and accurate
 * - All components use correct field names
 * - API endpoints follow camelCase standards
 * - Database operations use snake_case properly
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🧪 Running Comprehensive Field Naming Validation Suite...\n');

const validationCommands = [
  {
    name: 'Field Naming Validation',
    command: 'node scripts/validate-field-naming.js',
    description: 'Validates field naming consistency across all layers',
    critical: true
  },
  {
    name: 'Documentation Audit',
    command: 'node scripts/audit-documentation.js',
    description: 'Audits documentation for outdated field references',
    critical: false
  }
];

const testCommands = [
  {
    name: 'Unit Tests - Field Mapping',
    command: 'npm test tests/unit/field-mapping.test.ts',
    description: 'Tests the core field mapping functionality',
    critical: true
  },
  {
    name: 'Integration Tests - Team Member API',
    command: 'npm test tests/integration/team-member-api.test.ts',
    description: 'Tests the API endpoints for team member operations',
    critical: true
  },
  {
    name: 'E2E Tests - Team Member Workflow',
    command: 'npx playwright test tests/e2e/team-member-workflow.test.ts',
    description: 'Tests the complete user workflow for team member management',
    critical: true
  }
];

let allValidationsPassed = true;
let allTestsPassed = true;
let criticalFailures = 0;

// Run validation commands first
console.log('🔍 Phase 1: Field Naming Validation\n');

for (const validation of validationCommands) {
  console.log(`📋 ${validation.name}`);
  console.log(`   ${validation.description}`);
  console.log(`   Command: ${validation.command}\n`);

  try {
    execSync(validation.command, {
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '..')
    });
    console.log(`✅ ${validation.name} - PASSED\n`);
  } catch (error) {
    console.log(`❌ ${validation.name} - FAILED\n`);
    allValidationsPassed = false;
    if (validation.critical) {
      criticalFailures++;
    }
  }
}

// Run test commands
console.log('🧪 Phase 2: Test Execution\n');

for (const test of testCommands) {
  console.log(`📋 ${test.name}`);
  console.log(`   ${test.description}`);
  console.log(`   Command: ${test.command}\n`);

  try {
    execSync(test.command, {
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '..')
    });
    console.log(`✅ ${test.name} - PASSED\n`);
  } catch (error) {
    console.log(`❌ ${test.name} - FAILED\n`);
    allTestsPassed = false;
    if (test.critical) {
      criticalFailures++;
    }
  }
}

// Generate comprehensive summary report
console.log('📊 Comprehensive Validation Summary');
console.log('====================================');

console.log(`🔍 Validation Results:`);
console.log(`   Field Naming Validation: ${allValidationsPassed ? '✅ PASSED' : '❌ FAILED'}`);

console.log(`🧪 Test Results:`);
console.log(`   Unit Tests: ${allTestsPassed ? '✅ PASSED' : '❌ FAILED'}`);

console.log(`🚨 Critical Failures: ${criticalFailures}`);

if (allValidationsPassed && allTestsPassed) {
  console.log('\n🎉 All validations and tests passed! Field naming standardization is complete.');
  console.log('\n✅ Achievements:');
  console.log('   • Consistent field naming across all layers');
  console.log('   • Database operations use snake_case properly');
  console.log('   • API endpoints use camelCase consistently');
  console.log('   • Frontend components use camelCase field names');
  console.log('   • TeamMember interface consolidation completed');
  console.log('   • Field mapping system working correctly');
  console.log('   • "member.name is undefined" bug fixed');
  console.log('   • Documentation updated and accurate');

  // Generate success report
  generateSuccessReport();
  process.exit(0);
} else {
  console.log('\n❌ Some validations or tests failed. Please review the output above.');
  console.log('\n🔍 Troubleshooting Guide:');

  if (!allValidationsPassed) {
    console.log('\n📋 Field Naming Issues:');
    console.log('   • Check field-naming-violations.json for detailed violations');
    console.log('   • Update components to use correct field names');
    console.log('   • Ensure API endpoints use camelCase consistently');
    console.log('   • Verify database operations use snake_case');
  }

  if (!allTestsPassed) {
    console.log('\n🧪 Test Failures:');
    console.log('   • Database connection and test data setup');
    console.log('   • Field mapping configuration');
    console.log('   • API endpoint implementations');
    console.log('   • Component prop passing and state management');
  }

  if (criticalFailures > 0) {
    console.log(`\n🚨 Critical failures detected (${criticalFailures}). These must be fixed before deployment.`);
  }

  // Generate failure report
  generateFailureReport();
  process.exit(1);
}

/**
 * Generate success report
 */
function generateSuccessReport() {
  const report = {
    timestamp: new Date().toISOString(),
    status: 'SUCCESS',
    validationsPassed: allValidationsPassed,
    testsPassed: allTestsPassed,
    criticalFailures: criticalFailures,
    achievements: [
      'Field naming standardization completed',
      'Database layer uses snake_case consistently',
      'API layer uses camelCase consistently',
      'Frontend layer uses camelCase consistently',
      'TeamMember interface consolidated',
      'Field mapping system operational',
      'Team member display bug fixed',
      'Documentation updated'
    ]
  };

  fs.writeFileSync('validation-success-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 Success report saved to: validation-success-report.json');
}

/**
 * Generate failure report
 */
function generateFailureReport() {
  const report = {
    timestamp: new Date().toISOString(),
    status: 'FAILURE',
    validationsPassed: allValidationsPassed,
    testsPassed: allTestsPassed,
    criticalFailures: criticalFailures,
    nextSteps: [
      'Review field-naming-violations.json for specific issues',
      'Run individual test suites to isolate failures',
      'Check documentation-audit-report.json for doc issues',
      'Update field names in failing components',
      'Re-run validation after fixes'
    ]
  };

  fs.writeFileSync('validation-failure-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 Failure report saved to: validation-failure-report.json');
}
