import { getDatabase } from './database';
import bcrypt from 'bcryptjs';
import { User } from '@/lib/types/auth';

export interface DatabaseUser {
  id: number;
  email: string;
  username?: string;
  password_hash: string;
  role: 'user' | 'moderator' | 'admin';
  language?: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
  is_active: boolean;
}

export interface CreateUserData {
  email: string;
  username?: string;
  password: string;
  role?: 'user' | 'moderator' | 'admin';
}

export interface UpdateUserData {
  username?: string;
  role?: 'user' | 'moderator' | 'admin';
  language?: string;
  is_active?: boolean;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export class UserService {
  private db = getDatabase();

  // Prepared statements for better performance
  private getUserByIdStmt = this.db.prepare(`
    SELECT id, email, username, role, created_at, last_login, is_active
    FROM users
    WHERE id = ? AND is_active = 1
  `);

  private getUserByEmailStmt = this.db.prepare(`
    SELECT id, email, username, role, created_at, last_login, is_active
    FROM users
    WHERE email = ? AND is_active = 1
  `);

  private authenticateUserStmt = this.db.prepare(`
    SELECT id, email, username, password_hash, role, created_at, last_login, is_active
    FROM users
    WHERE email = ? AND is_active = 1
  `);

  private updateLastLoginStmt = this.db.prepare(`
    UPDATE users
    SET last_login = CURRENT_TIMESTAMP
    WHERE id = ?
  `);

  /**
   * Refresh database connection and prepared statements (for database reset)
   */
  refreshConnection(): void {
    this.db = getDatabase();

    // Recreate prepared statements with new database connection
    this.getUserByIdStmt = this.db.prepare(`
      SELECT id, email, username, role, created_at, last_login, is_active
      FROM users
      WHERE id = ? AND is_active = 1
    `);

    this.getUserByEmailStmt = this.db.prepare(`
      SELECT id, email, username, role, created_at, last_login, is_active
      FROM users
      WHERE email = ? AND is_active = 1
    `);

    this.authenticateUserStmt = this.db.prepare(`
      SELECT id, email, username, password_hash, role, created_at, last_login, is_active
      FROM users
      WHERE email = ? AND is_active = 1
    `);

    this.updateLastLoginStmt = this.db.prepare(`
      UPDATE users
      SET last_login = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    console.log('🔄 UserService database connection refreshed');
  }

  /**
   * Create a new user
   */
  async createUser(userData: CreateUserData): Promise<User> {
    const { email, username, password, role = 'user' } = userData;
    
    // Hash password
    const password_hash = await bcrypt.hash(password, 12);
    
    try {
      const stmt = this.db.prepare(`
        INSERT INTO users (email, username, password_hash, role)
        VALUES (?, ?, ?, ?)
      `);
      
      const result = stmt.run(email, username || null, password_hash, role);
      
      // Get the created user
      const user = this.getUserById(result.lastInsertRowid as number);
      if (!user) {
        throw new Error('Failed to create user');
      }
      
      return user;
    } catch (error) {
      if (error instanceof Error && error.message.includes('UNIQUE constraint failed')) {
        throw new Error('User with this email already exists');
      }
      throw error;
    }
  }

  /**
   * Get user by ID
   */
  getUserById(id: number): User | null {
    try {
      const user = this.getUserByIdStmt.get(id) as DatabaseUser | undefined;

      if (!user) {
        return null;
      }

      return {
        id: user.id.toString(),
        email: user.email,
        username: user.username,
        createdAt: user.created_at,
        role: user.role
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Get user by email
   */
  getUserByEmail(email: string): User | null {
    try {
      const user = this.getUserByEmailStmt.get(email) as DatabaseUser | undefined;

      if (!user) {
        return null;
      }

      return {
        id: user.id.toString(),
        email: user.email,
        username: user.username,
        createdAt: user.created_at,
        role: user.role
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Authenticate user with email and password
   */
  async authenticateUser(email: string, password: string): Promise<User | null> {
    try {
      const user = this.authenticateUserStmt.get(email) as DatabaseUser | undefined;

      if (!user) {
        return null;
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password_hash);

      if (!isValidPassword) {
        return null;
      }

      // Update last login
      this.updateLastLogin(user.id);

      return {
        id: user.id.toString(),
        email: user.email,
        username: user.username,
        createdAt: user.created_at,
        role: user.role
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Update user information
   */
  updateUser(id: number, updateData: UpdateUserData): boolean {
    try {
      const fields: string[] = [];
      const values: (string | number | boolean | null)[] = [];
      
      if (updateData.username !== undefined) {
        fields.push('username = ?');
        values.push(updateData.username);
      }
      
      if (updateData.role !== undefined) {
        fields.push('role = ?');
        values.push(updateData.role);
      }
      
      if (updateData.language !== undefined) {
        fields.push('language = ?');
        values.push(updateData.language);
      }

      if (updateData.is_active !== undefined) {
        fields.push('is_active = ?');
        values.push(updateData.is_active ? 1 : 0);
      }
      
      if (fields.length === 0) {
        return true; // No updates needed
      }
      
      fields.push('updated_at = CURRENT_TIMESTAMP');
      values.push(id);
      
      const stmt = this.db.prepare(`
        UPDATE users 
        SET ${fields.join(', ')}
        WHERE id = ?
      `);
      
      const result = stmt.run(...values);
      return result.changes > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Change user password
   */
  async changePassword(id: number, passwordData: ChangePasswordData): Promise<boolean> {
    try {
      // First verify current password
      const stmt = this.db.prepare(`
        SELECT password_hash FROM users WHERE id = ? AND is_active = 1
      `);
      
      const user = stmt.get(id) as { password_hash: string } | undefined;
      
      if (!user) {
        return false;
      }
      
      const isCurrentPasswordValid = await bcrypt.compare(passwordData.currentPassword, user.password_hash);
      
      if (!isCurrentPasswordValid) {
        return false;
      }
      
      // Hash new password
      const newPasswordHash = await bcrypt.hash(passwordData.newPassword, 12);
      
      // Update password
      const updateStmt = this.db.prepare(`
        UPDATE users 
        SET password_hash = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      
      const result = updateStmt.run(newPasswordHash, id);
      return result.changes > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Update last login timestamp
   */
  private updateLastLogin(id: number): void {
    try {
      this.updateLastLoginStmt.run(id);
    } catch (error) {
      // Silent fail for non-critical operation
    }
  }

  /**
   * Get all users (admin function)
   */
  getAllUsers(): User[] {
    try {
      const stmt = this.db.prepare(`
        SELECT id, email, username, role, created_at, last_login, is_active
        FROM users 
        ORDER BY created_at DESC
      `);
      
      const users = stmt.all() as DatabaseUser[];
      
      return users.map(user => ({
        id: user.id.toString(),
        email: user.email,
        username: user.username,
        createdAt: user.created_at,
        role: user.role,
        isActive: user.is_active
      }));
    } catch (error) {
      return [];
    }
  }

  /**
   * Update user language preference
   */
  async updateUserLanguage(email: string, language: string): Promise<boolean> {
    try {
      const stmt = this.db.prepare(`
        UPDATE users
        SET language = ?, updated_at = CURRENT_TIMESTAMP
        WHERE email = ? AND is_active = 1
      `);

      const result = stmt.run(language, email);
      return result.changes > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Delete user (soft delete)
   */
  deleteUser(id: number): boolean {
    try {
      const stmt = this.db.prepare(`
        UPDATE users
        SET is_active = 0, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);

      const result = stmt.run(id);
      return result.changes > 0;
    } catch (error) {
      return false;
    }
  }
}
