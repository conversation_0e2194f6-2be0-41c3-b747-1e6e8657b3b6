"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useUnit } from "@/lib/context/unit-context";
import {
  InnerTextProductionParameterData,
  TabSpecificCardProps
} from "@/lib/types/ui-components";

// Custom toggle switch component for side lip multiplier
function SideLipMultiplierToggle({ 
  value, 
  onChange, 
  className = "" 
}: { 
  value: number; 
  onChange: (value: number) => void; 
  className?: string; 
}) {
  const options = [
    { value: 1, label: "1x" },
    { value: 2, label: "2x" },
    { value: 3, label: "3x" }
  ];

  return (
    <div className={`flex rounded-md border border-gray-300 dark:border-neutral-600 ${className}`}>
      {options.map((option) => (
        <button
          key={option.value}
          type="button"
          onClick={() => onChange(option.value)}
          className={`px-3 py-1 text-sm font-medium transition-colors duration-200 first:rounded-l-md last:rounded-r-md ${
            value === option.value
              ? 'bg-blue-500 text-white'
              : 'bg-white dark:bg-neutral-800 text-gray-700 dark:text-neutral-300 hover:bg-gray-50 dark:hover:bg-neutral-700'
          }`}
        >
          {option.label}
        </button>
      ))}
    </div>
  );
}

// Custom toggle switch component for grain alignment
function GrainAlignmentToggle({ 
  value, 
  onChange, 
  className = "" 
}: { 
  value: boolean; 
  onChange: (value: boolean) => void; 
  className?: string; 
}) {
  return (
    <div className={`flex items-center ${className}`}>
      <style jsx>{`
        .alignment-switch {
          position: relative;
          cursor: pointer;
          width: 54px;
          height: 26px;
          border-radius: 13px;
          background: linear-gradient(135deg, #10b981, #34d399);
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1), inset 0 1px 2px rgba(255, 255, 255, 0.3);
          transition: all 0.15s ease;
        }
        .alignment-switch-toggle {
          position: absolute;
          top: 3px;
          left: 3px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #ffffff;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          transition: all 0.15s ease-in-out;
          z-index: 2;
        }
        .alignment-switch-toggle:after {
          content: "";
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background: rgba(16, 185, 129, 0.2);
          transition: all 0.15s ease;
        }
        .alignment-switch.misaligned {
          background: linear-gradient(135deg, #ef4444, #f87171);
          box-shadow: 0 2px 5px rgba(239, 68, 68, 0.2), inset 0 1px 2px rgba(255, 255, 255, 0.3);
        }
        .alignment-switch.misaligned .alignment-switch-toggle {
          transform: translateX(28px);
        }
        .alignment-switch.misaligned .alignment-switch-toggle:after {
          background: rgba(239, 68, 68, 0.2);
        }
        .alignment-switch:not(.misaligned):active .alignment-switch-toggle {
          transform: scale(0.95);
        }
        .alignment-switch.misaligned:active .alignment-switch-toggle {
          transform: translateX(28px) scale(0.95);
        }
        .alignment-mode-text {
          font-size: 0.875rem;
          transition: all 0.15s ease;
          margin-left: 0.5rem;
          font-weight: 600;
        }
        .alignment-mode-text.aligned-text {
          color: #10b981;
        }
        .alignment-mode-text.misaligned-text {
          color: #ef4444;
        }
        .dark .alignment-mode-text.aligned-text {
          color: #34d399;
        }
        .dark .alignment-mode-text.misaligned-text {
          color: #f87171;
        }
      `}</style>
      <div 
        className={`alignment-switch ${!value ? 'misaligned' : ''}`}
        onClick={() => onChange(!value)}
        title="Switch Grain Alignment"
      >
        <div className="alignment-switch-toggle"></div>
      </div>
      <span className="ml-2 text-sm">
        <span 
          className={`alignment-mode-text misaligned-text ${
            !value ? 'inline-block' : 'hidden'
          }`}
        >
          Misaligned
        </span>
        <span 
          className={`alignment-mode-text aligned-text ${
            value ? 'inline-block' : 'hidden'
          }`}
        >
          Aligned
        </span>
      </span>
    </div>
  );
}

export default function InnerTextProductionParametersCard({
  className = "",
  onDataChange,
  initialData
}: TabSpecificCardProps<InnerTextProductionParameterData>) {
  const { unit, convertToMm, convertFromMm, formatValue } = useUnit();

  // Store values in mm internally (for backend compatibility)
  const [bleedMm, setBleedMm] = useState(initialData?.bleed?.toString() || "3");
  const [gripperMm, setGripperMm] = useState(initialData?.gripper?.toString() || "12");
  const [colorBarMm, setColorBarMm] = useState(initialData?.colorBar?.toString() || "6");
  const [sideLipMm, setSideLipMm] = useState((initialData?.lip ? initialData.lip / (initialData.sideLipMultiplier || 1) : 5).toString());
  const [sideLipMultiplier, setSideLipMultiplier] = useState(initialData?.sideLipMultiplier || 1);
  const [grainAlignment, setGrainAlignment] = useState<boolean>(initialData?.alignmentMode === 'Aligned' || true);

  // Notify parent component when data changes (always send mm values)
  useEffect(() => {
    if (onDataChange) {
      const data: InnerTextProductionParameterData = {
        bleed: parseFloat(bleedMm) || 0,
        gripper: parseFloat(gripperMm) || 0,
        colorBar: parseFloat(colorBarMm) || 0,
        lip: (parseFloat(sideLipMm) || 0) * sideLipMultiplier,
        alignmentMode: grainAlignment ? 'Aligned' : 'Misaligned',
        sideLipMultiplier: sideLipMultiplier,
        isDoubleLipActive: sideLipMultiplier === 2 // Backward compatibility
      };
      onDataChange(data);
    }
  }, [bleedMm, gripperMm, colorBarMm, sideLipMm, sideLipMultiplier, grainAlignment, onDataChange]);

  // Get display values based on current unit
  const getDisplayValue = (mmValue: string) => {
    const numValue = parseFloat(mmValue);
    if (isNaN(numValue)) return "";
    if (unit === 'mm') {
      return mmValue;
    } else {
      return formatValue(convertFromMm(numValue, 'in'), 'in');
    }
  };

  // Handle input changes (convert to mm if needed)
  const handleMeasurementChange = (value: string, setMmValue: (v: string) => void) => {
    if (unit === 'mm') {
      setMmValue(value);
    } else {
      // Convert inches to mm
      const numValue = parseFloat(value);
      if (isNaN(numValue)) {
        setMmValue("");
      } else {
        const mmValue = convertToMm(numValue, 'in');
        setMmValue(formatValue(mmValue, 'mm'));
      }
    }
  };

  return (
    <Card className={className} data-glow>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <span className="w-2 h-2 bg-[#5E6AD2] dark:bg-[#6E56CF] rounded-full mr-2"></span>
          Production Parameters
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Bleed and Gripper Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="bleed" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                Bleed
              </Label>
              <span className="text-xs text-gray-500 dark:text-neutral-400">{unit}</span>
            </div>
            <Input
              id="bleed"
              type="number"
              step={unit === 'mm' ? "0.1" : "0.01"}
              placeholder={unit === 'mm' ? "3" : "0.12"}
              value={getDisplayValue(bleedMm)}
              onChange={(e) => handleMeasurementChange(e.target.value, setBleedMm)}
              className="h-9 text-sm"
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="gripper" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                Gripper
              </Label>
              <span className="text-xs text-gray-500 dark:text-neutral-400">{unit}</span>
            </div>
            <Input
              id="gripper"
              type="number"
              step={unit === 'mm' ? "0.1" : "0.01"}
              placeholder={unit === 'mm' ? "12" : "0.47"}
              value={getDisplayValue(gripperMm)}
              onChange={(e) => handleMeasurementChange(e.target.value, setGripperMm)}
              className="h-9 text-sm"
            />
          </div>
        </div>

        {/* Color Bar and Side Lip Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="color-bar" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                Color Bar
              </Label>
              <span className="text-xs text-gray-500 dark:text-neutral-400">{unit}</span>
            </div>
            <Input
              id="color-bar"
              type="number"
              step={unit === 'mm' ? "0.1" : "0.01"}
              placeholder={unit === 'mm' ? "6" : "0.24"}
              value={getDisplayValue(colorBarMm)}
              onChange={(e) => handleMeasurementChange(e.target.value, setColorBarMm)}
              className="h-9 text-sm"
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="side-lip" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                Side Lip
              </Label>
              <span className="text-xs text-gray-500 dark:text-neutral-400">{unit}</span>
            </div>
            <Input
              id="side-lip"
              type="number"
              step={unit === 'mm' ? "0.1" : "0.01"}
              placeholder={unit === 'mm' ? "5" : "0.20"}
              value={getDisplayValue(sideLipMm)}
              onChange={(e) => handleMeasurementChange(e.target.value, setSideLipMm)}
              className="h-9 text-sm bg-white dark:bg-neutral-700 border-gray-300 dark:border-neutral-600"
              data-glow
            />
          </div>
        </div>

        {/* Side Lip Multiplier */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label className="text-sm font-medium text-gray-700 dark:text-neutral-300">
              Side Lip Multiplier
            </Label>
            <span className="text-xs text-gray-500 dark:text-neutral-400">
              Total: {unit === 'mm'
                ? `${((parseFloat(sideLipMm) || 0) * sideLipMultiplier).toFixed(1)}mm`
                : `${formatValue(convertFromMm((parseFloat(sideLipMm) || 0) * sideLipMultiplier, 'in'), 'in')}in`
              }
            </span>
          </div>
          <SideLipMultiplierToggle
            value={sideLipMultiplier}
            onChange={setSideLipMultiplier}
          />
          <p className="text-xs text-gray-500 mt-1">
            {sideLipMultiplier === 1 && "Single side lip"}
            {sideLipMultiplier === 2 && "Double side lip (both sides)"}
            {sideLipMultiplier === 3 && "Triple side lip"}
          </p>
        </div>

        {/* Grain Alignment */}
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <Label className="text-sm font-medium text-gray-700 dark:text-neutral-300">
              Grain Alignment
            </Label>
            <button
              className="h-4 w-4 text-gray-500 dark:text-neutral-400 hover:text-gray-700 dark:hover:text-neutral-300 transition-colors"
              title={grainAlignment 
                ? "Grain direction matches paper orientation" 
                : "Grain direction doesn't match paper orientation"}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
          </div>
          <GrainAlignmentToggle
            value={grainAlignment}
            onChange={setGrainAlignment}
          />
        </div>

        {/* Note */}
        <div className="mt-4 p-3 bg-blue-50 dark:bg-purple-900/20 rounded-md">
          <p className="text-xs text-blue-700 dark:text-purple-300">
            * Gutter not used for sheet area. Margins applied to 720H x 1020W press.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
