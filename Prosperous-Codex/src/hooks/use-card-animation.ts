"use client";

import { useState, useCallback, useRef, useEffect } from 'react';
import { TaskMaster } from '@/lib/types/task-master';

interface AnimationData {
  cardId: string;
  cardData: TaskMaster;
  sourcePosition: DOMRect;
  destinationPosition: { x: number; y: number };
  fromColumnId: string;
  toColumnId: string;
}

interface UseCardAnimationOptions {
  duration?: number;
  easing?: string;
}

export function useCardAnimation(options: UseCardAnimationOptions = {}) {
  const { duration = 400, easing = 'cubic-bezier(0.4, 0.0, 0.2, 1)' } = options;
  
  const [animatingCards, setAnimatingCards] = useState<Set<string>>(new Set());
  const [currentAnimation, setCurrentAnimation] = useState<AnimationData | null>(null);
  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate where a card should appear in the destination column
  const calculateDestinationPosition = useCallback((
    toColumnElement: HTMLElement,
    cardHeight: number,
    insertIndex: number = 0
  ): { x: number; y: number } => {
    const columnRect = toColumnElement.getBoundingClientRect();
    
    // Find the task cards container within the column
    const cardsContainer = toColumnElement.querySelector('[data-cards-container]') as HTMLElement;
    if (!cardsContainer) {
      // Fallback to column position with some padding
      return {
        x: columnRect.left + 16, // Account for padding
        y: columnRect.top + 80 // Account for header
      };
    }

    const cardsRect = cardsContainer.getBoundingClientRect();
    const existingCards = cardsContainer.querySelectorAll('[data-task-card]');
    
    let targetY = cardsRect.top;
    
    // Calculate Y position based on insert index
    if (insertIndex > 0 && existingCards.length > 0) {
      const cardToInsertAfter = existingCards[Math.min(insertIndex - 1, existingCards.length - 1)];
      if (cardToInsertAfter) {
        const cardRect = cardToInsertAfter.getBoundingClientRect();
        targetY = cardRect.bottom + 16; // 16px gap between cards
      }
    }

    return {
      x: cardsRect.left,
      y: targetY
    };
  }, []);

  // Start animation for a card move
  const startAnimation = useCallback(async (
    cardId: string,
    cardData: TaskMaster,
    sourceElement: HTMLElement,
    destinationColumnElement: HTMLElement,
    fromColumnId: string,
    toColumnId: string,
    onComplete: () => void
  ) => {
    // Prevent multiple animations of the same card
    if (animatingCards.has(cardId)) {
      onComplete(); // Still call completion callback
      return;
    }

    try {
      // Get source position
      const sourceRect = sourceElement.getBoundingClientRect();

      // Calculate destination position
      // For now, we'll insert at the end of the destination column
      const destinationPosition = calculateDestinationPosition(
        destinationColumnElement,
        sourceRect.height
      );

      const animationData: AnimationData = {
        cardId,
        cardData,
        sourcePosition: sourceRect,
        destinationPosition,
        fromColumnId,
        toColumnId
      };

      // Update state
      setAnimatingCards(prev => new Set([...prev, cardId]));
      setCurrentAnimation(animationData);

      // Clear any existing timeout
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }

      // Set timeout to complete animation
      animationTimeoutRef.current = setTimeout(() => {
        setAnimatingCards(prev => {
          const newSet = new Set(prev);
          newSet.delete(cardId);
          return newSet;
        });
        setCurrentAnimation(null);
        onComplete();
      }, duration);

    } catch (error) {
      console.warn('Animation failed, falling back to immediate update:', error);
      // Fallback to immediate completion if animation setup fails
      onComplete();
    }

  }, [animatingCards, calculateDestinationPosition, duration]);

  // Check if a card is currently animating
  const isCardAnimating = useCallback((cardId: string) => {
    return animatingCards.has(cardId);
  }, [animatingCards]);

  // Cancel all animations (useful for cleanup)
  const cancelAnimations = useCallback(() => {
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }
    setAnimatingCards(new Set());
    setCurrentAnimation(null);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, []);

  return {
    startAnimation,
    isCardAnimating,
    cancelAnimations,
    currentAnimation,
    animationConfig: {
      duration,
      easing
    }
  };
}
