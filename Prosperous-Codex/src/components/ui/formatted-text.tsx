/**
 * FormattedText component for displaying text with preserved line breaks,
 * bullet points, and basic formatting while maintaining security.
 */

import React from 'react';
import { formatTextForDisplay, hasFormatting, truncateFormattedText } from '@/lib/utils/text-formatting';
import { cn } from '@/lib/utils';

interface FormattedTextProps {
  /** The text content to display */
  text: string;
  /** Additional CSS classes */
  className?: string;
  /** Whether to truncate long text */
  truncate?: boolean;
  /** Maximum length for truncation */
  maxLength?: number;
  /** Whether to show as inline or block element */
  inline?: boolean;
  /** Custom styling for different contexts */
  variant?: 'default' | 'muted' | 'card' | 'description';
}

/**
 * Component for displaying formatted text with preserved line breaks and basic formatting
 */
export function FormattedText({
  text,
  className,
  truncate = false,
  maxLength = 100,
  inline = false,
  variant = 'default'
}: FormattedTextProps) {
  // Return empty if no text
  if (!text || typeof text !== 'string') {
    return null;
  }

  // Prepare the text for display
  let displayText = text;
  
  if (truncate) {
    displayText = truncateFormattedText(text, maxLength);
  }

  // Check if the text has formatting that needs HTML rendering
  const needsFormatting = hasFormatting(displayText);

  // Get variant-specific styles
  const variantStyles = {
    default: 'text-foreground',
    muted: 'text-muted-foreground',
    card: 'text-xs text-muted-foreground',
    description: 'text-xs text-gray-600 dark:text-gray-400'
  };

  // Base styles for formatted text
  const baseStyles = cn(
    'leading-relaxed',
    variantStyles[variant],
    className
  );

  // If no special formatting is needed, render as plain text with preserved whitespace
  if (!needsFormatting) {
    const Element = inline ? 'span' : 'div';
    return (
      <Element className={cn(baseStyles, 'whitespace-pre-wrap')}>
        {displayText}
      </Element>
    );
  }

  // Render with HTML formatting
  const formattedHtml = formatTextForDisplay(displayText);
  const Element = inline ? 'span' : 'div';

  return (
    <Element
      className={cn(
        baseStyles,
        // Styles for formatted content
        '[&>p]:mb-2 [&>p:last-child]:mb-0',
        '[&>ul]:mb-2 [&>ul:last-child]:mb-0 [&>ul]:pl-4',
        '[&>ol]:mb-2 [&>ol:last-child]:mb-0 [&>ol]:pl-4',
        '[&>li]:mb-1 [&>li:last-child]:mb-0',
        // Preserve whitespace for line breaks
        'whitespace-pre-wrap'
      )}
      dangerouslySetInnerHTML={{ __html: formattedHtml }}
    />
  );
}

/**
 * Specialized component for task descriptions in cards
 */
export function TaskDescriptionText({
  text,
  className,
  truncate = true,
  maxLength = 100
}: Omit<FormattedTextProps, 'variant' | 'inline'>) {
  return (
    <FormattedText
      text={text}
      className={className}
      truncate={truncate}
      maxLength={maxLength}
      variant="card"
      inline={false}
    />
  );
}

/**
 * Specialized component for project descriptions
 */
export function ProjectDescriptionText({
  text,
  className
}: Omit<FormattedTextProps, 'variant' | 'inline' | 'truncate'>) {
  return (
    <FormattedText
      text={text}
      className={className}
      variant="description"
      inline={false}
    />
  );
}

/**
 * Specialized component for comments and activity descriptions
 */
export function CommentText({
  text,
  className,
  inline = false
}: Omit<FormattedTextProps, 'variant' | 'truncate'>) {
  return (
    <FormattedText
      text={text}
      className={className}
      variant="muted"
      inline={inline}
    />
  );
}

export default FormattedText;
