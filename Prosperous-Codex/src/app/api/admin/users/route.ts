import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/database/user-service';
import { requireRole } from '@/lib/auth/middleware';

export async function GET(request: NextRequest) {
  try {
    // Require admin or moderator role
    const authResult = await requireRole(request, 'moderator');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const userService = new UserService();
    const users = userService.getAllUsers();
    
    return NextResponse.json({ users });
    
  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require admin role for creating users
    const authResult = await requireRole(request, 'admin');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { email, username, password, role = 'user' } = await request.json();
    
    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }
    
    if (password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }
    
    if (!['user', 'moderator', 'admin'].includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role. Must be user, moderator, or admin' },
        { status: 400 }
      );
    }
    
    // Create user
    const userService = new UserService();
    
    try {
      const newUser = await userService.createUser({
        email,
        username,
        password,
        role
      });
      
      return NextResponse.json({ 
        message: 'User created successfully',
        user: newUser
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes('already exists')) {
        return NextResponse.json(
          { error: 'User with this email already exists' },
          { status: 409 }
        );
      }
      throw error;
    }
    
  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
