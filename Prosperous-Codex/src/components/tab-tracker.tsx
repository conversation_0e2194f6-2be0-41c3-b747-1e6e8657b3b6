'use client';

import { useEffect } from 'react';

export default function TabTracker() {
  useEffect(() => {
    const tabButtons = document.querySelectorAll('.tab-item');
    const tabIndicator = document.querySelector('.tab-indicator');
    const tabContents = document.querySelectorAll('.tab-content');
    let currentTab = 'innerText';

    function updateTabIndicator() {
      const activeTab = document.querySelector('.tab-item.active');
      if (activeTab && tabIndicator) {
        const tabsWrapper = activeTab.parentElement;
        const activeTabRect = activeTab.getBoundingClientRect();
        const wrapperRect = tabsWrapper?.getBoundingClientRect();
        
        if (wrapperRect) {
          const offsetLeft = activeTabRect.left - wrapperRect.left - 4; // Account for wrapper padding
          const width = activeTabRect.width;
          
          (tabIndicator as HTMLElement).style.width = `${width}px`;
          (tabIndicator as HTMLElement).style.transform = `translateX(${offsetLeft}px)`;
        }
      }
    }

    // Set initial indicator position
    setTimeout(updateTabIndicator, 100);

    // Update indicator position on window resize
    const handleResize = () => updateTabIndicator();
    window.addEventListener('resize', handleResize);

    // Update indicator position when fonts load
    document.fonts.ready.then(updateTabIndicator);

    // Add click listeners to tab buttons
    tabButtons.forEach(button => {
      const handleTabClick = () => {
        const targetTab = (button as HTMLElement).dataset.tab;
        if (!targetTab) return;
        
        currentTab = targetTab;
        
        // Update active states
        tabButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        updateTabIndicator();
        
        // Update tab content visibility
        tabContents.forEach(content => {
          content.classList.remove('active');
          if (content.id === `${targetTab}-tab-content`) {
            content.classList.add('active');
          }
        });
        
        // Dispatch custom event for tab change
        window.dispatchEvent(new CustomEvent('tabChanged', { 
          detail: { tab: targetTab } 
        }));
      };
      
      button.addEventListener('click', handleTabClick);
    });

    // Listen for language changes to update tab indicator
    const handleLanguageChange = () => {
      // Update tab indicator after language change to accommodate new text lengths
      setTimeout(() => {
        updateTabIndicator();
      }, 150); // Slightly after text animation completes
    };

    // Listen for custom language change events
    window.addEventListener('languageChanged', handleLanguageChange);

    // Cleanup function
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('languageChanged', handleLanguageChange);
      tabButtons.forEach(button => {
        button.removeEventListener('click', () => {});
      });
    };
  }, []);

  return null;
}