/**
 * Type Safety Enforcer for Task Master
 * 
 * Provides runtime and compile-time type safety enforcement to prevent
 * field name mismatches and ensure consistency between API and database layers.
 */

// z import removed as it's not used in this file
import { FieldMapper, FIELD_MAPPINGS, REVERSE_FIELD_MAPPINGS } from './field-mapping';
import {
  ValidationSchemas,
  TypeGuards,
  ProjectDbFields,
  TaskDbFields,
  CommentDbFields,
  FileDbFields,
  TeamMemberDbFields,
  ActivityLogDbFields,
  ProjectApiFields,
  TaskApiFields,
  CommentApiFields,
  FileApiFields,
  TeamMemberApiFields,
  ActivityLogApiFields
} from './validation-types';
import { ValidationError } from './errors';

/**
 * Type safety configuration
 */
export interface TypeSafetyConfig {
  strictMode: boolean;
  enforceFieldNaming: boolean;
  validateAtRuntime: boolean;
  throwOnMismatch: boolean;
  logWarnings: boolean;
}

/**
 * Default type safety configuration
 */
export const DEFAULT_TYPE_SAFETY_CONFIG: TypeSafetyConfig = {
  strictMode: true,
  enforceFieldNaming: true,
  validateAtRuntime: true,
  throwOnMismatch: true,
  logWarnings: true,
};

/**
 * Type safety violation details
 */
export interface TypeSafetyViolation {
  type: 'field_naming' | 'type_mismatch' | 'missing_field' | 'extra_field';
  field: string;
  expected: string;
  actual: string;
  severity: 'error' | 'warning';
  suggestion?: string;
}

/**
 * Type safety enforcement result
 */
export interface TypeSafetyResult {
  valid: boolean;
  violations: TypeSafetyViolation[];
  correctedData?: Record<string, unknown>;
  metadata: {
    checkedFields: number;
    violationCount: number;
    correctionsMade: number;
  };
}

/**
 * Type Safety Enforcer Class
 */
export class TypeSafetyEnforcer {
  private config: TypeSafetyConfig;

  constructor(config: Partial<TypeSafetyConfig> = {}) {
    this.config = { ...DEFAULT_TYPE_SAFETY_CONFIG, ...config };
  }

  /**
   * Enforce type safety for API to database field mapping
   */
  enforceApiToDb<T extends Record<string, unknown>>(
    data: T,
    entityType: 'project' | 'task' | 'comment' | 'file' | 'team_member' | 'activity_log'
  ): TypeSafetyResult {
    const violations: TypeSafetyViolation[] = [];
    const correctedData: Record<string, unknown> = {};
    let correctionsMade = 0;

    // Validate field naming conventions
    if (this.config.enforceFieldNaming) {
      const namingViolations = this.validateFieldNaming(data, 'api');
      violations.push(...namingViolations);
    }

    // Map fields and validate consistency
    for (const [apiField, value] of Object.entries(data)) {
      const dbField = FieldMapper.getDbFieldName(apiField);
      
      // Check if mapping exists for camelCase fields
      if (this.shouldHaveMapping(apiField) && !(apiField in FIELD_MAPPINGS)) {
        violations.push({
          type: 'missing_field',
          field: apiField,
          expected: 'mapped database field',
          actual: 'no mapping found',
          severity: 'error',
          suggestion: `Add mapping: ${apiField} -> ${this.suggestDbFieldName(apiField)}`,
        });
      }

      correctedData[dbField] = value;
      if (dbField !== apiField) {
        correctionsMade++;
      }
    }

    // Runtime type validation if enabled
    if (this.config.validateAtRuntime) {
      const runtimeViolations = this.validateRuntimeTypes(correctedData, entityType, 'db');
      violations.push(...runtimeViolations);
    }

    const result: TypeSafetyResult = {
      valid: violations.filter(v => v.severity === 'error').length === 0,
      violations,
      correctedData,
      metadata: {
        checkedFields: Object.keys(data).length,
        violationCount: violations.length,
        correctionsMade,
      },
    };

    this.handleViolations(result);
    return result;
  }

  /**
   * Enforce type safety for database to API field mapping
   */
  enforceDbToApi<T extends Record<string, unknown>>(
    data: T,
    entityType: 'project' | 'task' | 'comment' | 'file' | 'team_member' | 'activity_log'
  ): TypeSafetyResult {
    const violations: TypeSafetyViolation[] = [];
    const correctedData: Record<string, unknown> = {};
    let correctionsMade = 0;

    // Validate field naming conventions
    if (this.config.enforceFieldNaming) {
      const namingViolations = this.validateFieldNaming(data, 'db');
      violations.push(...namingViolations);
    }

    // Map fields and validate consistency
    for (const [dbField, value] of Object.entries(data)) {
      const apiField = FieldMapper.getApiFieldName(dbField);
      
      // Check if mapping exists for snake_case fields
      if (this.shouldHaveMapping(dbField) && !(dbField in REVERSE_FIELD_MAPPINGS)) {
        violations.push({
          type: 'missing_field',
          field: dbField,
          expected: 'mapped API field',
          actual: 'no mapping found',
          severity: 'error',
          suggestion: `Add mapping: ${this.suggestApiFieldName(dbField)} -> ${dbField}`,
        });
      }

      correctedData[apiField] = value;
      if (apiField !== dbField) {
        correctionsMade++;
      }
    }

    // Runtime type validation if enabled
    if (this.config.validateAtRuntime) {
      const runtimeViolations = this.validateRuntimeTypes(correctedData, entityType, 'api');
      violations.push(...runtimeViolations);
    }

    const result: TypeSafetyResult = {
      valid: violations.filter(v => v.severity === 'error').length === 0,
      violations,
      correctedData,
      metadata: {
        checkedFields: Object.keys(data).length,
        violationCount: violations.length,
        correctionsMade,
      },
    };

    this.handleViolations(result);
    return result;
  }

  /**
   * Validate field naming conventions
   */
  private validateFieldNaming(
    data: Record<string, any>,
    context: 'api' | 'db'
  ): TypeSafetyViolation[] {
    const violations: TypeSafetyViolation[] = [];
    const expectedPattern = context === 'api' ? /^[a-z][a-zA-Z0-9]*$/ : /^[a-z][a-z0-9_]*$/;

    for (const field of Object.keys(data)) {
      if (!expectedPattern.test(field)) {
        violations.push({
          type: 'field_naming',
          field,
          expected: context === 'api' ? 'camelCase' : 'snake_case',
          actual: field,
          severity: 'error',
          suggestion: context === 'api' 
            ? this.suggestApiFieldName(field)
            : this.suggestDbFieldName(field),
        });
      }
    }

    return violations;
  }

  /**
   * Validate runtime types using Zod schemas
   */
  private validateRuntimeTypes(
    data: Record<string, unknown>,
    entityType: string,
    context: 'api' | 'db'
  ): TypeSafetyViolation[] {
    const violations: TypeSafetyViolation[] = [];

    try {
      // Get appropriate schema
      const schemaKey = `${entityType}${context === 'api' ? 'Api' : 'Db'}Fields` as keyof typeof ValidationSchemas;
      const schema = ValidationSchemas[schemaKey];

      if (schema) {
        const result = schema.safeParse(data);
        if (!result.success) {
          for (const error of result.error.errors) {
            violations.push({
              type: 'type_mismatch',
              field: error.path.join('.'),
              expected: error.expected?.toString() || 'valid type',
              actual: error.received?.toString() || 'invalid type',
              severity: 'error',
              suggestion: error.message,
            });
          }
        }
      }
    } catch (error) {
      // Schema not found or validation error
      violations.push({
        type: 'type_mismatch',
        field: 'unknown',
        expected: 'valid schema',
        actual: 'schema validation failed',
        severity: 'warning',
        suggestion: `Runtime validation failed: ${error instanceof Error ? error.message : String(error)}`,
      });
    }

    return violations;
  }

  /**
   * Check if a field should have a mapping
   */
  private shouldHaveMapping(fieldName: string): boolean {
    // Skip common fields that don't need mapping
    const skipFields = ['id', 'title', 'description', 'status', 'priority', 'progress', 'role', 'content'];
    if (skipFields.includes(fieldName)) {
      return false;
    }

    // Check for camelCase or snake_case
    return /[A-Z]/.test(fieldName) || fieldName.includes('_');
  }

  /**
   * Suggest API field name (snake_case to camelCase)
   */
  private suggestApiFieldName(dbField: string): string {
    return dbField.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  /**
   * Suggest database field name (camelCase to snake_case)
   */
  private suggestDbFieldName(apiField: string): string {
    return apiField.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * Handle violations based on configuration
   */
  private handleViolations(result: TypeSafetyResult): void {
    const errors = result.violations.filter(v => v.severity === 'error');
    const warnings = result.violations.filter(v => v.severity === 'warning');

    if (this.config.logWarnings && warnings.length > 0) {
      console.warn('Type safety warnings:', warnings);
    }

    if (this.config.throwOnMismatch && errors.length > 0) {
      throw new ValidationError(
        `Type safety violations detected: ${errors.map(e => e.field).join(', ')}`,
        {
          context: { violations: errors }
        }
      );
    }
  }

  /**
   * Create a type-safe wrapper for service methods
   */
  static createTypeSafeWrapper<TInput, TOutput>(
    method: (input: TInput) => Promise<TOutput>,
    inputEntityType: 'project' | 'task' | 'comment' | 'file' | 'team_member' | 'activity_log',
    outputEntityType: 'project' | 'task' | 'comment' | 'file' | 'team_member' | 'activity_log',
    config?: Partial<TypeSafetyConfig>
  ) {
    const enforcer = new TypeSafetyEnforcer(config);

    return async (input: TInput): Promise<TOutput> => {
      // Enforce input type safety (API to DB)
      const inputResult = enforcer.enforceApiToDb(input as Record<string, unknown>, inputEntityType);

      // Call the original method with corrected data
      const output = await method(inputResult.correctedData as TInput);

      // Enforce output type safety (DB to API)
      const outputResult = enforcer.enforceDbToApi(output as Record<string, unknown>, outputEntityType);
      
      return outputResult.correctedData as TOutput;
    };
  }

  /**
   * Validate type consistency between API and database schemas
   */
  static validateSchemaConsistency(): {
    consistent: boolean;
    issues: Array<{
      entity: string;
      field: string;
      issue: string;
      suggestion: string;
    }>;
  } {
    const issues: Array<{
      entity: string;
      field: string;
      issue: string;
      suggestion: string;
    }> = [];

    // This would need to be implemented with actual schema comparison
    // Project field checking would go here when implemented
    // For now, return a placeholder
    return {
      consistent: true,
      issues,
    };
  }
}

/**
 * Utility functions for type safety enforcement
 */
const TypeSafetyUtils = {
  /**
   * Create a type-safe field mapper
   */
  createSafeMapper: <TSource, TTarget>(
    mapping: Record<keyof TSource, keyof TTarget>,
    config?: Partial<TypeSafetyConfig>
  ) => {
    // TypeSafetyEnforcer would be used here when fully implemented

    return (source: TSource): TTarget => {
      const mapped = {} as TTarget;
      
      for (const [sourceKey, targetKey] of Object.entries(mapping) as [keyof TSource, keyof TTarget][]) {
        if (sourceKey in source) {
          mapped[targetKey] = source[sourceKey] as any;
        }
      }
      
      return mapped;
    };
  },

  /**
   * Validate field name consistency
   */
  validateFieldConsistency: (
    apiFields: string[],
    dbFields: string[]
  ): { consistent: boolean; mismatches: string[] } => {
    const mismatches: string[] = [];
    
    for (const apiField of apiFields) {
      const expectedDbField = FieldMapper.getDbFieldName(apiField);
      if (!dbFields.includes(expectedDbField)) {
        mismatches.push(`${apiField} -> ${expectedDbField}`);
      }
    }
    
    return {
      consistent: mismatches.length === 0,
      mismatches,
    };
  },

  /**
   * Create type guards for entities
   */
  createTypeGuards: () => ({
    isProjectApiFields: (obj: unknown): obj is ProjectApiFields =>
      TypeGuards.isApiProjectFields(obj),
    isProjectDbFields: (obj: unknown): obj is ProjectDbFields =>
      TypeGuards.isDbProjectFields(obj),
    isTaskApiFields: (obj: unknown): obj is TaskApiFields =>
      TypeGuards.isApiTaskFields(obj),
    isTaskDbFields: (obj: unknown): obj is TaskDbFields =>
      TypeGuards.isDbTaskFields(obj),
  }),
};

/**
 * Export the main enforcer class and utilities
 */
export { TypeSafetyEnforcer as default, TypeSafetyUtils };
