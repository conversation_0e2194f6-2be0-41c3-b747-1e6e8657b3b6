import { NetworkAuthWrapper } from '@/components/auth/network-auth-wrapper';
import { AccessRequestForm } from '@/components/auth/access-request-form';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Paper Cost Estimator - Request Access',
  description: 'Request access to the Paper Cost Estimator.',
};

export default function RequestAccessPage() {
  return (
    <NetworkAuthWrapper
      welcomeMessage="REQUEST ACCESS"
      title="Join Our<br>Team"
      subtitle="Submit your information to request access to the Paper Cost Estimator system."
    >
      <AccessRequestForm />
    </NetworkAuthWrapper>
  );
}
