"use client";

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { AccessRequestSchema, type AccessRequestFormData } from '@/lib/schemas/auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import Link from 'next/link';

import { useToast } from '@/hooks/use-toast';
import { Send, Mail, User, MessageSquare, Loader2, CheckCircle } from 'lucide-react';

export function AccessRequestForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { toast } = useToast();
  
  const form = useForm<AccessRequestFormData>({
    resolver: zod<PERSON><PERSON>olver(AccessRequestSchema),
    defaultValues: {
      email: '',
      name: '',
      reason: '',
    },
  });

  const onSubmit = async (data: AccessRequestFormData) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/request-access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        setIsSubmitted(true);
        toast({
          title: "Request Submitted",
          description: "Your access request has been submitted successfully. You will be contacted once reviewed."
        });
      } else {
        toast({
          title: "Request Failed",
          description: result.error || "Failed to submit access request.",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <CheckCircle className="h-16 w-16 text-green-400" />
        </div>
        <h3 className="text-xl font-semibold text-gray-200 mb-2">Request Submitted</h3>
        <p className="text-sm text-gray-400 mb-6">
          Your access request has been submitted successfully. You will receive an email once your request has been reviewed.
        </p>
        <Link href="/auth/login">
          <Button variant="outline" className="bg-gray-800 border-gray-700 text-gray-300 hover:bg-gray-700">
            Back to Login
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div>
      <h3 className="text-xl font-semibold text-gray-200 mb-2">Request Access</h3>
      <p className="text-sm text-gray-400 mb-6">Submit your information to request access to the Paper Cost Estimator.</p>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <Label htmlFor="email" className="block text-sm font-medium text-gray-300">EMAIL *</Label>
                <FormControl>
                  <div className="relative">
                    <Input 
                      id="email"
                      type="email" 
                      placeholder="Enter your email address" 
                      {...field} 
                      className="w-full pl-3 pr-10 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                    />
                    <Mail className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  </div>
                </FormControl>
                <FormMessage className="text-red-400 text-xs" />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <Label htmlFor="name" className="block text-sm font-medium text-gray-300">FULL NAME</Label>
                <FormControl>
                  <div className="relative">
                    <Input 
                      id="name"
                      type="text" 
                      placeholder="Enter your full name" 
                      {...field} 
                      className="w-full pl-3 pr-10 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                    />
                    <User className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  </div>
                </FormControl>
                <FormMessage className="text-red-400 text-xs" />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="reason"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <Label htmlFor="reason" className="block text-sm font-medium text-gray-300">REASON FOR ACCESS</Label>
                <FormControl>
                  <div className="relative">
                    <Textarea 
                      id="reason"
                      placeholder="Please explain why you need access to the Paper Cost Estimator..." 
                      {...field} 
                      className="w-full pl-3 pr-10 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent min-h-[80px]"
                    />
                    <MessageSquare className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
                  </div>
                </FormControl>
                <FormMessage className="text-red-400 text-xs" />
              </FormItem>
            )}
          />
          
          <div className="pt-2">
            <Button 
              type="submit" 
              className="w-full px-4 py-2.5 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition flex items-center justify-center font-medium focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-900" 
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              {isLoading ? 'Submitting...' : 'Submit Request'}
            </Button>
          </div>
        </form>
      </Form>
        
      <div className="relative flex py-5 items-center">
        <div className="flex-grow border-t border-gray-700"></div>
        <span className="flex-shrink mx-4 text-gray-500 text-xs">OR</span>
        <div className="flex-grow border-t border-gray-700"></div>
      </div>
      <div className="text-center">
        <p className="text-xs text-gray-400">
          Already have access?{' '}
          <Link href="/auth/login" className="text-gray-300 hover:text-white">
            Sign In
          </Link>
        </p>
      </div>
    </div>
  );
}
