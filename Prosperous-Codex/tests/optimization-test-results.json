{"totalTests": 14, "passedTests": 1, "failedTests": 13, "successRate": 7.142857142857142, "results": [{"test": "Database Indexes - Project Status Query", "passed": false, "timestamp": "2025-06-19T21:16:27.224Z", "responseTime": 601.2179580000002, "error": "HTTP 401"}, {"test": "Transaction Integrity - Project Creation", "passed": false, "timestamp": "2025-06-19T21:16:27.235Z", "responseTime": 9.22399999999925, "error": "Transaction rollback or incomplete data"}, {"test": "Concurrent Operations", "passed": false, "timestamp": "2025-06-19T21:16:27.257Z", "responseTime": 21.040624999999636, "error": "Some concurrent operations failed", "details": "0/5 requests succeeded"}, {"test": "Lazy Loading - Basic Load", "passed": false, "timestamp": "2025-06-19T21:16:27.271Z", "responseTime": 5.506083000000217, "error": "Basic load too slow"}, {"test": "Lazy Loading - Full Load", "passed": false, "timestamp": "2025-06-19T21:16:27.271Z", "responseTime": 8.225416999999652, "error": "Full load too slow"}, {"test": "Lazy Loading - Performance Improvement", "passed": true, "timestamp": "2025-06-19T21:16:27.271Z", "details": "Basic: 5.51ms, Full: 8.23ms", "error": null}, {"test": "Pagination - Project List", "passed": false, "timestamp": "2025-06-19T21:16:27.278Z", "responseTime": 6.9435419999999795, "error": "Pagination metadata missing or incorrect", "details": null}, {"test": "Partial Response", "passed": false, "timestamp": "2025-06-19T21:16:27.288Z", "error": "Failed to fetch comparison data"}, {"test": "Authorization - Project Creation", "passed": false, "timestamp": "2025-06-19T21:16:27.293Z", "responseTime": 4.764583999999559, "error": "HTTP 401"}, {"test": "Input Sanitization - XSS Prevention", "passed": false, "timestamp": "2025-06-19T21:16:27.298Z", "responseTime": 4.123500000000604, "error": "Malicious input not properly sanitized", "details": null}, {"test": "Field Validation - Invalid Fields", "passed": false, "timestamp": "2025-06-19T21:16:27.302Z", "responseTime": 4.028207999999722, "error": "Invalid fields not properly rejected", "details": null}, {"test": "Error <PERSON> - Not Found", "passed": false, "timestamp": "2025-06-19T21:16:28.270Z", "responseTime": 967.7128329999996, "error": "Error response format not standardized", "details": null}, {"test": "Error <PERSON> - Validation Errors", "passed": false, "timestamp": "2025-06-19T21:16:28.336Z", "responseTime": 65.8592920000001, "error": "Validation errors not properly handled", "details": null}, {"test": "Integration - Complete Workflow", "passed": false, "timestamp": "2025-06-19T21:16:28.340Z", "error": "Project creation failed", "details": null}]}