/**
 * Naming Conventions Enforcer for Task Master
 * 
 * Provides tooling and automation to enforce field naming standards
 * across database, API, and frontend layers.
 */

import { FieldMapper, FIELD_MAPPINGS, REVERSE_FIELD_MAPPINGS } from './field-mapping';

/**
 * Naming convention configuration
 */
export interface NamingConventionConfig {
  enforceApiCamelCase: boolean;
  enforceDbSnakeCase: boolean;
  requireMappingForMixedCase: boolean;
  allowedExceptions: string[];
  strictMode: boolean;
}

/**
 * Default naming convention configuration
 */
export const DEFAULT_NAMING_CONFIG: NamingConventionConfig = {
  enforceApiCamelCase: true,
  enforceDbSnakeCase: true,
  requireMappingForMixedCase: true,
  allowedExceptions: ['id', 'title', 'description', 'status', 'priority', 'progress', 'role', 'content'],
  strictMode: true,
};

/**
 * Naming violation details
 */
export interface NamingViolation {
  field: string;
  violationType: 'case_convention' | 'missing_mapping' | 'inconsistent_pattern' | 'reserved_word';
  expected: string;
  actual: string;
  severity: 'error' | 'warning';
  suggestion: string;
  context: 'api' | 'database' | 'frontend';
}

/**
 * Naming validation result
 */
export interface NamingValidationResult {
  valid: boolean;
  violations: NamingViolation[];
  suggestions: string[];
  metadata: {
    totalFields: number;
    validFields: number;
    violationCount: number;
    contextChecked: string;
  };
}

/**
 * Naming Conventions Enforcer Class
 */
export class NamingConventionsEnforcer {
  private config: NamingConventionConfig;

  constructor(config: Partial<NamingConventionConfig> = {}) {
    this.config = { ...DEFAULT_NAMING_CONFIG, ...config };
  }

  /**
   * Validate API field naming conventions
   */
  validateApiFieldNames(fields: string[] | Record<string, any>): NamingValidationResult {
    const fieldNames = Array.isArray(fields) ? fields : Object.keys(fields);
    const violations: NamingViolation[] = [];
    const suggestions: string[] = [];

    for (const field of fieldNames) {
      // Skip allowed exceptions
      if (this.config.allowedExceptions.includes(field)) {
        continue;
      }

      // Check camelCase convention
      if (this.config.enforceApiCamelCase && !this.isValidCamelCase(field)) {
        const suggestion = this.convertToApiFieldName(field);
        violations.push({
          field,
          violationType: 'case_convention',
          expected: 'camelCase',
          actual: field,
          severity: 'error',
          suggestion: `Use '${suggestion}' instead of '${field}'`,
          context: 'api',
        });
        suggestions.push(`Rename '${field}' to '${suggestion}'`);
      }

      // Check for required mappings
      if (this.config.requireMappingForMixedCase && this.shouldHaveMapping(field, 'api')) {
        if (!(field in FIELD_MAPPINGS)) {
          const dbFieldSuggestion = this.convertToDbFieldName(field);
          violations.push({
            field,
            violationType: 'missing_mapping',
            expected: 'mapped database field',
            actual: 'no mapping found',
            severity: 'error',
            suggestion: `Add mapping: ${field} -> ${dbFieldSuggestion}`,
            context: 'api',
          });
          suggestions.push(`Add field mapping for '${field}'`);
        }
      }

      // Check for reserved words
      if (this.isReservedWord(field)) {
        violations.push({
          field,
          violationType: 'reserved_word',
          expected: 'non-reserved field name',
          actual: field,
          severity: 'warning',
          suggestion: `Consider renaming '${field}' to avoid conflicts`,
          context: 'api',
        });
      }
    }

    return {
      valid: violations.filter(v => v.severity === 'error').length === 0,
      violations,
      suggestions,
      metadata: {
        totalFields: fieldNames.length,
        validFields: fieldNames.length - violations.length,
        violationCount: violations.length,
        contextChecked: 'api',
      },
    };
  }

  /**
   * Validate database field naming conventions
   */
  validateDbFieldNames(fields: string[] | Record<string, any>): NamingValidationResult {
    const fieldNames = Array.isArray(fields) ? fields : Object.keys(fields);
    const violations: NamingViolation[] = [];
    const suggestions: string[] = [];

    for (const field of fieldNames) {
      // Skip allowed exceptions
      if (this.config.allowedExceptions.includes(field)) {
        continue;
      }

      // Check snake_case convention
      if (this.config.enforceDbSnakeCase && !this.isValidSnakeCase(field)) {
        const suggestion = this.convertToDbFieldName(field);
        violations.push({
          field,
          violationType: 'case_convention',
          expected: 'snake_case',
          actual: field,
          severity: 'error',
          suggestion: `Use '${suggestion}' instead of '${field}'`,
          context: 'database',
        });
        suggestions.push(`Rename '${field}' to '${suggestion}'`);
      }

      // Check for required mappings
      if (this.config.requireMappingForMixedCase && this.shouldHaveMapping(field, 'database')) {
        if (!(field in REVERSE_FIELD_MAPPINGS)) {
          const apiFieldSuggestion = this.convertToApiFieldName(field);
          violations.push({
            field,
            violationType: 'missing_mapping',
            expected: 'mapped API field',
            actual: 'no mapping found',
            severity: 'error',
            suggestion: `Add mapping: ${apiFieldSuggestion} -> ${field}`,
            context: 'database',
          });
          suggestions.push(`Add field mapping for '${field}'`);
        }
      }

      // Check for reserved words
      if (this.isReservedWord(field)) {
        violations.push({
          field,
          violationType: 'reserved_word',
          expected: 'non-reserved field name',
          actual: field,
          severity: 'warning',
          suggestion: `Consider renaming '${field}' to avoid conflicts`,
          context: 'database',
        });
      }
    }

    return {
      valid: violations.filter(v => v.severity === 'error').length === 0,
      violations,
      suggestions,
      metadata: {
        totalFields: fieldNames.length,
        validFields: fieldNames.length - violations.length,
        violationCount: violations.length,
        contextChecked: 'database',
      },
    };
  }

  /**
   * Validate frontend component prop naming
   */
  validateFrontendFieldNames(fields: string[] | Record<string, any>): NamingValidationResult {
    // Frontend uses same conventions as API (camelCase)
    const result = this.validateApiFieldNames(fields);
    result.metadata.contextChecked = 'frontend';
    
    // Update context in violations
    result.violations.forEach(violation => {
      violation.context = 'frontend';
    });

    return result;
  }

  /**
   * Generate comprehensive naming report
   */
  generateNamingReport(
    apiFields: string[],
    dbFields: string[],
    frontendFields?: string[]
  ): {
    overall: { valid: boolean; totalViolations: number };
    api: NamingValidationResult;
    database: NamingValidationResult;
    frontend?: NamingValidationResult;
    crossLayerIssues: NamingViolation[];
    recommendations: string[];
  } {
    const apiResult = this.validateApiFieldNames(apiFields);
    const dbResult = this.validateDbFieldNames(dbFields);
    const frontendResult = frontendFields ? this.validateFrontendFieldNames(frontendFields) : undefined;

    // Check cross-layer consistency
    const crossLayerIssues = this.validateCrossLayerConsistency(apiFields, dbFields);

    const totalViolations = 
      apiResult.violations.length + 
      dbResult.violations.length + 
      (frontendResult?.violations.length || 0) + 
      crossLayerIssues.length;

    const recommendations = [
      ...apiResult.suggestions,
      ...dbResult.suggestions,
      ...(frontendResult?.suggestions || []),
      ...crossLayerIssues.map(issue => issue.suggestion),
    ];

    return {
      overall: {
        valid: totalViolations === 0,
        totalViolations,
      },
      api: apiResult,
      database: dbResult,
      frontend: frontendResult,
      crossLayerIssues,
      recommendations: [...new Set(recommendations)], // Remove duplicates
    };
  }

  /**
   * Validate consistency across layers
   */
  private validateCrossLayerConsistency(apiFields: string[], dbFields: string[]): NamingViolation[] {
    const violations: NamingViolation[] = [];

    // Check that mapped API fields have corresponding DB fields
    for (const apiField of apiFields) {
      if (this.shouldHaveMapping(apiField, 'api')) {
        const expectedDbField = FieldMapper.getDbFieldName(apiField);
        if (!dbFields.includes(expectedDbField)) {
          violations.push({
            field: apiField,
            violationType: 'inconsistent_pattern',
            expected: `database field '${expectedDbField}'`,
            actual: 'field not found in database',
            severity: 'error',
            suggestion: `Add '${expectedDbField}' to database schema or update mapping`,
            context: 'api',
          });
        }
      }
    }

    // Check that mapped DB fields have corresponding API fields
    for (const dbField of dbFields) {
      if (this.shouldHaveMapping(dbField, 'database')) {
        const expectedApiField = FieldMapper.getApiFieldName(dbField);
        if (!apiFields.includes(expectedApiField)) {
          violations.push({
            field: dbField,
            violationType: 'inconsistent_pattern',
            expected: `API field '${expectedApiField}'`,
            actual: 'field not found in API',
            severity: 'error',
            suggestion: `Add '${expectedApiField}' to API interface or update mapping`,
            context: 'database',
          });
        }
      }
    }

    return violations;
  }

  /**
   * Check if field name is valid camelCase
   */
  private isValidCamelCase(field: string): boolean {
    // Must start with lowercase letter, can contain letters and numbers
    return /^[a-z][a-zA-Z0-9]*$/.test(field);
  }

  /**
   * Check if field name is valid snake_case
   */
  private isValidSnakeCase(field: string): boolean {
    // Must be lowercase letters, numbers, and underscores only
    return /^[a-z][a-z0-9_]*$/.test(field);
  }

  /**
   * Check if field should have a mapping
   */
  private shouldHaveMapping(field: string, context: 'api' | 'database'): boolean {
    if (this.config.allowedExceptions.includes(field)) {
      return false;
    }

    if (context === 'api') {
      // API fields with capital letters should have mappings
      return /[A-Z]/.test(field);
    } else {
      // Database fields with underscores should have mappings
      return field.includes('_');
    }
  }

  /**
   * Convert field name to API format (camelCase)
   */
  private convertToApiFieldName(field: string): string {
    return field.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  /**
   * Convert field name to database format (snake_case)
   */
  private convertToDbFieldName(field: string): string {
    return field.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * Check if field name is a reserved word
   */
  private isReservedWord(field: string): boolean {
    const reservedWords = [
      // SQL reserved words
      'select', 'insert', 'update', 'delete', 'from', 'where', 'join', 'order', 'group',
      'having', 'union', 'create', 'alter', 'drop', 'table', 'index', 'view', 'database',
      
      // JavaScript reserved words
      'class', 'function', 'var', 'let', 'const', 'if', 'else', 'for', 'while', 'do',
      'switch', 'case', 'default', 'break', 'continue', 'return', 'try', 'catch', 'finally',
      
      // Common problematic names
      'constructor', 'prototype', 'length', 'name', 'toString', 'valueOf',
    ];

    return reservedWords.includes(field.toLowerCase());
  }

  /**
   * Create ESLint rule configuration for field naming
   */
  static generateESLintConfig(): Record<string, any> {
    return {
      rules: {
        '@typescript-eslint/naming-convention': [
          'error',
          {
            selector: 'interface',
            format: ['PascalCase'],
            custom: {
              regex: '^I[A-Z]',
              match: false,
            },
          },
          {
            selector: 'typeAlias',
            format: ['PascalCase'],
          },
          {
            selector: 'property',
            format: ['camelCase'],
            filter: {
              regex: '^(id|title|description|status|priority|progress|role|content)$',
              match: false,
            },
          },
          {
            selector: 'parameter',
            format: ['camelCase'],
            leadingUnderscore: 'allow',
          },
          {
            selector: 'variable',
            format: ['camelCase', 'UPPER_CASE'],
            leadingUnderscore: 'allow',
          },
        ],
      },
    };
  }

  /**
   * Create TypeScript compiler options for strict naming
   */
  static generateTSConfig(): Record<string, any> {
    return {
      compilerOptions: {
        strict: true,
        noImplicitAny: true,
        strictNullChecks: true,
        strictFunctionTypes: true,
        strictBindCallApply: true,
        strictPropertyInitialization: true,
        noImplicitReturns: true,
        noFallthroughCasesInSwitch: true,
        noUncheckedIndexedAccess: true,
        exactOptionalPropertyTypes: true,
      },
    };
  }

  /**
   * Generate validation middleware for API routes
   */
  static createValidationMiddleware(config?: Partial<NamingConventionConfig>) {
    const enforcer = new NamingConventionsEnforcer(config);

    return function validateNaming(req: any, res: any, next: any) {
      if (req.body && typeof req.body === 'object') {
        const result = enforcer.validateApiFieldNames(req.body);
        
        if (!result.valid && enforcer.config.strictMode) {
          return res.status(400).json({
            error: 'Field naming convention violations',
            violations: result.violations,
            suggestions: result.suggestions,
          });
        }

        if (result.violations.length > 0) {
          console.warn('Field naming violations detected:', result.violations);
        }
      }

      next();
    };
  }
}

/**
 * Utility functions for naming convention enforcement
 */
const NamingUtils = {
  /**
   * Quick validation for API field names
   */
  validateApiFields: (fields: string[] | Record<string, any>): boolean => {
    const enforcer = new NamingConventionsEnforcer();
    return enforcer.validateApiFieldNames(fields).valid;
  },

  /**
   * Quick validation for database field names
   */
  validateDbFields: (fields: string[] | Record<string, any>): boolean => {
    const enforcer = new NamingConventionsEnforcer();
    return enforcer.validateDbFieldNames(fields).valid;
  },

  /**
   * Convert between naming conventions
   */
  convertNaming: {
    toApiField: (dbField: string): string =>
      dbField.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()),

    toDbField: (apiField: string): string =>
      apiField.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`),
  },

  /**
   * Check if field needs mapping
   */
  needsMapping: (field: string, context: 'api' | 'database'): boolean => {
    const enforcer = new NamingConventionsEnforcer();
    return enforcer['shouldHaveMapping'](field, context);
  },
};

/**
 * Export the main enforcer class and utilities
 */
export { NamingConventionsEnforcer as default, NamingUtils };
