/**
 * Input sanitization utilities for Task Master system
 * Provides XSS protection for user-generated content
 * Based on DOMPurify with server-side configuration
 */

import { JSD<PERSON> } from 'jsdom';
import createDOMPurify from 'dompurify';

// Initialize DOMPurify for server-side use
const window = new JSDOM('').window;
const DOMPurify = createDOMPurify(window as any);

/**
 * Sanitization configuration for different content types
 */
const SANITIZATION_CONFIGS = {
  // For rich text content (descriptions, comments)
  richText: {
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'ul', 'ol', 'li',
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'code', 'pre'
    ],
    ALLOWED_ATTR: ['class'],
    ALLOW_DATA_ATTR: false,
    ALLOW_ARIA_ATTR: false,
    FORBID_TAGS: ['script', 'style', 'iframe', 'object', 'embed', 'form', 'input'],
    FORBID_ATTR: ['style', 'onclick', 'onerror', 'onload', 'onmouseover'],
    KEEP_CONTENT: true,
    USE_PROFILES: { html: true }
  },

  // For plain text content (titles, names)
  plainText: {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
    ALLOW_DATA_ATTR: false,
    ALLOW_ARIA_ATTR: false,
    KEEP_CONTENT: true,
    RETURN_DOM_FRAGMENT: false
  },

  // For markdown content (event logs, notes)
  markdown: {
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'b', 'em', 'i', 'ul', 'ol', 'li',
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'code', 'pre',
      'a', 'img'
    ],
    ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'class'],
    ALLOW_DATA_ATTR: false,
    ALLOW_ARIA_ATTR: false,
    FORBID_TAGS: ['script', 'style', 'iframe', 'object', 'embed', 'form', 'input'],
    FORBID_ATTR: ['style', 'onclick', 'onerror', 'onload', 'onmouseover'],
    KEEP_CONTENT: true,
    USE_PROFILES: { html: true }
  }
};

/**
 * Sanitization utility class
 */
export class InputSanitizer {
  /**
   * Sanitize rich text content (descriptions, comments)
   */
  static sanitizeRichText(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    // Preserve line breaks by temporarily replacing them with placeholders
    const withPlaceholders = input
      .replace(/\r\n/g, '___CRLF___')
      .replace(/\n/g, '___LF___')
      .replace(/\r/g, '___CR___');

    const sanitized = DOMPurify.sanitize(withPlaceholders.trim(), SANITIZATION_CONFIGS.richText);

    // Restore line breaks
    return sanitized
      .replace(/___CRLF___/g, '\r\n')
      .replace(/___LF___/g, '\n')
      .replace(/___CR___/g, '\r');
  }

  /**
   * Sanitize plain text content (titles, names)
   */
  static sanitizePlainText(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    // Remove all HTML tags and decode entities
    const sanitized = DOMPurify.sanitize(input.trim(), SANITIZATION_CONFIGS.plainText);
    
    // Additional cleanup for plain text
    return sanitized
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
      .substring(0, 500); // Limit length for titles/names
  }

  /**
   * Sanitize markdown content (event logs, notes)
   */
  static sanitizeMarkdown(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    // Preserve line breaks by temporarily replacing them with placeholders
    const withPlaceholders = input
      .replace(/\r\n/g, '___CRLF___')
      .replace(/\n/g, '___LF___')
      .replace(/\r/g, '___CR___');

    const sanitized = DOMPurify.sanitize(withPlaceholders.trim(), SANITIZATION_CONFIGS.markdown);

    // Restore line breaks
    return sanitized
      .replace(/___CRLF___/g, '\r\n')
      .replace(/___LF___/g, '\n')
      .replace(/___CR___/g, '\r');
  }

  /**
   * Sanitize file names
   */
  static sanitizeFileName(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    // Remove HTML and dangerous characters
    const sanitized = DOMPurify.sanitize(input.trim(), SANITIZATION_CONFIGS.plainText);
    
    // Remove path traversal and dangerous file characters
    return sanitized
      .replace(/[<>:"/\\|?*\x00-\x1f]/g, '') // Remove dangerous file characters
      .replace(/^\.+/, '') // Remove leading dots
      .replace(/\.+$/, '') // Remove trailing dots
      .trim()
      .substring(0, 255); // Limit file name length
  }

  /**
   * Sanitize email addresses
   */
  static sanitizeEmail(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    // Basic email sanitization
    const sanitized = DOMPurify.sanitize(input.trim(), SANITIZATION_CONFIGS.plainText);
    
    // Simple email validation pattern
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    return emailPattern.test(sanitized) ? sanitized.toLowerCase() : '';
  }

  /**
   * Sanitize URLs
   */
  static sanitizeUrl(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    const sanitized = DOMPurify.sanitize(input.trim(), SANITIZATION_CONFIGS.plainText);
    
    // Only allow HTTP/HTTPS URLs
    try {
      const url = new URL(sanitized);
      if (url.protocol === 'http:' || url.protocol === 'https:') {
        return url.toString();
      }
    } catch {
      // Invalid URL
    }
    
    return '';
  }

  /**
   * Sanitize user input based on field type
   */
  static sanitizeByType(input: string, type: 'richText' | 'plainText' | 'markdown' | 'fileName' | 'email' | 'url'): string {
    switch (type) {
      case 'richText':
        return this.sanitizeRichText(input);
      case 'plainText':
        return this.sanitizePlainText(input);
      case 'markdown':
        return this.sanitizeMarkdown(input);
      case 'fileName':
        return this.sanitizeFileName(input);
      case 'email':
        return this.sanitizeEmail(input);
      case 'url':
        return this.sanitizeUrl(input);
      default:
        return this.sanitizePlainText(input);
    }
  }

  /**
   * Batch sanitize an object's string properties
   */
  static sanitizeObject<T extends Record<string, any>>(
    obj: T,
    fieldTypes: Partial<Record<keyof T, 'richText' | 'plainText' | 'markdown' | 'fileName' | 'email' | 'url'>>
  ): T {
    const sanitized = { ...obj };

    for (const [key, value] of Object.entries(sanitized)) {
      if (typeof value === 'string') {
        const fieldType = fieldTypes[key as keyof T] || 'plainText';
        sanitized[key as keyof T] = this.sanitizeByType(value, fieldType) as T[keyof T];
      }
    }

    return sanitized;
  }
}

/**
 * Convenience functions for common sanitization tasks
 */
export const sanitize = {
  projectTitle: (input: string) => InputSanitizer.sanitizePlainText(input),
  projectDescription: (input: string) => InputSanitizer.sanitizeRichText(input),
  projectEventLog: (input: string) => InputSanitizer.sanitizeMarkdown(input),
  taskTitle: (input: string) => InputSanitizer.sanitizePlainText(input),
  taskDescription: (input: string) => InputSanitizer.sanitizeRichText(input),
  commentContent: (input: string) => InputSanitizer.sanitizeRichText(input),
  fileName: (input: string) => InputSanitizer.sanitizeFileName(input),
  userEmail: (input: string) => InputSanitizer.sanitizeEmail(input),
  url: (input: string) => InputSanitizer.sanitizeUrl(input)
};

export default InputSanitizer;
