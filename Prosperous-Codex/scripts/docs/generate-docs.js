#!/usr/bin/env node

/**
 * Documentation Generation Script for Task Master
 * 
 * Generates comprehensive documentation including:
 * - JSDoc API documentation
 * - OpenAPI specification
 * - Type definitions
 * - Usage examples
 * - Field mapping documentation
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Configuration for documentation generation
 */
const config = {
  outputDir: './docs',
  jsdocConfig: './jsdoc.config.js',
  openApiSpec: './docs/api/task-master-openapi.yaml',
  typeDefsDir: './src/lib/task-master/types',
  examplesDir: './docs/examples',
  verbose: process.argv.includes('--verbose') || process.argv.includes('-v'),
};

/**
 * Logger utility
 */
const logger = {
  info: (message) => console.log(`ℹ️  ${message}`),
  success: (message) => console.log(`✅ ${message}`),
  error: (message) => console.error(`❌ ${message}`),
  warn: (message) => console.warn(`⚠️  ${message}`),
  verbose: (message) => config.verbose && console.log(`🔍 ${message}`),
};

/**
 * Ensure directory exists
 */
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    logger.verbose(`Created directory: ${dirPath}`);
  }
}

/**
 * Execute command with error handling
 */
function execCommand(command, description) {
  try {
    logger.verbose(`Executing: ${command}`);
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    logger.success(description);
    return output;
  } catch (error) {
    logger.error(`Failed to ${description.toLowerCase()}: ${error.message}`);
    throw error;
  }
}

/**
 * Generate JSDoc documentation
 */
function generateJSDoc() {
  logger.info('Generating JSDoc documentation...');
  
  // Ensure JSDoc is installed
  try {
    execSync('npx jsdoc --version', { stdio: 'pipe' });
  } catch (error) {
    logger.warn('JSDoc not found, installing...');
    execCommand('npm install -g jsdoc', 'Install JSDoc');
  }

  // Generate documentation
  const jsdocCommand = `npx jsdoc -c ${config.jsdocConfig}`;
  execCommand(jsdocCommand, 'Generate JSDoc documentation');
}

/**
 * Generate TypeScript declaration files
 */
function generateTypeDeclarations() {
  logger.info('Generating TypeScript declaration files...');
  
  const tscCommand = 'npx tsc --declaration --emitDeclarationOnly --outDir ./docs/types';
  execCommand(tscCommand, 'Generate TypeScript declarations');
}

/**
 * Generate API documentation from OpenAPI spec
 */
function generateApiDocs() {
  logger.info('Generating API documentation from OpenAPI spec...');
  
  if (!fs.existsSync(config.openApiSpec)) {
    logger.warn('OpenAPI specification not found, skipping API docs generation');
    return;
  }

  try {
    // Try to use redoc-cli if available
    execCommand(
      `npx redoc-cli build ${config.openApiSpec} --output ./docs/api/index.html`,
      'Generate API documentation with Redoc'
    );
  } catch (error) {
    logger.warn('Redoc-cli not available, trying swagger-codegen...');
    try {
      execCommand(
        `npx swagger-codegen-cli generate -i ${config.openApiSpec} -l html2 -o ./docs/api/`,
        'Generate API documentation with Swagger Codegen'
      );
    } catch (swaggerError) {
      logger.warn('Could not generate API documentation automatically');
      logger.info('Please install redoc-cli or swagger-codegen-cli for API docs generation');
    }
  }
}

/**
 * Generate field mapping documentation
 */
function generateFieldMappingDocs() {
  logger.info('Generating field mapping documentation...');
  
  const mappingDocsPath = path.join(config.outputDir, 'field-mappings.md');
  
  const mappingDocs = `# Field Mapping Documentation

## Overview

This document describes the field mappings between API (camelCase) and database (snake_case) field names in the Task Master system.

## Field Mappings

The following table shows all defined field mappings:

| API Field (camelCase) | Database Field (snake_case) | Description |
|----------------------|------------------------------|-------------|
| fullDescription | full_description | Detailed description field |
| eventLog | event_log | Event log content |
| dueDate | due_date | Due date timestamp |
| completedDate | completed_date | Completion date timestamp |
| createdBy | created_by | Creator user ID |
| assignedTo | assigned_to | Assignee user ID |
| createdAt | created_at | Creation timestamp |
| updatedAt | updated_at | Last update timestamp |
| projectId | project_id | Project ID reference |
| parentTaskId | parent_task_id | Parent task ID reference |
| parentCommentId | parent_comment_id | Parent comment ID reference |
| fileName | file_name | Original file name |
| fileSize | file_size | File size in bytes |
| mimeType | mime_type | MIME type |
| uploadedBy | uploaded_by | Uploader user ID |
| uploadedAt | uploaded_at | Upload timestamp |
| userId | user_id | User ID reference |
| joinedAt | joined_at | Join timestamp |
| activityType | activity_type | Activity type |
| entityType | entity_type | Entity type |
| entityId | entity_id | Entity ID |
| oldValue | old_value | Previous value |
| newValue | new_value | New value |
| performedBy | performed_by | User who performed action |
| performedAt | performed_at | Action timestamp |

## Usage Examples

### Converting API data to database format

\`\`\`typescript
import { FieldMapper } from '@/lib/task-master/field-mapping';

const apiData = {
  fullDescription: 'Project description',
  dueDate: '2024-12-31T23:59:59Z',
  assignedTo: 123
};

const dbData = FieldMapper.apiToDb(apiData);
// Result: {
//   full_description: 'Project description',
//   due_date: '2024-12-31T23:59:59Z',
//   assigned_to: 123
// }
\`\`\`

### Converting database data to API format

\`\`\`typescript
import { FieldMapper } from '@/lib/task-master/field-mapping';

const dbData = {
  full_description: 'Project description',
  due_date: '2024-12-31T23:59:59Z',
  assigned_to: 123
};

const apiData = FieldMapper.dbToApi(dbData);
// Result: {
//   fullDescription: 'Project description',
//   dueDate: '2024-12-31T23:59:59Z',
//   assignedTo: 123
// }
\`\`\`

## Validation

The field mapping system includes validation to ensure consistency:

\`\`\`typescript
import { FieldMapper } from '@/lib/task-master/field-mapping';

const apiFields = ['fullDescription', 'dueDate', 'assignedTo'];
const dbFields = ['full_description', 'due_date', 'assigned_to'];

const validation = FieldMapper.validateConsistency(apiFields, dbFields);
console.log(validation.valid); // true if all mappings are consistent
\`\`\`

## Adding New Mappings

To add new field mappings:

1. Update the \`FIELD_MAPPINGS\` constant in \`src/lib/task-master/field-mapping.ts\`
2. Add corresponding database column to schema
3. Update TypeScript type definitions
4. Add validation tests
5. Update this documentation

## Best Practices

1. Always use camelCase for API field names
2. Always use snake_case for database column names
3. Use descriptive, full words (avoid abbreviations)
4. Foreign keys should end with \`_id\` in database and \`Id\` in API
5. Timestamp fields should end with \`_at\` or \`_date\` in database and \`At\` or \`Date\` in API
6. Boolean fields should start with \`is_\` or \`has_\` in database and \`is\` or \`has\` in API

Generated on: ${new Date().toISOString()}
`;

  ensureDir(config.outputDir);
  fs.writeFileSync(mappingDocsPath, mappingDocs);
  logger.success('Generated field mapping documentation');
}

/**
 * Generate usage examples
 */
function generateExamples() {
  logger.info('Generating usage examples...');
  
  ensureDir(config.examplesDir);
  
  const examples = {
    'project-management.md': `# Project Management Examples

## Creating a Project

\`\`\`typescript
import { TaskMasterService } from '@/lib/task-master';

const service = new TaskMasterService(database, authService);

const project = await service.createProject({
  title: 'Website Redesign',
  description: 'Complete redesign of company website',
  fullDescription: 'Comprehensive redesign including UX research, wireframes, and implementation',
  status: 'todo',
  priority: 'high',
  dueDate: '2024-12-31T23:59:59Z',
  assignedTo: 2
}, userId);
\`\`\`

## Updating a Project

\`\`\`typescript
const updatedProject = await service.updateProject(projectId, {
  status: 'in_progress',
  progress: 25,
  eventLog: 'Started development phase'
}, userId);
\`\`\`

## Getting Project Tasks

\`\`\`typescript
const tasks = await service.getProjectTasks(projectId, userId);
\`\`\`
`,
    'task-management.md': `# Task Management Examples

## Creating a Task

\`\`\`typescript
const task = await service.createTask({
  title: 'Design homepage mockup',
  description: 'Create wireframes and mockups for the new homepage',
  projectId: 1,
  status: 'todo',
  priority: 'high',
  dueDate: '2024-06-30T23:59:59Z',
  assignedTo: 2
}, userId);
\`\`\`

## Creating a Subtask

\`\`\`typescript
const subtask = await service.createTask({
  title: 'Create wireframes',
  description: 'Low-fidelity wireframes for homepage layout',
  projectId: 1,
  parentTaskId: parentTask.id,
  status: 'todo',
  priority: 'medium',
  assignedTo: 2
}, userId);
\`\`\`

## Updating Task Progress

\`\`\`typescript
const updatedTask = await service.updateTask(taskId, {
  progress: 75,
  status: 'in_progress'
}, userId);
\`\`\`
`,
    'field-validation.md': `# Field Validation Examples

## Using Type Safety Enforcer

\`\`\`typescript
import { TypeSafetyEnforcer } from '@/lib/task-master/type-safety-enforcer';

const enforcer = new TypeSafetyEnforcer({
  strictMode: true,
  enforceFieldNaming: true,
  validateAtRuntime: true
});

// Validate API to DB conversion
const result = enforcer.enforceApiToDb(apiData, 'project');
if (result.valid) {
  // Use result.correctedData for database operations
} else {
  // Handle validation errors
  console.error(result.violations);
}
\`\`\`

## API Contract Validation

\`\`\`typescript
import { ApiValidator } from '@/lib/task-master/api-validator';

// Validate request fields
const requestValidation = ApiValidator.validateRequestFields(
  '/api/task-master/projects',
  requestData
);

// Validate response fields
const responseValidation = ApiValidator.validateResponseFields(
  '/api/task-master/projects/1',
  responseData
);
\`\`\`
`
  };

  for (const [filename, content] of Object.entries(examples)) {
    const filePath = path.join(config.examplesDir, filename);
    fs.writeFileSync(filePath, content);
    logger.verbose(`Generated example: ${filename}`);
  }
  
  logger.success('Generated usage examples');
}

/**
 * Generate README for documentation
 */
function generateDocsReadme() {
  logger.info('Generating documentation README...');
  
  const readmePath = path.join(config.outputDir, 'README.md');
  
  const readme = `# Task Master Documentation

This directory contains comprehensive documentation for the Task Master project management system.

## Documentation Structure

- \`jsdoc/\` - JSDoc generated API documentation
- \`api/\` - OpenAPI specification and generated API docs
- \`types/\` - TypeScript declaration files
- \`examples/\` - Usage examples and tutorials
- \`field-mappings.md\` - Field mapping documentation

## Getting Started

1. **API Documentation**: Start with the [OpenAPI specification](./api/task-master-openapi.yaml) for a complete API reference
2. **Code Documentation**: Browse the [JSDoc documentation](./jsdoc/index.html) for detailed code documentation
3. **Examples**: Check the [examples directory](./examples/) for usage examples
4. **Field Mappings**: Review [field mappings](./field-mappings.md) to understand API/database field conversions

## Key Features

- **Field Name Validation**: Automatic validation and conversion between camelCase (API) and snake_case (database)
- **Type Safety**: Comprehensive TypeScript types and runtime validation
- **Authorization**: Role-based access control for all operations
- **API Contracts**: Validated request/response schemas
- **Comprehensive Testing**: Unit tests, integration tests, and contract tests

## Development

To regenerate this documentation:

\`\`\`bash
npm run docs:generate
\`\`\`

To serve documentation locally:

\`\`\`bash
npm run docs:serve
\`\`\`

## Contributing

When adding new features:

1. Add JSDoc comments to all public methods
2. Update TypeScript type definitions
3. Add usage examples
4. Update field mappings if needed
5. Regenerate documentation

Generated on: ${new Date().toISOString()}
`;

  fs.writeFileSync(readmePath, readme);
  logger.success('Generated documentation README');
}

/**
 * Main documentation generation function
 */
async function generateDocs() {
  try {
    logger.info('Starting documentation generation...');
    
    // Ensure output directory exists
    ensureDir(config.outputDir);
    
    // Generate different types of documentation
    generateJSDoc();
    generateTypeDeclarations();
    generateApiDocs();
    generateFieldMappingDocs();
    generateExamples();
    generateDocsReadme();
    
    logger.success('Documentation generation completed successfully!');
    logger.info(`Documentation available in: ${config.outputDir}`);
    
  } catch (error) {
    logger.error(`Documentation generation failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  generateDocs();
}

module.exports = { generateDocs, config, logger };
