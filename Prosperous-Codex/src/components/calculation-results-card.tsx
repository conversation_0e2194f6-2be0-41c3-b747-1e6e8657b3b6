"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calculator, ChevronDown, ChevronUp, AlertTriangle, CheckCircle, XCircle } from "lucide-react";
import { Separator } from "@/components/ui/separator";

export interface CalculationResult {
  id: string;
  paperName: string;
  source: string;
  sheetHeight: number | null;
  sheetWidth: number;
  grainDirection: string;
  gsm: number;
  caliper: number;
  costPerReam: number | null;
  costPerTon: number | null;

  // Calculation results (would come from backend API)
  isOptimal?: boolean;
  isError?: boolean;
  errorMessage?: string;

  // Layout results
  layoutFit?: string; // e.g., "2 x 3"
  pagesPerSide?: number;
  sheetUtilization?: number;
  wastePercent?: number;

  // Cost results
  totalSheets?: number;
  costPerSheet?: number;
  totalCost?: number;
  costPerBook?: number;

  // Technical details - Enhanced to match reference HTML
  inputSheetH?: number;
  inputSheetW?: number;
  pressH?: number;
  pressW?: number;
  usableH?: number;
  usableW?: number;
  grainAlignment?: 'Aligned' | 'Misaligned';
  untrimmedPageH?: number;
  untrimmedPageW?: number;
  imposedAreaH?: number;
  imposedAreaW?: number;
  layoutDown?: number;
  layoutAcross?: number;
  layoutDescription?: string;
  resultingSig?: string;
  bookBlockThickness?: number;

  // Additional fields from HTML reference
  inputSheet?: string;
  pressSize?: string;
  usableArea?: string;
  untrimmedPage?: string;
  imposedArea?: string;
}

interface CalculationResultsCardProps {
  title: string;
  results: CalculationResult[];
  isCalculating: boolean;
  isButtonDisabled: boolean; // New prop for button disabled state
  onCalculate: () => void;
  onSelectOption?: (result: CalculationResult) => void;
  selectedOptionIds?: string[];
  countdownSeconds?: number;
}

export default function CalculationResultsCard({
  title,
  results,
  isCalculating,
  isButtonDisabled,
  onCalculate,
  onSelectOption,
  selectedOptionIds = [],
  countdownSeconds = 0
}: CalculationResultsCardProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  
  // Group results by type
  const optimalResults = results.filter(r => r.isOptimal && !r.isError);
  const suboptimalResults = results.filter(r => !r.isOptimal && !r.isError);
  const errorResults = results.filter(r => r.isError);
  
  const hasResults = results.length > 0;
  const bestResult = optimalResults.length > 0 ? optimalResults[0] : suboptimalResults[0];

  const formatCurrency = (value: number | undefined) => {
    if (value === undefined || value === null) return 'N/A';
    return `$${value.toFixed(2)}`;
  };

  const formatPercent = (value: number | undefined) => {
    if (value === undefined || value === null) return 'N/A';
    return `${(value * 100).toFixed(1)}%`;
  };

  const formatDimensions = (heightMm: number | undefined, widthMm: number | undefined) => {
    if (heightMm === undefined || widthMm === undefined || heightMm === null || widthMm === null) {
      return { mm: 'N/A', inches: 'N/A' };
    }
    const heightIn = (heightMm / 25.4).toFixed(2);
    const widthIn = (widthMm / 25.4).toFixed(2);
    return {
      mm: `${heightMm.toFixed(1)} H × ${widthMm.toFixed(1)} W`,
      inches: `(${heightIn}" × ${widthIn}")`
    };
  };

  const formatNumber = (value: number | undefined) => {
    if (value === undefined || value === null) return 'N/A';
    return value.toLocaleString();
  };

  const ResultCard = ({ result }: { result: CalculationResult }) => {
    const isSelected = selectedOptionIds.includes(result.id);
    const cardClasses = `relative transition-all duration-200 ${
      result.isError
        ? 'border-red-200 dark:border-red-800 bg-red-50/50 dark:bg-red-950/20'
        : result.isOptimal
          ? 'border-[#5E6AD2]/30 dark:border-[#6E56CF]/40 bg-[#5E6AD2]/5 dark:bg-[#6E56CF]/10'
          : 'border-gray-200 dark:border-neutral-700 bg-white dark:bg-neutral-900'
    } ${isSelected ? 'border-[#5E6AD2] dark:border-[#6E56CF] shadow-lg' : ''}`;

    return (
      <Card className={cardClasses}>
        <CardHeader className="pb-0">
          <Popover>
            <PopoverTrigger asChild>
              <div className="cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors duration-200 rounded-lg p-2 -m-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-semibold flex items-center gap-2 text-black dark:text-white">
                    {result.isError ? (
                      <XCircle className="h-4 w-4 text-red-500" />
                    ) : result.isOptimal ? (
                      <CheckCircle className="h-4 w-4 text-[#5E6AD2] dark:text-[#6E56CF]" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-gray-500 dark:text-neutral-400" />
                    )}
                    {result.paperName}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    {result.isOptimal && (
                      <Badge variant="secondary" className="bg-[#5E6AD2]/10 text-[#5E6AD2] dark:bg-[#6E56CF]/20 dark:text-[#6E56CF] border-[#5E6AD2]/20 dark:border-[#6E56CF]/30">
                        Optimal
                      </Badge>
                    )}
                    <Badge variant="outline" className="text-xs border-gray-300 dark:border-neutral-600 text-gray-700 dark:text-neutral-300">
                      {result.source}
                    </Badge>
                  </div>
                </div>
                <Separator className="mt-2" />
              </div>
            </PopoverTrigger>
            <PopoverContent 
              className="w-auto p-3 text-xs" 
              side="top"
              onOpenAutoFocus={(e) => {
                // Auto-close after 6 seconds
                setTimeout(() => {
                  const closeButton = document.querySelector('[data-radix-popper-content-wrapper] button');
                  if (closeButton) {
                    (closeButton as HTMLElement).click();
                  }
                }, 6000);
              }}
            >
              <div className="space-y-1">
                <div><span className="font-medium">Source:</span> {result.source}</div>
                <div><span className="font-medium">Input Grain:</span> {result.grainDirection}</div>
                <div><span className="font-medium">GSM:</span> {result.gsm || 'N/A'}g/m²</div>
                <div><span className="font-medium">Caliper:</span> {result.caliper > 0 ? result.caliper.toFixed(0) + ' µm' : 'N/A'}</div>
              </div>
            </PopoverContent>
          </Popover>
        </CardHeader>

        <CardContent className="pt-0">
          {result.isError ? (
            <div className="text-sm text-red-600 dark:text-red-400">
              <p className="font-medium">Error:</p>
              <p>{result.errorMessage}</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-2 gap-x-4 gap-y-4 text-sm mb-4">
                {/* Input Sheet */}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Input Sheet</div>
                  {(() => {
                    // For roll papers, show "Auto H" when sheetHeight is null
                    if (result.source === 'Roll' && !result.sheetHeight) {
                      const widthIn = (result.sheetWidth / 25.4).toFixed(2);
                      return (
                        <>
                          <div className="font-medium text-black dark:text-white">Auto H × {result.sheetWidth.toFixed(1)} W</div>
                          <div className="text-xs text-gray-600 dark:text-neutral-400">(Auto&quot; × {widthIn}&quot;)</div>
                        </>
                      );
                    } else {
                      const dims = formatDimensions((result.inputSheetH ?? result.sheetHeight) ?? 0, (result.inputSheetW ?? result.sheetWidth) ?? 0);
                      return (
                        <>
                          <div className="font-medium text-black dark:text-white">{dims.mm}</div>
                          <div className="text-xs text-gray-600 dark:text-neutral-400">{dims.inches}</div>
                        </>
                      );
                    }
                  })()}
                </div>

                {/* Press Size */}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Press Size</div>
                  {(() => {
                    const dims = formatDimensions(result.pressH, result.pressW);
                    return (
                      <>
                        <div className="font-medium text-black dark:text-white">{dims.mm}</div>
                        <div className="text-xs text-gray-600 dark:text-neutral-400">{dims.inches}</div>
                      </>
                    );
                  })()}
                </div>

                {/* Usable Area */}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Usable Area</div>
                  {(() => {
                    const dims = formatDimensions(result.usableH, result.usableW);
                    return (
                      <>
                        <div className="font-medium text-black dark:text-white">{dims.mm}</div>
                        <div className="text-xs text-gray-600 dark:text-neutral-400">{dims.inches}</div>
                      </>
                    );
                  })()}
                </div>

                {/* Grain Alignment */}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Grain Alignment</div>
                  <div className={`font-medium ${
                    result.grainAlignment === 'Aligned'
                      ? 'text-[#5E6AD2] dark:text-[#6E56CF]'
                      : result.grainAlignment === 'Misaligned'
                        ? 'text-red-600 dark:text-red-400'
                        : 'text-black dark:text-white'
                  }`}>
                    {result.grainAlignment || 'N/A'}
                  </div>
                </div>

                {/* Layout Fit */}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Layout Fit (Down x Across)</div>
                  <div className="font-medium text-black dark:text-white">
                    {result.layoutDown && result.layoutAcross ? `${result.layoutDown} × ${result.layoutAcross}` : result.layoutFit || 'N/A'}
                  </div>
                  {result.layoutDescription && (
                    <div className="text-xs text-gray-600 dark:text-gray-400">{result.layoutDescription}</div>
                  )}
                </div>

                {/* Pages/Side */}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Pages/Side</div>
                  <div className="font-medium text-black dark:text-white">{result.pagesPerSide || 'N/A'}</div>
                </div>

                {/* Untrimmed Page */}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Untrimmed Page</div>
                  {(() => {
                    const dims = formatDimensions(result.untrimmedPageH, result.untrimmedPageW);
                    return (
                      <>
                        <div className="font-medium text-black dark:text-white">{dims.mm}</div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">{dims.inches}</div>
                      </>
                    );
                  })()}
                </div>

                {/* Imposed Area */}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Imposed Area</div>
                  {(() => {
                    const dims = formatDimensions(result.imposedAreaH, result.imposedAreaW);
                    return (
                      <>
                        <div className="font-medium text-black dark:text-white">{dims.mm}</div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">{dims.inches}</div>
                      </>
                    );
                  })()}
                </div>

                {/* Sheet Utilization */}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Sheet Utilization</div>
                  <div className="font-medium text-[#5E6AD2] dark:text-[#6E56CF]">{formatPercent(result.sheetUtilization)}</div>
                </div>

                {/* Resulting Sig */}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Resulting Sig</div>
                  <div className="font-medium text-black dark:text-white">{result.resultingSig || 'N/A'}</div>
                </div>

                {/* Total Sheets */}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Total Sheets</div>
                  <div className="font-medium text-black dark:text-white">{formatNumber(result.totalSheets)}</div>
                </div>

                {/* Cost/Sheet */}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Cost/Sheet</div>
                  <div className="font-medium text-[#5E6AD2] dark:text-[#6E56CF]">{formatCurrency(result.costPerSheet)}</div>
                </div>

                {/* Total Cost - Full Width */}
                <div className="col-span-2 pt-2">
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Total Cost</div>
                  <div className="font-bold text-xl text-[#5E6AD2] dark:text-[#6E56CF]">{formatCurrency(result.totalCost)}</div>
                </div>
              </div>

              {/* Additional Information Section */}
              <div className="grid grid-cols-2 gap-x-4 gap-y-3 mt-4 pt-4 border-t border-gray-200 dark:border-neutral-700">
                {result.bookBlockThickness && (
                  <div>
                    <div className="text-black dark:text-white text-xs font-medium mb-1">Book Block Thickness</div>
                    <div className="font-medium text-black dark:text-white">{result.bookBlockThickness.toFixed(2)} mm</div>
                  </div>
                )}
                <div>
                  <div className="text-black dark:text-white text-xs font-medium mb-1">Cost Per Book</div>
                  <div className="font-medium text-[#5E6AD2] dark:text-[#6E56CF]">{formatCurrency(result.costPerBook)}</div>
                </div>
              </div>

              {/* Remove the Source Information Footer section */}
              {/* The source info is now in the hover tooltip */}
            </>
          )}

          {!result.isError && onSelectOption && (
            <div className="mt-4">
              <Button
                size="sm"
                onClick={() => onSelectOption(result)}
                className={`w-full transition-colors duration-200 ${
                  isSelected
                    ? "bg-transparent border border-[#5E6AD2] text-[#5E6AD2] hover:bg-[#5E6AD2]/10 dark:border-[#6E56CF] dark:text-[#6E56CF] dark:hover:bg-[#6E56CF]/10"
                    : "bg-[#5E6AD2] hover:bg-[#5E6AD2]/90 dark:bg-[#6E56CF] dark:hover:bg-[#9E8CFC] text-white"
                }`}
              >
                {isSelected ? "Selected" : "Select this Option"}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Card className="w-full" data-glow>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <span className="w-2 h-2 bg-[#5E6AD2] dark:bg-[#6E56CF] rounded-full mr-2"></span>
            {title}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              onClick={onCalculate}
              disabled={isButtonDisabled} // Use the new prop here
              size="sm"
              className="flex items-center gap-2 h-8 px-3 text-xs bg-[#5E6AD2] hover:bg-[#5E6AD2]/90 dark:bg-[#6E56CF] dark:hover:bg-[#9E8CFC] text-white transition-colors duration-200"
            >
              <Calculator className="h-3 w-3" />
              {isCalculating ? 'Calculating...' :
               countdownSeconds > 0 ? `Wait ${countdownSeconds}s` :
               'Calculate Costs'}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-8 w-8 p-0"
            >
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      {isExpanded && (
        <CardContent className="pt-0">
          {isCalculating ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#5E6AD2] dark:border-[#6E56CF]"></div>
              <span className="ml-3 text-gray-600 dark:text-neutral-400">Calculating optimal paper options...</span>
            </div>
          ) : !hasResults ? (
            <div className="text-center py-8 text-gray-500 dark:text-neutral-400">
              <Calculator className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No calculation results yet.</p>
              <p className="text-sm">Click &quot;Calculate Costs&quot; to analyze paper options.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Summary */}
              {bestResult && (
                <div className="bg-[#5E6AD2]/5 dark:bg-[#6E56CF]/10 rounded-lg p-4 border border-[#5E6AD2]/20 dark:border-[#6E56CF]/30">
                  <h4 className="font-semibold text-[#5E6AD2] dark:text-[#6E56CF] mb-3">Best Option Summary</h4>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-black dark:text-white font-medium">Paper:</span>
                      <span className="ml-2 font-medium text-black dark:text-white">{bestResult.paperName}</span>
                    </div>
                    <div>
                      <span className="text-black dark:text-white font-medium">Cost/Book:</span>
                      <span className="ml-2 font-semibold text-[#5E6AD2] dark:text-[#6E56CF]">{formatCurrency(bestResult.costPerBook)}</span>
                    </div>
                    <div>
                      <span className="text-black dark:text-white font-medium">Utilization:</span>
                      <span className="ml-2 font-semibold text-[#5E6AD2] dark:text-[#6E56CF]">{formatPercent(bestResult.sheetUtilization)}</span>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Optimal Results */}
              {optimalResults.length > 0 && (
                <div>
                  <h4 className="font-semibold text-[#5E6AD2] dark:text-[#6E56CF] mb-4 flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Optimal options ({optimalResults.length})
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {optimalResults.map(result => (
                      <ResultCard key={result.id} result={result} />
                    ))}
                  </div>
                </div>
              )}

              {/* Suboptimal Results */}
              {suboptimalResults.length > 0 && (
                <div>
                  <h4 className="font-semibold text-gray-700 dark:text-neutral-300 mb-4 flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Alternative options ({suboptimalResults.length})
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {suboptimalResults.map(result => (
                      <ResultCard key={result.id} result={result} />
                    ))}
                  </div>
                </div>
              )}

              {/* Error Results */}
              {errorResults.length > 0 && (
                <div>
                  <h4 className="font-semibold text-red-600 dark:text-red-400 mb-4 flex items-center">
                    <XCircle className="h-4 w-4 mr-2" />
                    Error results ({errorResults.length})
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {errorResults.map(result => (
                      <ResultCard key={result.id} result={result} />
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}
