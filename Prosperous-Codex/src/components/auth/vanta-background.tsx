"use client";
import { useEffect, useRef, useState, useCallback } from 'react';

// Forward declaration for VANTA object for TypeScript
interface VantaEffect {
  destroy: () => void;
}

interface VantaLib {
  BIRDS: (options: {
    el: HTMLElement;
    mouseControls: boolean;
    touchControls: boolean;
    gyroControls: boolean;
    minHeight: number;
    minWidth: number;
    scale: number;
    scaleMobile: number;
    color1: number;
    color2: number;
    colorMode: string;
    birdSize: number;
    wingSpan: number;
    speedLimit: number;
    separation: number;
    alignment: number;
    cohesion: number;
    quantity: number;
  }) => VantaEffect;
}

declare global {
  interface Window {
    VANTA: VantaLib;
    THREE: unknown; // THREE.js library - keeping as unknown since we don't use it directly
  }
}

interface VantaBackgroundProps {
  onLoadingChange?: (isLoading: boolean) => void;
}

const VantaBackground = ({ onLoadingChange }: VantaBackgroundProps = {}) => {
  const vantaRef = useRef<HTMLDivElement>(null);
  const vantaEffect = useRef<VantaEffect | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const scriptsLoaded = useRef(false);

  // Load scripts dynamically
  const loadScript = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      // Check if script is already loaded
      if (document.querySelector(`script[src="${src}"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = src;
      script.async = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
      document.head.appendChild(script);
    });
  };

  // Load all required scripts
  const loadVantaScripts = useCallback(async (): Promise<void> => {
    try {
      // Load Three.js first
      await loadScript('https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js');

      // Then load Vanta
      await loadScript('https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.rings.min.js');

      // Brief wait for scripts to be available
      await new Promise(resolve => setTimeout(resolve, 100));

      scriptsLoaded.current = true;
    } catch (error) {
      console.error('Failed to load Vanta scripts:', error);
      throw error;
    }
  }, []);

  // Check if scripts are loaded
  const areScriptsLoaded = (): boolean => {
    return typeof window !== 'undefined' &&
           window.THREE &&
           window.VANTA &&
           window.VANTA.RINGS;
  };

  // Check if element has proper dimensions
  const hasValidDimensions = (element: HTMLElement): boolean => {
    const rect = element.getBoundingClientRect();
    return rect.width > 0 && rect.height > 0;
  };

  // Initialize Vanta effect
  const initializeVanta = useCallback((): boolean => {
    if (!vantaRef.current || vantaEffect.current || !areScriptsLoaded()) {
      return false;
    }

    if (!hasValidDimensions(vantaRef.current)) {
      return false;
    }

    try {
      vantaEffect.current = window.VANTA.RINGS({
        el: vantaRef.current,
        mouseControls: true,
        touchControls: true,
        gyroControls: false,
        minHeight: 200.00,
        minWidth: 200.00,
        scale: 1.00,
        scaleMobile: 1.00,
        backgroundColor: 0x030712,
        color: 0xd1d5db,
      });

      setIsLoading(false);
      onLoadingChange?.(false);
      return true;
    } catch (error) {
      console.error('Failed to initialize Vanta effect:', error);
      return false;
    }
  }, [onLoadingChange]);

  // Simple initialization attempt
  const attemptInitialization = useCallback(() => {
    if (initializeVanta()) {
      return; // Success
    }

    // If failed, just show fallback
    console.warn('Vanta initialization failed, showing fallback background');
    setIsLoading(false);
    onLoadingChange?.(false);
  }, [initializeVanta, onLoadingChange]);

  useEffect(() => {
    let mounted = true;
    setIsLoading(true);
    onLoadingChange?.(true);

    const initializeAfterScriptsLoad = async () => {
      if (!mounted) return;

      try {
        // First, try to use existing scripts
        if (areScriptsLoaded()) {
          attemptInitialization();
          return;
        }

        // If not available, load them dynamically
        await loadVantaScripts();

        // Attempt initialization after loading
        if (mounted) {
          attemptInitialization();
        }

      } catch (error) {
        console.error('Failed to load Vanta scripts:', error);
        if (mounted) {
          setIsLoading(false);
          onLoadingChange?.(false);
        }
      }
    };

    // Start the initialization process
    initializeAfterScriptsLoad();

    return () => {
      mounted = false;
      if (vantaEffect.current && typeof vantaEffect.current.destroy === 'function') {
        vantaEffect.current.destroy();
        vantaEffect.current = null;
      }
    };
  }, [attemptInitialization, loadVantaScripts, onLoadingChange]);

  return (
    <div ref={vantaRef} className="absolute inset-0 w-full h-full overflow-hidden">
      {isLoading && (
        <div className="absolute inset-0 bg-gray-950 flex items-center justify-center">
          <div className="animate-pulse text-gray-400 text-sm">Loading background...</div>
        </div>
      )}
      {/* Fallback gradient background if Vanta fails */}
      {!isLoading && !vantaEffect.current && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-950 via-gray-900 to-gray-950 opacity-80">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]"></div>
        </div>
      )}
    </div>
  );
};

export default VantaBackground;
