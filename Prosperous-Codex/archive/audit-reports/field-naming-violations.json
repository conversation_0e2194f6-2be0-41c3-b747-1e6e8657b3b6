{"timestamp": "2025-06-22T18:49:31.229Z", "summary": {"totalFiles": 128, "violationCount": 548, "fixableViolations": 548, "criticalViolations": 548}, "violations": [{"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 59, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "getUserPaperOptions(userId: number, category?: string): UserPaperOption[] {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 65, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const params: any[] = [userId];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 85, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "addPaperOption(userId: number, paperData: Omit<UserPaperOption, 'id' | 'user_id' | 'created_at' | 'updated_at'>): UserPaperOption | null {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 95, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 124, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "saveCalculation(userId: number, calculationData: Omit<SavedCalculation, 'id' | 'user_id' | 'created_at' | 'updated_at'>): SavedCalculation | null {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 132, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 154, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "getSavedCalculations(userId: number): SavedCalculation[] {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 162, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "return stmt.all(userId) as SavedCalculation[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 172, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "updateUserSelection(userId: number, componentType: string, componentId: string, componentData: any): boolean {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 180, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 196, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "getUserSelections(userId: number): UserSelection[] {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 204, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "return stmt.all(userId) as UserSelection[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 214, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "removeUserSelection(userId: number, componentType: string, componentId: string): boolean {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 221, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const result = stmt.run(userId, componentType, componentId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 232, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "addCalculationHistory(userId: number, historyData: Omit<CalculationHistory, 'id' | 'user_id' | 'created_at'>): boolean {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 240, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 257, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "getCalculationHistory(userId: number, limit: number = 50): CalculationHistory[] {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 266, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "return stmt.all(userId, limit) as CalculationHistory[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 276, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "deleteCalculation(userId: number, calculationId: number): boolean {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 283, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const result = stmt.run(calculationId, userId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 294, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "deletePaperOption(userId: number, paperOptionId: number): boolean {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/paper-cost-service.ts", "line": 301, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const result = stmt.run(paperOptionId, userId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 8, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 11, "field": "createdAt", "expected": "created_at", "actual": "createdAt", "layer": "database", "context": "createdAt: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 15, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 33, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const { userId, rememberMe = false } = data;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 44, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.cleanupUserSessions(userId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 52, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "stmt.run(userId, sessionToken, expiresAt);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 88, "field": "createdAt", "expected": "created_at", "actual": "createdAt", "layer": "database", "context": "createdAt: result.created_at", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 116, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "async invalidateAllUserSessions(userId: number): Promise<boolean> {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 122, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const result = stmt.run(userId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 133, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "getUserSessions(userId: number): Session[] {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 142, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "return stmt.all(userId) as Session[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 176, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "private async cleanupUserSessions(userId: number): Promise<void> {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/session-service.ts", "line": 188, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "stmt.run(userId, userId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 49, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "async createProject(userId: number, projectData: CreateProjectInput): Promise<Project> {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 53, "field": "fullDescription", "expected": "full_description", "actual": "fullDescription", "layer": "database", "context": "fullDescription,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 54, "field": "eventLog", "expected": "event_log", "actual": "eventLog", "layer": "database", "context": "eventLog,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 58, "field": "dueDate", "expected": "due_date", "actual": "dueDate", "layer": "database", "context": "dueDate,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 59, "field": "assignedTo", "expected": "assigned_to", "actual": "assignedTo", "layer": "database", "context": "assignedTo,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 71, "field": "fullDescription", "expected": "full_description", "actual": "fullDescription", "layer": "database", "context": "const sanitizedFullDescription = fullDescription ? sanitize.projectDescription(fullDescription) : null;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 72, "field": "eventLog", "expected": "event_log", "actual": "eventLog", "layer": "database", "context": "const sanitizedEventLog = eventLog ? sanitize.projectEventLog(eventLog) : null;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 100, "field": "dueDate", "expected": "due_date", "actual": "dueDate", "layer": "database", "context": "dueDate || null,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 102, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 103, "field": "assignedTo", "expected": "assigned_to", "actual": "assignedTo", "layer": "database", "context": "assignedTo || null", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 106, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const projectId = result.lastInsertRowid as number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 116, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "tagStmt.run(projectId, tag);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 126, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "activityStmt.run(projectId, userId, 'creation', `Created project \"${sanitizedTitle}\"`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 126, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "activityStmt.run(projectId, userId, 'creation', `Created project \"${sanitizedTitle}\"`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 128, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "return projectId;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 131, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const projectId = transaction();", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 134, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 153, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "getProjectById(projectId: number, options?: {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 161, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "if (!projectId || projectId <= 0) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 162, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 189, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const projectRow = projectStmt.get(projectId) as ProjectDbRow;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 191, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.notFound('Project', projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 201, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const tags = tagsStmt.all(projectId) as { tag_name: string }[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 207, "field": "teamMembers", "expected": "team_members", "actual": "teamMembers", "layer": "database", "context": "teamMembers: [],", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 221, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const teamMemberRows = teamStmt.all(projectId) as ProjectTeamMemberDbRow[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 223, "field": "teamMembers", "expected": "team_members", "actual": "teamMembers", "layer": "database", "context": "result.teamMembers = DataMapper.teamMembersFromDb(teamMemberRows);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 227, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "result.files = this.getProjectFiles(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 231, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "result.comments = this.getProjectComments(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 235, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "result.tasks = this.getProjectTasks(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 239, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "result.activity = this.getProjectActivity(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 249, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve project ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 256, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "getProjectByIdFull(projectId: number): Project {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 257, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "return this.getProjectById(projectId, {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 269, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "getUserProjects(userId: number, options?: {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 276, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "if (!userId || userId <= 0) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 277, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "throw ErrorFactory.invalidInput('userId', userId, 'User ID must be a positive number');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 309, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const params: any[] = [userId, userId, userId];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 337, "field": "teamMembers", "expected": "team_members", "actual": "teamMembers", "layer": "database", "context": "teamMembers: [],", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 373, "field": "teamMembers", "expected": "team_members", "actual": "teamMembers", "layer": "database", "context": "result.teamMembers = teamMemberRows.map(DataMapper.teamMemberFromDb);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 400, "field": "teamMembers", "expected": "team_members", "actual": "teamMembers", "layer": "database", "context": "teamMembers: [],", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 410, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve projects for user ${userId}: ${error instanceof Error ? error.message : 'Unknown error'}`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 417, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "getUserProjectCount(userId: number, status?: string): number {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 419, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "if (!userId || userId <= 0) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 420, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "throw ErrorFactory.invalidInput('userId', userId, 'User ID must be a positive number');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 437, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const params: any[] = [userId, userId, userId];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 450, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve project count for user ${userId}: ${error instanceof Error ? error.message : 'Unknown error'}`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 457, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "async updateProject(projectId: number, user: { id: string; role: string }, updateData: UpdateProjectInput): Promise<void> {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 459, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "if (!projectId || projectId <= 0) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 460, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 482, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 498, "field": "fullDescription", "expected": "full_description", "actual": "fullDescription", "layer": "database", "context": "'fullDescription': 'full_description',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 499, "field": "eventLog", "expected": "event_log", "actual": "eventLog", "layer": "database", "context": "'eventLog': 'event_log',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 500, "field": "dueDate", "expected": "due_date", "actual": "dueDate", "layer": "database", "context": "'dueDate': 'due_date',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 501, "field": "assignedTo", "expected": "assigned_to", "actual": "assignedTo", "layer": "database", "context": "'assignedTo': 'assigned_to',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 503, "field": "created<PERSON>y", "expected": "created_by", "actual": "created<PERSON>y", "layer": "database", "context": "'createdBy': 'created_by',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 504, "field": "createdAt", "expected": "created_at", "actual": "createdAt", "layer": "database", "context": "'createdAt': 'created_at',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 505, "field": "updatedAt", "expected": "updated_at", "actual": "updatedAt", "layer": "database", "context": "'updatedAt': 'updated_at'", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 517, "field": "fullDescription", "expected": "full_description", "actual": "fullDescription", "layer": "database", "context": "if (key === 'fullDescription' || key === 'description') {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 519, "field": "eventLog", "expected": "event_log", "actual": "eventLog", "layer": "database", "context": "} else if (key === 'eventLog') {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 533, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "values.push(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 542, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const currentProject = this.getProjectById(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 552, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const userId = parseInt(user.id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 554, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.logActivity(userId, 'status_change', `Changed status to ${updateData.status}`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 554, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.logActivity(userId, 'status_change', `Changed status to ${updateData.status}`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 556, "field": "assignedTo", "expected": "assigned_to", "actual": "assignedTo", "layer": "database", "context": "if (updateData.assignedTo !== undefined) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 557, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.logActivity(userId, 'assignment', `Updated assignment`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 557, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.logActivity(userId, 'assignment', `Updated assignment`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 559, "field": "dueDate", "expected": "due_date", "actual": "dueDate", "layer": "database", "context": "if (updateData.dueDate) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 560, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.logActivity(userId, 'due_date', `Updated due date`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 560, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.logActivity(userId, 'due_date', `Updated due date`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 563, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.logActivity(userId, 'completion', `Completed project`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 563, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.logActivity(userId, 'completion', `Completed project`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 571, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'full_description', updateData.full_description, currentProject?.fullDescription);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 571, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'full_description', updateData.full_description, currentProject?.fullDescription);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 571, "field": "fullDescription", "expected": "full_description", "actual": "fullDescription", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'full_description', updateData.full_description, currentProject?.fullDescription);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 574, "field": "fullDescription", "expected": "full_description", "actual": "fullDescription", "layer": "database", "context": "else if (updateData.fullDescription !== undefined) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 575, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'full_description', updateData.fullDescription, currentProject?.fullDescription);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 575, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'full_description', updateData.fullDescription, currentProject?.fullDescription);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 575, "field": "fullDescription", "expected": "full_description", "actual": "fullDescription", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'full_description', updateData.fullDescription, currentProject?.fullDescription);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 580, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'event_log', updateData.event_log, currentProject?.eventLog);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 580, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'event_log', updateData.event_log, currentProject?.eventLog);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 580, "field": "eventLog", "expected": "event_log", "actual": "eventLog", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'event_log', updateData.event_log, currentProject?.eventLog);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 583, "field": "eventLog", "expected": "event_log", "actual": "eventLog", "layer": "database", "context": "else if (updateData.eventLog !== undefined) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 584, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'event_log', updateData.eventLog, currentProject?.eventLog);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 584, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'event_log', updateData.eventLog, currentProject?.eventLog);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 584, "field": "eventLog", "expected": "event_log", "actual": "eventLog", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'event_log', updateData.eventLog, currentProject?.eventLog);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 595, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.databaseOperation('update', `Failed to update project ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 602, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "async toggleProjectVisibility(projectId: number, user: { id: string; role: string }): Promise<{ visibility: 'public' | 'private' }> {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 604, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "if (!projectId || projectId <= 0) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 605, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 614, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 634, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const result = stmt.run(newVisibility, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 642, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const userId = parseInt(user.id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 643, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.logActivity(userId, 'update', `Changed project visibility to ${newVisibility}`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 643, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.logActivity(userId, 'update', `Changed project visibility to ${newVisibility}`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 655, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.databaseOperation('update', `Failed to toggle project visibility ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 662, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "async deleteProject(projectId: number, user: { id: string; role: string }): Promise<void> {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 664, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "if (!projectId || projectId <= 0) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 665, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 674, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 689, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const result = stmt.run(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 697, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const userId = parseInt(user.id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 698, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.logActivity(userId, 'update', `Deleted project \"${project.title}\"`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 698, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.logActivity(userId, 'update', `Deleted project \"${project.title}\"`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 708, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.databaseOperation('delete', `Failed to delete project ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 715, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "getProjectComments(projectId: number): ProjectComment[] {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 729, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const rows = stmt.all(projectId) as ProjectCommentDbRow[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 740, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "async addComment(projectId: number, user: { id: string; role: string }, content: string, parentCommentId?: number): Promise<ProjectComment> {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 742, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "if (!projectId || projectId <= 0) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 743, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 757, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 767, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const userId = parseInt(user.id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 774, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const result = stmt.run(projectId, parentCommentId || null, userId, sanitizedContent);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 774, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const result = stmt.run(projectId, parentCommentId || null, userId, sanitizedContent);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 778, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.logActivity(userId, 'comment', `Added comment`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 778, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.logActivity(userId, 'comment', `Added comment`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 844, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(existingComment.projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 853, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const userId = parseInt(user.id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 854, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const isAuthor = existingComment.authorId === userId;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 873, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "historyStmt.run(commentId, newVersionNumber, existingComment.content, userId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 887, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const result = updateStmt.run(sanitizedContent, newVersionNumber, userId, commentId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 900, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 903, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "existingComment.projectId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 972, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(existingComment.projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 981, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const userId = parseInt(user.id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 982, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const isAuthor = existingComment.authorId === userId;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1003, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1006, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "existingComment.projectId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1059, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(comment.projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1095, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "async createTask(projectId: number, user: { id: string; role: string }, taskData: CreateTaskInput): Promise<Task> {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1101, "field": "dueDate", "expected": "due_date", "actual": "dueDate", "layer": "database", "context": "dueDate,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1102, "field": "assignedTo", "expected": "assigned_to", "actual": "assignedTo", "layer": "database", "context": "assignedTo,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1103, "field": "parentTaskId", "expected": "parent_task_id", "actual": "parentTaskId", "layer": "database", "context": "parentTaskId", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1107, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "if (!projectId || projectId <= 0) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1108, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1132, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1141, "field": "parentTaskId", "expected": "parent_task_id", "actual": "parentTaskId", "layer": "database", "context": "if (parentTaskId) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1145, "field": "parentTaskId", "expected": "parent_task_id", "actual": "parentTaskId", "layer": "database", "context": "const parentTask = parentTaskStmt.get(parentTaskId) as { project_id: number } | undefined;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1148, "field": "parentTaskId", "expected": "parent_task_id", "actual": "parentTaskId", "layer": "database", "context": "throw ErrorFactory.notFound('Parent task', parentTaskId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1151, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "if (parentTask.project_id !== projectId) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1156, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const userId = parseInt(user.id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1167, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "projectId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1168, "field": "parentTaskId", "expected": "parent_task_id", "actual": "parentTaskId", "layer": "database", "context": "parentTaskId || null,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1173, "field": "dueDate", "expected": "due_date", "actual": "dueDate", "layer": "database", "context": "dueDate || null,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1174, "field": "assignedTo", "expected": "assigned_to", "actual": "assignedTo", "layer": "database", "context": "assignedTo || null,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1175, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1181, "field": "parentTaskId", "expected": "parent_task_id", "actual": "parentTaskId", "layer": "database", "context": "const taskType = parentTaskId ? 'subtask' : 'task';", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1187, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "activityStmt.run(projectId, taskId, userId, 'task_creation', `Created ${taskType} \"${sanitizedTitle}\"`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1187, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "activityStmt.run(projectId, taskId, userId, 'task_creation', `Created ${taskType} \"${sanitizedTitle}\"`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1190, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "this.updateProjectProgressSync(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1236, "field": "parentTaskId", "expected": "parent_task_id", "actual": "parentTaskId", "layer": "database", "context": "if (!task.parentTaskId) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1263, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "getProjectTasks(projectId: number): Task[] {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1265, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "if (!projectId || projectId <= 0) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1266, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1280, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const mainTaskRows = mainTasksStmt.all(projectId) as TaskDbRow[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1312, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve tasks for project ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1346, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(task.projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1377, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "task.projectId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1392, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "this.updateProjectProgressSync(task.projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1417, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(task.projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1428, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const userId = parseInt(user.id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1439, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "task.projectId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1440, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1450, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const result = stmt.run(taskId, userId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1454, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "this.updateProjectProgressSync(task.projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1475, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "projectId: number,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1476, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId: number,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1492, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const editCountRow = editCountStmt.get(projectId) as { edit_count: number | null };", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1502, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "historyStmt.run(projectId, fieldName, newVersionNumber, currentContent, userId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1502, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "historyStmt.run(projectId, fieldName, newVersionNumber, currentContent, userId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1513, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "updateEditTrackingStmt.run(newVersionNumber, userId, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1513, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "updateEditTrackingStmt.run(newVersionNumber, userId, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1520, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1523, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "projectId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1542, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "projectId: number,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1543, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId: number,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1549, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1551, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.notFound('project', projectId, 'Project not found');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1554, "field": "fullDescription", "expected": "full_description", "actual": "fullDescription", "layer": "database", "context": "const currentContent = fieldName === 'full_description' ? project.fullDescription : project.eventLog;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1554, "field": "eventLog", "expected": "event_log", "actual": "eventLog", "layer": "database", "context": "const currentContent = fieldName === 'full_description' ? project.fullDescription : project.eventLog;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1557, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, fieldName, newContent, currentContent);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1557, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.handleProjectFieldEditWithCurrentData(projectId, userId, fieldName, newContent, currentContent);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1567, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "projectId: number,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1572, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "if (!projectId || projectId <= 0) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1573, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1582, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1584, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "throw ErrorFactory.notFound('project', projectId, 'Project not found');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1605, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const historyRows = historyStmt.all(projectId, fieldName) as ProjectEditHistoryDbRow[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1621, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId: number,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1624, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "projectId?: number,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1635, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "projectId || null,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1637, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1650, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "getProjectActivity(projectId: number, options?: {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1665, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const params: any[] = [projectId];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1687, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "getProjectActivityCount(projectId: number, taskId?: number): number {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1695, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const params: any[] = [projectId];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1714, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "getUserActivity(userId: number): ActivityLogEntry[] {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1730, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "return stmt.all(userId, userId, userId) as ActivityLogEntry[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1740, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "private updateProjectProgressSync(projectId: number): void {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1746, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const allTasks = stmt.all(projectId) as { status: string }[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1753, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "updateStmt.run(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1764, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "updateStmt.run(progress, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1773, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "private async updateProjectProgress(projectId: number): Promise<void> {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1775, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "this.updateProjectProgressSync(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1796, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "projectId: number,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1808, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1818, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const userId = parseInt(user.id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1832, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "projectId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1838, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1844, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.logActivity(userId, 'upload', `Uploaded file \"${sanitizedFileName}\"`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1844, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "await this.logActivity(userId, 'upload', `Uploaded file \"${sanitizedFileName}\"`, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1876, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "getProjectFiles(projectId: number, options?: {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1892, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "return stmt.all(projectId, limit, offset) as ProjectFile[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1902, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "getProjectFileCount(projectId: number): number {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1910, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const result = stmt.get(projectId) as { count: number };", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1978, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "const userId = parseInt(user.id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 1988, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "await this.logActivity(userId, 'upload', `Deleted file \"${file.file_name}\"`, file.project_id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2011, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "getProjectComments(projectId: number, options?: {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2039, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const topLevelRows = topLevelStmt.all(projectId, limit, offset) as ProjectCommentDbRow[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2083, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "getProjectCommentCount(projectId: number, includeReplies: boolean = true): number {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2096, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const result = stmt.get(projectId) as { count: number };", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2108, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "getProjectTeamMembers(projectId: number): ProjectTeamMember[] {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2118, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const rows = stmt.all(projectId) as ProjectTeamMemberDbRow[];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2121, "field": "teamMembers", "expected": "team_members", "actual": "teamMembers", "layer": "database", "context": "return DataMapper.teamMembersFromDb(rows);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2132, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "projectId: number,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2134, "field": "userEmail", "expected": "user_email", "actual": "userEmail", "layer": "database", "context": "userEmail: string,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2139, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2150, "field": "userEmail", "expected": "user_email", "actual": "userEmail", "layer": "database", "context": "const sanitizedEmail = sanitize.userEmail(userEmail);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2152, "field": "userEmail", "expected": "user_email", "actual": "userEmail", "layer": "database", "context": "throw ErrorFactory.invalidInput('userEmail', userEmail, 'Valid email address is required');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2162, "field": "userEmail", "expected": "user_email", "actual": "userEmail", "layer": "database", "context": "throw ErrorFactory.notFound('User', userEmail);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2169, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const existing = existingStmt.get(projectId, user.id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2173, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "projectId,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2174, "field": "userEmail", "expected": "user_email", "actual": "userEmail", "layer": "database", "context": "userEmail,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2175, "field": "userId", "expected": "user_id", "actual": "userId", "layer": "database", "context": "userId: user.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2189, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const result = stmt.run(projectId, user.id, role, addedBy);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2198, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "activityStmt.run(projectId, addedBy, 'assignment', `Added ${sanitizedEmail} to project team`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2238, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "async removeTeamMember(projectId: number, memberId: number, removedByUser: { id: string; role: string }): Promise<boolean> {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2241, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const project = this.getProjectById(projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2258, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const member = memberStmt.get(memberId, projectId) as (ProjectTeamMember & { email: string }) | undefined;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2273, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "const result = deleteStmt.run(memberId, projectId);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/task-master-service.ts", "line": 2282, "field": "projectId", "expected": "project_id", "actual": "projectId", "layer": "database", "context": "activityStmt.run(projectId, removedBy, 'assignment', `Removed ${member.email} from project team`);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/user-service.ts", "line": 112, "field": "createdAt", "expected": "created_at", "actual": "createdAt", "layer": "database", "context": "createdAt: user.created_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/user-service.ts", "line": 135, "field": "createdAt", "expected": "created_at", "actual": "createdAt", "layer": "database", "context": "createdAt: user.created_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/user-service.ts", "line": 168, "field": "createdAt", "expected": "created_at", "actual": "createdAt", "layer": "database", "context": "createdAt: user.created_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/database/user-service.ts", "line": 291, "field": "createdAt", "expected": "created_at", "actual": "createdAt", "layer": "database", "context": "createdAt: user.created_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/route.ts", "line": 258, "field": "team_members", "expected": "teamMembers", "actual": "team_members", "layer": "api", "context": "tables: ['projects', 'project_comments', 'activity_log', 'project_team_members', 'project_files'],", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/route.ts", "line": 352, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "api", "context": "LEFT JOIN users u1 ON p.created_by = u1.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/route.ts", "line": 353, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "api", "context": "LEFT JOIN users u2 ON p.assigned_to = u2.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/route.ts", "line": 360, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "api", "context": "LEFT JOIN projects p ON pc.project_id = p.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/route.ts", "line": 366, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "api", "context": "LEFT JOIN users u ON al.user_id = u.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/route.ts", "line": 367, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "api", "context": "LEFT JOIN projects p ON al.project_id = p.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/route.ts", "line": 380, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "api", "context": "LEFT JOIN users u ON sc.user_id = u.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/route.ts", "line": 386, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "api", "context": "LEFT JOIN users u ON ch.user_id = u.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/route.ts", "line": 397, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "api", "context": "SELECT id, email, username, role, created_at, last_login, is_active", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/route.ts", "line": 404, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "api", "context": "LEFT JOIN users u ON us.user_id = u.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 164, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "api", "context": "LEFT JOIN users creator ON p.created_by = creator.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 165, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "api", "context": "LEFT JOIN users assignee ON p.assigned_to = assignee.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 166, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "api", "context": "ORDER BY p.created_at DESC", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 178, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "api", "context": "LEFT JOIN projects p ON pc.project_id = p.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 179, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "api", "context": "ORDER BY pc.created_at DESC", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 187, "field": "user_email", "expected": "userEmail", "actual": "user_email", "layer": "api", "context": "u.email as user_email,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 190, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "api", "context": "LEFT JOIN users u ON al.user_id = u.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 191, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "api", "context": "LEFT JOIN projects p ON al.project_id = p.id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 192, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "api", "context": "ORDER BY al.created_at DESC", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 202, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "api", "context": "u.created_at", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 205, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "api", "context": "SELECT DISTINCT created_by FROM projects", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 207, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "api", "context": "SELECT DISTINCT assigned_to FROM projects WHERE assigned_to IS NOT NULL", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/backup/task-master/route.ts", "line": 211, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "api", "context": "SELECT DISTINCT user_id FROM activity_log", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 12, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "full_description: 'Complete overhaul of the company website including new branding, improved user experience, mobile responsiveness, and modern design patterns. This project involves collaboration with the design team and requires extensive testing across multiple devices and browsers.',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 16, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "api", "context": "due_date: '2024-12-15',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 22, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "full_description: 'Plan and execute migration of legacy database systems to modern cloud infrastructure. Includes data validation, backup procedures, and minimal downtime deployment strategy.',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 26, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "api", "context": "due_date: '2024-12-20',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 32, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "full_description: 'Design and implement a comprehensive user authentication system with multi-factor authentication, password policies, and session management.',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 36, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "api", "context": "due_date: '2025-01-10',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 44, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "full_description: 'Create native mobile applications for both iOS and Android platforms with cross-platform functionality. Features include user authentication, real-time notifications, and offline capabilities.',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 48, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "api", "context": "due_date: '2025-01-30',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 54, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "full_description: 'Develop detailed API documentation including endpoint descriptions, request/response examples, authentication methods, and integration guides for third-party developers.',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 58, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "api", "context": "due_date: '2024-12-30',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 64, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "full_description: 'Analyze and improve application performance including database query optimization, caching strategies, and frontend bundle size reduction.',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 68, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "api", "context": "due_date: '2025-02-15',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 76, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "full_description: 'Comprehensive brand guidelines including logo usage, typography, color palette, and brand voice. This document serves as the foundation for all company communications and design work.',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 80, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "api", "context": "due_date: '2024-11-30',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 87, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "full_description: 'Complete security audit of all systems including penetration testing, vulnerability assessment, and compliance review. All critical issues have been addressed.',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 91, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "api", "context": "due_date: '2024-11-15',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 98, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "full_description: 'Responsive email templates for notifications, newsletters, and transactional emails. All templates are tested across major email clients and include dark mode support.',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/admin/seed-data/route.ts", "line": 102, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "api", "context": "due_date: '2024-11-20',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 59, "field": "sheet_width", "expected": "sheetWidth", "actual": "sheet_width", "layer": "api", "context": "sheetWidth: 'sheet_width',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 60, "field": "sheet_width", "expected": "sheetWidth", "actual": "sheet_width", "layer": "api", "context": "sheet_width: 'sheet_width',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 61, "field": "grain_direction", "expected": "grainDirection", "actual": "grain_direction", "layer": "api", "context": "grainDirection: 'grain_direction',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 62, "field": "grain_direction", "expected": "grainDirection", "actual": "grain_direction", "layer": "api", "context": "grain_direction: 'grain_direction',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 69, "field": "sheet_width", "expected": "sheetWidth", "actual": "sheet_width", "layer": "api", "context": "const requiredWidthFields = ['sheetWidth', 'sheet_width'];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 70, "field": "grain_direction", "expected": "grainDirection", "actual": "grain_direction", "layer": "api", "context": "const requiredGrainFields = ['grainDirection', 'grain_direction'];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 91, "field": "sheet_width", "expected": "sheetWidth", "actual": "sheet_width", "layer": "api", "context": "if (!paperData.sheetWidth && !paperData.sheet_width) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 93, "field": "sheet_width", "expected": "sheetWidth", "actual": "sheet_width", "layer": "api", "context": "{ error: 'Missing required field: sheetWidth or sheet_width' },", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 99, "field": "grain_direction", "expected": "grainDirection", "actual": "grain_direction", "layer": "api", "context": "if (!paperData.grainDirection && !paperData.grain_direction) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 101, "field": "grain_direction", "expected": "grainDirection", "actual": "grain_direction", "layer": "api", "context": "{ error: 'Missing required field: grainDirection or grain_direction' },", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 115, "field": "sheet_width", "expected": "sheetWidth", "actual": "sheet_width", "layer": "api", "context": "const sheetWidth = paperData.sheetWidth || paperData.sheet_width;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 121, "field": "sheet_width", "expected": "sheetWidth", "actual": "sheet_width", "layer": "api", "context": "{ error: 'Invalid sheetWidth/sheet_width. Must be a number' },", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 141, "field": "sheet_height", "expected": "sheetHeight", "actual": "sheet_height", "layer": "api", "context": "const sheetHeight = paperData.sheetHeight || paperData.sheet_height;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 144, "field": "sheet_height", "expected": "sheetHeight", "actual": "sheet_height", "layer": "api", "context": "{ error: 'Invalid sheetHeight/sheet_height. Must be a number' },", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 149, "field": "cost_per_ream", "expected": "costPerReam", "actual": "cost_per_ream", "layer": "api", "context": "if (paperData.cost_per_ream !== undefined && paperData.cost_per_ream !== null && isNaN(parseFloat(paperData.cost_per_ream))) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 151, "field": "cost_per_ream", "expected": "costPerReam", "actual": "cost_per_ream", "layer": "api", "context": "{ error: 'Invalid cost_per_ream. Must be a number' },", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 156, "field": "cost_per_ton", "expected": "costPerTon", "actual": "cost_per_ton", "layer": "api", "context": "if (paperData.cost_per_ton !== undefined && paperData.cost_per_ton !== null && isNaN(parseFloat(paperData.cost_per_ton))) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 158, "field": "cost_per_ton", "expected": "costPerTon", "actual": "cost_per_ton", "layer": "api", "context": "{ error: 'Invalid cost_per_ton. Must be a number' },", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 171, "field": "sheet_height", "expected": "sheetHeight", "actual": "sheet_height", "layer": "api", "context": "sheet_height: sheetHeight ? parseFloat(sheetHeight) : undefined,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 172, "field": "sheet_width", "expected": "sheetWidth", "actual": "sheet_width", "layer": "api", "context": "sheet_width: parseFloat(sheetWidth),", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 173, "field": "grain_direction", "expected": "grainDirection", "actual": "grain_direction", "layer": "api", "context": "grain_direction: paperData.grainDirection || paperData.grain_direction,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 175, "field": "cost_per_ream", "expected": "costPerReam", "actual": "cost_per_ream", "layer": "api", "context": "cost_per_ream: (paperData.costPerReam || paperData.cost_per_ream) ? parseFloat(paperData.costPerReam || paperData.cost_per_ream) : undefined,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 177, "field": "cost_per_ton", "expected": "costPerTon", "actual": "cost_per_ton", "layer": "api", "context": "cost_per_ton: (paperData.costPerTon || paperData.cost_per_ton) ? parseFloat(paperData.costPerTon || paperData.cost_per_ton) : undefined,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/paper-cost/paper-options/route.ts", "line": 178, "field": "is_custom", "expected": "isCustom", "actual": "is_custom", "layer": "api", "context": "is_custom: paperData.isCustom || paperData.is_custom || true", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/activity/route.ts", "line": 19, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "api", "context": "const projectId = searchParams.get('project_id');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/files/[fileId]/download/route.ts", "line": 47, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "api", "context": "const hasAccess = userProjects.some(p => p.id === file.project_id);", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/projects/[id]/field-history/route.ts", "line": 35, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "if (!fieldName || !['full_description', 'event_log'].includes(fieldName)) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/projects/[id]/field-history/route.ts", "line": 35, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "api", "context": "if (!fieldName || !['full_description', 'event_log'].includes(fieldName)) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/projects/[id]/field-history/route.ts", "line": 37, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "{ error: 'Invalid field name. Must be full_description or event_log' },", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/projects/[id]/field-history/route.ts", "line": 37, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "api", "context": "{ error: 'Invalid field name. Must be full_description or event_log' },", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/projects/[id]/field-history/route.ts", "line": 58, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "fieldName as 'full_description' | 'event_log',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/projects/[id]/field-history/route.ts", "line": 58, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "api", "context": "fieldName as 'full_description' | 'event_log',", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/projects/[id]/team/[memberId]/route.ts", "line": 36, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "api", "context": "if (!project || project.created_by !== parseInt(user.id)) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/projects/[id]/team/route.ts", "line": 86, "field": "user_email", "expected": "userEmail", "actual": "user_email", "layer": "api", "context": "const { user_email, userEmail, role = 'member' } = body;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/projects/[id]/team/route.ts", "line": 87, "field": "user_email", "expected": "userEmail", "actual": "user_email", "layer": "api", "context": "const email = userEmail || user_email;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/projects/route.ts", "line": 85, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "api", "context": "fullDescription: projectData.full_description,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/projects/route.ts", "line": 89, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "api", "context": "dueDate: projectData.due_date,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/projects/route.ts", "line": 90, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "api", "context": "assignedTo: projectData.assigned_to ? parseInt(projectData.assigned_to) : undefined,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/tasks/route.ts", "line": 19, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "api", "context": "const projectId = query!.projectId || query!.project_id;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/tasks/route.ts", "line": 59, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "api", "context": "const { project_id, projectId, ...taskData } = body;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/app/api/task-master/tasks/route.ts", "line": 60, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "api", "context": "const finalProjectId = projectId || project_id;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 485, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "frontend", "context": "const fetchProjectFieldEditHistory = async (fieldName: 'full_description' | 'event_log', loadMore = false) => {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 485, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "frontend", "context": "const fetchProjectFieldEditHistory = async (fieldName: 'full_description' | 'event_log', loadMore = false) => {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 622, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "frontend", "context": "event_log: eventLogText", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 660, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "frontend", "context": "full_description: projectDescriptionText", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 783, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "frontend", "context": "case 'due_date':", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 799, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "frontend", "context": "case 'event_log_edit':", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 821, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "frontend", "context": "case 'event_log_edit':", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1040, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "frontend", "context": "const fieldKey = 'full_description';", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1045, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "frontend", "context": "fetchProjectFieldEditHistory('full_description');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1056, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "frontend", "context": "{showProjectFieldHistory === 'full_description' && (", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1063, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "frontend", "context": ") : projectFieldEditHistory['full_description']?.length > 0 ? (", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1066, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "frontend", "context": "{projectFieldEditHistory['full_description'].map((edit: any, index: number) => (", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1084, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "frontend", "context": "{projectFieldEditHistory['full_description'] &&", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1085, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "frontend", "context": "loadedProjectFieldVersions['full_description'] < (fullProjectData?.fullDescriptionEditCount || 0) && (", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1089, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "frontend", "context": "onClick={() => fetchProjectFieldEditHistory('full_description', true)}", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1352, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "frontend", "context": "const fieldKey = 'event_log';", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1357, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "frontend", "context": "fetchProjectFieldEditHistory('event_log');", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1368, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "frontend", "context": "{showProjectFieldHistory === 'event_log' && (", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1375, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "frontend", "context": ") : projectFieldEditHistory['event_log']?.length > 0 ? (", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1378, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "frontend", "context": "{projectFieldEditHistory['event_log'].map((edit: any, index: number) => (", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1396, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "frontend", "context": "{projectFieldEditHistory['event_log'] &&", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1397, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "frontend", "context": "loadedProjectFieldVersions['event_log'] < (fullProjectData?.eventLogEditCount || 0) && (", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/components/task-master/project-flow-board.tsx", "line": 1401, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "frontend", "context": "onClick={() => fetchProjectFieldEditHistory('event_log', true)}", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 15, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "created_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 16, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updated_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 27, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "types", "context": "'status_change' | 'assignment' | 'due_date' | 'completion' | 'creation' | 'update' |", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 36, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "full_description?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 37, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "event_log?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 41, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "types", "context": "due_date?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 44, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "created_by: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 45, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assigned_to?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 48, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "full_description_edit_count?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 49, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "full_description_last_edited_at?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 50, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "full_description_last_edited_by?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 51, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "event_log_edit_count?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 52, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "event_log_last_edited_at?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 53, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "event_log_last_edited_by?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 56, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "created_by_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 57, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assigned_to_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 58, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "full_description_last_edited_by_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 59, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "event_log_last_edited_by_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 66, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 67, "field": "parent_task_id", "expected": "parentTaskId", "actual": "parent_task_id", "layer": "types", "context": "parent_task_id?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 72, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "types", "context": "due_date?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 74, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assigned_to?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 75, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "created_by: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 78, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assigned_to_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 79, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "created_by_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 86, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 87, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "types", "context": "user_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 89, "field": "added_at", "expected": "addedAt", "actual": "added_at", "layer": "types", "context": "added_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 102, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 119, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 154, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 155, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "field_name: 'full_description' | 'event_log';", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 155, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "field_name: 'full_description' | 'event_log';", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 169, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 171, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "types", "context": "user_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 198, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "types", "context": "user_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 203, "field": "sheet_height", "expected": "sheetHeight", "actual": "sheet_height", "layer": "types", "context": "sheet_height?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 204, "field": "sheet_width", "expected": "sheetWidth", "actual": "sheet_width", "layer": "types", "context": "sheet_width: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 205, "field": "grain_direction", "expected": "grainDirection", "actual": "grain_direction", "layer": "types", "context": "grain_direction: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 207, "field": "cost_per_ream", "expected": "costPerReam", "actual": "cost_per_ream", "layer": "types", "context": "cost_per_ream?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 209, "field": "cost_per_ton", "expected": "costPerTon", "actual": "cost_per_ton", "layer": "types", "context": "cost_per_ton?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 210, "field": "is_custom", "expected": "isCustom", "actual": "is_custom", "layer": "types", "context": "is_custom: boolean;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/database.ts", "line": 217, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "types", "context": "user_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/task-master.ts", "line": 8, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/task-master.ts", "line": 9, "field": "parent_task_id", "expected": "parentTaskId", "actual": "parent_task_id", "layer": "types", "context": "parent_task_id?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/task-master.ts", "line": 14, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "types", "context": "due_date?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/task-master.ts", "line": 16, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assigned_to?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/task-master.ts", "line": 17, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "created_by: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/task-master.ts", "line": 18, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "created_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/task-master.ts", "line": 19, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updated_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/task-master.ts", "line": 20, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assigned_to_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/task-master.ts", "line": 21, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "created_by_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/task-master.ts", "line": 61, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/task-master.ts", "line": 82, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "types", "context": "type: 'upload' | 'comment' | 'status_change' | 'assignment' | 'due_date' | 'completion';", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "/Users/<USER>/Desktop/Imposition system v3/Prosperous-Codex/src/lib/types/task-master.ts", "line": 109, "field": "parent_task_id", "expected": "parentTaskId", "actual": "parent_task_id", "layer": "types", "context": "parent_task_id?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 29, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "types", "context": "| 'due_date'", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 36, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "| 'event_log_edit';", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 203, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "fieldName: 'full_description' | 'event_log';", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 203, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "fieldName: 'full_description' | 'event_log';", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 240, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "full_description?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 241, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "event_log?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 245, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "types", "context": "due_date?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 248, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "created_by: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 249, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assigned_to?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 250, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "full_description_edit_count?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 251, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "full_description_last_edited_at?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 252, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "full_description_last_edited_by?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 253, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "event_log_edit_count?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 254, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "event_log_last_edited_at?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 255, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "event_log_last_edited_by?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 256, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "created_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 257, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updated_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 258, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "created_by_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 259, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assigned_to_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 260, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "full_description_last_edited_by_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 261, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "event_log_last_edited_by_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 269, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 270, "field": "parent_task_id", "expected": "parentTaskId", "actual": "parent_task_id", "layer": "types", "context": "parent_task_id?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 275, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "types", "context": "due_date?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 277, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assigned_to?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 278, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "created_by: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 279, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "created_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 280, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updated_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 281, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assigned_to_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 282, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "created_by_username?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 290, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 291, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "types", "context": "user_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 293, "field": "added_at", "expected": "addedAt", "actual": "added_at", "layer": "types", "context": "added_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 295, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "created_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 296, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updated_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 306, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 314, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "created_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 315, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updated_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 324, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 332, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "created_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 333, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updated_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 349, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "created_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 350, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updated_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 359, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 360, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "field_name: 'full_description' | 'event_log';", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 360, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "field_name: 'full_description' | 'event_log';", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 365, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "created_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 366, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updated_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 375, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id?: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 377, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "types", "context": "user_id: number;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 381, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "created_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 382, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updated_at: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 398, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "fullDescription: row.full_description,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 399, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "eventLog: row.event_log,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 403, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "types", "context": "dueDate: row.due_date,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 406, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "createdBy: row.created_by,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 407, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assignedTo: row.assigned_to,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 408, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "fullDescriptionEditCount: row.full_description_edit_count || 0,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 409, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "fullDescriptionLastEditedAt: row.full_description_last_edited_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 410, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "fullDescriptionLastEditedBy: row.full_description_last_edited_by,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 411, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "eventLogEditCount: row.event_log_edit_count || 0,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 412, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "eventLogLastEditedAt: row.event_log_last_edited_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 413, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "eventLogLastEditedBy: row.event_log_last_edited_by,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 414, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "createdAt: row.created_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 415, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updatedAt: row.updated_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 416, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "createdByUsername: row.created_by_username,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 417, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assignedToUsername: row.assigned_to_username,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 418, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "fullDescriptionLastEditedByUsername: row.full_description_last_edited_by_username,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 419, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "eventLogLastEditedByUsername: row.event_log_last_edited_by_username,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 429, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "projectId: row.project_id,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 430, "field": "parent_task_id", "expected": "parentTaskId", "actual": "parent_task_id", "layer": "types", "context": "parentTaskId: row.parent_task_id,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 435, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "types", "context": "dueDate: row.due_date,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 437, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assignedTo: row.assigned_to,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 438, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "createdBy: row.created_by,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 439, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "createdAt: row.created_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 440, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updatedAt: row.updated_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 441, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assignedToUsername: row.assigned_to_username,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 442, "field": "created_by", "expected": "created<PERSON>y", "actual": "created_by", "layer": "types", "context": "createdByUsername: row.created_by_username,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 456, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "projectId: row.project_id,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 457, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "types", "context": "userId: row.user_id,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 459, "field": "added_at", "expected": "addedAt", "actual": "added_at", "layer": "types", "context": "addedAt: row.added_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 461, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "createdAt: row.created_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 462, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updatedAt: row.updated_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 476, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "projectId: row.project_id,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 484, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "createdAt: row.created_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 485, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updatedAt: row.updated_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 496, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "projectId: row.project_id,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 504, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "createdAt: row.created_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 505, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updatedAt: row.updated_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 523, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "createdAt: row.created_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 524, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updatedAt: row.updated_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 535, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "projectId: row.project_id,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 541, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "createdAt: row.created_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 542, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updatedAt: row.updated_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 553, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "projectId: row.project_id,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 555, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "types", "context": "userId: row.user_id,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 559, "field": "created_at", "expected": "createdAt", "actual": "created_at", "layer": "types", "context": "createdAt: row.created_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 560, "field": "updated_at", "expected": "updatedAt", "actual": "updated_at", "layer": "types", "context": "updatedAt: row.updated_at,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 581, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "types", "context": "if (!row.id || !row.user_id || !row.project_id) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 581, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "if (!row.id || !row.user_id || !row.project_id) {", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 584, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "types", "context": "user_id: row.user_id,", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 585, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": "project_id: row.project_id", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 627, "field": "user_id", "expected": "userId", "actual": "user_id", "layer": "types", "context": ": ['id', 'project_id', 'user_id'];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 627, "field": "project_id", "expected": "projectId", "actual": "project_id", "layer": "types", "context": ": ['id', 'project_id', 'user_id'];", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 725, "field": "full_description", "expected": "fullDescription", "actual": "full_description", "layer": "types", "context": "full_description?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 726, "field": "event_log", "expected": "eventLog", "actual": "event_log", "layer": "types", "context": "event_log?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 727, "field": "due_date", "expected": "dueDate", "actual": "due_date", "layer": "types", "context": "due_date?: string;", "fixable": true}, {"type": "WRONG_CASE", "severity": "ERROR", "file": "src/lib/task-master/types.ts", "line": 728, "field": "assigned_to", "expected": "assignedTo", "actual": "assigned_to", "layer": "types", "context": "assigned_to?: number;", "fixable": true}]}