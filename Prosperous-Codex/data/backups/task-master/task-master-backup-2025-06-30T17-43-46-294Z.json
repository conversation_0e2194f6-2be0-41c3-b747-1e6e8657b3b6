{"metadata": {"backupType": "task-master-selective", "createdAt": "2025-06-30T17:43:46.294Z", "createdBy": "1", "version": "1.0.0", "description": "Task Master selective backup including projects, comments, activities, and user relationships"}, "statistics": {"totalProjects": 160, "totalComments": 21, "totalActivities": 593, "totalUsers": 2}, "data": {"projects": [{"id": 169, "title": "New Task", "description": null, "full_description": "123123", "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-30 17:41:17", "updated_at": "2025-06-30 17:41:17", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 168, "title": "New Task", "description": null, "full_description": "Subject: Project Briefing: Book Printing Project // Client\n\n---EventLog---\nEvent Log:\n\n- 27/10/2023 - Project Team (Prosperous): Received client feedback regarding book specifications (4,200 pages) and a request for a quotation based on custom paper options, including print quality test rolls.\n\n---EndOfEventLog---\n\n---Description---\n\nDescription:\n\nThe project involves printing a book for a client. The situation has evolved with the client providing updated specifications and preferences. The core trigger for the current request is the client's feedback on the book's length, which is confirmed as 4,200 pages (equivalent to 2,100 sheets). Furthermore, the client has indicated that the paper is custom-made and that its GSM (Grams per Square Meter) can be adjusted. A key requirement from the client is the willingness to send test rolls to verify print quality if this custom paper route is pursued. The immediate objective is to process this information and provide the client with a quotation based on the specified material and the potential for print quality testing.\n\n---EndofDescription---\n\n---TaskAssignment---\nTask Assignment:\n\n» Task 1: Prepare Quotation for Book Printing Project\n- Description: The objective is to provide the client with a detailed and accurate quotation for printing the book, considering the specified page count and the potential for custom-made paper with adjustable GSM. This is crucial for the client's decision-making process and to confirm our capability to meet their needs, including print quality verification via test rolls.\n  - Subtasks:\n    - 1.1: Confirm Book Specifications\n      - Description: Verify the exact book specifications provided by the client, including the 4,200-page count (2,100 sheets).\n    - 1.2: Investigate Custom Paper Options\n      - Description: Research and identify suitable suppliers for custom-made paper, focusing on the ability to adjust GSM and associated lead times and costs.\n    - 1.3: Estimate Material and Production Costs\n      - Description: Calculate the projected costs for the custom paper, considering GSM variations, and estimate the printing costs based on the book's length and any special requirements.\n    - 1.4: Account for Print Quality Testing\n      - Description: Include the logistical and potential material costs associated with sending test rolls to the client for print quality verification.\n    - 1.5: Compile Comprehensive Quotation\n      - Description: Consolidate all estimated costs into a formal quotation document, clearly outlining the proposed materials, services, and any specific conditions.\n    - 1.6: Prepare for Client Follow-up\n      - Description: Anticipate potential client questions regarding the quotation, paper quality, or testing process and prepare appropriate responses.\n\n---EndOfTaskAssignment---", "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 2, "assigned_to": null, "created_at": "2025-06-30 17:23:55", "updated_at": "2025-06-30 17:23:55", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 166, "title": "項目簡報：書本項目 // CallKodi", "description": null, "full_description": "詳細說明：\n\n此項目涉及為客戶 CallKodi 印刷一本規格特殊的書本。客戶最初的要求是印製10,000本，每本書包含4,200頁，使用25磅（約38gsm）的未塗布紙，書本厚度預計約為105毫米。\n\n在收到客戶的初步詢價後，Prosperous Printing 的 Harry Lam 指出，該項目規格對公司的設備構成了嚴峻挑戰。主要的限制在於書本的總厚度。客戶要求的4,200頁（相當於2,100張紙，每張紙約0.05毫米厚）導致書本厚度達到105毫米，這遠遠超過了 Prosperous Printing 裝訂機的最大承載能力，即60毫米。Harry Lam 強調，即使考慮提高紙張克重（GSM）或採用手動裝訂，如此厚的書本在結構完整性和裝訂過程中都存在高風險，並且業界普遍難以處理超過100毫米厚度的書籍。\n\n為了解決此問題，Harry Lam 向客戶提出了將書本分割成三冊的建議，認為這樣不僅能確保產品的可管理性和耐用性，也能符合更多印刷服務的能力範圍。\n\n客戶方面，Mark Lu 表示已與客戶溝通，客戶願意調整紙張的GSM並提供測試卷以驗證印刷品質，但並未明確回應關於書本厚度或分割的建議。他要求 Prosperous Printing 在此基礎上提供報價。Harry Lam 在後續溝通中再次詳細說明了厚度限制和風險，並重申了三冊書的解決方案。Mark Lu 表示已將 Harry 的回覆傳達給客戶，並正在等待客戶的最終意見。\n\n核心問題：\n- 書本的總厚度（約105毫米）遠超 Prosperous Printing 的裝訂機能力（最大60毫米）。\n- 如此厚的書本在結構穩定性和裝訂過程中存在高風險。\n- 業界普遍難以處理超過100毫米厚度的書籍。\n\n目前的首要任務是等待客戶對將書本分割成三冊的建議做出回應，並評估後續的處理方案。", "event_log": "事件日誌：\n\n- 08/05/2024 - <PERSON> (Prosperous): 通知 Mark <PERSON>，該項目規格（105毫米的書本厚度）超出 Prosperous 的設備能力（最大60毫米）。建議將書本分成三冊。\n- 08/05/2024 - <PERSON> (CallKodi): 回覆 Harry，感謝其澄清，並表示已將其建議與客戶分享，等待客戶回饋。\n- 09/05/2024 - <PERSON> (CallKodi): 向 Harry 確認該書本為4,200頁（2,100張），紙張可訂製，客戶願意調整GSM並寄送測試卷。要求提供報價。\n- 10/05/2024 - <PERSON> (Prosperous): 詳細解釋裝訂機的限制（最大60毫米厚度）以及2,100張紙（約105毫米厚度）的問題。提到手動裝訂的風險，以及業界對超過100毫米厚度書籍的普遍限制。再次強調三冊書的建議。\n- 10/05/2024 - Mark Lu (CallKodi): 確認收到 Harry 的回覆，表示已與客戶分享，並等待客戶的回饋。", "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 2, "assigned_to": null, "created_at": "2025-06-30 16:27:22", "updated_at": "2025-06-30 16:27:48", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-30 16:27:48", "full_description_last_edited_by": 2, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-30 16:27:42", "event_log_last_edited_by": 2, "visibility": "public", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 165, "title": "Project Briefing: Book Project // CallKodi", "description": null, "full_description": "Description:\nThis project involves a request for printing services for a book project initiated by <PERSON> from CallKodi. The initial request specified a quantity of 10,000 units, a 7x10 trim size, and a substantial 4,200 pages, which translates to 2100 sheets. The specified body stock was 25lb uncoated paper, resulting in an estimated book block thickness of 105mm.\n\nProsperous Printing, through <PERSON>, responded by highlighting critical limitations of their equipment. The company's binding machines have a maximum capacity of 60mm for book block thickness, and their paper handling capabilities start from approximately 50gsm. The project's 105mm thickness and 25lb (38gsm) paper specification significantly exceed these operational ranges. <PERSON> further explained that even with manual assembly, the structural integrity of such a thick volume would be at high risk, and industry consultation indicated that few printers can handle books exceeding 100mm in thickness.\n\nAs a potential solution to overcome these limitations, Prosperous Printing recommended splitting the book into three separate volumes. This approach would make the project more manageable and align with available printing and binding capabilities. <PERSON> has since communicated this feedback to his client and is awaiting their decision on the proposed solution or any alternative directions.\n\nThe core problem identified is the book block thickness of approximately 105mm, which is far beyond Prosperous Printing's binding machinery capacity of 60mm and presents significant structural challenges.\n\nThe immediate objective is to await the client's feedback from CallKodi regarding the proposed three-volume split or any other revised specifications they may provide, to determine the subsequent steps for this project.", "event_log": "Event Log:\n- 08/05/2024 - <PERSON> (Prosperous): Notified <PERSON> and <PERSON> that project specifications exceed Prosperous's equipment capabilities (min paper thickness ~50gsm, max book block thickness ~60mm), specifically regarding the 25lb paper and 105mm thickness. Offered to quote under different specifications.\n- 08/05/2024 - <PERSON> (CallKodi): Sent RFQ to <PERSON> (cc <PERSON>) for a book project (10,000 qty, 7x10 trim, 4,200 pages, 25lb paper, 105mm thickness). Requested quotes with vendor-supplied and customer-supplied paper.\n- 08/05/2024 - <PERSON> (Prosperous): Provided a detailed explanation to <PERSON> and <PERSON> about the unfeasibility due to the 105mm book block thickness exceeding the 60mm binding machine capacity. Mentioned structural integrity risks and industry limitations for books over 100mm thick. Recommended splitting the book into three volumes.\n- 09/05/2024 - <PERSON> (CallKodi): Clarified with <PERSON> and <PERSON> that the client confirmed 4,200 pages (2100 sheets) and can adjust GSM. Requested a quotation based on material usage.\n- 10/05/2024 - <PERSON> (CallKodi): Informed <PERSON> and <PERSON> that he shared <PERSON><PERSON><PERSON>'s explanation with the client and is awaiting feedback.", "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 2, "assigned_to": null, "created_at": "2025-06-30 12:16:21", "updated_at": "2025-06-30 12:16:46", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-30 12:16:46", "full_description_last_edited_by": 2, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-30 12:16:37", "event_log_last_edited_by": 2, "visibility": "public", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 163, "title": "項目簡報：以色列小書專案 // [客戶名稱待定]", "description": null, "full_description": "此項目涉及「以色列小書」及其配套的兩款紙袋（「以色列小書 - 袋子」與「耶路撒冷小書 - 袋子」）的生產與出貨事宜。\n\n項目初期，客戶（透過Prosperous Printing的Zoey與Candy）向供應商（Tianhong Printing的Lily）提交了生產訂單與相關文件，並積極要求加快樣品製作與大貨生產的時程。過程中曾出現樣品寄送延遲，以及大貨完成時間的協調問題。近期，主要關注點轉移至供應商提供的最終包裝清單出現多項錯誤，包括每箱的裝載數量、重量標示不符，以及缺少兩款紙袋的資訊。這些錯誤若未及時修正，將直接影響貨物的準確記錄與後續的出貨安排。\n\n目前最緊迫的問題是供應商提供的最終包裝清單存在多項錯誤，包括每箱裝載數量、重量標示不符，以及缺少兩款紙袋的資訊。這些錯誤若未及時修正，將直接影響貨物的準確記錄與後續的出貨安排。\n\nImmediate Objective:\n確保所有與本次訂單相關的文件，特別是最終包裝清單，在所有相關方之間得到準確的確認與修正，以便順利完成貨物的寄送流程。", "event_log": "- 08/05/2025 - Zoey (Prosperous): 寄出營業單與加工單，請Lily盡快提供PDF，並已上傳文件至指定連結。\n- 13/05/2025 - Zoey (Prosperous): 確認透過微信傳送全書PDF，文件無誤，請Lily使用客戶改正後的文件進行印刷。\n- 14/05/2025 - Zoe<PERSON> (Prosperous): 確認舊書樣及手袋樣品已寄出，並收到OK蘭紙，要求Lily盡快安排生產。\n- 14/05/2025 - Lily (Tianhong): 要求Zoey將舊書樣寄至惠州工廠，並詢問手袋樣品情況。\n- 20/05/2025 - Zoey (Prosperous): 確認舊書樣與手袋樣品已寄達，並收到OK蘭紙，要求Lily立即安排生產。\n- 02/06/2025 - Lily (Tianhong): 通知Zoey成品樣品預計於6月10日完成寄出。\n- 02/06/2025 - Zoey (Prosperous): 通知Lily樣品寄送延遲（原定5月30日），要求趕在本週五前寄到香港。\n- 03/06/2025 - Lily (<PERSON>ianhong): 通知Zoey大貨預計於6月15日前完成。\n- 03/06/2025 - Candy (Prosperous): 要求Lily盡量趕工，希望大貨於6月13日（星期五）完成。\n- 03/06/2025 - Candy (Prosperous): 通知Lily客戶未能如期在6月13日收到大貨，詢問是否能提前2天完成。\n- 06/06/2025 - Lily (Tianhong): 要求Candy提供SI以訂製紙箱，大貨正在生產中。\n- 06/06/2025 - Candy (Prosperous): 表示正在追蹤客戶資訊，預計下週一（6月9日）收到後立即提供給Lily。\n- 17/06/2025 - Lily (Tianhong): 向Candy發送更新後的出貨資料及對帳發票。\n- 23/06/2025 - Candy (Prosperous): 通知Lily最終包裝清單有誤（每箱裝150個、重量不對、兩款紙袋未列出），要求更改後再提供。\n- 23/06/2025 - Lily (Tianhong): 向Candy發送更新後的包裝資料。\n- 26/06/2025 - Candy (Prosperous): 感謝Lily提供的已修正最終包裝清單。", "status": "completed", "priority": "high", "progress": 100, "due_date": "2025-06-02T16:00:00.000Z", "completed_date": "2025-06-30T17:40:10.724Z", "created_by": 2, "assigned_to": null, "created_at": "2025-06-30 11:43:40", "updated_at": "2025-06-30 17:40:10", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-30 11:44:02", "full_description_last_edited_by": 2, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-30 11:43:59", "event_log_last_edited_by": 2, "visibility": "public", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 162, "title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd", "description": null, "full_description": "This briefing document pertains to shipping services for Great Wall Printing Company Ltd, initiated by a communication from Publiship Logistics Ltd. Publiship, through its representative <PERSON>, has sent updated quotations and special offerings to Great Wall Printing Company Ltd, with the information being shared with <PERSON> and Candy at Prosperous Printing Company.\n\nThe core of this event is the proactive provision of revised pricing and service details by Publiship. This includes special rates for both Less than Container Load (LCL) and Full Container Load (FCL) shipments from Hong Kong or Shenzhen to various worldwide destinations, as well as specific through rates for LCL cargo from Hong Kong to door UK warehouses and from Hong Kong or Shenzhen to door Australia warehouses.\n\nA key detail in the offering is the distinction in pricing for Hong Kong CFS (Container Freight Station) charges:\n-   HK CFS >>> HKD175.00 / CBM will apply for \"Freight Prepaid\" shipments exclusively.\n-   For FOB (Free On Board) orders, the normal HK CFS charge will be HKD208.00 / CBM.\n\nPubliship has also provided their Shipping Order blank form for reference. The immediate objective is to review these updated quotations and service details to assess their suitability and potential benefits for Great Wall Printing Company Ltd.", "event_log": "- 27/06/2025 - <PERSON> (Publiship): Sent updated quotation and special offerings for shipping services to Great Wall Printing Company Ltd, addressed to <PERSON> and Candy at Prosperous Printing Company.", "status": "inProgress", "priority": "medium", "progress": 9, "due_date": "2025-06-29T16:00:00.000Z", "completed_date": null, "created_by": 2, "assigned_to": null, "created_at": "2025-06-30 11:33:50", "updated_at": "2025-06-30 11:39:35", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-30 11:35:37", "full_description_last_edited_by": 2, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-30 11:35:25", "event_log_last_edited_by": 2, "visibility": "public", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 161, "title": "項目簡報：以色列小書 // 客戶", "description": null, "full_description": "此項目涉及「以色列小書」及兩款紙袋的生產。項目初期由 Zoey 向 Lily 索取 PDF 文件以啟動生產。在時程方面，曾有將樣品於5月30日寄出，後調整為6月9日寄出，以及要求將大貨生產提前至6月13日完成（原預計6月15日）。Lily（天泓印刷）持續溝通生產進度及潛在延誤。Candy（Prosperous）則負責跟進客戶反饋並提供營業單、加工單、出貨資料及對賬發票等文件。近期出現的主要問題是 Lily 於6月17日提供的最終裝箱單出現了錯誤，包括每箱數量（150件）及重量不對，且兩款紙袋的尾箱遺漏。Lily 於6月23日提供了修正後的裝箱單，Candy 於6月26日確認收到。\n\n項目目標：為客戶生產「以色列小書」及相關紙袋。\n\n情況演變：項目涉及樣品製作、生產及最終文件準備。曾有多項關於樣品及大貨生產的時程調整及催促。最後，裝箱單出現錯誤及遺漏，需要修正。\n\n核心問題/觸發點：本次更新的直接觸發點是裝箱單的最終確認，因其包含錯誤和遺漏，需要進行修正。在此之前，樣品交貨時程及大貨生產催促也曾是關注點。\n\n緊急目標：確認最新的裝箱單準確無誤且內容完整，並確保所有生產及出貨文件均已備妥。", "event_log": "- 08/05/2025 - Zoey (Prosperous): 要求提供書籍與兩款袋子的PDF文件。\n- 13/05/2025 - Zoey (Prosperous): 確認已透過微信/電郵發送PDF，待審批後印刷。\n- 14/05/2025 - Lily (天泓印刷): 收到OK資訊，要求將舊書樣及手袋樣品寄往惠州工廠。\n- 20/05/2025 - Zoey (Prosperous): 確認已寄出舊樣品，退回OK紙，要求安排生產。\n- 20/05/2025 - Lily (天泓印刷): 表示樣品預計於6月10日完成並寄出。\n- 02/06/2025 - Zoey (Prosperous): 要求樣品於本週五寄至香港。\n- 02/06/2025 - Lily (天泓印刷): 表示樣品仍在印刷中，會緊追生產，但無法保證週五前完成。\n- 03/06/2025 - Zoey (Prosperous): 請 Lily 盡快安排樣品於6月9日前寄出，並詢問大貨完成日期。\n- 03/06/2025 - Lily (天泓印刷): 預計大貨生產於6月15日完成。\n- 03/06/2025 - Candy (Prosperous): 請 Lily 嘗試將大貨生產提前2天，於6月13日完成。\n- 06/06/2025 - Lily (天泓印刷): 發送出貨資料及對賬發票。\n- 06/06/2025 - Candy (Prosperous): 通知 Lily 正在追蹤客戶的最終裝箱單，預計下週一收到，收到後立即提供。\n- 17/06/2025 - Lily (天泓印刷): 發送更新後的裝箱資料。\n- 17/06/2025 - Candy (Prosperous): 指出最終裝箱單的錯誤（每箱150件、重量不對）以及兩款紙袋的尾箱遺漏，要求更改。\n- 23/06/2025 - Lily (天泓印刷): 發送更新後的裝箱資料。\n- 26/06/2025 - Candy (Prosperous): 確認收到更新後的最終裝箱單。", "status": "todo", "priority": "high", "progress": 0, "due_date": "2025-06-17T16:00:00.000Z", "completed_date": null, "created_by": 2, "assigned_to": null, "created_at": "2025-06-30 10:24:50", "updated_at": "2025-06-30 11:46:43", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-30 10:25:52", "full_description_last_edited_by": 2, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-30 10:25:43", "event_log_last_edited_by": 2, "visibility": "public", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 160, "title": "New Task", "description": null, "full_description": "項目名稱：以色列小書 / 以色列小書 - 袋子 / 耶路撒冷小書 - 袋子\n\n事件背景：\n此項目始於五月初，涉及以色列小書及兩款袋子的生產與樣品製作。過程中，Prosperous Printing（Candy, Zoey）與Tianhong Printing（Lily）就文件審核、樣品寄送、生產進度及包裝細節進行了頻繁溝通。初期重點在於文件確認與樣品製作，隨後轉為大貨生產進度與出貨安排的協調。\n\n核心問題：\n在貨物即將出貨的階段，發現最終包裝清單存在錯誤（如裝箱數量與重量標示不清）以及部分紙袋尾箱資訊遺漏。這導致了對包裝清單的多次修訂，影響了出貨的準確性與效率。\n\n即時目標：\n確認並批准最終、準確的包裝清單，確保所有出貨細節無誤，並協調後續的大貨生產與出貨事宜。", "event_log": "- 08/05/2025 - Zoey (Prosperous): 發送營業單與加工單，要求提供PDF。上傳文件至WeTransfer。\n- 13/05/2025 - Zoey (Prosperous): 確認已透過微信及郵件傳送PDF，文件可供印刷。\n- 14/05/2025 - Lily (Tianhong): 收到OK文件資訊，請Zoey將舊書樣寄至惠州工廠。\n- 20/05/2025 - Zoey (Prosperous): 寄出舊書樣及手袋樣品，收到OK蘭紙，要求安排生產。\n- 02/06/2025 - Lily (Tianhong): 通知成品樣品預計於6月10日完成寄出。\n- 02/06/2025 - Zoey (Prosperous): 要求Lily盡快於本週五前將原樣品寄至香港。\n- 02/06/2025 - Lily (Tianhong): 回覆印刷仍在進行中，會追蹤生產，盡量早些出樣，但本週五無法完成。\n- 03/06/2025 - Candy (Prosperous): 要求Lily盡量趕工於6月13日（星期五）完成大貨，並詢問大貨完成日期。\n- 03/06/2025 - Lily (Tianhong): 通知大貨預計於6月15日前完成。\n- 03/06/2025 - Candy (Prosperous): 通知客戶無法於6月13日收到大貨，詢問是否可提早2天完成。\n- 06/06/2025 - Lily (Tianhong): 提供以色列小書的出貨資料及對賬發票。\n- 06/06/2025 - Candy (Prosperous): 正在追蹤客戶，預計下週一收到資訊，收到後會立即提供。\n- 17/06/2025 - Lily (Tianhong): 提供更新後的裝箱資料（以色列小書出貨資料）。\n- 23/06/2025 - Candy (Prosperous): 指出最終包裝清單有誤（裝箱數量150個/箱、重量不對），且兩款紙袋的尾箱未列出，要求更改後再提供。\n- 26/06/2025 - Candy (Prosperous): 確認收到已修訂的最終包裝清單。", "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 2, "assigned_to": null, "created_at": "2025-06-30 10:15:35", "updated_at": "2025-06-30 10:16:07", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-30 10:16:07", "full_description_last_edited_by": 2, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-30 10:15:55", "event_log_last_edited_by": 2, "visibility": "public", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 159, "title": "Task #159", "description": null, "full_description": "此項目涉及為客戶生產及運送「以色列小書」及兩款相關的紙袋。項目初期，Zoey Poon (Prosperous) 向Lily (Tianhong) 提供了產品資料，並要求提供印刷PDF、紙袋PDF及蘭紙樣品。文件審批和生產進度追蹤是整個過程的核心。\n\n項目進展曾出現延誤和需求變更：\n-   最初的樣品寄送日期為5月30日，但Lily表示印刷仍在進行中，無法在該週五前完成。\n-   Zoey要求盡快寄出樣品，並追問大貨完成時間。Candy亦曾要求加快生產進度，希望於6月9日寄出，並嘗試將大貨完成日期提前至6月13日。\n-   其後，Candy發現最終裝箱資料有誤，包括每個紙箱的裝箱數量、重量不正確，以及兩款紙袋在尾箱列表中有遺漏。Lily已於6月23日提供了更新後的裝箱資料，Candy於6月26日確認收到。\n\n目前最迫切的問題是確保所有裝箱資料準確無誤，並跟進大貨的最終生產及出貨安排。", "event_log": "---\n--- Event Log ---\n\nObjective: To provide a complete, chronological history of all material events for this project.\n\n- 08/05/2025 - <PERSON><PERSON> (Prosperous): 發送1款書及2款袋子的資料，要求提供全書PDF、2款袋子PDF及蘭紙樣品。\n- 08/05/2025 - <PERSON><PERSON> (Prosperous): 附上營業單及加工單，要求盡快提供PDF。\n- 13/05/2025 - <PERSON><PERSON> (Prosperous): 確認透過微信及電郵已傳送全書PDF，文件無問題可供印刷。\n- 14/05/2025 - <PERSON><PERSON> (Prosperous): 確認所有PDF已獲批，指示印刷顏色需跟舊樣，並將安排寄送舊樣。提供出樣日期 (5月30日) 及走貨日期 (6月6日)，並提醒注意板紙品質。\n- 14/05/2025 - Lily (Tianhong): 確認收到OK文件資訊，要求將舊書樣寄至惠州工廠，並詢問手袋樣板事宜。\n- 20/05/2025 - <PERSON><PERSON> (Prosperous): 確認舊書樣及手袋樣板已寄出，OK蘭紙亦已寄回，要求盡快安排生產。\n- 01/06/2025 - Lily (T<PERSON><PERSON>): 通知成品樣預計於6月10日完成寄出。\n- 02/06/2025 - <PERSON> (Tianhong): 通知印刷仍在進行中，會追趕生產並盡量提早出樣，但本週五無法完成。\n- 02/06/2025 - Zoey Poon (Prosperous): 要求Lily盡快趕在週五前將原定5月30日寄出的樣品寄到香港。\n- 03/06/2025 - Candy (Prosperous): 要求Lily盡量趕工，希望9/6寄出，並詢問大貨何時完成。\n- 03/06/2025 - Candy (Prosperous): 詢問Lily是否可將大貨完成日期提前至6月13日(星期五)。\n- 03/06/2025 - Candy (Prosperous): 通知Lily客戶未能如期在13/6完成大貨，詢問是否能提早2天完成。\n- 06/06/2025 - Lily (Tianhong): 通知大貨正在生產中，請Candy提供SI以準備訂製紙箱。\n- 06/06/2025 - Lily (Tianhong): 提供以色列小書的出貨資料及對賬發票。\n- 23/06/2025 - Candy (Prosperous): 通知Lily最終裝箱資料有誤 (裝150個/箱，重量不對)，且兩款紙袋有尾箱未列出，要求更改後再提供。\n- 23/06/2025 - Lily (Tianhong): 提供更新後的裝箱資料。\n- 26/06/2025 - Candy (Prosperous): 確認收到已修訂的最終裝箱資料。", "status": "todo", "priority": "high", "progress": 100, "due_date": "2025-06-23T16:00:00.000Z", "completed_date": null, "created_by": 2, "assigned_to": null, "created_at": "2025-06-29 22:14:01", "updated_at": "2025-06-29 22:16:18", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-29 22:14:24", "full_description_last_edited_by": 2, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-29 22:14:18", "event_log_last_edited_by": 2, "visibility": "public", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 158, "title": "New Task", "description": null, "full_description": "此項目涉及為客戶製作一本「以色列小書」及兩款紙袋。項目初期，Prosperous Printing Company Limited（Zoey）向供應商Tianhong Printing（Lily）提供了製作所需的文件資料，並要求盡快提供PDF文件以安排生產及製作樣品。\n\n在項目進展中，雙方就樣品的寄送時間、大貨的生產進度及預計完成日期進行了多次溝通與協調。Prosperous Printing（Candy）曾多次要求加快生產進度，希望能提前完成大貨交付，但供應商表示生產仍在進行中，並提供了預計的完成日期。\n\n目前，項目面臨一個關鍵問題：Prosperous Printing的Candy於2025年6月23日通知Lily，最終裝箱單的資料有誤，具體包括每箱的裝載數量（標示為150個/箱）及重量不正確，同時，兩款紙袋在尾箱的列表中有遺漏。此情況需要立即修正，以確保後續的運輸和物流安排能夠準確無誤地進行。\n\n因此，目前的當務之急是修正並重新提交一份準確的最終裝箱單，確保所有相關資訊（包括書籍的數量、重量以及兩款紙袋的完整列出）都符合實際情況。", "event_log": "**1. 事件日誌**\n\nObjective: To provide a complete, chronological history of all material events for this project.\n\n- 08/05/2025 - <PERSON><PERSON> (Prosperous Printing Company Limited): 發送了關於一本「以色列小書」及兩款袋子的初步資料，包含WeTransfer連結，要求Lily更新文件後提供全書PDF及兩款袋子的PDF，並需提供各兩份蘭紙。\n- 08/05/2025 - Zoey Poon (Prosperous Printing Company Limited): 附上營業單及加工單，要求Lily盡快提供PDF。\n- 13/05/2025 - <PERSON>y Poon (Prosperous Printing Company Limited): 確認已透過微信及電郵傳送所有書籍PDF，文件無誤可印刷，要求Lily安排生產，並指示印色需跟舊樣。\n- 14/05/2025 - Lily (Tianhong Printing): 收到OK文件資訊，要求Zoey將舊書樣寄至惠州工廠，並詢問手袋樣版情況，提供惠州工廠地址。\n- 20/05/2025 - Zoey Poon (Prosperous Printing Company Limited): 確認舊書樣及手袋樣版已寄出，寄回一套OK蘭紙，要求盡快安排生產。\n- 01/06/2025 - Lily (Tianhong Printing): 預計成品樣品將於6月10日左右完成寄出。\n- 02/06/2025 - Zoey Poon (Prosperous Printing Company Limited): 詢問書籍和兩款袋子的進度及樣品預計何時完成。\n- 02/06/2025 - Lily (Tianhong Printing): 表示大貨仍在印刷中，會緊追生產，盡量早些出樣，但本周五無法完成。\n- 02/06/2025 - Zoey Poon (Prosperous Printing Company Limited): 要求Lily盡快將原樣品於本周五前寄到香港，因已延遲數日。\n- 03/06/2025 - Lily (Tianhong Printing): 通知Zoey大貨預計於6月15日前完成。\n- 03/06/2025 - Candy (Prosperous Printing Company Limited): 要求Lily盡力趕工，希望於6月13日（星期五）完成大貨。\n- 03/06/2025 - Candy (Prosperous Printing Company Limited): 再次要求Lily盡力趕工，希望於6月13日（星期五）完成大貨。\n- 03/06/2025 - Candy (Prosperous Printing Company Limited): 通知Lily客戶未能如期收到6月13日完成的大貨（見附件），詢問是否能提前兩天完成並回覆。\n- 06/06/2025 - Lily (Tianhong Printing): 提供以色列小書的出貨資料及對帳發票。\n- 06/06/2025 - Candy (Prosperous Printing Company Limited): 表示正在追趕客戶，預計下星期一收到資料，一收到立即提供給Lily。\n- 17/06/2025 - Lily (Tianhong Printing): 提供以色列小書的出貨資料及對帳發票。\n- 23/06/2025 - Candy (Prosperous Printing Company Limited): 通知Lily最終裝箱單資料有誤（裝150個/箱，重量亦不對），並且兩款紙袋在尾箱無列出，要求更改後再提供。", "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 2, "assigned_to": null, "created_at": "2025-06-29 20:48:38", "updated_at": "2025-06-29 22:08:00", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-29 22:08:00", "full_description_last_edited_by": 2, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-29 22:07:55", "event_log_last_edited_by": 2, "visibility": "public", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 154, "title": "項目簡報：《白雲故事書》", "description": null, "full_description": "Task ID as Title: New tasks now display \"Task #151\" format using backend-generated IDs\n✅ Description Input: The gradient input field now creates task descriptions instead of titles\n✅ Project Flow Board: Descriptions properly appear in the Overview tab\n✅ Field Mapping: Proper camelCase ↔ snake_case conversion between frontend and backend\n✅ Optimistic UI: Maintains existing smooth UI behavior with immediate feedback\n✅ Gradient Styling: Preserved existing visual design and behavior\n\nTechnical Implementation:\nFrontend (camelCase): Sends fullDescription in API requests\nAPI Middleware: Converts to full_description (snake_case) for database\nBackend (snake_case): Stores and retrieves data using database conventions\nResponse Mapping: Converts back to camelCase for frontend display\nTesting Results:\n✅ Task creation works with gradient input field\n✅ Tasks display \"Task #[ID]\" as titles (e.g., \"Task #151\")\n✅ Descriptions appear correctly in Project Flow Board drawer\n✅ Success notifications and input field reset work properly\n✅ No breaking changes to existing functionality", "event_log": "*   28/11/2024 - <PERSON> (Prosperous Printing Company Limited): 發送採購訂單 PO# SHP28112024，項目為《白雲故事書》(7款，每款1560本)，要求將日期代碼更改為0325並確認PDF。\n*   29/11/2024 - 阿娣 (Shanghai Offset): 回覆Harry <PERSON>，確認收到訂單，並向Candy詢問標籤報價 (6x3cm, 單色黑, 共910個)。\n*   29/11/2024 - 阿娣 (Shanghai Offset): 提供標籤報價：每張HK$0.92 (單一款式總價HK$120，暗示7款總價HK$840)。\n*   29/11/2024 - Candy (Prosperous Printing Company Limited): 向阿娣確認標籤價格，並詢問910個標籤的總價。\n*   29/11/2024 - 阿娣 (Shanghai Offset): 確認標籤價格為每張HK$0.92，共130張x7款=910張，總價HK$840。\n*   29/11/2024 - <PERSON> (Prosperous Printing Company Limited): 更新訂單，加入貼紙費用，並請阿娣提供外箱價格（作為節省項目），因萬里將自行採購910個外箱。\n*   29/11/2024 - 阿娣 (Shanghai Offset): 向Harry Lam詢問外箱價格是基於書本單價還是紙箱單價。\n*   29/11/2024 - 阿娣 (Shanghai Offset): 回覆外箱報價為每本HK$0.30 (CPS)。\n*   30/11/2024 - Harry Lam (Prosperous Printing Company Limited): 發送經修訂的採購訂單 (PO# SHPR28112024AR2)，包含貼紙費用。\n*   30/11/2024 - 阿娣 (Shanghai Offset): 將Harry的回覆轉發給Candy。\n*   05/12/2024 - 1A 運 (Shanghai Offset): 通知Harry Lam，建議將封面改為雙粉紙，並在每款書名後加上ISBN，要求更新PO。\n*   11/12/2024 - Harry Lam (Prosperous Printing Company Limited): 確認收到PO更新，並發送修訂版PO (PO SHP28112024AR3)，包含ISBN。\n*   16/12/2024 - Harry Lam (Prosperous Printing Company Limited): 將外箱貼紙條碼發送給1A 運。\n*   25/03/2025 - 1A 運 (Shanghai Offset): 向Harry Lam詢問出貨時間。\n*   25/03/2025 - Candy (Prosperous Printing Company Limited): 通知1A 運預計3月底出貨，仍在等待客戶資料。詢問貨櫃加網及鐵柱固定貨品的安排。\n*   25/03/2025 - 1A 運 (Shanghai Offset): 表示尚未收到外箱，若週末收到則需4月初出貨。同意加網，但要求Prosperous Printing提供鐵柱，因其公司未曾操作。建議若貨櫃未滿或需併櫃時，打板較為穩妥。\n*   26/03/2025 - 1A 運 (Shanghai Offset): 表示樣本已於2月18日交貨，並提醒Candy日後使用外箱時需小心。\n*   26/03/2025 - Candy (Prosperous Printing Company Limited): 確認收到樣本及外箱數量。並指出外箱將於27/3送到SZ興申發廠，預留下週中至尾包裝。重申Harry曾多次與1A 運討論鐵柱事宜，並獲同意。強調不接受打板入櫃，因浪費時間，貨櫃裝好後將立即運往客戶廠房同日裝貨及交碼頭，要求盡快處理。\n*   26/03/2025 - 1A 運 (Shanghai Offset): 確認樣本已於2月18日交付。\n*   27/03/2025 - Candy (Prosperous Printing Company Limited): 確認外箱已安排於27/3送達SZ興申發廠，並指示1A 運於29/3將91箱貨物運送至中山市集渼玩具有限公司，因貨量不多，建議直接送貨至客戶廠房。要求出貨後盡快提供最終包裝明細。\n*   28/03/2025 - Candy (Prosperous Printing Company Limited): 確認收到1A 運提供的最終包裝明細。\n*   28/03/2025 - 1A 運 (Shanghai Offset): 發送《白雲故事書X7款》澳洲版的最終包裝明細 (共91箱)。\n*   14/05/2025 - 1A 運 (Shanghai Offset): 確認已安排事項。\n*   14/05/2025 - Candy (Prosperous Printing Company Limited): 感謝1A 運安排，並等待通知。\n*   14/05/2025 - 1A 運 (Shanghai Offset): 發送附件掃描件，請Candy確認是否OK。\n*   14/05/2025 - Candy (Prosperous Printing Company Limited): 要求修改第5點有關「非相關營運方(Non-Party Operator)」提供報關資料的要求。", "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 2, "assigned_to": null, "created_at": "2025-06-27 13:03:23", "updated_at": "2025-06-29 20:14:45", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-29 20:14:45", "event_log_last_edited_by": 2, "visibility": "public", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 153, "title": "New Task", "description": null, "full_description": "FINAL TEST: This task should display Task #[ID] as the title and this text should appear as the description in both the card and the Project Flow Board drawer.", "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-27 13:02:09", "updated_at": "2025-06-27 13:02:09", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 151, "title": "New Task", "description": null, "full_description": "Testing the fixed field mapping - this description should appear in the Project Flow Board drawer and the card should show Task #[ID] as the title.", "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-27 12:56:14", "updated_at": "2025-06-27 12:56:14", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 150, "title": "New Task", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-27 12:51:12", "updated_at": "2025-06-27 12:51:12", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 149, "title": "Moderator Task NGP fetch", "description": null, "full_description": "Ohh~  誤解了，抱歉，了解是刀線文件而非實體刀模。但我認為你還是跳過了太多個步驟啦！\n \n我們的義務是將客人提供的書本掃描，並重新繪製刀線，以「還原」當初的生產文件。\n \n至於您提及的point 123已屬於「內容編輯」，超出了文件還原的範疇。此部分工作應由客人或其新的生產商在收到客人認可的文件後自行處理更為妥當。\n \n主要原因有兩個：\n \n權責分明：我們專注與「歸還」，客人則需要「認可」文件。之後才能有其他「做新」的動作。\n \n流程正確：試想我們持有完整檔案或菲林，今天也只會是歸還檔案，並不會涉及編輯環節。\n \n因此，我們將會專注於完成掃描及還原刀線的工作，並將檔案提供給您們，以履行我們的義務。\n \n希望你能理解我們的立場，謝謝。", "event_log": "", "status": "todo", "priority": "high", "progress": 67, "due_date": "2025-06-26T16:00:00.000Z", "completed_date": null, "created_by": 2, "assigned_to": null, "created_at": "2025-06-25 07:04:23", "updated_at": "2025-06-25 20:54:47", "full_description_edit_count": 3, "full_description_last_edited_at": "2025-06-25 15:08:28", "full_description_last_edited_by": 2, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-25 11:03:26", "event_log_last_edited_by": 2, "visibility": "public", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 140, "title": "Website Redesign", "description": "Update company website with new branding", "full_description": "🚀 OPTIMISTIC UI TEST - This description should appear INSTANTLY in the drawer when I click Save!\n\nThis is a comprehensive website redesign project with optimistic UI updates.\n\nKey features:\n• ⚡ Instant visual feedback with optimistic updates\n• 🎨 Modern responsive design\n• 🚀 Improved user experience\n• ⚡ Better performance\n• ♿ Enhanced accessibility\n• 🔄 Background server synchronization", "event_log": null, "status": "todo", "priority": "high", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 20:30:56", "updated_at": "2025-06-24 21:24:02", "full_description_edit_count": 6, "full_description_last_edited_at": "2025-06-24 21:08:26", "full_description_last_edited_by": 1, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 141, "title": "Database Migration", "description": "Migrate legacy database to new infrastructure", "full_description": null, "event_log": null, "status": "todo", "priority": "high", "progress": 15, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 20:30:56", "updated_at": "2025-06-24 20:30:56", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 142, "title": "User Authentication System", "description": "Implement secure user authentication", "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 25, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 20:30:56", "updated_at": "2025-06-24 20:30:56", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 143, "title": "Mobile App Development", "description": "Build iOS and Android applications", "full_description": null, "event_log": null, "status": "inProgress", "priority": "medium", "progress": 40, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 20:30:56", "updated_at": "2025-06-24 20:30:56", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 144, "title": "API Documentation", "description": "Create comprehensive API documentation", "full_description": null, "event_log": null, "status": "inProgress", "priority": "medium", "progress": 60, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 20:30:56", "updated_at": "2025-06-24 20:30:56", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 145, "title": "Performance Optimization", "description": "Optimize application performance", "full_description": null, "event_log": null, "status": "inProgress", "priority": "low", "progress": 75, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 20:30:56", "updated_at": "2025-06-24 20:30:56", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 146, "title": "Brand Guidelines", "description": "Company brand identity documentation", "full_description": null, "event_log": null, "status": "completed", "priority": "high", "progress": 100, "due_date": null, "completed_date": "2025-06-25T07:37:43.422Z", "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 20:30:56", "updated_at": "2025-06-25 07:37:43", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 147, "title": "Security Audit", "description": "Comprehensive security assessment", "full_description": null, "event_log": null, "status": "completed", "priority": "high", "progress": 100, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 20:30:56", "updated_at": "2025-06-24 20:30:56", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 148, "title": "Email Templates", "description": "Design and implement email templates", "full_description": null, "event_log": null, "status": "completed", "priority": "low", "progress": 100, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 20:30:56", "updated_at": "2025-06-24 20:30:56", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 125, "title": "Test Project", "description": "Test description", "full_description": "Full test description", "event_log": null, "status": "inProgress", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": 2, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:52:37", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": "moderator", "assignee_email": "<EMAIL>"}, {"id": 126, "title": "試驗卡牌", "description": null, "full_description": "第4次內容更新。", "event_log": "第4次事件更新。", "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-25 09:56:22", "full_description_edit_count": 5, "full_description_last_edited_at": "2025-06-24 21:11:33", "full_description_last_edited_by": 1, "event_log_edit_count": 4, "event_log_last_edited_at": "2025-06-24 21:11:40", "event_log_last_edited_by": 1, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 127, "title": "Updated Title", "description": "Updated description", "full_description": "Can I edit this? v2", "event_log": "", "status": "inProgress", "priority": "high", "progress": 100, "due_date": "2025-06-10T16:00:00.000Z", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:57:27", "full_description_edit_count": 3, "full_description_last_edited_at": "2025-06-24 19:57:03", "full_description_last_edited_by": 1, "event_log_edit_count": 2, "event_log_last_edited_at": "2025-06-24 19:52:51", "event_log_last_edited_by": 1, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 128, "title": "Updated Title", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:05:36", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 129, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:05:36", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 131, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:05:36", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 132, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:05:36", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 133, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:05:36", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 134, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:05:36", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 135, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:05:36", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 136, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:05:36", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 137, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:05:36", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 138, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:05:36", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 139, "title": "Test Project", "description": null, "full_description": "Full description", "event_log": "Event log", "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 19:05:36", "updated_at": "2025-06-24 19:05:36", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 110, "title": "Test Project", "description": "Test description", "full_description": "Full test description", "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": 2, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:34:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": "moderator", "assignee_email": "<EMAIL>"}, {"id": 112, "title": "<PERSON>", "description": "Updated description", "full_description": "Can I edit this as admin?", "event_log": null, "status": "inProgress", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:39:49", "full_description_edit_count": 3, "full_description_last_edited_at": "2025-06-24 17:39:49", "full_description_last_edited_by": 1, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 113, "title": "Updated Title", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:34:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 114, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:34:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 116, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:34:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 117, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:34:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 118, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:34:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 119, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:34:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 120, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:34:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 121, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:34:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 122, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:34:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 123, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:34:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 124, "title": "Test Project", "description": null, "full_description": "Full description", "event_log": "Event log", "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:34:30", "updated_at": "2025-06-24 17:34:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 109, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:32:29", "updated_at": "2025-06-24 17:32:29", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 108, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:31:32", "updated_at": "2025-06-24 17:31:32", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 107, "title": "Updated Title", "description": "Updated description", "full_description": null, "event_log": null, "status": "inProgress", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:31:14", "updated_at": "2025-06-24 17:31:14", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 106, "title": "Updated Title", "description": "Updated description", "full_description": null, "event_log": null, "status": "inProgress", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:30:22", "updated_at": "2025-06-24 17:30:22", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 105, "title": "Original Title", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:29:17", "updated_at": "2025-06-24 17:29:17", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 90, "title": "Test Project", "description": "Test description", "full_description": "Full test description", "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": 2, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": "moderator", "assignee_email": "<EMAIL>"}, {"id": 91, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 92, "title": "Original Title", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 93, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 94, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 95, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 96, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 97, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 98, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 99, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 100, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 101, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 102, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 103, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 104, "title": "Test Project", "description": null, "full_description": "Full description", "event_log": "Event log", "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:27:44", "updated_at": "2025-06-24 17:27:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 89, "title": "Mod Task test.", "description": null, "full_description": "Can I write anything here?", "event_log": "Can admin type and write anything?", "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 2, "assigned_to": null, "created_at": "2025-06-24 17:10:28", "updated_at": "2025-06-24 17:13:15", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-24 17:11:25", "full_description_last_edited_by": 2, "event_log_edit_count": 2, "event_log_last_edited_at": "2025-06-24 17:13:15", "event_log_last_edited_by": 1, "visibility": "public", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 74, "title": "Test Project", "description": "Test description", "full_description": "1111111Full test description", "event_log": "Test result shows that mods can change things here.", "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": 2, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:10:07", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-24 17:07:12", "full_description_last_edited_by": 1, "event_log_edit_count": 3, "event_log_last_edited_at": "2025-06-24 17:10:07", "event_log_last_edited_by": 2, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": "moderator", "assignee_email": "<EMAIL>"}, {"id": 75, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 76, "title": "Original Title", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 77, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 78, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 79, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 80, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 81, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 82, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 83, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 84, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 85, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 86, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 87, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 88, "title": "Test Project", "description": null, "full_description": "Full description", "event_log": "Event log", "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 17:01:25", "updated_at": "2025-06-24 17:01:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 73, "title": "Test Project", "description": "Test description", "full_description": "Full test description", "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": 2, "created_at": "2025-06-24 17:00:54", "updated_at": "2025-06-24 17:00:54", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": "moderator", "assignee_email": "<EMAIL>"}, {"id": 58, "title": "Test Project", "description": "Test description", "full_description": "Full test description", "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": 2, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": "moderator", "assignee_email": "<EMAIL>"}, {"id": 59, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 60, "title": "Original Title", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 61, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 62, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 63, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 64, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 65, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 66, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 67, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 68, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 69, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 70, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 71, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 72, "title": "Test Project", "description": null, "full_description": "Full description", "event_log": "Event log", "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:59:10", "updated_at": "2025-06-24 16:59:10", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 43, "title": "Test Project", "description": "Test description", "full_description": "Full test description", "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": 2, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": "moderator", "assignee_email": "<EMAIL>"}, {"id": 44, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 45, "title": "Original Title", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 46, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 47, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 48, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 49, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 50, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 51, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 52, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 53, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 54, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 55, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 56, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 57, "title": "Test Project", "description": null, "full_description": "Full description", "event_log": "Event log", "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:58:25", "updated_at": "2025-06-24 16:58:25", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 42, "title": "1", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:50:49", "updated_at": "2025-06-24 16:50:49", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 27, "title": "Test Project", "description": "Test description", "full_description": "Full test description", "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": 2, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": "moderator", "assignee_email": "<EMAIL>"}, {"id": 28, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 29, "title": "Original Title", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 30, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 31, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 32, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 33, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 34, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 35, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 36, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 37, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 38, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 39, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 40, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 41, "title": "Test Project", "description": null, "full_description": "Full description", "event_log": "Event log", "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 16:02:13", "updated_at": "2025-06-24 16:02:13", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 12, "title": "Test Project", "description": "Test description", "full_description": "Full test description", "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": 2, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": "moderator", "assignee_email": "<EMAIL>"}, {"id": 13, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 14, "title": "Original Title", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 15, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 16, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 17, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 18, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 19, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 20, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 21, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 22, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 23, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 24, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 25, "title": "Test Project", "description": null, "full_description": null, "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 26, "title": "Test Project", "description": null, "full_description": "Full description", "event_log": "Event log", "status": "todo", "priority": "medium", "progress": 0, "due_date": "2024-12-31T23:59:59Z", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-24 15:57:30", "updated_at": "2025-06-24 15:57:30", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 11, "title": "Moderator's Task", "description": null, "full_description": "Can anyone update this?", "event_log": "11:17 AM", "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "created_by": 2, "assigned_to": null, "created_at": "2025-06-22 03:16:39", "updated_at": "2025-06-22 03:18:16", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-22 03:16:55", "full_description_last_edited_by": 2, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-22 03:17:19", "event_log_last_edited_by": 2, "visibility": "private", "creator_username": "moderator", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 2, "title": "Website Redesign", "description": "Update company website with new branding", "full_description": "Complete overhaul of the company website including new branding, improved user experience, mobile responsiveness, and modern design patterns. This project involves collaboration with the design team and requires extensive testing across multiple devices and browsers.", "event_log": "", "status": "completed", "priority": "low", "progress": 100, "due_date": "2024-12-15", "completed_date": "2025-06-23T17:33:50.489Z", "created_by": 1, "assigned_to": null, "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-23 17:33:50", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 3, "title": "改變人生，躺平由我不由天", "description": "Migrate legacy database to new infrastructure", "full_description": "Plan and execute migration of legacy database systems to modern cloud infrastructure. Includes data validation, backup procedures, and minimal downtime deployment strategy. New workload updates. \n - 0534v2", "event_log": "Test v3 456", "status": "todo", "priority": "high", "progress": 25, "due_date": "2024-12-20", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-24 15:39:06", "full_description_edit_count": 9, "full_description_last_edited_at": "2025-06-24 15:39:06", "full_description_last_edited_by": 1, "event_log_edit_count": 8, "event_log_last_edited_at": "2025-06-24 08:56:06", "event_log_last_edited_by": 1, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 4, "title": "User Authentication System", "description": "Implement secure user authentication", "full_description": "Design and implement a comprehensive user authentication system with multi-factor authentication, password policies, and session management.", "event_log": "", "status": "inProgress", "priority": "medium", "progress": 25, "due_date": "2025-01-10", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-23 06:55:36", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 5, "title": "Mobile App Development", "description": "Build iOS and Android applications", "full_description": "123123213123Create native mobile applications for both iOS and Android platforms with cross-platform functionality. Features include user authentication, real-time notifications, and offline capabilities.", "event_log": "", "status": "inProgress", "priority": "medium", "progress": 40, "due_date": "2025-01-30", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-23 06:25:07", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 6, "title": "API Documentation", "description": "Create comprehensive API documentation", "full_description": "Develop detailed API documentation including endpoint descriptions, request/response examples, authentication methods, and integration guides for third-party developers.", "event_log": "", "status": "inProgress", "priority": "medium", "progress": 100, "due_date": "2024-12-30", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-23 17:43:49", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 7, "title": "Performance Optimization", "description": "Optimize application performance", "full_description": "Analyze and improve application performance including database query optimization, caching strategies, and frontend bundle size reduction.", "event_log": "", "status": "todo", "priority": "low", "progress": 75, "due_date": "2025-02-15", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-23 06:15:01", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 8, "title": "Brand Guidelines", "description": "Company brand identity documentation", "full_description": "Comprehensive brand guidelines including logo usage, typography, color palette, and brand voice. This document serves as the foundation for all company communications and design work.", "event_log": "", "status": "todo", "priority": "high", "progress": 0, "due_date": "2024-11-30", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-23 06:26:03", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 9, "title": "Security Audit", "description": "Comprehensive security assessment", "full_description": "Complete security audit of all systems including penetration testing, vulnerability assessment, and compliance review. All critical issues have been addressed.", "event_log": "", "status": "completed", "priority": "high", "progress": 100, "due_date": "2024-11-15", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-18 18:40:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 10, "title": "Email Templates", "description": "Design and implement email templates", "full_description": "Responsive email templates for notifications, newsletters, and transactional emails. All templates are tested across major email clients and include dark mode support.", "event_log": "", "status": "completed", "priority": "low", "progress": 100, "due_date": "2024-11-20", "completed_date": null, "created_by": 1, "assigned_to": null, "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-18 18:40:44", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}, {"id": 1, "title": "This is an experimental Title", "description": null, "full_description": "**Fixed Issues in Task Master Project Flow Board *Slight edit v1**\n", "event_log": "Fixed Issues in Task Master Project Flow Board v2\nIssue 1: Activity Timeline Bug - RESOLVED\nProblem: Activity Timeline was displaying raw JSON metadata instead of user-friendly descriptions for comment edit activities.\n\nSolution Implemented:\n\nAdded comment_edit activity type support to getActivityIcon() function:\nAdded specific icon (Edit2) and color scheme for comment edit activities\nCreated formatActivityDescription() function to parse and format activity descriptions:\nParses JSON metadata for comment edit activities\nExtracts version number and formats as \"edited comment (V3)\"\nFalls back to original description for other activity types\nHandles parsing errors gracefully\nUpdated both Overview and Activity Timeline sections to use the formatted descriptions:\nOverview Recent Activity section now shows formatted descriptions\nActivity Timeline section shows formatted descriptions and removes raw metadata display\nRemoved the metadata display box that was showing raw JSON\nResult: Activity Timeline now shows clean, user-friendly messages like \"Harry edited comment (V3)\" instead of raw JSON objects.\n\nIssue 2: Comment Section Placeholder Text - RESOLVED\nProblem: Comment input had incorrect placeholder text and additional descriptive text.\n\nSolution Implemented:\n\nUpdated placeholder text from:\n\"Add a comment... (Shift+Enter for line breaks, Enter to send)\"\nTo: \"Add a comment... (Shift+Enter for line breaks)\"\nRemoved additional descriptive text:\nRemoved the paragraph below comment input that mentioned \"Press Shift+Enter for line breaks, Enter to send\"\nThis eliminates redundant information and cleans up the interface\nResult: Comment input now has the exact placeholder text requested and no additional descriptive text.\n\nKey Code Changes:\nProsperous-Codex/src/components/task-master\n// Added comment_edit support to activity icons\ncase 'comment_edit':\n  return { icon: Edit2, color: 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' };\n\n// New function to format activity descriptions\nconst formatActivityDescription = (activity: any) => {\n  const { activityType, description, metadata, username } = activity;\n  \n  // For comment edit activities, parse metadata and create user-friendly \nTesting Results:\n✅ Activity Timeline displays formatted descriptions instead of raw JSON\n✅ Comment edit activities show as \"edited comment (V3)\" format\n✅ Icons display correctly for all activity types including comment edits\n✅ Comment placeholder text updated to exact specification\n✅ Removed redundant descriptive text below comment input\n✅ All existing comment edit history functionality preserved\n✅ Application compiles and runs without errors\nThe Task Master Project Flow Board now provides a clean, user-friendly activity timeline and simplified comment interface as requested.", "status": "completed", "priority": "medium", "progress": 100, "due_date": null, "completed_date": "2025-06-23T06:25:50.766Z", "created_by": 1, "assigned_to": null, "created_at": "2025-06-18 18:01:33", "updated_at": "2025-06-23 06:25:50", "full_description_edit_count": 4, "full_description_last_edited_at": "2025-06-22 18:40:11", "full_description_last_edited_by": 1, "event_log_edit_count": 2, "event_log_last_edited_at": "2025-06-21 23:07:40", "event_log_last_edited_by": 1, "visibility": "public", "creator_username": "<PERSON>", "creator_email": "<EMAIL>", "assignee_username": null, "assignee_email": null}], "comments": [{"id": 24, "project_id": 163, "parent_comment_id": null, "author_id": 2, "content": "HARRY THIS IS AWESOME", "created_at": "2025-06-30 11:44:41", "updated_at": "2025-06-30 11:44:41", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "moderator", "author_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 22, "project_id": 149, "parent_comment_id": null, "author_id": 2, "content": "Can I comment?", "created_at": "2025-06-25 20:57:30", "updated_at": "2025-06-25 20:57:30", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "moderator", "author_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 21, "project_id": 126, "parent_comment_id": null, "author_id": 1, "content": "Comment. 更新1.", "created_at": "2025-06-24 20:26:35", "updated_at": "2025-06-24 20:26:44", "is_edited": 1, "edit_count": 1, "last_edited_at": "2025-06-24 20:26:44", "last_edited_by": 1, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 20, "project_id": 127, "parent_comment_id": null, "author_id": 1, "content": "Does this work?", "created_at": "2025-06-24 19:51:05", "updated_at": "2025-06-24 19:51:05", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 19, "project_id": 89, "parent_comment_id": null, "author_id": 1, "content": "No access, can I write?", "created_at": "2025-06-24 17:13:25", "updated_at": "2025-06-24 17:13:25", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "Mod Task test."}, {"id": 18, "project_id": 74, "parent_comment_id": null, "author_id": 2, "content": "Can't", "created_at": "2025-06-24 17:09:13", "updated_at": "2025-06-24 17:09:13", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "moderator", "author_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 17, "project_id": 74, "parent_comment_id": null, "author_id": 1, "content": "bro, can I see?", "created_at": "2025-06-24 17:07:43", "updated_at": "2025-06-24 17:07:43", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 16, "project_id": 3, "parent_comment_id": null, "author_id": 1, "content": "123", "created_at": "2025-06-24 15:36:10", "updated_at": "2025-06-24 15:36:10", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 15, "project_id": 3, "parent_comment_id": null, "author_id": 1, "content": "0534", "created_at": "2025-06-23 08:41:53", "updated_at": "2025-06-23 09:34:30", "is_edited": 1, "edit_count": 2, "last_edited_at": "2025-06-23 09:34:30", "last_edited_by": 1, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 13, "project_id": 3, "parent_comment_id": null, "author_id": 1, "content": "不要用工作平台的留言功能嘮嗑，謝謝。", "created_at": "2025-06-23 06:53:22", "updated_at": "2025-06-23 06:53:22", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 12, "project_id": 3, "parent_comment_id": null, "author_id": 1, "content": "工作了沒有。", "created_at": "2025-06-23 06:52:42", "updated_at": "2025-06-23 06:53:43", "is_edited": 1, "edit_count": 1, "last_edited_at": "2025-06-23 06:53:43", "last_edited_by": 1, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 11, "project_id": 11, "parent_comment_id": null, "author_id": 2, "content": "Heyya!", "created_at": "2025-06-22 03:17:21", "updated_at": "2025-06-22 03:17:21", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "moderator", "author_email": "<EMAIL>", "project_title": "Moderator's Task"}, {"id": 10, "project_id": 1, "parent_comment_id": null, "author_id": 1, "content": "Awesome!  See you later!", "created_at": "2025-06-21 21:58:25", "updated_at": "2025-06-21 22:00:08", "is_edited": 1, "edit_count": 1, "last_edited_at": "2025-06-21 22:00:08", "last_edited_by": 1, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 8, "project_id": 1, "parent_comment_id": null, "author_id": 1, "content": "Wow, it is looking like it is working nicely. v2", "created_at": "2025-06-21 13:18:28", "updated_at": "2025-06-21 19:15:01", "is_edited": 1, "edit_count": 3, "last_edited_at": "2025-06-21 19:15:01", "last_edited_by": 1, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 7, "project_id": 2, "parent_comment_id": null, "author_id": 1, "content": "Are you sure about that, <PERSON>?", "created_at": "2025-06-21 13:16:38", "updated_at": "2025-06-21 13:16:38", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 6, "project_id": 9, "parent_comment_id": null, "author_id": 1, "content": "Updated requirements based on stakeholder feedback.", "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-18 18:40:44", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "Security Audit"}, {"id": 5, "project_id": 7, "parent_comment_id": null, "author_id": 1, "content": "Initial project setup completed. Ready for development phase.", "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-18 18:40:44", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "Performance Optimization"}, {"id": 4, "project_id": 6, "parent_comment_id": null, "author_id": 1, "content": "Initial project setup completed. Ready for development phase.", "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-18 18:40:44", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "API Documentation"}, {"id": 3, "project_id": 5, "parent_comment_id": null, "author_id": 1, "content": "Updated requirements based on stakeholder feedback.111", "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-21 13:17:06", "is_edited": 1, "edit_count": 1, "last_edited_at": "2025-06-21 13:17:06", "last_edited_by": 1, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "Mobile App Development"}, {"id": 2, "project_id": 5, "parent_comment_id": null, "author_id": 1, "content": "Initial project setup completed. Ready for development phase.", "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-18 18:40:44", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "Mobile App Development"}, {"id": 1, "project_id": 2, "parent_comment_id": null, "author_id": 1, "content": "Initial project setup completed. Ready for development phase.", "created_at": "2025-06-18 18:40:44", "updated_at": "2025-06-18 18:40:44", "is_edited": 0, "edit_count": 0, "last_edited_at": null, "last_edited_by": null, "author_username": "<PERSON>", "author_email": "<EMAIL>", "project_title": "Website Redesign"}], "activities": [{"id": 756, "project_id": 169, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-30 17:41:17", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 755, "project_id": 163, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-30 17:40:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 754, "project_id": 163, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-30 17:40:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 737, "project_id": 168, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-30 17:23:55", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 735, "project_id": 166, "task_id": 175, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.2: 從識別出的供應商那裡獲取初步的可行性及成本估算。\" in task \"» 任務 2：研究處理厚書的印刷商能力（若適用）\"", "metadata": null, "created_at": "2025-06-30 16:27:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：書本項目 // CallKodi"}, {"id": 734, "project_id": 166, "task_id": 174, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.1: 識別具有處理大尺寸或厚書裝訂專門設備的潛在供應商或合作夥伴。\" in task \"» 任務 2：研究處理厚書的印刷商能力（若適用）\"", "metadata": null, "created_at": "2025-06-30 16:27:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：書本項目 // CallKodi"}, {"id": 733, "project_id": 166, "task_id": 173, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» 任務 2：研究處理厚書的印刷商能力（若適用）\"", "metadata": null, "created_at": "2025-06-30 16:27:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：書本項目 // CallKodi"}, {"id": 732, "project_id": 166, "task_id": 172, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.3: 若客戶拒絕該提案，則探索其他解決方案，或明確告知無法滿足當前規格的要求。\" in task \"» 任務 1：評估客戶關於書本分割建議的回應\"", "metadata": null, "created_at": "2025-06-30 16:27:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：書本項目 // CallKodi"}, {"id": 731, "project_id": 166, "task_id": 171, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.2: 若客戶同意分割，則評估可行性，並提供針對三冊書項目的更新報價及時程。\" in task \"» 任務 1：評估客戶關於書本分割建議的回應\"", "metadata": null, "created_at": "2025-06-30 16:27:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：書本項目 // CallKodi"}, {"id": 730, "project_id": 166, "task_id": 170, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.1: 接收並分析客戶對三冊書提案的回覆。\" in task \"» 任務 1：評估客戶關於書本分割建議的回應\"", "metadata": null, "created_at": "2025-06-30 16:27:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：書本項目 // CallKodi"}, {"id": 729, "project_id": 166, "task_id": 169, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» 任務 1：評估客戶關於書本分割建議的回應\"", "metadata": null, "created_at": "2025-06-30 16:27:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：書本項目 // CallKodi"}, {"id": 728, "project_id": 166, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-30 16:27:22", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：書本項目 // CallKodi"}, {"id": 727, "project_id": 165, "task_id": 168, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.2: If the client indicates flexibility on paper GSM, research and identify paper stocks that fit within Prosperous's capabilities and meet project requirements for the revised scope.\" in task \"» Task 2: Evaluate Potential for Revised Project Scope (Three-Volume Split)\"", "metadata": null, "created_at": "2025-06-30 12:16:37", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Book Project // CallKodi"}, {"id": 726, "project_id": 165, "task_id": 167, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.1: Analyze production impact of a three-volume split.\" in task \"» Task 2: Evaluate Potential for Revised Project Scope (Three-Volume Split)\"", "metadata": null, "created_at": "2025-06-30 12:16:37", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Book Project // CallKodi"}, {"id": 725, "project_id": 165, "task_id": 166, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» Task 2: <PERSON><PERSON><PERSON> for Revised Project Scope (Three-Volume Split)\"", "metadata": null, "created_at": "2025-06-30 12:16:37", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Book Project // CallKodi"}, {"id": 724, "project_id": 165, "task_id": 165, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.2: Be ready to promptly respond to the client's feedback, whether it's an acceptance of the three-volume proposal, a request for revised quotes, or a need to explore alternative solutions.\" in task \"» Task 1: Await Client Feedback on Project Feasibility and Proposed Solution\"", "metadata": null, "created_at": "2025-06-30 12:16:37", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Book Project // CallKodi"}, {"id": 723, "project_id": 165, "task_id": 164, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.1: Monitor communications from <PERSON> for client feedback.\" in task \"» Task 1: Await Client Feedback on Project Feasibility and Proposed Solution\"", "metadata": null, "created_at": "2025-06-30 12:16:37", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Book Project // CallKodi"}, {"id": 722, "project_id": 165, "task_id": 163, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» Task 1: Await Client Feedback on Project Feasibility and Proposed Solution\"", "metadata": null, "created_at": "2025-06-30 12:16:37", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Book Project // CallKodi"}, {"id": 721, "project_id": 165, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-30 12:16:21", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Book Project // CallKodi"}, {"id": 710, "project_id": 163, "task_id": null, "user_id": 2, "activity_type": "assignment", "description": "Added <EMAIL> to project team", "metadata": null, "created_at": "2025-06-30 11:44:53", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 709, "project_id": 163, "task_id": null, "user_id": 2, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-30 11:44:41", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 708, "project_id": 163, "task_id": 151, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"» 任務 2: 追蹤生產進度與樣品寄送協調\" status to completed", "metadata": null, "created_at": "2025-06-30 11:44:30", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 707, "project_id": 163, "task_id": 152, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"2.1: 確認成品樣品的寄送與簽收 - 描述: 追蹤舊書樣與手袋樣品的寄送狀況，並確認已成功寄達指定工廠，以便後續的生產作業。\" status to completed", "metadata": null, "created_at": "2025-06-30 11:44:26", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 706, "project_id": 163, "task_id": 153, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"2.2: 追蹤大貨生產進度與預計完成時間 - 描述: 根據供應商提供的進度，密切關注大貨的生產情況，並確認其預計完成日期，以利後續出貨計畫的安排。\" status to completed", "metadata": null, "created_at": "2025-06-30 11:44:25", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 705, "project_id": 163, "task_id": 148, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"» 任務 1: 確認與修正最終包裝清單\" status to completed", "metadata": null, "created_at": "2025-06-30 11:44:19", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 704, "project_id": 163, "task_id": 149, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"1.1: 核實每箱裝載數量與重量的準確性 - 描述: 根據先前溝通的錯誤資訊（如每箱150個、重量不對），與供應商協調並修正包裝清單中的數量與重量數據。\" status to completed", "metadata": null, "created_at": "2025-06-30 11:44:15", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 703, "project_id": 163, "task_id": 150, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"1.2: 確保所有紙袋產品均已列入包裝清單 - 描述: 檢查並補齊先前因遺漏而未列於包裝清單的兩款紙袋資訊，確保清單的完整性。\" status to completed", "metadata": null, "created_at": "2025-06-30 11:44:13", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 702, "project_id": 163, "task_id": 153, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.2: 追蹤大貨生產進度與預計完成時間 - 描述: 根據供應商提供的進度，密切關注大貨的生產情況，並確認其預計完成日期，以利後續出貨計畫的安排。\" in task \"» 任務 2: 追蹤生產進度與樣品寄送協調\"", "metadata": null, "created_at": "2025-06-30 11:44:00", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 701, "project_id": 163, "task_id": 152, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.1: 確認成品樣品的寄送與簽收 - 描述: 追蹤舊書樣與手袋樣品的寄送狀況，並確認已成功寄達指定工廠，以便後續的生產作業。\" in task \"» 任務 2: 追蹤生產進度與樣品寄送協調\"", "metadata": null, "created_at": "2025-06-30 11:44:00", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 700, "project_id": 163, "task_id": 151, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» 任務 2: 追蹤生產進度與樣品寄送協調\"", "metadata": null, "created_at": "2025-06-30 11:44:00", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 699, "project_id": 163, "task_id": 150, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.2: 確保所有紙袋產品均已列入包裝清單 - 描述: 檢查並補齊先前因遺漏而未列於包裝清單的兩款紙袋資訊，確保清單的完整性。\" in task \"» 任務 1: 確認與修正最終包裝清單\"", "metadata": null, "created_at": "2025-06-30 11:44:00", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 698, "project_id": 163, "task_id": 149, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.1: 核實每箱裝載數量與重量的準確性 - 描述: 根據先前溝通的錯誤資訊（如每箱150個、重量不對），與供應商協調並修正包裝清單中的數量與重量數據。\" in task \"» 任務 1: 確認與修正最終包裝清單\"", "metadata": null, "created_at": "2025-06-30 11:44:00", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 697, "project_id": 163, "task_id": 148, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» 任務 1: 確認與修正最終包裝清單\"", "metadata": null, "created_at": "2025-06-30 11:43:59", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 696, "project_id": 163, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-30 11:43:46", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 695, "project_id": 163, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-30 11:43:40", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書專案 // [客戶名稱待定]"}, {"id": 694, "project_id": 162, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-30 11:39:09", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 693, "project_id": 162, "task_id": 147, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"Bring your own water bottle\" status to completed", "metadata": null, "created_at": "2025-06-30 11:39:00", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 692, "project_id": 162, "task_id": 147, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"Bring your own water bottle\"", "metadata": null, "created_at": "2025-06-30 11:38:55", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 691, "project_id": 162, "task_id": 146, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.3: Formulate preliminary recommendations or next steps.\" in task \"» Task 2: Summarize and Identify Next Steps for Quotations\"", "metadata": null, "created_at": "2025-06-30 11:35:26", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 690, "project_id": 162, "task_id": 145, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.2: Identify any potential discrepancies or areas requiring clarification.\" in task \"» Task 2: Summarize and Identify Next Steps for Quotations\"", "metadata": null, "created_at": "2025-06-30 11:35:26", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 689, "project_id": 162, "task_id": 144, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.1: Summarize key pricing and service points from the quotations.\" in task \"» Task 2: Summarize and Identify Next Steps for Quotations\"", "metadata": null, "created_at": "2025-06-30 11:35:26", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 688, "project_id": 162, "task_id": 143, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» Task 2: Summarize and Identify Next Steps for Quotations\"", "metadata": null, "created_at": "2025-06-30 11:35:26", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 687, "project_id": 162, "task_id": 142, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.5: Review the Shipping Order blank form.\" in task \"» Task 1: Review Updated Shipping Quotations from Publiship\"", "metadata": null, "created_at": "2025-06-30 11:35:25", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 686, "project_id": 162, "task_id": 141, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.4: Understand the difference between HK CFS rates for \"Freight Prepaid\" (HKD175.00/CBM) and FOB orders (HKD208.00/CBM).\" in task \"» Task 1: Review Updated Shipping Quotations from Publiship\"", "metadata": null, "created_at": "2025-06-30 11:35:25", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 685, "project_id": 162, "task_id": 140, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.3: Analyze Special Through Rate for LCL from HK (or Shenzhen) to door Australia warehouse.\" in task \"» Task 1: Review Updated Shipping Quotations from Publiship\"", "metadata": null, "created_at": "2025-06-30 11:35:25", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 684, "project_id": 162, "task_id": 139, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.2: Analyze \"Great Wall Printing-250627b\" (Special Through Rate for LCL Hong Kong to door UK warehouse).\" in task \"» Task 1: Review Updated Shipping Quotations from Publiship\"", "metadata": null, "created_at": "2025-06-30 11:35:25", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 683, "project_id": 162, "task_id": 138, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.1: Analyze \"Great Wall Printing-250627a\" (Special offering for LCL and FCL from HK/Shenzhen to worldwide).\" in task \"» Task 1: Review Updated Shipping Quotations from Publiship\"", "metadata": null, "created_at": "2025-06-30 11:35:25", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 682, "project_id": 162, "task_id": 137, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» Task 1: Review Updated Shipping Quotations from Publiship\"", "metadata": null, "created_at": "2025-06-30 11:35:25", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 681, "project_id": 162, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-30 11:33:50", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Project Briefing: Publiship Shipping Quotation // Great Wall Printing Company Ltd"}, {"id": 680, "project_id": 161, "task_id": 136, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.2: 根據生產狀態確認已完成訂單的最終發貨日期。\" in task \"» 任務 2: 確認大貨生產完成及發貨安排\"", "metadata": null, "created_at": "2025-06-30 10:25:44", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書 // 客戶"}, {"id": 679, "project_id": 161, "task_id": 135, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.1: 從工廠獲取「以色列小書」及袋子的最新生產狀態更新。\" in task \"» 任務 2: 確認大貨生產完成及發貨安排\"", "metadata": null, "created_at": "2025-06-30 10:25:44", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書 // 客戶"}, {"id": 678, "project_id": 161, "task_id": 134, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» 任務 2: 確認大貨生產完成及發貨安排\"", "metadata": null, "created_at": "2025-06-30 10:25:44", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書 // 客戶"}, {"id": 677, "project_id": 161, "task_id": 133, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.2: 確保所有其他出貨相關文件（例如發票、報關單等）均與最終確定的裝箱單一致。\" in task \"» 任務 1: 確定並核實裝箱單及出貨文件\"", "metadata": null, "created_at": "2025-06-30 10:25:44", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書 // 客戶"}, {"id": 676, "project_id": 161, "task_id": 132, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.1: 確認收到 Lily 的更新版裝箱單並核實其準確性。\" in task \"» 任務 1: 確定並核實裝箱單及出貨文件\"", "metadata": null, "created_at": "2025-06-30 10:25:44", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書 // 客戶"}, {"id": 675, "project_id": 161, "task_id": 131, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» 任務 1: 確定並核實裝箱單及出貨文件\"", "metadata": null, "created_at": "2025-06-30 10:25:43", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書 // 客戶"}, {"id": 674, "project_id": 161, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-30 10:24:50", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：以色列小書 // 客戶"}, {"id": 673, "project_id": 160, "task_id": 130, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.2: 協調出貨與物流安排\" in task \"» 任務 2: 追蹤大貨生產進度與出貨安排\"", "metadata": null, "created_at": "2025-06-30 10:15:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 672, "project_id": 160, "task_id": 129, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.1: 詢問大貨確切的完成日期\" in task \"» 任務 2: 追蹤大貨生產進度與出貨安排\"", "metadata": null, "created_at": "2025-06-30 10:15:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 671, "project_id": 160, "task_id": 128, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» 任務 2: 追蹤大貨生產進度與出貨安排\"", "metadata": null, "created_at": "2025-06-30 10:15:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 670, "project_id": 160, "task_id": 127, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.3: 向天泓印刷（Lily）確認最終包裝清單的準確性\" in task \"» 任務 1: 審核並確認最終包裝清單與出貨文件\"", "metadata": null, "created_at": "2025-06-30 10:15:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 669, "project_id": 160, "task_id": 126, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.2: 確認所有紙袋尾箱資訊已正確列出\" in task \"» 任務 1: 審核並確認最終包裝清單與出貨文件\"", "metadata": null, "created_at": "2025-06-30 10:15:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 668, "project_id": 160, "task_id": 125, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.1: 核對收到的更新版最終包裝清單\" in task \"» 任務 1: 審核並確認最終包裝清單與出貨文件\"", "metadata": null, "created_at": "2025-06-30 10:15:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 667, "project_id": 160, "task_id": 124, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» 任務 1: 審核並確認最終包裝清單與出貨文件\"", "metadata": null, "created_at": "2025-06-30 10:15:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 666, "project_id": 160, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-30 10:15:35", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 665, "project_id": 159, "task_id": 114, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"» 任務 1: 確認並更新最終裝箱資料\" status to completed", "metadata": null, "created_at": "2025-06-29 22:15:36", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 664, "project_id": 159, "task_id": 117, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"1.3: 將已確認無誤的最終裝箱資料發送給所有相關方。\" status to completed", "metadata": null, "created_at": "2025-06-29 22:15:34", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 663, "project_id": 159, "task_id": 118, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"» 任務 2: 追蹤生產及出貨進度\" status to completed", "metadata": null, "created_at": "2025-06-29 22:15:33", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 662, "project_id": 159, "task_id": 119, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"2.1: 密切關注「以色列小書」及兩款紙袋的生產進度。\" status to completed", "metadata": null, "created_at": "2025-06-29 22:15:31", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 661, "project_id": 159, "task_id": 120, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"2.2: 確認最終的大貨出貨日期及所有相關運送細節。\" status to completed", "metadata": null, "created_at": "2025-06-29 22:15:30", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 660, "project_id": 159, "task_id": 121, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"» 任務 3: 樣品管理與品質監控\" status to completed", "metadata": null, "created_at": "2025-06-29 22:15:29", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 659, "project_id": 159, "task_id": 122, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"3.1: 確保所有必需的樣品（舊書樣、手袋樣、蘭紙樣）已準確寄送並妥善接收。\" status to completed", "metadata": null, "created_at": "2025-06-29 22:15:29", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 658, "project_id": 159, "task_id": 123, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"3.2: 確認生產過程嚴格遵循批准的顏色對比和品質標準。\" status to completed", "metadata": null, "created_at": "2025-06-29 22:15:28", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 657, "project_id": 159, "task_id": 115, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"1.1: 核對每個紙箱的裝箱數量與重量是否符合要求。\" status to completed", "metadata": null, "created_at": "2025-06-29 22:15:26", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 656, "project_id": 159, "task_id": 116, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"1.2: 確保所有款式的紙袋均已準確列於尾箱資料中。\" status to completed", "metadata": null, "created_at": "2025-06-29 22:15:26", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 655, "project_id": 159, "task_id": 123, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"3.2: 確認生產過程嚴格遵循批准的顏色對比和品質標準。\" in task \"» 任務 3: 樣品管理與品質監控\"", "metadata": null, "created_at": "2025-06-29 22:14:19", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 654, "project_id": 159, "task_id": 122, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"3.1: 確保所有必需的樣品（舊書樣、手袋樣、蘭紙樣）已準確寄送並妥善接收。\" in task \"» 任務 3: 樣品管理與品質監控\"", "metadata": null, "created_at": "2025-06-29 22:14:19", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 653, "project_id": 159, "task_id": 121, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» 任務 3: 樣品管理與品質監控\"", "metadata": null, "created_at": "2025-06-29 22:14:19", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 652, "project_id": 159, "task_id": 120, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.2: 確認最終的大貨出貨日期及所有相關運送細節。\" in task \"» 任務 2: 追蹤生產及出貨進度\"", "metadata": null, "created_at": "2025-06-29 22:14:19", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 651, "project_id": 159, "task_id": 119, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"2.1: 密切關注「以色列小書」及兩款紙袋的生產進度。\" in task \"» 任務 2: 追蹤生產及出貨進度\"", "metadata": null, "created_at": "2025-06-29 22:14:19", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 650, "project_id": 159, "task_id": 118, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» 任務 2: 追蹤生產及出貨進度\"", "metadata": null, "created_at": "2025-06-29 22:14:19", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 649, "project_id": 159, "task_id": 117, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.3: 將已確認無誤的最終裝箱資料發送給所有相關方。\" in task \"» 任務 1: 確認並更新最終裝箱資料\"", "metadata": null, "created_at": "2025-06-29 22:14:19", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 648, "project_id": 159, "task_id": 116, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.2: 確保所有款式的紙袋均已準確列於尾箱資料中。\" in task \"» 任務 1: 確認並更新最終裝箱資料\"", "metadata": null, "created_at": "2025-06-29 22:14:18", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 647, "project_id": 159, "task_id": 115, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.1: 核對每個紙箱的裝箱數量與重量是否符合要求。\" in task \"» 任務 1: 確認並更新最終裝箱資料\"", "metadata": null, "created_at": "2025-06-29 22:14:18", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 646, "project_id": 159, "task_id": 114, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» 任務 1: 確認並更新最終裝箱資料\"", "metadata": null, "created_at": "2025-06-29 22:14:18", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 645, "project_id": 159, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-29 22:14:01", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Task #159"}, {"id": 644, "project_id": 158, "task_id": 113, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.4: 重新提交已修正的最終裝箱單。\" in task \"» 任務 1: 修正並重新提交最終裝箱單\"", "metadata": null, "created_at": "2025-06-29 22:07:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 643, "project_id": 158, "task_id": 112, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.3: 確保兩款紙袋在尾箱列表中被正確列出。\" in task \"» 任務 1: 修正並重新提交最終裝箱單\"", "metadata": null, "created_at": "2025-06-29 22:07:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 642, "project_id": 158, "task_id": 111, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.2: 核實「以色列小書」/「耶路撒冷小書」的正確每箱重量。\" in task \"» 任務 1: 修正並重新提交最終裝箱單\"", "metadata": null, "created_at": "2025-06-29 22:07:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 641, "project_id": 158, "task_id": 110, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1.1: 核實「以色列小書」/「耶路撒冷小書」的正確每箱數量。\" in task \"» 任務 1: 修正並重新提交最終裝箱單\"", "metadata": null, "created_at": "2025-06-29 22:07:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 640, "project_id": 158, "task_id": 109, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"» 任務 1: 修正並重新提交最終裝箱單\"", "metadata": null, "created_at": "2025-06-29 22:07:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 625, "project_id": 158, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-29 20:48:38", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 604, "project_id": 154, "task_id": 88, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"使用正確的資訊更新C/O申請表。\" in task \"處理產地來源證 (C/O) 申請\"", "metadata": null, "created_at": "2025-06-29 20:14:45", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：《白雲故事書》"}, {"id": 603, "project_id": 154, "task_id": 87, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"根據修訂後的規定，從「非相關營運方(Non-Party Operator)」獲取必要的報關資料。\" in task \"處理產地來源證 (C/O) 申請\"", "metadata": null, "created_at": "2025-06-29 20:14:45", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：《白雲故事書》"}, {"id": 602, "project_id": 154, "task_id": 86, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"審閱並理解Candy關於第5點「非相關營運方(Non-Party Operator)」及報關資料的具體修改要求。\" in task \"處理產地來源證 (C/O) 申請\"", "metadata": null, "created_at": "2025-06-29 20:14:45", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：《白雲故事書》"}, {"id": 601, "project_id": 154, "task_id": 85, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"處理產地來源證 (C/O) 申請\"", "metadata": null, "created_at": "2025-06-29 20:14:45", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：《白雲故事書》"}, {"id": 545, "project_id": 154, "task_id": 62, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-29 19:25:48", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：《白雲故事書》"}, {"id": 544, "project_id": 154, "task_id": 62, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-29 19:25:47", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：《白雲故事書》"}, {"id": 543, "project_id": 154, "task_id": 62, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-29 19:25:47", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：《白雲故事書》"}, {"id": 542, "project_id": 154, "task_id": 62, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-29 19:25:46", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：《白雲故事書》"}, {"id": 541, "project_id": 154, "task_id": 62, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"123\"", "metadata": null, "created_at": "2025-06-29 19:25:44", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：《白雲故事書》"}, {"id": 513, "project_id": 154, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-27 13:03:23", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "項目簡報：《白雲故事書》"}, {"id": 512, "project_id": 153, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-27 13:02:09", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 510, "project_id": 151, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-27 12:56:14", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 509, "project_id": 150, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-27 12:51:12", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "New Task"}, {"id": 508, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-25 20:57:30", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 507, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 20:47:50", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 506, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 20:47:49", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 505, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "assignment", "description": "Added <EMAIL> to project team", "metadata": null, "created_at": "2025-06-25 19:19:46", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 504, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "assignment", "description": "Added <EMAIL> to project team", "metadata": null, "created_at": "2025-06-25 19:19:35", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 503, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 19:04:08", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 502, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 19:04:05", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 501, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 19:04:05", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 500, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 19:03:58", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 499, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 19:03:54", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 498, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 19:03:54", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 497, "project_id": 149, "task_id": 45, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"Can I have an ice cream?\" status to todo", "metadata": null, "created_at": "2025-06-25 19:03:50", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 496, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 19:03:45", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 495, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 19:03:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 494, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 19:03:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 493, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 18:54:27", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 492, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 18:54:25", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 491, "project_id": 149, "task_id": 45, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"Can I have an ice cream?\" status to completed", "metadata": null, "created_at": "2025-06-25 18:54:22", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 490, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 16:13:15", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 489, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 16:13:13", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 488, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 16:13:13", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 487, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 16:13:11", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 486, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-25 16:13:10", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 485, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 16:11:56", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 484, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 16:11:53", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 483, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 16:11:53", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 482, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 16:11:50", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 481, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-25 16:11:49", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 480, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 16:11:45", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 479, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 16:11:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 478, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 16:11:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 477, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 16:11:38", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 476, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-25 16:11:37", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 475, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 16:08:48", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 474, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 16:06:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 473, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 16:06:40", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 472, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 16:06:40", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 471, "project_id": 149, "task_id": 45, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"Can I have an ice cream?\" status to todo", "metadata": null, "created_at": "2025-06-25 16:06:37", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 470, "project_id": 149, "task_id": 45, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"Can I have an ice cream?\" status to completed", "metadata": null, "created_at": "2025-06-25 15:55:07", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 469, "project_id": 149, "task_id": 45, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"Can I have an ice cream?\" status to todo", "metadata": null, "created_at": "2025-06-25 15:54:45", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 468, "project_id": 149, "task_id": 45, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"Can I have an ice cream?\" status to completed", "metadata": null, "created_at": "2025-06-25 15:54:44", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 467, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 15:52:34", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 466, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 15:52:31", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 465, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 15:52:31", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 464, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 15:52:28", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 463, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 15:52:15", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 462, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 15:52:15", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 461, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 15:51:55", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 460, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 15:51:52", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 459, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 15:51:50", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 458, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 15:51:47", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 457, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 15:51:43", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 456, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 15:51:40", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 455, "project_id": 149, "task_id": 45, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"Can I have an ice cream?\" status to todo", "metadata": null, "created_at": "2025-06-25 15:51:14", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 454, "project_id": 149, "task_id": 45, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"Can I have an ice cream?\" status to completed", "metadata": null, "created_at": "2025-06-25 15:51:12", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 453, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 15:50:53", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 452, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 15:50:45", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 451, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 15:50:45", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 450, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 15:50:45", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 449, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 15:50:45", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 448, "project_id": 149, "task_id": 45, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"Can I have an ice cream?\" status to todo", "metadata": null, "created_at": "2025-06-25 15:50:11", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 447, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 15:50:04", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 446, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 15:49:51", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 445, "project_id": 149, "task_id": 45, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"Can I have an ice cream?\" status to completed", "metadata": null, "created_at": "2025-06-25 15:19:37", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 444, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 15:19:08", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 443, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 15:19:02", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 442, "project_id": 149, "task_id": 45, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"Can I have an ice cream?\"", "metadata": null, "created_at": "2025-06-25 15:18:32", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 441, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 15:08:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 440, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 15:08:42", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 439, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-25 15:08:39", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 438, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 12:32:21", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 437, "project_id": 149, "task_id": 43, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 12:32:20", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 436, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-25 12:32:16", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 435, "project_id": 149, "task_id": 43, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-25 12:32:16", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 434, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 12:31:29", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 433, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 12:31:26", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 432, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 12:31:13", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 431, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-25 12:14:33", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 430, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 12:14:31", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 429, "project_id": 149, "task_id": 43, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 12:14:31", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 428, "project_id": 149, "task_id": 43, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-25 12:14:30", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 427, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-25 12:14:28", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 426, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 12:14:27", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 425, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 12:14:11", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 424, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 12:14:09", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 423, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 12:14:07", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 422, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 12:14:03", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 421, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-25 12:13:57", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 420, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 12:13:34", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 419, "project_id": 149, "task_id": 44, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"123\"", "metadata": null, "created_at": "2025-06-25 11:50:39", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 418, "project_id": 149, "task_id": 43, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 11:43:52", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 417, "project_id": 149, "task_id": 43, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to todo", "metadata": null, "created_at": "2025-06-25 11:31:21", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 416, "project_id": 149, "task_id": 43, "user_id": 2, "activity_type": "status_change", "description": "Changed task \"123\" status to completed", "metadata": null, "created_at": "2025-06-25 11:31:06", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 415, "project_id": 149, "task_id": 43, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"123\"", "metadata": null, "created_at": "2025-06-25 11:29:40", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 414, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "update", "description": "Changed project visibility to public", "metadata": null, "created_at": "2025-06-25 11:02:32", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 413, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Changed project visibility to public", "metadata": null, "created_at": "2025-06-25 09:56:22", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 412, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Changed project visibility to private", "metadata": null, "created_at": "2025-06-25 09:56:07", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 411, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Changed project visibility to private", "metadata": null, "created_at": "2025-06-25 09:55:57", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 410, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 09:55:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 409, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 09:55:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 408, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 09:55:50", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 407, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 09:55:50", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 406, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 09:55:40", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 405, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 09:55:40", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 404, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 09:55:40", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 403, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 09:55:40", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 402, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 09:55:34", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 401, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 09:55:32", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 400, "project_id": 146, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 07:37:43", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Brand Guidelines"}, {"id": 399, "project_id": 146, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 07:37:43", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Brand Guidelines"}, {"id": 398, "project_id": 146, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 07:37:43", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Brand Guidelines"}, {"id": 397, "project_id": 146, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 07:37:43", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Brand Guidelines"}, {"id": 396, "project_id": 146, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 07:37:31", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Brand Guidelines"}, {"id": 395, "project_id": 146, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 07:37:21", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Brand Guidelines"}, {"id": 394, "project_id": 146, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 07:37:21", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Brand Guidelines"}, {"id": 393, "project_id": 146, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 07:36:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Brand Guidelines"}, {"id": 392, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 07:20:57", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 391, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:20:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 390, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 07:20:55", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 389, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:20:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 388, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 07:20:31", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 387, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:20:29", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 386, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 07:20:16", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 385, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 07:20:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 384, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 07:20:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 383, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-25 07:20:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 382, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-25 07:20:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 381, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:20:00", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 380, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:20:00", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 379, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 07:10:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 378, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 07:10:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 377, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 07:10:09", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 376, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 07:10:09", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 375, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:09:48", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 374, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:09:34", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 373, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:09:34", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 372, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:09:17", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 371, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:09:17", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 370, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:08:59", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 369, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:08:50", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 368, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 07:08:33", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 367, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 07:08:33", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 366, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:08:18", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 365, "project_id": 149, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 07:08:18", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 364, "project_id": 149, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"Moderator Task\"", "metadata": null, "created_at": "2025-06-25 07:04:23", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator Task NGP fetch"}, {"id": 363, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 06:37:33", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 362, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-25 06:37:33", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 361, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 06:37:24", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 360, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 06:37:24", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 359, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 06:37:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 358, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-25 06:37:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 357, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-24 21:29:27", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 356, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-24 21:24:02", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 355, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 21:23:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 354, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 21:23:47", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 353, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-24 21:12:38", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 352, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 21:12:28", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 351, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 21:12:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 350, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-24 21:11:59", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 349, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 21:11:48", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 348, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 21:00:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 347, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 21:00:07", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 346, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 20:59:55", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 345, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-24 20:58:35", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 344, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 20:58:28", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 343, "project_id": 126, "task_id": 42, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"晚睡晚起\" in task \"早睡早起\"", "metadata": null, "created_at": "2025-06-24 20:50:16", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 342, "project_id": 126, "task_id": 41, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"早睡早起\"", "metadata": null, "created_at": "2025-06-24 20:50:02", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 341, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"任務1\"", "metadata": null, "created_at": "2025-06-24 20:49:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 340, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"任務1\"", "metadata": null, "created_at": "2025-06-24 20:49:39", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 337, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-24 20:31:14", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 336, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Changed project visibility to public", "metadata": null, "created_at": "2025-06-24 20:31:12", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 335, "project_id": 148, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Email Templates\"", "metadata": null, "created_at": "2025-06-24 20:30:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Email Templates"}, {"id": 334, "project_id": 147, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Security Audit\"", "metadata": null, "created_at": "2025-06-24 20:30:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Security Audit"}, {"id": 333, "project_id": 146, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Brand Guidelines\"", "metadata": null, "created_at": "2025-06-24 20:30:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Brand Guidelines"}, {"id": 332, "project_id": 145, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Performance Optimization\"", "metadata": null, "created_at": "2025-06-24 20:30:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Performance Optimization"}, {"id": 331, "project_id": 144, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"API Documentation\"", "metadata": null, "created_at": "2025-06-24 20:30:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "API Documentation"}, {"id": 330, "project_id": 143, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Mobile App Development\"", "metadata": null, "created_at": "2025-06-24 20:30:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Mobile App Development"}, {"id": 329, "project_id": 142, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"User Authentication System\"", "metadata": null, "created_at": "2025-06-24 20:30:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "User Authentication System"}, {"id": 328, "project_id": 141, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Database Migration\"", "metadata": null, "created_at": "2025-06-24 20:30:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Database Migration"}, {"id": 327, "project_id": 140, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Website Redesign\"", "metadata": null, "created_at": "2025-06-24 20:30:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 326, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-24 20:28:57", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 325, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-24 20:28:57", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 324, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 20:28:50", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 323, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Changed project visibility to private", "metadata": null, "created_at": "2025-06-24 20:28:50", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 322, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Changed project visibility to public", "metadata": null, "created_at": "2025-06-24 20:28:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 321, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Changed project visibility to private", "metadata": null, "created_at": "2025-06-24 20:28:29", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 320, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "comment_edit", "description": "Edited comment (version 1)", "metadata": "{\"commentId\":21,\"previousContent\":\"Comment.\",\"newContent\":\"Comment. 更新1.\",\"versionNumber\":1,\"editCount\":1}", "created_at": "2025-06-24 20:26:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 319, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-24 20:26:35", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 318, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-24 20:25:45", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 317, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 20:25:43", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 316, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-24 20:25:42", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 315, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 20:25:40", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 314, "project_id": 127, "task_id": 38, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"由我不由天。\" status to completed", "metadata": null, "created_at": "2025-06-24 19:54:35", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 313, "project_id": 127, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 19:52:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 312, "project_id": 127, "task_id": 38, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"由我不由天。\"", "metadata": null, "created_at": "2025-06-24 19:51:28", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 311, "project_id": 127, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-24 19:51:05", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 310, "project_id": 127, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-24 19:50:21", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 309, "project_id": 127, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-24 19:50:21", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 308, "project_id": 125, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 19:37:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 307, "project_id": 139, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 306, "project_id": 138, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 305, "project_id": 137, "task_id": 37, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Test Task\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 304, "project_id": 137, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 303, "project_id": 136, "task_id": 36, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Test Task\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 302, "project_id": 136, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 301, "project_id": 135, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 300, "project_id": 134, "task_id": 35, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"Subtask\" in task \"Parent Task\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 299, "project_id": 134, "task_id": 34, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Parent Task\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 298, "project_id": 134, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 297, "project_id": 133, "task_id": 33, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Test Task\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 296, "project_id": 133, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 295, "project_id": 132, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 294, "project_id": 131, "task_id": 32, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Test Task\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 293, "project_id": 131, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 291, "project_id": 129, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 290, "project_id": 128, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 289, "project_id": 127, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 288, "project_id": 127, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Original Title\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 287, "project_id": 126, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "試驗卡牌"}, {"id": 286, "project_id": 125, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 19:05:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 285, "project_id": 124, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 284, "project_id": 123, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 283, "project_id": 122, "task_id": 31, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Test Task\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 282, "project_id": 122, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 281, "project_id": 121, "task_id": 30, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Test Task\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 280, "project_id": 121, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 279, "project_id": 120, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 278, "project_id": 119, "task_id": 29, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"Subtask\" in task \"Parent Task\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 277, "project_id": 119, "task_id": 28, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Parent Task\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 276, "project_id": 119, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 275, "project_id": 118, "task_id": 27, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Test Task\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 274, "project_id": 118, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 273, "project_id": 117, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 272, "project_id": 116, "task_id": 26, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Test Task\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 271, "project_id": 116, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 269, "project_id": 114, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 268, "project_id": 113, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 267, "project_id": 112, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "<PERSON>"}, {"id": 266, "project_id": 112, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Original Title\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "<PERSON>"}, {"id": 264, "project_id": 110, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 263, "project_id": 109, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:32:29", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 262, "project_id": 108, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:31:32", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 261, "project_id": 107, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 17:31:14", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 260, "project_id": 107, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Original Title\"", "metadata": null, "created_at": "2025-06-24 17:31:14", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 259, "project_id": 106, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 17:30:22", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 258, "project_id": 106, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Original Title\"", "metadata": null, "created_at": "2025-06-24 17:30:22", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Updated Title"}, {"id": 257, "project_id": 105, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Original Title\"", "metadata": null, "created_at": "2025-06-24 17:29:17", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Original Title"}, {"id": 256, "project_id": 104, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 255, "project_id": 103, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 254, "project_id": 102, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 253, "project_id": 101, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 252, "project_id": 100, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 251, "project_id": 99, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 250, "project_id": 98, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 249, "project_id": 97, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 248, "project_id": 96, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 247, "project_id": 95, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 246, "project_id": 94, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 245, "project_id": 93, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 244, "project_id": 92, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Original Title\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Original Title"}, {"id": 243, "project_id": 91, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 242, "project_id": 90, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:27:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 241, "project_id": 89, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-24 17:13:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Mod Task test."}, {"id": 240, "project_id": 89, "task_id": 25, "user_id": 2, "activity_type": "task_creation", "description": "Created subtask \"1\" in task \"Important task\"", "metadata": null, "created_at": "2025-06-24 17:11:48", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Mod Task test."}, {"id": 239, "project_id": 89, "task_id": 24, "user_id": 2, "activity_type": "task_creation", "description": "Created task \"Important task\"", "metadata": null, "created_at": "2025-06-24 17:11:36", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Mod Task test."}, {"id": 238, "project_id": 89, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"Mod Task test.\"", "metadata": null, "created_at": "2025-06-24 17:10:28", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Mod Task test."}, {"id": 237, "project_id": 74, "task_id": null, "user_id": 2, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-24 17:09:13", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 236, "project_id": 74, "task_id": null, "user_id": 1, "activity_type": "assignment", "description": "Added <EMAIL> to project team", "metadata": null, "created_at": "2025-06-24 17:08:20", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 235, "project_id": 74, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-24 17:07:43", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 234, "project_id": 74, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-24 17:07:00", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 233, "project_id": 74, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 17:06:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 232, "project_id": 88, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 231, "project_id": 87, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 230, "project_id": 86, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 229, "project_id": 85, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 228, "project_id": 84, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 227, "project_id": 83, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 226, "project_id": 82, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 225, "project_id": 81, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 224, "project_id": 80, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 223, "project_id": 79, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 222, "project_id": 78, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 221, "project_id": 77, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 220, "project_id": 76, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Original Title\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Original Title"}, {"id": 219, "project_id": 75, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 218, "project_id": 74, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:01:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 217, "project_id": 73, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 17:00:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 216, "project_id": 72, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 215, "project_id": 71, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 214, "project_id": 70, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 213, "project_id": 69, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 212, "project_id": 68, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 211, "project_id": 67, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 210, "project_id": 66, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 209, "project_id": 65, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 208, "project_id": 64, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 207, "project_id": 63, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 206, "project_id": 62, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 205, "project_id": 61, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 204, "project_id": 60, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Original Title\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Original Title"}, {"id": 203, "project_id": 59, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 202, "project_id": 58, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:59:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 201, "project_id": 57, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 200, "project_id": 56, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 199, "project_id": 55, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 198, "project_id": 54, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 197, "project_id": 53, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 196, "project_id": 52, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 195, "project_id": 51, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 194, "project_id": 50, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 193, "project_id": 49, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 192, "project_id": 48, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 191, "project_id": 47, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 190, "project_id": 46, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 189, "project_id": 45, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Original Title\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Original Title"}, {"id": 188, "project_id": 44, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 187, "project_id": 43, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 186, "project_id": 42, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"1\"", "metadata": null, "created_at": "2025-06-24 16:50:49", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "1"}, {"id": 185, "project_id": 41, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 184, "project_id": 40, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 183, "project_id": 39, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 182, "project_id": 38, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 181, "project_id": 37, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 180, "project_id": 36, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 179, "project_id": 35, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 178, "project_id": 34, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 177, "project_id": 33, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 176, "project_id": 32, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 175, "project_id": 31, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 174, "project_id": 30, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 173, "project_id": 29, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Original Title\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Original Title"}, {"id": 172, "project_id": 28, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 171, "project_id": 27, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 16:02:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 170, "project_id": 26, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 169, "project_id": 25, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 168, "project_id": 24, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 167, "project_id": 23, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 166, "project_id": 22, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 165, "project_id": 21, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 164, "project_id": 20, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 163, "project_id": 19, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 162, "project_id": 18, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 161, "project_id": 17, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 160, "project_id": 16, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 159, "project_id": 15, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 158, "project_id": 14, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Original Title\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Original Title"}, {"id": 157, "project_id": 13, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 156, "project_id": 12, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"Test Project\"", "metadata": null, "created_at": "2025-06-24 15:57:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Test Project"}, {"id": 155, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-24 15:36:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 154, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "assignment", "description": "Added <EMAIL> to project team", "metadata": null, "created_at": "2025-06-24 15:36:02", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 153, "project_id": 3, "task_id": 22, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"不刷牙不洗臉\" status to todo", "metadata": null, "created_at": "2025-06-24 08:45:16", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 152, "project_id": 3, "task_id": 22, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"不刷牙不洗臉\" status to completed", "metadata": null, "created_at": "2025-06-24 08:45:14", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 151, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-24 08:44:41", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 150, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-24 08:44:39", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 149, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"Debug Test Subtask\"", "metadata": null, "created_at": "2025-06-23 20:55:31", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 148, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"Second Subtask\"", "metadata": null, "created_at": "2025-06-23 20:55:27", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 147, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"First Subtask\"", "metadata": null, "created_at": "2025-06-23 20:55:26", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 146, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"1123\"", "metadata": null, "created_at": "2025-06-23 20:55:24", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 145, "project_id": 3, "task_id": 20, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"晚上要早點睡覺\" status to completed", "metadata": null, "created_at": "2025-06-23 20:54:31", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 144, "project_id": 3, "task_id": 19, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"早點打原神\" status to completed", "metadata": null, "created_at": "2025-06-23 20:54:31", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 143, "project_id": 3, "task_id": 19, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"早點打原神\" status to todo", "metadata": null, "created_at": "2025-06-23 20:54:29", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 142, "project_id": 3, "task_id": 20, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"晚上要早點睡覺\" status to todo", "metadata": null, "created_at": "2025-06-23 20:54:28", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 141, "project_id": 3, "task_id": 22, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"不刷牙不洗臉\" status to todo", "metadata": null, "created_at": "2025-06-23 20:54:27", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 140, "project_id": 3, "task_id": 23, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"改變善良的平和心態\" status to todo", "metadata": null, "created_at": "2025-06-23 20:54:27", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 139, "project_id": 3, "task_id": 13, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"早上上班打卡\" status to todo", "metadata": null, "created_at": "2025-06-23 20:54:24", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 138, "project_id": 3, "task_id": 13, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"早上上班打卡\" status to completed", "metadata": null, "created_at": "2025-06-23 20:54:22", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 137, "project_id": 3, "task_id": 23, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"改變善良的平和心態\" status to completed", "metadata": null, "created_at": "2025-06-23 20:54:17", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 136, "project_id": 3, "task_id": 22, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"不刷牙不洗臉\" status to completed", "metadata": null, "created_at": "2025-06-23 20:54:16", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 135, "project_id": 6, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 17:43:49", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "API Documentation"}, {"id": 134, "project_id": 6, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-23 17:43:47", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "API Documentation"}, {"id": 133, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-23 17:43:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 132, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 17:43:41", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 131, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-23 17:43:38", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 130, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 17:43:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 129, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-23 17:33:50", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 128, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-23 17:33:50", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 127, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 17:33:41", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 126, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-23 17:33:35", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 125, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 17:33:28", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 124, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"不刷牙不洗臉\"", "metadata": null, "created_at": "2025-06-23 11:11:58", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 123, "project_id": 3, "task_id": 20, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"晚上要早點睡覺\" status to completed", "metadata": null, "created_at": "2025-06-23 11:10:20", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 122, "project_id": 3, "task_id": 20, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"晚上要早點睡覺\" status to todo", "metadata": null, "created_at": "2025-06-23 11:10:19", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 121, "project_id": 3, "task_id": 20, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"晚上要早點睡覺\" status to completed", "metadata": null, "created_at": "2025-06-23 11:10:12", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 120, "project_id": 3, "task_id": 19, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"早點打飛機\" status to completed", "metadata": null, "created_at": "2025-06-23 11:10:11", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 119, "project_id": 3, "task_id": 23, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"改變善良的平和心態\" in task \"早上上班打卡\"", "metadata": null, "created_at": "2025-06-23 11:09:58", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 118, "project_id": 3, "task_id": 22, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"不刷牙不洗臉\" in task \"早上上班打卡\"", "metadata": null, "created_at": "2025-06-23 10:53:42", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 116, "project_id": 3, "task_id": 20, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"晚上要早點睡覺\" in task \"早上上班\"", "metadata": null, "created_at": "2025-06-23 10:36:16", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 115, "project_id": 3, "task_id": 19, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"Fixed Subtask Test\" in task \"Main Task with Subtasks\"", "metadata": null, "created_at": "2025-06-23 10:34:31", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 113, "project_id": 3, "task_id": 17, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Test Subtask with Parent ID\"", "metadata": null, "created_at": "2025-06-23 10:29:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 111, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"123\"", "metadata": null, "created_at": "2025-06-23 10:26:01", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 108, "project_id": 3, "task_id": 13, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Main Task with Subtasks\"", "metadata": null, "created_at": "2025-06-23 10:17:06", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 106, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"123\"", "metadata": null, "created_at": "2025-06-23 09:46:55", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 105, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"123\"", "metadata": null, "created_at": "2025-06-23 09:46:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 104, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"111\"", "metadata": null, "created_at": "2025-06-23 09:46:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 99, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"123\"", "metadata": null, "created_at": "2025-06-23 09:45:17", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 96, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"Tst\"", "metadata": null, "created_at": "2025-06-23 09:34:55", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 95, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "comment_edit", "description": "Edited comment (version 2)", "metadata": "{\"commentId\":15,\"previousContent\":\"0508\",\"newContent\":\"0534\",\"versionNumber\":2,\"editCount\":2}", "created_at": "2025-06-23 09:34:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 94, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-23 09:33:22", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 93, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 09:33:14", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 92, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "comment_edit", "description": "Edited comment (version 1)", "metadata": "{\"commentId\":15,\"previousContent\":\"123\",\"newContent\":\"0508\",\"versionNumber\":1,\"editCount\":1}", "created_at": "2025-06-23 09:08:57", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 91, "project_id": 3, "task_id": 7, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"New Task 508\"", "metadata": null, "created_at": "2025-06-23 09:08:29", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 90, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Deleted task \"Test\"", "metadata": null, "created_at": "2025-06-23 09:08:14", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 88, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-23 08:41:53", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 86, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "comment_delete", "description": "Deleted comment by <PERSON>", "metadata": "{\"commentId\":14,\"deletedContent\":\"123\",\"originalAuthorId\":1,\"originalAuthorUsername\":\"<PERSON>\",\"wasEdited\":false,\"editCount\":0}", "created_at": "2025-06-23 08:33:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 85, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-23 08:30:35", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 84, "project_id": 4, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 06:55:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "User Authentication System"}, {"id": 83, "project_id": 4, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-23 06:55:31", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "User Authentication System"}, {"id": 82, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "comment_edit", "description": "Edited comment (version 1)", "metadata": "{\"commentId\":12,\"previousContent\":\"吃飯了沒有。\",\"newContent\":\"工作了沒有。\",\"versionNumber\":1,\"editCount\":1}", "created_at": "2025-06-23 06:53:43", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 81, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-23 06:53:22", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 80, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-23 06:52:42", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 79, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-23 06:52:31", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 78, "project_id": 3, "task_id": 4, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Add something important here.\"", "metadata": null, "created_at": "2025-06-23 06:52:26", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 77, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "assignment", "description": "Added <EMAIL> to project team", "metadata": null, "created_at": "2025-06-23 06:52:12", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 76, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "assignment", "description": "Added <EMAIL> to project team", "metadata": null, "created_at": "2025-06-23 06:51:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 75, "project_id": 4, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 06:36:55", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "User Authentication System"}, {"id": 74, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 06:27:17", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 73, "project_id": 6, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 06:27:15", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "API Documentation"}, {"id": 72, "project_id": 6, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-23 06:27:09", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "API Documentation"}, {"id": 71, "project_id": 6, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-23 06:27:09", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "API Documentation"}, {"id": 70, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-23 06:27:05", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 69, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-23 06:27:05", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 68, "project_id": 8, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-23 06:26:03", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Brand Guidelines"}, {"id": 67, "project_id": 8, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 06:25:57", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Brand Guidelines"}, {"id": 66, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-23 06:25:50", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 65, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-23 06:25:50", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 64, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 06:25:44", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 63, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-23 06:25:32", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 62, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-23 06:25:32", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 61, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 06:25:26", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 60, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-23 06:25:20", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 59, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-23 06:25:20", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 58, "project_id": 5, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 06:25:07", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Mobile App Development"}, {"id": 57, "project_id": 7, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-23 06:15:01", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Performance Optimization"}, {"id": 56, "project_id": 7, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 06:14:57", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Performance Optimization"}, {"id": 55, "project_id": 5, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-23 06:14:50", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Mobile App Development"}, {"id": 54, "project_id": 6, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 06:14:46", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "API Documentation"}, {"id": 53, "project_id": 6, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-23 06:10:55", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "API Documentation"}, {"id": 52, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 06:10:40", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "改變人生，躺平由我不由天"}, {"id": 51, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-23 06:08:10", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 50, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-23 06:07:59", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 49, "project_id": 8, "task_id": 3, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"Grade Guidelines\"", "metadata": null, "created_at": "2025-06-22 18:58:42", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Brand Guidelines"}, {"id": 48, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-22 18:56:23", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 47, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-22 18:40:21", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 46, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "assignment", "description": "Added <EMAIL> to project team", "metadata": null, "created_at": "2025-06-22 17:22:49", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 45, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "assignment", "description": "Added <EMAIL> to project team", "metadata": null, "created_at": "2025-06-22 17:06:56", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 44, "project_id": 7, "task_id": null, "user_id": 1, "activity_type": "assignment", "description": "Added <EMAIL> to project team", "metadata": null, "created_at": "2025-06-22 16:27:06", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Performance Optimization"}, {"id": 43, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "assignment", "description": "Added <EMAIL> to project team", "metadata": null, "created_at": "2025-06-22 16:12:40", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 42, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-22 16:12:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 41, "project_id": 7, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-22 11:45:09", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Performance Optimization"}, {"id": 40, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-22 11:45:03", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 39, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-22 11:42:27", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 38, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-22 11:42:26", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 37, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-22 11:42:26", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 36, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-22 11:42:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 35, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-22 11:33:55", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 34, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-22 11:33:52", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 33, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-22 08:44:05", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 32, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-22 08:44:00", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 31, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-22 08:44:00", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 30, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-22 08:43:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 29, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to todo", "metadata": null, "created_at": "2025-06-22 08:43:46", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 28, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-22 08:43:43", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 27, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-22 08:43:43", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 26, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "completion", "description": "Completed project", "metadata": null, "created_at": "2025-06-22 08:43:42", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 25, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to completed", "metadata": null, "created_at": "2025-06-22 08:43:42", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 24, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-22 08:43:37", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 23, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Changed project visibility to public", "metadata": null, "created_at": "2025-06-22 08:42:36", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 22, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Changed project visibility to private", "metadata": null, "created_at": "2025-06-22 08:42:33", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 21, "project_id": 11, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Changed project visibility to private", "metadata": null, "created_at": "2025-06-22 03:18:16", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Moderator's Task"}, {"id": 20, "project_id": 11, "task_id": null, "user_id": 2, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-22 03:17:21", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator's Task"}, {"id": 19, "project_id": 11, "task_id": null, "user_id": 2, "activity_type": "creation", "description": "Created project \"New task\"", "metadata": null, "created_at": "2025-06-22 03:16:39", "user_username": "moderator", "user_email": "<EMAIL>", "project_title": "Moderator's Task"}, {"id": 18, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Changed project visibility to public", "metadata": null, "created_at": "2025-06-22 02:57:54", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 17, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "update", "description": "Changed project visibility to private", "metadata": null, "created_at": "2025-06-22 02:57:37", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 16, "project_id": 1, "task_id": 2, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"New title\"", "metadata": null, "created_at": "2025-06-21 22:04:30", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 15, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "comment_edit", "description": "Edited comment (version 1)", "metadata": "{\"commentId\":10,\"previousContent\":\"Awesome!\",\"newContent\":\"Awesome!  See you later!\",\"versionNumber\":1,\"editCount\":1}", "created_at": "2025-06-21 22:00:08", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 14, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-21 21:58:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 13, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "comment_delete", "description": "Deleted comment by <PERSON>", "metadata": "{\"commentId\":9,\"deletedContent\":\"ioadshoifahosdhfaphsdghaphdgiohiosdhghsdioghaposdhogiahpsdoighaosdghioasdgdsag\",\"originalAuthorId\":1,\"originalAuthorUsername\":\"<PERSON>\",\"wasEdited\":false,\"editCount\":0}", "created_at": "2025-06-21 21:51:12", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 12, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "comment_edit", "description": "Edited comment (version 3)", "metadata": "{\"commentId\":8,\"previousContent\":\"Wow, it is looking like it is working nicely.\",\"newContent\":\"Wow, it is looking like it is working nicely. v2\",\"versionNumber\":3,\"editCount\":3}", "created_at": "2025-06-21 19:15:01", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 11, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "comment_edit", "description": "Edited comment (version 2)", "metadata": "{\"commentId\":8,\"previousContent\":\"12312312312123\",\"newContent\":\"Wow, it is looking like it is working nicely.\",\"versionNumber\":2,\"editCount\":2}", "created_at": "2025-06-21 19:08:02", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 10, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "comment_edit", "description": "Edited comment (version 1)", "metadata": "{\"commentId\":8,\"previousContent\":\"12312312312\",\"newContent\":\"12312312312123\",\"versionNumber\":1,\"editCount\":1}", "created_at": "2025-06-21 14:20:25", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 9, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-21 13:18:40", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 8, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-21 13:18:28", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 7, "project_id": 5, "task_id": null, "user_id": 1, "activity_type": "comment_edit", "description": "Edited comment (version 1)", "metadata": "{\"commentId\":3,\"previousContent\":\"Updated requirements based on stakeholder feedback.\",\"newContent\":\"Updated requirements based on stakeholder feedback.111\",\"versionNumber\":1,\"editCount\":1}", "created_at": "2025-06-21 13:17:06", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Mobile App Development"}, {"id": 6, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-21 13:16:38", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "Website Redesign"}, {"id": 5, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "status_change", "description": "Changed status to inProgress", "metadata": null, "created_at": "2025-06-21 13:02:23", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 4, "project_id": 1, "task_id": 1, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"asdfasdfdsa\"", "metadata": null, "created_at": "2025-06-21 12:58:38", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 3, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-21 12:58:13", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 2, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "comment", "description": "Added comment", "metadata": null, "created_at": "2025-06-21 12:57:24", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}, {"id": 1, "project_id": 1, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"aosidhfashdfs\"", "metadata": null, "created_at": "2025-06-21 12:57:16", "user_username": "<PERSON>", "user_email": "<EMAIL>", "project_title": "This is an experimental Title"}], "users": [{"id": 1, "username": "<PERSON>", "email": "<EMAIL>", "role": "admin", "created_at": "2025-06-21 12:54:57"}, {"id": 2, "username": "moderator", "email": "<EMAIL>", "role": "moderator", "created_at": "2025-06-21 12:54:57"}]}}