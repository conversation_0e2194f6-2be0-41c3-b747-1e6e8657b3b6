import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost:
          "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
        link: "text-primary underline-offset-4 hover:underline",
        // Clean design system variants with our purple color scheme
        "brand-primary": "bg-[#5E6AD2] text-white shadow-xs hover:bg-[#5E6AD2]/90 active:bg-[#5E6AD2] dark:bg-[#6E56CF] dark:hover:bg-[#9E8CFC] dark:active:bg-[#6E56CF]",
        "brand-secondary": "bg-[#5E6AD2]/10 text-[#5E6AD2] border border-[#5E6AD2]/20 shadow-xs hover:bg-[#5E6AD2]/20 active:bg-[#5E6AD2]/10 dark:bg-[#6E56CF]/20 dark:text-[#9E8CFC] dark:border-[#6E56CF]/30 dark:hover:bg-[#6E56CF]/30",
        "brand-tertiary": "bg-transparent text-[#5E6AD2] hover:bg-[#5E6AD2]/10 active:bg-[#5E6AD2]/20 dark:text-[#9E8CFC] dark:hover:bg-[#6E56CF]/20",
        "neutral-primary": "bg-gray-100 text-gray-700 shadow-xs hover:bg-gray-200 active:bg-gray-100 dark:bg-neutral-800 dark:text-neutral-300 dark:hover:bg-neutral-700",
    "neutral-secondary": "border border-gray-200 bg-white text-gray-700 shadow-xs hover:bg-gray-50 active:bg-white dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-800",
    "neutral-tertiary": "bg-transparent text-gray-700 hover:bg-gray-100 active:bg-gray-200 dark:text-neutral-300 dark:hover:bg-neutral-800",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
  }) {
  const Comp = asChild ? Slot : "button"

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  )
}

export { Button, buttonVariants }
