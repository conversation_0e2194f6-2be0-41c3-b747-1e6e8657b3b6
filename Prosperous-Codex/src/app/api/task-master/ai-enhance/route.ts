import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/middleware';
import { AIEnhancementService } from '@/lib/services/ai-enhancement-service';
import { FieldMapper } from '@/lib/task-master/field-mapping';

/**
 * POST /api/task-master/ai-enhance
 * 
 * Enhances task description text using AI language models.
 * Requires authentication and follows camelCase ↔ snake_case field mapping.
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;

    // Parse and validate request body
    const body = await request.json();
    
    // Apply field mapping from camelCase to snake_case
    const mappedBody = FieldMapper.apiToDb(body);
    
    // Validate required fields
    if (!mappedBody.input_text || typeof mappedBody.input_text !== 'string') {
      return NextResponse.json(
        { error: 'Missing or invalid input_text field' },
        { status: 400 }
      );
    }

    // Validate input length (prevent abuse)
    const maxInputLength = parseInt(process.env.AI_MAX_INPUT_LENGTH || '65536');
    if (mappedBody.input_text.length > maxInputLength) {
      return NextResponse.json(
        { error: `Input text too long (maximum ${maxInputLength.toLocaleString()} characters)` },
        { status: 400 }
      );
    }

    if (mappedBody.input_text.trim().length === 0) {
      return NextResponse.json(
        { error: 'Input text cannot be empty' },
        { status: 400 }
      );
    }

    // Initialize AI enhancement service
    const aiService = new AIEnhancementService();

    // Check if AI service is available
    if (!aiService.isAvailable()) {
      return NextResponse.json(
        { error: 'AI enhancement service is not available. Please check configuration.' },
        { status: 503 }
      );
    }

    // Enhance the text
    const enhancedText = await aiService.enhanceTaskDescription(
      mappedBody.input_text,
      {
        context: mappedBody.context || 'task_description',
        tone: mappedBody.tone || 'professional',
        max_tokens: mappedBody.max_tokens || parseInt(process.env.AI_MAX_TOKENS || '65536')
      }
    );

    // Prepare response with field mapping back to camelCase
    const response = FieldMapper.dbToApi({
      enhanced_text: enhancedText,
      original_text: mappedBody.input_text,
      enhancement_applied: true
    });

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error('AI Enhancement API Error:', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'AI service configuration error' },
          { status: 503 }
        );
      }
      
      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please try again later.' },
          { status: 429 }
        );
      }

      if (error.message.includes('content filter')) {
        return NextResponse.json(
          { error: 'Content cannot be processed due to safety guidelines' },
          { status: 400 }
        );
      }
    }

    // Generic error response
    return NextResponse.json(
      { error: 'Failed to enhance text. Please try again.' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/task-master/ai-enhance
 * 
 * Returns the status of the AI enhancement service
 */
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const aiService = new AIEnhancementService();
    
    return NextResponse.json({
      available: aiService.isAvailable(),
      configured: aiService.isServiceConfigured(),
      provider: aiService.getProvider(),
      models: aiService.getAvailableModels()
    });

  } catch (error) {
    console.error('AI Enhancement Status Error:', error);
    return NextResponse.json(
      { error: 'Failed to check service status' },
      { status: 500 }
    );
  }
}
