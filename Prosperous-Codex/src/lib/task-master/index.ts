/**
 * Task Master Library
 *
 * Main entry point for the Task Master functionality
 * Reorganized for better structure and maintainability
 */

// Core services
export * from './services';

// Validation and schemas
export * from './schemas';

// Types
export * from './types';

// Utilities
export * from './utils';

// Authorization and security
export * from './authorization';
export * from './sanitization';
export * from './security-audit';
export * from './errors';

// API middleware
export * from './middleware';
export * from './api-middleware';
export * from './partial-response';

// Field validation system
export * from './field-mapping';
export * from './schema-validator';
export * from './api-validator';
export * from './type-safety-enforcer';
export * from './validation-types';
export * from './naming-conventions-enforcer';
