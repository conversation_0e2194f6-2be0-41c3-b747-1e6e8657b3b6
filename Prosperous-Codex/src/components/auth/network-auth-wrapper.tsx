import type { ReactNode } from 'react';
import VantaBackground from './vanta-background';

interface NetworkAuthWrapperProps {
  children: ReactNode; // This will be the form side
  title: string;
  subtitle: string;
  welcomeMessage: string;
}

export function NetworkAuthWrapper({
  children,
  title,
  subtitle,
  welcomeMessage,
}: NetworkAuthWrapperProps) {
  return (
    <div className="max-w-5xl w-full bg-gray-900 rounded-xl overflow-hidden shadow-2xl border border-gray-800 flex flex-col md:flex-row">
      {/* Visualization Side */}
      <div className="md:w-1/2 h-64 md:h-auto relative">
        <VantaBackground />
        <div className="absolute inset-0 bg-gradient-to-r from-transparent to-gray-900/90 md:via-transparent md:to-gray-900"></div>
        <div className="absolute top-8 left-8 z-10">
          <span className="px-2 py-1 bg-gray-800/80 rounded-full text-xs text-gray-400 mb-2 inline-block">
            {welcomeMessage}
          </span>
          <h2 className="text-3xl font-bold text-white" dangerouslySetInnerHTML={{ __html: title }}></h2>
          <div className="h-1 w-16 bg-gray-400 mt-3 rounded-full"></div>
          <p className="mt-4 text-gray-300 max-w-xs">{subtitle}</p>
        </div>
        <div className="absolute bottom-8 left-8 bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2 z-10">
          <div className="flex space-x-4 text-xs text-gray-300">
            <div className="flex items-center">
              <span className="w-2 h-2 rounded-full bg-green-400 mr-1.5"></span>
              <span>System Online</span>
            </div>
          </div>
        </div>
      </div>

      {/* Form Side */}
      <div className="md:w-1/2 p-8 flex flex-col justify-center">
        {children}
      </div>
    </div>
  );
}
