import { calculatePaperCosts, CalculationRequest, getSortedResults } from '../calculator';
import { InnerTextJobInputs, CoverJobInputs, PRESS_CONSTRAINTS } from '../../types/calculation';

describe('Summary v23.0 Compliance Verification', () => {
  const testInnerTextInputs: InnerTextJobInputs = {
    trimH: 216,
    trimW: 151,
    quantity: 1000,
    bleed: 3,
    gripper: 10,
    colorBar: 10,
    spoilagePct: 0.05,
    alignmentMode: 'Aligned',
    totalPages: 200,
    bindingMethod: 'perfectBound',
    lip: 12,
    isDoubleLipActive: false
  };

  const testCoverInputs: CoverJobInputs = {
    trimH: 216,
    trimW: 151,
    quantity: 1000,
    bleed: 3,
    gripper: 10,
    colorBar: 10,
    spoilagePct: 0.05,
    alignmentMode: 'Aligned',
    spineThickness: 12,
    coverType: 'softcover',
    turnInAllowance: 0,
    flapWidth: 0
  };

  describe('Point 1: Roll Dimension Maximization', () => {
    it('should use Math.max(MAX_PRESS_H, MAX_PRESS_W) for roll dimension inference', async () => {
      const rollPaper = {
        id: 'test-roll-max-dimension',
        name: 'Roll Paper - Max Dimension Test',
        source: 'Roll',
        sheetHeight: null, // Height will be inferred
        sheetWidth: 640,   // Width provided
        grainDirection: 'Height',
        caliper: 100,
        costPerReam: null,
        gsm: 80,
        costPerTon: 1200,
        category: 'Inner Text' as const
      };

      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: [rollPaper]
      };

      const result = await calculatePaperCosts(request);

      expect(result.results).toHaveLength(1);
      const rollResult = result.results[0];
      
      // Verify roll optimization flags
      expect(rollResult.rollDimensionOptimizedForFitting).toBeTruthy();
      expect(rollResult.inputNoteKey).toBe('resNoteHeightInferred');
      
      // Verify that the inferred dimension uses Math.max(720, 1020) = 1020
      // The press height should be optimized to maximum possible dimension
      const maxPressDimension = Math.max(PRESS_CONSTRAINTS.MAX_PRESS_H, PRESS_CONSTRAINTS.MAX_PRESS_W);
      expect(maxPressDimension).toBe(1020); // Verify our assumption
      
      // The roll should be optimized to find the best dimension for the layout
      // It starts with Math.max(720, 1020) = 1020 and then optimizes down to fit the layout
      expect(rollResult.pressH).toBeGreaterThan(0);
      expect(rollResult.pressH).toBeLessThanOrEqual(maxPressDimension); // Should not exceed 1020

      // Verify that the optimization process used the maximum dimension as starting point
      expect(maxPressDimension).toBe(1020); // Verify our assumption about max dimension
    });
  });

  describe('Point 2: Sheet Trimming for Press Fit', () => {
    it('should trim oversized sheets instead of erroring out', async () => {
      const oversizedPaper = {
        id: 'test-oversized',
        name: 'Oversized Paper - Trimming Test',
        source: 'Pre-Cut',
        sheetHeight: 750,  // Exceeds MAX_PRESS_H (720)
        sheetWidth: 1050,  // Exceeds MAX_PRESS_W (1020)
        grainDirection: 'Height',
        caliper: 100,
        costPerReam: 50,
        gsm: 80,
        costPerTon: null,
        category: 'Inner Text' as const
      };

      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: [oversizedPaper]
      };

      const result = await calculatePaperCosts(request);

      expect(result.results).toHaveLength(1);
      const trimmedResult = result.results[0];
      
      // Should not error out
      expect(trimmedResult.error).toBeFalsy();
      
      // Should be trimmed to press limits
      expect(trimmedResult.pressH).toBeLessThanOrEqual(PRESS_CONSTRAINTS.MAX_PRESS_H);
      expect(trimmedResult.pressW).toBeLessThanOrEqual(PRESS_CONSTRAINTS.MAX_PRESS_W);
      
      // Should have trimming note
      expect(trimmedResult.pressSizeNoteKey).toBe('resNoteTrimmed');
    });

    it('should rotate sheets when rotation makes them fit', async () => {
      const rotatablePaper = {
        id: 'test-rotatable',
        name: 'Rotatable Paper - Rotation Test',
        source: 'Pre-Cut',
        sheetHeight: 787.4, // Exceeds MAX_PRESS_H (720) but fits MAX_PRESS_W (1020)
        sheetWidth: 546.1,  // Fits within MAX_PRESS_H (720)
        grainDirection: 'Height',
        caliper: 100,
        costPerReam: 50,
        gsm: 80,
        costPerTon: null,
        category: 'Inner Text' as const
      };

      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: [rotatablePaper]
      };

      const result = await calculatePaperCosts(request);

      expect(result.results).toHaveLength(1);
      const rotatedResult = result.results[0];
      
      // Should not error out
      expect(rotatedResult.error).toBeFalsy();
      
      // Should be rotated (dimensions swapped)
      expect(rotatedResult.paperWasRotated).toBeTruthy();
      expect(rotatedResult.pressH).toBe(546.1); // Original width
      expect(rotatedResult.pressW).toBe(787.4); // Original height
    });
  });

  describe('Point 3: Result Sorting by Waste Percent and Total Cost', () => {
    it('should sort results by wastePercent first, then totalCost', async () => {
      const papers = [
        {
          id: 'paper-high-waste',
          name: 'High Waste Paper',
          source: 'Pre-Cut',
          sheetHeight: 400, // Smaller sheet = higher waste
          sheetWidth: 300,
          grainDirection: 'Height',
          caliper: 100,
          costPerReam: 30, // Lower cost
          gsm: 80,
          costPerTon: null,
          category: 'Inner Text' as const
        },
        {
          id: 'paper-low-waste',
          name: 'Low Waste Paper',
          source: 'Pre-Cut',
          sheetHeight: 640, // Larger sheet = lower waste
          sheetWidth: 450,
          grainDirection: 'Height',
          caliper: 100,
          costPerReam: 50, // Higher cost
          gsm: 80,
          costPerTon: null,
          category: 'Inner Text' as const
        }
      ];

      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: papers
      };

      const result = await calculatePaperCosts(request);

      expect(result.results).toHaveLength(2);

      // Get sorted results to test the sorting logic
      const sortedResults = getSortedResults(result.results);
      const allSortedResults = [...sortedResults.optimal, ...sortedResults.good, ...sortedResults.errors];

      expect(allSortedResults).toHaveLength(2);

      // Results should be sorted by waste percent (ascending)
      const [first, second] = allSortedResults;
      expect(first.wastePercent).toBeLessThanOrEqual(second.wastePercent);

      // The low waste paper should come first despite higher cost
      expect(first.name).toBe('Low Waste Paper');
      expect(second.name).toBe('High Waste Paper');
    });
  });

  describe('Point 4: Even Rule Application for Cover/Endpapers', () => {
    it('should apply direct N-up geometric fit for covers (no even rules)', async () => {
      const coverPaper = {
        id: 'test-cover',
        name: 'Cover Paper - Even Rule Test',
        source: 'Pre-Cut',
        sheetHeight: 640,
        sheetWidth: 450,
        grainDirection: 'Height',
        caliper: 200,
        costPerReam: 75,
        gsm: 250,
        costPerTon: null,
        category: 'Cover' as const
      };

      const request: CalculationRequest = {
        tabId: 'cover',
        jobInputs: testCoverInputs,
        paperOptions: [coverPaper]
      };

      const result = await calculatePaperCosts(request);

      expect(result.results).toHaveLength(1);
      const coverResult = result.results[0];
      
      // Should not error out
      expect(coverResult.error).toBeFalsy();
      
      // Should have valid layout (direct geometric fit, no even rule constraints)
      expect(coverResult.maxItemsPerSide).toBeGreaterThan(0);
      expect(coverResult.layoutDown).toBeGreaterThan(0);
      expect(coverResult.layoutAcross).toBeGreaterThan(0);
      
      // For covers, both layout dimensions can be odd (no even rule applied)
      // This is the key difference from inner text which requires even spreads
    });
  });
});
