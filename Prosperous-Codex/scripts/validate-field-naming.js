#!/usr/bin/env node

/**
 * Field Naming Validation Tool
 * 
 * This script validates field naming consistency across the codebase to ensure:
 * - Database operations use snake_case
 * - API operations use camelCase
 * - Frontend components use camelCase
 * - No mixed naming conventions in the same context
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const config = {
  // Directories to scan
  directories: {
    database: ['src/lib/database', 'src/lib/task-master/services'],
    api: ['src/app/api'],
    frontend: ['src/components'],
    types: ['src/lib/types']
  },

  // Individual files to scan
  files: {
    types: ['src/lib/task-master/types.ts']
  },
  
  // Field patterns to check
  patterns: {
    snakeCase: /\b[a-z]+(_[a-z]+)+\b/g,
    camelCase: /\b[a-z]+([A-Z][a-z]*)+\b/g,
    problematicFields: [
      'user_id', 'userId',
      'project_id', 'projectId', 
      'team_members', 'teamMembers',
      'created_at', 'createdAt',
      'updated_at', 'updatedAt',
      'added_at', 'addedAt',
      'due_date', 'dueDate',
      'assigned_to', 'assignedTo',
      'created_by', 'createdBy',
      'full_description', 'fullDescription',
      'event_log', 'eventLog',
      'user_email', 'userEmail',
      'parent_task_id', 'parentTaskId',
      'sheet_width', 'sheetWidth',
      'sheet_height', 'sheetHeight',
      'grain_direction', 'grainDirection',
      'cost_per_ream', 'costPerReam',
      'cost_per_ton', 'costPerTon',
      'is_custom', 'isCustom'
    ]
  },
  
  // Files to exclude from validation
  excludeFiles: [
    'node_modules',
    '.git',
    'dist',
    'build',
    '.next',
    'coverage',
    'test-results',
    'playwright-report',
    '.backup',
    'mock-data.ts',
    'test.ts',
    'spec.ts'
  ]
};

class FieldNamingValidator {
  constructor() {
    this.violations = [];
    this.summary = {
      totalFiles: 0,
      violationCount: 0,
      fixableViolations: 0,
      criticalViolations: 0
    };
  }

  /**
   * Main validation entry point
   */
  async validate() {
    console.log('🔍 Starting Field Naming Validation...\n');
    
    // Validate each layer
    await this.validateDatabaseLayer();
    await this.validateApiLayer();
    await this.validateFrontendLayer();
    await this.validateTypeDefinitions();
    
    // Generate report
    this.generateReport();
    
    return this.violations.length === 0;
  }

  /**
   * Validate database layer (should use snake_case)
   */
  async validateDatabaseLayer() {
    console.log('📊 Validating Database Layer (expecting snake_case)...');
    
    for (const dir of config.directories.database) {
      await this.scanDirectory(dir, 'database');
    }
  }

  /**
   * Validate API layer (should use camelCase)
   */
  async validateApiLayer() {
    console.log('🌐 Validating API Layer (expecting camelCase)...');
    
    for (const dir of config.directories.api) {
      await this.scanDirectory(dir, 'api');
    }
  }

  /**
   * Validate frontend layer (should use camelCase)
   */
  async validateFrontendLayer() {
    console.log('🎨 Validating Frontend Layer (expecting camelCase)...');
    
    for (const dir of config.directories.frontend) {
      await this.scanDirectory(dir, 'frontend');
    }
  }

  /**
   * Validate type definitions
   */
  async validateTypeDefinitions() {
    console.log('📝 Validating Type Definitions...');

    // Scan type directories
    for (const dir of config.directories.types) {
      await this.scanDirectory(dir, 'types');
    }

    // Scan individual type files
    for (const file of config.files.types) {
      if (fs.existsSync(file)) {
        this.summary.totalFiles++;
        await this.validateFile(file, 'types');
      }
    }
  }

  /**
   * Scan directory for field naming issues
   */
  async scanDirectory(dirPath, layer) {
    const fullPath = path.resolve(dirPath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  Directory not found: ${dirPath}`);
      return;
    }

    const files = this.getFilesRecursively(fullPath);
    
    for (const file of files) {
      if (this.shouldSkipFile(file)) continue;
      
      this.summary.totalFiles++;
      await this.validateFile(file, layer);
    }
  }

  /**
   * Get all files recursively from directory
   */
  getFilesRecursively(dir) {
    const files = [];
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (!this.shouldSkipFile(item)) {
          files.push(...this.getFilesRecursively(fullPath));
        }
      } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  /**
   * Check if file should be skipped
   */
  shouldSkipFile(filePath) {
    const fileName = path.basename(filePath);
    return config.excludeFiles.some(exclude => 
      filePath.includes(exclude) || fileName.includes(exclude)
    );
  }

  /**
   * Validate individual file
   */
  async validateFile(filePath, layer) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      lines.forEach((line, lineNumber) => {
        this.validateLine(line, lineNumber + 1, filePath, layer);
      });
      
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error.message);
    }
  }

  /**
   * Validate individual line for field naming issues
   */
  validateLine(line, lineNumber, filePath, layer) {
    // Skip comments and strings
    if (line.trim().startsWith('//') || line.trim().startsWith('*')) return;
    
    // Check for problematic field usage
    for (const field of config.patterns.problematicFields) {
      if (line.includes(field)) {
        this.checkFieldUsage(line, lineNumber, filePath, layer, field);
      }
    }
  }

  /**
   * Check specific field usage for naming convention violations
   */
  checkFieldUsage(line, lineNumber, filePath, layer, field) {
    const isSnakeCase = field.includes('_');
    const expectedCase = this.getExpectedCase(layer);
    
    // Check if field usage matches expected convention for this layer
    if (expectedCase === 'camelCase' && isSnakeCase) {
      // Found snake_case in layer that should use camelCase
      this.addViolation({
        type: 'WRONG_CASE',
        severity: 'ERROR',
        file: filePath,
        line: lineNumber,
        field: field,
        expected: this.toCamelCase(field),
        actual: field,
        layer: layer,
        context: line.trim(),
        fixable: true
      });
    } else if (expectedCase === 'snake_case' && !isSnakeCase) {
      // Found camelCase in layer that should use snake_case
      this.addViolation({
        type: 'WRONG_CASE',
        severity: 'ERROR',
        file: filePath,
        line: lineNumber,
        field: field,
        expected: this.toSnakeCase(field),
        actual: field,
        layer: layer,
        context: line.trim(),
        fixable: true
      });
    }
  }

  /**
   * Get expected naming convention for layer
   */
  getExpectedCase(layer) {
    switch (layer) {
      case 'database':
        return 'snake_case';
      case 'api':
      case 'frontend':
      case 'types':
        return 'camelCase';
      default:
        return 'camelCase';
    }
  }

  /**
   * Convert snake_case to camelCase
   */
  toCamelCase(str) {
    return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
  }

  /**
   * Convert camelCase to snake_case
   */
  toSnakeCase(str) {
    return str.replace(/([A-Z])/g, '_$1').toLowerCase();
  }

  /**
   * Add violation to list
   */
  addViolation(violation) {
    this.violations.push(violation);
    this.summary.violationCount++;
    
    if (violation.fixable) {
      this.summary.fixableViolations++;
    }
    
    if (violation.severity === 'ERROR') {
      this.summary.criticalViolations++;
    }
  }

  /**
   * Generate validation report
   */
  generateReport() {
    console.log('\n📋 Field Naming Validation Report');
    console.log('=====================================');
    
    console.log(`📁 Files Scanned: ${this.summary.totalFiles}`);
    console.log(`❌ Total Violations: ${this.summary.violationCount}`);
    console.log(`🔧 Fixable Violations: ${this.summary.fixableViolations}`);
    console.log(`🚨 Critical Violations: ${this.summary.criticalViolations}`);
    
    if (this.violations.length === 0) {
      console.log('\n✅ No field naming violations found! All layers are using correct naming conventions.');
      return;
    }
    
    console.log('\n🔍 Violations by Layer:');
    const violationsByLayer = this.groupViolationsByLayer();
    
    for (const [layer, violations] of Object.entries(violationsByLayer)) {
      console.log(`\n📂 ${layer.toUpperCase()} Layer (${violations.length} violations):`);
      
      violations.slice(0, 10).forEach(violation => {
        console.log(`  ❌ ${path.relative(process.cwd(), violation.file)}:${violation.line}`);
        console.log(`     Expected: ${violation.expected}, Found: ${violation.actual}`);
        console.log(`     Context: ${violation.context}`);
        console.log('');
      });
      
      if (violations.length > 10) {
        console.log(`     ... and ${violations.length - 10} more violations`);
      }
    }
    
    console.log('\n🛠️  Recommended Actions:');
    console.log('1. Run the field mapping migration script');
    console.log('2. Update components to use correct field names');
    console.log('3. Ensure API endpoints use camelCase consistently');
    console.log('4. Verify database operations use snake_case');
    
    // Save detailed report to file
    this.saveDetailedReport();
  }

  /**
   * Group violations by layer
   */
  groupViolationsByLayer() {
    const grouped = {};
    
    this.violations.forEach(violation => {
      if (!grouped[violation.layer]) {
        grouped[violation.layer] = [];
      }
      grouped[violation.layer].push(violation);
    });
    
    return grouped;
  }

  /**
   * Save detailed report to file
   */
  saveDetailedReport() {
    const reportPath = path.join(process.cwd(), 'field-naming-violations.json');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: this.summary,
      violations: this.violations
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new FieldNamingValidator();
  
  validator.validate().then(success => {
    if (success) {
      console.log('\n🎉 Field naming validation passed!');
      process.exit(0);
    } else {
      console.log('\n❌ Field naming validation failed. Please fix the violations above.');
      process.exit(1);
    }
  }).catch(error => {
    console.error('❌ Validation failed with error:', error);
    process.exit(1);
  });
}

module.exports = FieldNamingValidator;
