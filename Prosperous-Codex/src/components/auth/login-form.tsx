"use client";

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { LoginSchema, type LoginFormData } from '@/lib/schemas/auth';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Link } from '@/i18n/navigation';
import { signIn } from 'next-auth/react';
import { useRouter } from '@/i18n/navigation';
import { useToast } from '@/hooks/use-toast';
import { useTranslations } from 'next-intl';
import { LogIn, Mail, Eye, EyeOff, Loader2 } from 'lucide-react';
import { useState } from 'react';

export function LoginForm() {
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations('auth.login');
  const tErrors = useTranslations('auth.errors');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const form = useForm<LoginFormData>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      const result = await signIn('credentials', {
        email: data.email,
        password: data.password,
        rememberMe: data.rememberMe,
        redirect: false,
      });

      if (result?.error) {
        toast({
          title: "Login Failed",
          description: tErrors('invalidCredentials'),
          variant: "destructive"
        });
      } else {
        toast({
          title: "Login Successful",
          description: "Welcome back!"
        });
        router.push('/dashboard');
      }
    } catch (error) {
      toast({
        title: "Login Failed",
        description: tErrors('serverError'),
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <h3 className="text-xl font-semibold text-gray-200 mb-2">{t('title')}</h3>
      <p className="text-sm text-gray-400 mb-6">{t('subtitle')}</p>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <Label htmlFor="email" className="block text-sm font-medium text-gray-300">{t('email').toUpperCase()}</Label>
                <FormControl>
                  <div className="relative">
                    <input
                      id="email"
                      type="email"
                      placeholder={t('email')}
                      {...field}
                      className="w-full pl-3 pr-10 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                    />
                    <Mail className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  </div>
                </FormControl>
                <FormMessage className="text-red-400 text-xs" />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="password" className="block text-sm font-medium text-gray-300">{t('password').toUpperCase()}</Label>
                  <Link href="/auth/request-access" className="text-xs text-gray-400 hover:text-gray-300">Need Access?</Link>
                </div>
                <FormControl>
                  <div className="relative">
                    <input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      placeholder={t('password')}
                      {...field}
                      className="w-full pl-3 pr-10 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-300 focus:outline-none"
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </FormControl>
                <FormMessage className="text-red-400 text-xs" />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="rememberMe"
            render={({ field }) => (
              <FormItem className="flex items-center">
                <FormControl>
                  <Checkbox
                    id="remember"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    className="h-4 w-4 bg-gray-800 border-gray-700 rounded text-gray-500 focus:ring-gray-600 data-[state=checked]:bg-gray-600 data-[state=checked]:text-white"
                  />
                </FormControl>
                <Label htmlFor="remember" className="ml-2 block text-sm text-gray-400">
                  {t('rememberMe')}
                </Label>
              </FormItem>
            )}
          />
          
          <div className="pt-2">
            <Button type="submit" className="w-full px-4 py-2.5 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition flex items-center justify-center font-medium focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-900" disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <LogIn className="h-4 w-4 mr-2" />
              )}
              {isLoading ? 'Signing in...' : t('signIn')}
            </Button>
          </div>
        </form>
      </Form>
        
      <div className="relative flex py-5 items-center">
        <div className="flex-grow border-t border-gray-700"></div>
        <span className="flex-shrink mx-4 text-gray-500 text-xs">OR</span>
        <div className="flex-grow border-t border-gray-700"></div>
      </div>
      <div className="text-center">
        <p className="text-xs text-gray-400">
          {t('noAccount')}{' '}
          <Link href="/auth/request-access" className="text-gray-300 hover:text-white">
            Request Access
          </Link>
        </p>
      </div>
      
      <div className="mt-4 text-center">
        <div className="flex items-center justify-center text-xs text-gray-400">
          <span className="w-2 h-2 rounded-full bg-green-400 mr-2"></span>
          <span>System Status: Operational</span>
        </div>
      </div>
    </div>
  );
}
