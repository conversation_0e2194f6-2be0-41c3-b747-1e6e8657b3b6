import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/database/user-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);

    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const userService = new UserService();
    
    // Get all active users (for team member selection)
    const users = userService.getAllUsers()
      .filter(user => user.isActive)
      .map(user => ({
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
        isActive: user.isActive
      }));
    
    return NextResponse.json({ users });
    
  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
