"use client";

import { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UnitToggle } from "@/components/ui/unit-toggle";
import { useUnit } from "@/lib/context/unit-context";
import {
  CoverJobSpecificationData,
  TabSpecificCardProps
} from "@/lib/types/ui-components";

// Add interface for ref methods
export interface CoverJobSpecificationsRef {
  getCurrentMmValues: () => CoverJobSpecificationData;
}

const CoverJobSpecificationsCard = forwardRef<CoverJobSpecificationsRef, TabSpecificCardProps<CoverJobSpecificationData>>(({
  className = "",
  onDataChange,
  initialData
}, ref) => {
  const { unit, convertMmToDisplayValue, convertDisplayValueToMm } = useUnit();

  // Store display values (what user sees)
  const [coverHeightDisplay, setCoverHeightDisplay] = useState("");
  const [coverWidthDisplay, setCoverWidthDisplay] = useState("");
  const [spineThicknessDisplay, setSpineThicknessDisplay] = useState("");
  const [flapWidthDisplay, setFlapWidthDisplay] = useState("");
  const [quantity, setQuantity] = useState(initialData?.quantity?.toString() || "1000");
  const [coverType, setCoverType] = useState<'paperback' | 'hardcover' | 'dustJacket'>(initialData?.coverType || "paperback");
  const [spoilage, setSpoilage] = useState((initialData?.spoilagePct ? (initialData.spoilagePct * 100).toFixed(1) : "7.0"));

  // Initialize display values from initial data
  useEffect(() => {
    if (initialData?.trimH) {
      setCoverHeightDisplay(convertMmToDisplayValue(initialData.trimH.toString(), unit));
    } else {
      setCoverHeightDisplay(convertMmToDisplayValue("225", unit));
    }

    if (initialData?.trimW) {
      setCoverWidthDisplay(convertMmToDisplayValue(initialData.trimW.toString(), unit));
    } else {
      setCoverWidthDisplay(convertMmToDisplayValue("150", unit));
    }

    if (initialData?.spineThickness) {
      setSpineThicknessDisplay(convertMmToDisplayValue(initialData.spineThickness.toString(), unit));
    } else {
      setSpineThicknessDisplay(convertMmToDisplayValue("10", unit));
    }

    if (initialData?.flapWidth) {
      setFlapWidthDisplay(convertMmToDisplayValue(initialData.flapWidth.toString(), unit));
    }
  }, [initialData, unit, convertMmToDisplayValue]);

  // Convert display values when unit changes
  useEffect(() => {
    if (coverHeightDisplay) {
      const mmValue = convertDisplayValueToMm(coverHeightDisplay, unit === 'mm' ? 'in' : 'mm');
      setCoverHeightDisplay(convertMmToDisplayValue(mmValue, unit));
    }
    if (coverWidthDisplay) {
      const mmValue = convertDisplayValueToMm(coverWidthDisplay, unit === 'mm' ? 'in' : 'mm');
      setCoverWidthDisplay(convertMmToDisplayValue(mmValue, unit));
    }
    if (spineThicknessDisplay) {
      const mmValue = convertDisplayValueToMm(spineThicknessDisplay, unit === 'mm' ? 'in' : 'mm');
      setSpineThicknessDisplay(convertMmToDisplayValue(mmValue, unit));
    }
    if (flapWidthDisplay) {
      const mmValue = convertDisplayValueToMm(flapWidthDisplay, unit === 'mm' ? 'in' : 'mm');
      setFlapWidthDisplay(convertMmToDisplayValue(mmValue, unit));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [unit]); // Only depend on unit changes

  // Function to get current mm values for calculation (called externally)
  const getCurrentMmValues = useCallback(() => {
    const data: CoverJobSpecificationData = {
      trimH: parseFloat(convertDisplayValueToMm(coverHeightDisplay, unit)) || 0,
      trimW: parseFloat(convertDisplayValueToMm(coverWidthDisplay, unit)) || 0,
      spineThickness: parseFloat(convertDisplayValueToMm(spineThicknessDisplay, unit)) || 0,
      quantity: parseInt(quantity) || 0,
      coverType,
      spoilagePct: (parseFloat(spoilage) || 0) / 100,
      ...(coverType === 'dustJacket' && flapWidthDisplay && { flapWidth: parseFloat(convertDisplayValueToMm(flapWidthDisplay, unit)) })
    };
    return data;
  }, [convertDisplayValueToMm, coverHeightDisplay, coverWidthDisplay, spineThicknessDisplay, unit, quantity, coverType, spoilage, flapWidthDisplay]);

  // Expose getCurrentMmValues function via ref
  useImperativeHandle(ref, () => ({
    getCurrentMmValues
  }));

  // Notify parent component when data changes (for real-time updates of non-dimension fields)
  useEffect(() => {
    if (onDataChange) {
      onDataChange(getCurrentMmValues());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [quantity, coverType, spoilage, onDataChange]);

  // Input validation function
  const validateNumberInput = (value: string) => {
    // Allow empty string, numbers, decimal points, and negative signs
    return value === '' || /^-?\d*\.?\d*$/.test(value);
  };

  // Auto-calculate flap width for dust jackets (typically half the book width)
  useEffect(() => {
    if (coverType === 'dustJacket' && !flapWidthDisplay && coverWidthDisplay) {
      const widthMm = convertDisplayValueToMm(coverWidthDisplay, unit);
      const autoFlapWidthMm = (parseFloat(widthMm) / 2).toFixed(1);
      setFlapWidthDisplay(convertMmToDisplayValue(autoFlapWidthMm, unit));
    }
  }, [coverType, coverWidthDisplay, flapWidthDisplay, unit, convertDisplayValueToMm, convertMmToDisplayValue]);

  return (
    <Card className={className} data-glow>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center justify-between w-full">
          <div className="flex items-center">
            <span className="w-2 h-2 bg-[#5E6AD2]/80 dark:bg-[#9E8CFC] rounded-full mr-2"></span>
            Job Specifications
          </div>
          <UnitToggle className="ml-auto" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Cover Dimensions Row - Height and Width side by side */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="cover-height" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                Height (H)
              </Label>
              <span className="text-xs text-gray-500 dark:text-neutral-400">{unit}</span>
            </div>
            <Input
              id="cover-height"
              type="number"
              step={unit === 'mm' ? "0.1" : "0.01"}
              placeholder={unit === 'mm' ? "225" : "8.86"}
              value={coverHeightDisplay}
              onChange={(e) => {
                if (validateNumberInput(e.target.value)) {
                  setCoverHeightDisplay(e.target.value);
                }
              }}
              className="h-9 text-sm bg-white dark:bg-neutral-700 border-gray-300 dark:border-neutral-600"
              data-glow
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="cover-width" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                Width (W)
              </Label>
              <span className="text-xs text-gray-500 dark:text-neutral-400">{unit}</span>
            </div>
            <Input
              id="cover-width"
              type="number"
              step={unit === 'mm' ? "0.1" : "0.01"}
              placeholder={unit === 'mm' ? "150" : "5.91"}
              value={coverWidthDisplay}
              onChange={(e) => {
                if (validateNumberInput(e.target.value)) {
                  setCoverWidthDisplay(e.target.value);
                }
              }}
              className="h-9 text-sm bg-white dark:bg-neutral-700 border-gray-300 dark:border-neutral-600"
              data-glow
            />
            <p className="text-xs text-gray-500 mt-1">Typically same as inner text page width</p>
          </div>
        </div>



        {/* Spine Thickness Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="spine-thickness" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                Spine Thickness
              </Label>
              <span className="text-xs text-gray-500 dark:text-neutral-400">{unit}</span>
            </div>
            <Input
              id="spine-thickness"
              type="number"
              step={unit === 'mm' ? "0.1" : "0.01"}
              placeholder={unit === 'mm' ? "10" : "0.39"}
              value={spineThicknessDisplay}
              onChange={(e) => {
                if (validateNumberInput(e.target.value)) {
                  setSpineThicknessDisplay(e.target.value);
                }
              }}
              className="h-9 text-sm"
            />
          </div>
          <div></div>
        </div>

        {/* Cover Type and Quantity Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="cover-type" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
              Cover Type
            </Label>
            <Select value={coverType} onValueChange={(value: 'paperback' | 'hardcover' | 'dustJacket') => setCoverType(value)}>
              <SelectTrigger className="mt-1 h-9 text-sm" data-glow>
                <SelectValue placeholder="Select cover type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="paperback">Paperback</SelectItem>
                <SelectItem value="hardcover">Hardcover (Case)</SelectItem>
                <SelectItem value="dustJacket">Dust Jacket</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="quantity" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
              Quantity
            </Label>
            <Input
              id="quantity"
              type="number"
              placeholder="1000"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              className="mt-1 h-9 text-sm bg-white dark:bg-neutral-600 border-gray-300 dark:border-neutral-600"
              data-glow
            />
          </div>
        </div>

        {/* Conditional Flap Width for Dust Jacket */}
        {coverType === 'dustJacket' && (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="flex items-center justify-between mb-1">
                <Label htmlFor="flap-width" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                  Flap Width
                </Label>
                <span className="text-xs text-gray-500 dark:text-neutral-400">{unit}</span>
              </div>
              <Input
                id="flap-width"
                type="number"
                step={unit === 'mm' ? "0.1" : "0.01"}
                placeholder="Auto-calculated"
                value={flapWidthDisplay}
                onChange={(e) => {
                  if (validateNumberInput(e.target.value)) {
                    setFlapWidthDisplay(e.target.value);
                  }
                }}
                className="h-9 text-sm bg-white dark:bg-neutral-600 border-gray-300 dark:border-neutral-600"
                data-glow
              />
              <p className="text-xs text-gray-500 mt-1">Typically half the book width</p>
            </div>
            <div>
              <Label htmlFor="spoilage" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                Spoilage (%)
              </Label>
              <Input
                id="spoilage"
                type="number"
                step="0.1"
                placeholder="7"
                value={spoilage}
                onChange={(e) => {
                  const value = parseFloat(e.target.value);
                  if (!isNaN(value)) {
                    setSpoilage(value.toFixed(1));
                  } else {
                    setSpoilage(e.target.value);
                  }
                }}
                className="col-span-2"
              />
            </div>
          </div>
        )}

        {/* Spoilage for non-dust jacket covers */}
        {coverType !== 'dustJacket' && (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="spoilage" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                Spoilage (%)
              </Label>
              <Input
                id="spoilage"
                type="number"
                step="0.1"
                placeholder="7"
                value={spoilage}
                onChange={(e) => setSpoilage(e.target.value)}
                className="mt-1 h-9 text-sm"
              />
            </div>
            <div></div>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

CoverJobSpecificationsCard.displayName = 'CoverJobSpecificationsCard';

export default CoverJobSpecificationsCard;
