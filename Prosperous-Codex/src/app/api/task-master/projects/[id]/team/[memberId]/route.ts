import { NextRequest, NextResponse } from 'next/server';
import { TaskMasterService } from '@/lib/database/task-master-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; memberId: string }> }
) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);

    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;
    const { id, memberId } = await params;
    const projectId = parseInt(id);
    const memberIdNum = parseInt(memberId);
    
    if (isNaN(projectId) || isNaN(memberIdNum)) {
      return NextResponse.json(
        { error: 'Invalid project ID or member ID' },
        { status: 400 }
      );
    }
    
    const taskMasterService = new TaskMasterService();
    
    // Check if user has access to this project (must be project owner)
    const project = taskMasterService.getProjectById(projectId);
    if (!project || project.created_by !== parseInt(user.id)) {
      return NextResponse.json(
        { error: 'Access denied. Only project owners can remove team members.' },
        { status: 403 }
      );
    }
    
    // Remove team member
    const success = await taskMasterService.removeTeamMember(projectId, memberIdNum, parseInt(user.id));
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to remove team member' },
        { status: 400 }
      );
    }
    
    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error('Remove team member error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
