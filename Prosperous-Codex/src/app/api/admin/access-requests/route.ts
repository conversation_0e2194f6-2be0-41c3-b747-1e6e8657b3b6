import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database/database';
import { UserService } from '@/lib/database/user-service';
import { requireRole } from '@/lib/auth/middleware';

export async function GET(request: NextRequest) {
  try {
    // Require moderator role
    const authResult = await requireRole(request, 'moderator');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const db = getDatabase();
    const stmt = db.prepare(`
      SELECT 
        ar.id, 
        ar.email, 
        ar.name, 
        ar.reason, 
        ar.status, 
        ar.requested_at, 
        ar.processed_at,
        u.username as processed_by_username
      FROM access_requests ar
      LEFT JOIN users u ON ar.processed_by = u.id
      ORDER BY ar.requested_at DESC
    `);
    
    const requests = stmt.all();
    
    return NextResponse.json({ requests });
    
  } catch (error) {
    console.error('Get access requests error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require moderator role
    const authResult = await requireRole(request, 'moderator');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { requestId, action, password } = await request.json();
    
    if (!requestId || !action) {
      return NextResponse.json(
        { error: 'Request ID and action are required' },
        { status: 400 }
      );
    }
    
    if (!['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be approve or reject' },
        { status: 400 }
      );
    }
    
    if (action === 'approve' && !password) {
      return NextResponse.json(
        { error: 'Password is required for approval' },
        { status: 400 }
      );
    }
    
    if (action === 'approve' && password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }
    
    const db = getDatabase();
    
    // Get the request
    const requestStmt = db.prepare(`
      SELECT id, email, name, status FROM access_requests WHERE id = ?
    `);
    const accessRequest = requestStmt.get(requestId) as any;
    
    if (!accessRequest) {
      return NextResponse.json(
        { error: 'Access request not found' },
        { status: 404 }
      );
    }
    
    if (accessRequest.status !== 'pending') {
      return NextResponse.json(
        { error: 'Access request has already been processed' },
        { status: 400 }
      );
    }
    
    if (action === 'approve') {
      // Create new user
      const userService = new UserService();
      
      try {
        await userService.createUser({
          email: accessRequest.email,
          username: accessRequest.name || accessRequest.email.split('@')[0],
          password: password,
          role: 'user'
        });
        
        // New user created successfully
      } catch (error) {
        if (error instanceof Error && error.message.includes('already exists')) {
          return NextResponse.json(
            { error: 'User with this email already exists' },
            { status: 409 }
          );
        }
        throw error;
      }
    }
    
    // Update request status
    const updateStmt = db.prepare(`
      UPDATE access_requests 
      SET status = ?, processed_at = CURRENT_TIMESTAMP, processed_by = ?
      WHERE id = ?
    `);
    
    updateStmt.run(
      action === 'approve' ? 'approved' : 'rejected',
      parseInt(authResult.user.id),
      requestId
    );
    
    return NextResponse.json({ 
      message: `Access request ${action}d successfully` 
    });
    
  } catch (error) {
    console.error('Process access request error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
