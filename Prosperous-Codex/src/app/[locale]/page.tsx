"use client";

import { useEffect } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { Loader2 } from 'lucide-react';

export default function HomePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const t = useTranslations('common');

  useEffect(() => {
    if (status !== "loading") {
      if (session?.user) {
        router.replace('/dashboard');
      } else {
        router.replace('/auth/login');
      }
    }
  }, [session, status, router]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center" suppressHydrationWarning>
      <Loader2 className="h-12 w-12 animate-spin text-primary" />
      <p className="mt-4 text-muted-foreground">{t('loading')} Prosperous Codex...</p>
    </div>
  );
}
