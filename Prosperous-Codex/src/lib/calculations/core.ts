// Core Calculation Functions
// Ported from the HTML version with TypeScript improvements

import {
  JobInputs,
  InnerTextJobInputs,
  BleedDimensions,
  PaperCandidate,
  LayoutFitResult,
  PRESS_CONSTRAINTS,
  FOUR_SIDE_BLEED_METHODS,
  ERROR_KEYS
} from '../types/calculation';
import { PaperOption } from '../paper-data';

// Utility functions
export const safeParseFloat = (value: any): number => {
  const parsed = parseFloat(value);
  return isNaN(parsed) ? 0 : parsed;
};

export const safeParseInt = (value: any): number => {
  const parsed = parseInt(value);
  return isNaN(parsed) ? 0 : parsed;
};

export const floor = Math.floor;
export const ceil = Math.ceil;

export function getPaperOptionDataAndValidate(
  paperOption: PaperOption,
  tabId: 'innerText' | 'cover' | 'endpapers'
): PaperCandidate {
  const result: PaperCandidate = {
    id: paperOption.id,
    name: paperOption.name,
    source: paperOption.source as 'Pre-Cut' | 'Roll',
    sheetH: paperOption.sheetHeight,
    sheetW: paperOption.sheetWidth,
    grainDirection: paperOption.grainDirection as 'Height' | 'Width',
    caliperMicrons: paperOption.caliper,
    costReam: paperOption.costPerReam,
    gsm: paperOption.gsm,
    costTonne: paperOption.costPerTon || null,
    error: false,
    errorMessageKey: undefined,
    isOptimalCandidate: false,
    maxItemsPerSide: 0,
    pagesPerSheetOutput: 0,
    totalSheetsNeeded: 0,
    costPerSheet: 0,
    totalCost: 0,
    costPerBook: 0,
    bookBlockThickness_mm: 0,
    wastePercent: 0,
    utilisationRate: 0,
    pressH: 0,
    pressW: 0,
    usableH: 0,
    usableW: 0,
    occupiedHeight: 0,
    occupiedWidth: 0,
    paperWasRotated: false,
    rollDimensionOptimizedForFitting: false,
    inputNoteKey: undefined,
    rollOptimizationNoteKey: undefined
  };

  // Basic validation for all paper types
  const sheetWidth = paperOption.sheetWidth;
  const sheetHeight = paperOption.sheetHeight;
  const costPerReam = paperOption.costPerReam;
  const costPerTon = paperOption.costPerTon;

  if (paperOption.source === 'Pre-Cut') {
    // Pre-cut papers must have both dimensions > 0
    if (sheetWidth <= 0 || sheetHeight === null || sheetHeight <= 0) {
      result.error = true;
      result.errorMessageKey = ERROR_KEYS.INVALID_PAPER_DIMENSIONS;
      return result;
    }
    if (costPerReam === null || costPerReam <= 0) {
      result.error = true;
      result.errorMessageKey = ERROR_KEYS.MISSING_COST_REAM;
      return result;
    }
  } else if (paperOption.source === 'Roll') {
    // Roll papers can have one dimension as 0/null (to be inferred), but not both
    const hasValidWidth = sheetWidth > 0;
    const hasValidHeight = sheetHeight !== null && sheetHeight > 0;

    if (!hasValidWidth && !hasValidHeight) {
      // Both dimensions missing is invalid
      result.error = true;
      result.errorMessageKey = ERROR_KEYS.INVALID_PAPER_DIMENSIONS;
      return result;
    }
    if (paperOption.gsm <= 0 || costPerTon === null || costPerTon === undefined || costPerTon <= 0) {
      result.error = true;
      result.errorMessageKey = ERROR_KEYS.INVALID_ROLL_COST;
      return result;
    }
  }

  // Category specific validation (optional, based on your needs)
  // Map tabId to expected category names
  const expectedCategory = tabId === 'innerText' ? 'Inner Text' :
                          tabId === 'cover' ? 'Cover' :
                          tabId === 'endpapers' ? 'Endpapers' : null;

  if (paperOption.category && expectedCategory && paperOption.category !== expectedCategory) {
    // This might indicate a mismatch, depending on how strict you want the validation
    // For now, we'll just log a warning or handle it gracefully without erroring out.
    console.warn(`Paper category mismatch: ${paperOption.name} is ${paperOption.category}, but used for ${tabId}`);
  }

  return result;
}

// Validation Functions
export function validateJobInputs(inputs: JobInputs): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (inputs.trimH <= 0 || inputs.trimW <= 0) {
    errors.push(ERROR_KEYS.INVALID_TRIM_DIMENSIONS);
  }
  
  if (inputs.quantity <= 0) {
    errors.push('Quantity must be positive');
  }
  
  if (inputs.bleed < 0 || inputs.gripper < 0 || inputs.colorBar < 0 || inputs.spoilagePct < 0) {
    errors.push(ERROR_KEYS.NEGATIVE_PROD_PARAMS);
  }
  
  return { isValid: errors.length === 0, errors };
}

export function validateInnerTextInputs(inputs: InnerTextJobInputs): { isValid: boolean; errors: string[] } {
  const baseValidation = validateJobInputs(inputs);
  const errors = [...baseValidation.errors];
  
  if (inputs.totalPages <= 0 || inputs.totalPages % 2 !== 0) {
    errors.push(ERROR_KEYS.INVALID_TOTAL_PAGES);
  }
  
  if (inputs.lip < 0) {
    errors.push(ERROR_KEYS.NEGATIVE_PROD_PARAMS);
  }
  
  return { isValid: errors.length === 0, errors };
}

// Bleed Dimension Calculations
export function calculateBleedDimensions_InnerText(
  trimW: number, 
  trimH: number, 
  bleed: number, 
  bindingMethod: string
): BleedDimensions | null {
  let layoutPageH = 0;
  let layoutPageW = 0;
  
  const fourSide = FOUR_SIDE_BLEED_METHODS.includes(bindingMethod as any);
  
  if (fourSide) {
    layoutPageH = trimH + 2 * bleed;
    layoutPageW = trimW + 2 * bleed;
  } else {
    layoutPageH = trimH + 2 * bleed;
    layoutPageW = trimW + bleed; // Spread logic
  }
  
  return (layoutPageH > 0 && layoutPageW > 0) ? { layoutPageH, layoutPageW } : null;
}

export function calculateBleedDimensions_Cover(
  trimH: number,
  trimW: number,
  spine: number,
  bleed: number,
  coverType: string,
  turnInAllowance: number,
  flapWidth: number
): BleedDimensions | null {
  // Calculate core dimensions based on cover type
  const coreLayoutPageH = trimH + (2 * bleed);
  let coreLayoutPageW: number;

  if (coverType === 'dustJacket' && flapWidth > 0) {
    // Dust Jacket formula: Total Width = (Book Width × 2) + (Flap Width × 2) + Spine Width + (Bleed × 2)
    coreLayoutPageW = (2 * trimW) + (2 * flapWidth) + spine + (2 * bleed);
  } else {
    // Standard cover formula: Back_Cover_W + Spine_W + Front_Cover_W + Bleeds on outer edges
    coreLayoutPageW = (2 * trimW) + spine + (2 * bleed);
  }

  let finalLayoutPageH = coreLayoutPageH;
  let finalLayoutPageW = coreLayoutPageW;

  if (coverType === 'hardcover' && turnInAllowance > 0) {
    finalLayoutPageH += (2 * turnInAllowance);
    finalLayoutPageW += (2 * turnInAllowance);
  }

  return (finalLayoutPageH > 0 && finalLayoutPageW > 0) ? 
    { layoutPageH: finalLayoutPageH, layoutPageW: finalLayoutPageW } : null;
}

export function calculateBleedDimensions_Endpapers(
  trimH: number,
  trimW: number,
  bleed: number
): BleedDimensions | null {
  // An endpaper spread is typically TrimH x (2 * TrimW) before bleed.
  const layoutPageH = trimH + (2 * bleed); // Top and bottom bleed
  // For a spread (2 * TrimW), bleed is typically only on the outer edge, not the spine fold.
  const layoutPageW = (2 * trimW) + bleed; // Bleed on one side of the spread width
  
  return (layoutPageH > 0 && layoutPageW > 0) ? { layoutPageH, layoutPageW } : null;
}

// Optimal Layout Benchmark Calculations
export function determineOptimalLayoutBenchmark_InnerText(
  maxPressH: number,
  maxPressW: number,
  gripper: number,
  colorBar: number,
  lip: number,
  bleed: number,
  trimW: number,
  trimH: number,
  bindingMethod: string
): number {
  const usableH = maxPressH - gripper - colorBar;
  const usableW = maxPressW - lip;
  
  if (usableH <= 0 || usableW <= 0) return 0;
  
  const dims = calculateBleedDimensions_InnerText(trimW, trimH, bleed, bindingMethod);
  if (!dims || !dims.layoutPageH || !dims.layoutPageW) return 0;
  
  const { layoutPageH, layoutPageW } = dims;

  // Orientation A: Item H || Press H
  let fitA = 0;
  const dA = floor(usableH / layoutPageH);
  const aA_i = floor(usableW / layoutPageW);
  const aA = (aA_i > 0) ? floor(aA_i / 2) * 2 : 0; // Even rule for spreads
  fitA = dA * aA;
  fitA = isNaN(fitA) ? 0 : fitA;

  // Orientation B: Item W || Press H (rotated)
  let fitB = 0;
  const dB_i = floor(usableH / layoutPageW);
  const dB = (dB_i > 0) ? floor(dB_i / 2) * 2 : 0; // Even rule for spreads
  const aB = floor(usableW / layoutPageH);
  fitB = dB * aB;
  fitB = isNaN(fitB) ? 0 : fitB;

  return Math.min(Math.max(fitA, fitB), PRESS_CONSTRAINTS.MAX_PAGES_PER_SIDE);
}

// Reference implementation's robust layout fitting for inner text (benchmark-constrained)
export function calculateSingleLayoutFit_InnerText(
  usableH: number,
  usableW: number,
  itemDimH: number,
  itemDimW: number
): LayoutFitResult | null {
  if (itemDimH <= 0 || itemDimW <= 0) return null;

  const layoutDown_initial = floor(usableH / itemDimH);
  const layoutAcross_initial = floor(usableW / itemDimW);

  // Apply even rule for inner text spreads (only to width for spreads across)
  const layoutDown = layoutDown_initial;
  const layoutAcross = (layoutAcross_initial > 0) ? floor(layoutAcross_initial / 2) * 2 : 0;

  const geometricItemsPerSide = layoutDown * layoutAcross;
  if (geometricItemsPerSide <= 0) return null;

  const targetItemsPerSide = Math.min(geometricItemsPerSide, 16); // Folding limit

  // Robust search for inner text to maximize page count within folding limits
  // This matches the reference implementation's comprehensive search
  let bestFittingItems = 0;
  let finalLayoutDown = 0;
  let finalLayoutAcross = 0;

  for (let currentItems = targetItemsPerSide; currentItems >= 2; currentItems -= 2) {
    for (let rows = 1; rows <= currentItems; rows++) {
      if (currentItems % rows === 0) {
        const cols = currentItems / rows;

        // Re-apply imposition rules for spreads
        if (cols % 2 !== 0) continue; // Even rule for spreads across

        const reqH = rows * itemDimH;
        const reqW = cols * itemDimW;

        if (reqH <= usableH && reqW <= usableW) {
          if (currentItems > bestFittingItems) {
            bestFittingItems = currentItems;
            finalLayoutDown = rows;
            finalLayoutAcross = cols;
          }
          // Could add area efficiency tie-breaker here if currentItems === bestFittingItems
        }
      }
    }
    if (bestFittingItems > 0 && bestFittingItems === currentItems) break;
  }

  // Handle case where only 1 item might fit (e.g. single page)
  if (bestFittingItems === 0 && targetItemsPerSide === 1) {
    if (itemDimH <= usableH && itemDimW <= usableW) {
      bestFittingItems = 1;
      finalLayoutDown = 1;
      finalLayoutAcross = 1;
    }
  }

  if (bestFittingItems <= 0) return null;

  const occupiedHeight = finalLayoutDown * itemDimH;
  const occupiedWidth = finalLayoutAcross * itemDimW;

  // Final check that the calculated layout actually fits
  if (occupiedHeight <= usableH && occupiedWidth <= usableW) {
    return {
      layoutDown: finalLayoutDown,
      layoutAcross: finalLayoutAcross,
      fit: bestFittingItems,
      occupiedHeight,
      occupiedWidth
    };
  }

  return null; // Should not happen if logic above is correct
}

export function determineOptimalLayoutBenchmark_Cover(
  maxPressH: number,
  maxPressW: number,
  gripper: number,
  colorBar: number,
  itemH: number,
  itemW: number
): number {
  const usableH = maxPressH - gripper - colorBar;
  const usableW = maxPressW;
  
  if (usableH <= 0 || usableW <= 0 || itemH <= 0 || itemW <= 0) return 0;
  
  // How many flat cover spreads can fit (N-up)
  const fitA = floor(usableH / itemH) * floor(usableW / itemW); // Item H || Press H
  const fitB = floor(usableH / itemW) * floor(usableW / itemH); // Item W || Press H (rotated)
  
  return Math.max(fitA, fitB);
}

export function determineOptimalLayoutBenchmark_Endpapers(
  maxPressH: number,
  maxPressW: number,
  gripper: number,
  colorBar: number,
  itemH: number,
  itemW: number
): number {
  return determineOptimalLayoutBenchmark_Cover(maxPressH, maxPressW, gripper, colorBar, itemH, itemW);
}

// Initial Press Fit Validation
export function determineInitialPressFit(
  paperData: any,
  maxPressH: number,
  maxPressW: number
): any {
  // Validate that the paper data is valid
  if (!paperData || paperData.error) {
    return {
      error: true,
      errorMessageKey: 'INVALID_PAPER_DATA',
      ...paperData
    };
  }

  const { sheetH, sheetW } = paperData;

  // CRITICAL FIX B: Don't error out for oversized sheets here
  // Let applyPressLimitsAndGetGrain handle rotation/trimming
  // Only error if both dimensions are larger than press limits (impossible to fit)
  if (sheetH > maxPressH && sheetW > maxPressW &&
      sheetH > maxPressW && sheetW > maxPressH) {
    // Paper cannot fit even with rotation
    return {
      ...paperData,
      error: true,
      errorMessageKey: 'PAPER_TOO_LARGE_FOR_PRESS'
    };
  }

  // Paper can potentially fit (with or without rotation/trimming)
  return paperData;
}

// Helper function to sort candidates by efficiency
// Updated to match HTML reference implementation sorting logic
function sortCandidatesByEfficiency(candidates: any[]): any[] {
  return candidates.sort((x, y) => {
    // Primary: maxItemsPerSide (descending - more items per side is better)
    if ((x.maxItemsPerSide || 0) !== (y.maxItemsPerSide || 0)) {
      return (y.maxItemsPerSide || 0) - (x.maxItemsPerSide || 0);
    }

    // Secondary: waste area (ascending - less waste is better)
    const wasteX = (x.usableW || 0) * (x.usableH || 0) - (x.occupiedWidth || 0) * (x.occupiedHeight || 0);
    const wasteY = (y.usableW || 0) * (y.usableH || 0) - (y.occupiedWidth || 0) * (y.occupiedHeight || 0);

    return wasteX - wasteY;
  });
}

 // Select winning candidate between two orientations (matches reference implementation)
export function selectWinningCandidate(
  candidateA: any,
  candidateB: any,
  alignmentMode: 'Aligned' | 'Misaligned'
): any {
  // Filter valid candidates (non-null and with positive maxItemsPerSide)
  const validCandidates = [candidateA, candidateB].filter(c =>
    c !== null &&
    c !== undefined &&
    !c.error &&
    (c.maxItemsPerSide || 0) > 0
  );

  // Primary: Try to find candidates matching alignment requirements
  const matchingCandidates = validCandidates.filter(c =>
    c.grainAlignmentStatus === alignmentMode
  );

  if (matchingCandidates.length > 0) {
    // Sort and return best matching candidate (will be optimal if achieves benchmark)
    return sortCandidatesByEfficiency(matchingCandidates)[0];
  }

  // Fallback for 'Aligned' mode: If no aligned candidates, return best valid candidate with a warning
  // This ensures more paper options are returned, aligning with reference behavior
  if (alignmentMode === 'Aligned' && validCandidates.length > 0) {
    const fallbackCandidate = sortCandidatesByEfficiency(validCandidates)[0];
    fallbackCandidate.alignmentWarning = 'No aligned candidates found; fallback to misaligned option';
    return fallbackCandidate;
  }

  // Fallback for 'Misaligned' mode: If no alignment match, return best valid candidate
  if (validCandidates.length > 0) {
    return sortCandidatesByEfficiency(validCandidates)[0];
  }

  // Only return null if no candidates work at all (physical fit impossible)
  return null;
}

// Apply press limits and determine grain alignment
// CRITICAL FIX B: Add rotation and trimming logic for oversized sheets
export function applyPressLimitsAndGetGrain(
  candidate: any,
  alignmentMode: 'Aligned' | 'Misaligned'
): any {
  if (!candidate || candidate.error) {
    return {
      ...candidate,
      error: true,
      errorMessageKey: 'INVALID_CANDIDATE'
    };
  }

  let pressH = candidate.pressH;
  let pressW = candidate.pressW;
  let paperWasRotated = candidate.paperWasRotated || false;
  let pressSizeNoteKey = '';

  // Handle oversized sheets: try rotation first, then trimming
  if (pressH > PRESS_CONSTRAINTS.MAX_PRESS_H || pressW > PRESS_CONSTRAINTS.MAX_PRESS_W) {
    // Try rotation if it would make the sheet fit
    if (pressW <= PRESS_CONSTRAINTS.MAX_PRESS_H && pressH <= PRESS_CONSTRAINTS.MAX_PRESS_W) {
      // Rotation will make it fit
      const temp = pressH;
      pressH = pressW;
      pressW = temp;
      paperWasRotated = !paperWasRotated;
      pressSizeNoteKey = 'resNoteRotated';
    } else {
      // Rotation won't help, trim to press limits
      pressH = Math.min(pressH, PRESS_CONSTRAINTS.MAX_PRESS_H);
      pressW = Math.min(pressW, PRESS_CONSTRAINTS.MAX_PRESS_W);
      pressSizeNoteKey = 'resNoteTrimmed';
    }
  }

  // Determine grain alignment based on paper orientation
  const grainDirection = candidate.grainDirection || 'Height';

  let grainAlignmentStatus: 'Aligned' | 'Misaligned';

  // Grain alignment logic using correct values (Height/Width instead of Long/Short)
  // "Aligned" means spine direction parallel to paper's long grain
  if (grainDirection === 'Height' && !paperWasRotated) {
    // Height grain + not rotated = grain runs parallel to height = Aligned
    grainAlignmentStatus = 'Aligned';
  } else if (grainDirection === 'Width' && paperWasRotated) {
    // Width grain + rotated = original width grain now runs parallel to height = Aligned
    grainAlignmentStatus = 'Aligned';
  } else {
    grainAlignmentStatus = 'Misaligned';
  }

  return {
    ...candidate,
    pressH,
    pressW,
    paperWasRotated,
    pressSizeNoteKey,
    grainAlignmentStatus
  };
}

// Calculate usable printing area
export function calculateUsableArea(
  candidate: any,
  gripper: number,
  colorBar: number,
  lip: number
): any {
  if (!candidate || candidate.error) {
    return {
      ...candidate,
      error: true,
      errorMessageKey: 'INVALID_CANDIDATE'
    };
  }

  const usableH = candidate.pressH - gripper - colorBar;
  const usableW = candidate.pressW - lip;

  if (usableH <= 0 || usableW <= 0) {
    return {
      ...candidate,
      error: true,
      errorMessageKey: 'INSUFFICIENT_USABLE_AREA'
    };
  }

  return {
    ...candidate,
    usableH,
    usableW
  };
}

// Reference implementation's layout calculation with even rule parameters
function calculateSingleLayoutFit_ComponentSpecific_Reference(
  tabId: string,
  usableH: number,
  usableW: number,
  itemDimH: number,
  itemDimW: number,
  applyEvenRuleToHeight: boolean,
  applyEvenRuleToWidth: boolean
): any {
  if (itemDimH <= 0 || itemDimW <= 0) return null;

  const layoutDown_initial = floor(usableH / itemDimH);
  const layoutAcross_initial = floor(usableW / itemDimW);
  let layoutDown = layoutDown_initial;
  let layoutAcross = layoutAcross_initial;

  if (tabId === 'innerText') {
    // For inner text, these rules apply to how spreads are imposed
    if (applyEvenRuleToHeight) layoutDown = (layoutDown_initial > 0) ? floor(layoutDown_initial / 2) * 2 : 0;
    if (applyEvenRuleToWidth) layoutAcross = (layoutAcross_initial > 0) ? floor(layoutAcross_initial / 2) * 2 : 0;
  }
  // For Cover/Endpapers, the even rules don't apply in the same way

  // For the robust search, we need to use the ORIGINAL geometric calculation
  // The even rules are applied during the search, not to limit the initial target
  const originalGeometricItemsPerSide = layoutDown_initial * layoutAcross_initial;
  if (originalGeometricItemsPerSide <= 0) return null;

  let targetItemsPerSide = originalGeometricItemsPerSide;
  if (tabId === 'innerText') {
    targetItemsPerSide = Math.min(originalGeometricItemsPerSide, 16); // Folding limit for inner text signatures
  }

  if (targetItemsPerSide <= 0) return null;

  let bestFittingItems = 0;
  let finalLayoutDown = 0;
  let finalLayoutAcross = 0;

  if (tabId === 'innerText') {
    // Robust search for inner text to maximize page count within folding limits
    for (let currentItems = targetItemsPerSide; currentItems >= 2; currentItems -= 2) { // Iterate by 2 for signatures
      for (let rows = 1; rows <= currentItems; rows++) {
        if (currentItems % rows === 0) {
          const cols = currentItems / rows;
          // Re-apply imposition rules if they were for spreads
          if (applyEvenRuleToWidth && cols % 2 !== 0) continue;
          if (applyEvenRuleToHeight && rows % 2 !== 0) continue;

          const reqH = rows * itemDimH;
          const reqW = cols * itemDimW;
          if (reqH <= usableH && reqW <= usableW) {
            if (currentItems > bestFittingItems) {
              bestFittingItems = currentItems;
              finalLayoutDown = rows;
              finalLayoutAcross = cols;
            }
          }
        }
      }
      if (bestFittingItems > 0 && bestFittingItems === currentItems) break;
    }
    // Handle case where only 1 item might fit (e.g. single page)
    if (bestFittingItems === 0 && targetItemsPerSide === 1) {
      if (itemDimH <= usableH && itemDimW <= usableW) {
        bestFittingItems = 1;
        finalLayoutDown = 1;
        finalLayoutAcross = 1;
      }
    }
  } else { // For Cover & Endpapers - simpler: use the initial geometric N-up calculation
    finalLayoutDown = layoutDown;       // From initial geometric calc based on itemDimH vs usableH
    finalLayoutAcross = layoutAcross;   // From initial geometric calc based on itemDimW vs usableW
    bestFittingItems = finalLayoutDown * finalLayoutAcross;
  }

  if (bestFittingItems <= 0) return null;

  const occupiedHeight = finalLayoutDown * itemDimH;
  const occupiedWidth = finalLayoutAcross * itemDimW;

  // Final check that the calculated layout actually fits
  if (occupiedHeight <= usableH && occupiedWidth <= usableW) {
    return { layoutDown: finalLayoutDown, layoutAcross: finalLayoutAcross, fit: bestFittingItems, occupiedHeight, occupiedWidth };
  }
  return null; // Should not happen if logic above is correct
}

// Calculate layout fit for specific component (matches reference v23.html algorithm exactly)
export function calculateSingleLayoutFit_ComponentSpecific(
  candidate: any,
  bleedDims: any,
  tabId: string,
  jobInputs: any,
  applyEvenRuleToHeight: boolean = false,
  applyEvenRuleToWidth: boolean = true
): any {
  if (!candidate || candidate.error || !bleedDims) {
    return {
      ...candidate,
      error: true,
      errorMessageKey: 'INVALID_LAYOUT_PARAMETERS'
    };
  }

  const { usableH, usableW } = candidate;
  const { layoutPageH, layoutPageW } = bleedDims;

  if (!usableH || !usableW || !layoutPageH || !layoutPageW) {
    return {
      ...candidate,
      error: true,
      errorMessageKey: 'MISSING_DIMENSIONS'
    };
  }

  // Use the reference implementation's algorithm
  const layoutFit = calculateSingleLayoutFit_ComponentSpecific_Reference(
    tabId,
    usableH,
    usableW,
    layoutPageH,
    layoutPageW,
    applyEvenRuleToHeight,
    applyEvenRuleToWidth
  );

  if (!layoutFit) {
    return {
      ...candidate,
      error: true,
      errorMessageKey: 'NO_LAYOUT_FIT'
    };
  }

  const totalUsableArea = usableH * usableW;
  const occupiedArea = layoutFit.occupiedHeight * layoutFit.occupiedWidth;
  const utilisationRate = totalUsableArea > 0 ? occupiedArea / totalUsableArea : 0;
  const wastePercent = 1 - utilisationRate;

  return {
    ...candidate,
    layoutDown: layoutFit.layoutDown,
    layoutAcross: layoutFit.layoutAcross,
    maxItemsPerSide: layoutFit.fit,
    occupiedHeight: layoutFit.occupiedHeight,
    occupiedWidth: layoutFit.occupiedWidth,
    utilisationRate,
    wastePercent
  };
}
