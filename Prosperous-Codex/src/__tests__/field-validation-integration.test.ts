/**
 * Field Validation Integration Tests
 * 
 * Tests the complete field validation system integration including
 * schema validation, field mapping, and type safety enforcement
 * across the entire Task Master application.
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import Database from 'better-sqlite3';
import { SchemaValidator, createSchemaValidator } from '@/lib/task-master/schema-validator';
import { FieldMapper } from '@/lib/task-master/field-mapping';
import { TypeSafetyEnforcer } from '@/lib/task-master/type-safety-enforcer';
import { ApiValidator } from '@/lib/task-master/api-validator';

describe('Field Validation Integration Tests', () => {
  let db: Database.Database;
  let schemaValidator: SchemaValidator;

  beforeAll(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Create test tables with proper schema
    db.exec(`
      CREATE TABLE projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        full_description TEXT,
        event_log TEXT,
        status TEXT NOT NULL DEFAULT 'todo',
        priority TEXT NOT NULL DEFAULT 'medium',
        progress INTEGER NOT NULL DEFAULT 0,
        due_date TEXT,
        completed_date TEXT,
        created_by INTEGER NOT NULL,
        assigned_to INTEGER,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE tasks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        status TEXT NOT NULL DEFAULT 'todo',
        priority TEXT NOT NULL DEFAULT 'medium',
        progress INTEGER NOT NULL DEFAULT 0,
        due_date TEXT,
        completed_date TEXT,
        project_id INTEGER NOT NULL,
        parent_task_id INTEGER,
        created_by INTEGER NOT NULL,
        assigned_to INTEGER,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id),
        FOREIGN KEY (parent_task_id) REFERENCES tasks(id)
      );

      CREATE TABLE comments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content TEXT NOT NULL,
        task_id INTEGER NOT NULL,
        parent_comment_id INTEGER,
        created_by INTEGER NOT NULL,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(id),
        FOREIGN KEY (parent_comment_id) REFERENCES comments(id)
      );

      CREATE TABLE files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_name TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        mime_type TEXT NOT NULL,
        file_path TEXT NOT NULL,
        project_id INTEGER NOT NULL,
        uploaded_by INTEGER NOT NULL,
        uploaded_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id)
      );

      CREATE TABLE project_team_members (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        role TEXT NOT NULL DEFAULT 'member',
        joined_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id),
        UNIQUE(project_id, user_id)
      );

      CREATE TABLE activity_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        activity_type TEXT NOT NULL,
        entity_type TEXT NOT NULL,
        entity_id INTEGER NOT NULL,
        old_value TEXT,
        new_value TEXT,
        project_id INTEGER NOT NULL,
        performed_by INTEGER NOT NULL,
        performed_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id)
      );
    `);

    schemaValidator = createSchemaValidator(db);
  });

  afterAll(() => {
    if (db) {
      db.close();
    }
  });

  describe('Schema Validation Integration', () => {
    it('should validate all table schemas successfully', () => {
      const tables = ['projects', 'tasks', 'comments', 'files', 'project_team_members', 'activity_log'];
      
      for (const table of tables) {
        const result = schemaValidator.validateTableSchema(table);
        
        expect(result.valid).toBe(true);
        expect(result.tableName).toBe(table);
        expect(result.fieldInfo.length).toBeGreaterThan(0);
        expect(result.errors).toHaveLength(0);
      }
    });

    it('should check field name consistency across all tables', () => {
      const consistencyReport = schemaValidator.checkFieldNameConsistency();
      
      expect(consistencyReport.tablesChecked).toBe(6);
      expect(consistencyReport.totalFields).toBeGreaterThan(0);
      expect(consistencyReport.overallValid).toBe(true);
      
      // Should have minimal inconsistencies in our well-designed schema
      const errors = consistencyReport.inconsistencies.filter(i => i.severity === 'error');
      expect(errors.length).toBe(0);
    });

    it('should generate appropriate migration suggestions', () => {
      const migrationPlan = schemaValidator.generateMigrationSuggestions();
      
      expect(migrationPlan).toHaveProperty('requiredChanges');
      expect(migrationPlan).toHaveProperty('optionalChanges');
      expect(migrationPlan).toHaveProperty('riskLevel');
      expect(migrationPlan).toHaveProperty('estimatedEffort');
      
      // With our proper schema, should be low risk
      expect(['low', 'medium', 'high']).toContain(migrationPlan.riskLevel);
    });

    it('should validate field mappings for each table', () => {
      const projectApiFields = ['id', 'title', 'description', 'fullDescription', 'eventLog', 'status', 'priority', 'progress', 'dueDate', 'completedDate', 'createdBy', 'assignedTo', 'createdAt', 'updatedAt'];
      const projectResult = schemaValidator.validateFieldMappings('projects', projectApiFields);
      
      expect(projectResult.valid).toBe(true);
      
      const taskApiFields = ['id', 'title', 'description', 'status', 'priority', 'progress', 'dueDate', 'completedDate', 'projectId', 'parentTaskId', 'createdBy', 'assignedTo', 'createdAt', 'updatedAt'];
      const taskResult = schemaValidator.validateFieldMappings('tasks', taskApiFields);
      
      expect(taskResult.valid).toBe(true);
    });
  });

  describe('End-to-End Field Mapping', () => {
    it('should handle complete project data flow', () => {
      // Simulate API request data
      const apiRequestData = {
        title: 'Integration Test Project',
        description: 'Test description',
        fullDescription: 'Full test description',
        eventLog: 'Initial event log',
        status: 'todo',
        priority: 'high',
        dueDate: '2024-12-31T23:59:59Z',
        assignedTo: 1
      };

      // Validate API request
      const requestValidation = ApiValidator.validateRequestFields(
        '/api/task-master/projects',
        apiRequestData
      );
      expect(requestValidation.valid).toBe(true);

      // Convert API to DB format
      const dbData = FieldMapper.apiToDb(apiRequestData);
      expect(dbData).toHaveProperty('full_description');
      expect(dbData).toHaveProperty('event_log');
      expect(dbData).toHaveProperty('due_date');
      expect(dbData).toHaveProperty('assigned_to');

      // Simulate database response (with additional fields)
      const dbResponseData = {
        id: 1,
        ...dbData,
        created_by: 1,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        progress: 0
      };

      // Convert DB to API format
      const apiResponseData = FieldMapper.dbToApi(dbResponseData);
      expect(apiResponseData).toHaveProperty('fullDescription');
      expect(apiResponseData).toHaveProperty('eventLog');
      expect(apiResponseData).toHaveProperty('dueDate');
      expect(apiResponseData).toHaveProperty('assignedTo');
      expect(apiResponseData).toHaveProperty('createdBy');
      expect(apiResponseData).toHaveProperty('createdAt');
      expect(apiResponseData).toHaveProperty('updatedAt');

      // Validate API response
      const responseValidation = ApiValidator.validateResponseFields(
        '/api/task-master/projects/1',
        apiResponseData
      );
      expect(responseValidation.valid).toBe(true);
    });

    it('should handle task creation with parent relationships', () => {
      const taskApiData = {
        title: 'Child Task',
        description: 'Child task description',
        status: 'in_progress',
        priority: 'medium',
        projectId: 1,
        parentTaskId: 2,
        assignedTo: 3,
        dueDate: '2024-06-30T23:59:59Z'
      };

      // Validate and convert
      const requestValidation = ApiValidator.validateRequestFields(
        '/api/task-master/projects/1/tasks',
        taskApiData
      );
      expect(requestValidation.valid).toBe(true);

      const dbData = FieldMapper.apiToDb(taskApiData);
      expect(dbData).toHaveProperty('project_id', 1);
      expect(dbData).toHaveProperty('parent_task_id', 2);
      expect(dbData).toHaveProperty('assigned_to', 3);
      expect(dbData).toHaveProperty('due_date');

      // Convert back
      const apiData = FieldMapper.dbToApi({
        id: 5,
        ...dbData,
        created_by: 1,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        progress: 25
      });

      expect(apiData).toHaveProperty('projectId', 1);
      expect(apiData).toHaveProperty('parentTaskId', 2);
      expect(apiData).toHaveProperty('assignedTo', 3);
      expect(apiData).toHaveProperty('createdBy', 1);
    });
  });

  describe('Type Safety Integration', () => {
    it('should enforce type safety throughout the data flow', () => {
      const enforcer = new TypeSafetyEnforcer({
        strictMode: true,
        enforceFieldNaming: true,
        validateAtRuntime: true,
        throwOnMismatch: false,
        logWarnings: false
      });

      // Test project data flow
      const projectApiData = {
        title: 'Type Safe Project',
        fullDescription: 'Full description',
        eventLog: 'Event log',
        status: 'todo',
        priority: 'high',
        progress: 0,
        dueDate: '2024-12-31T23:59:59Z',
        assignedTo: 1
      };

      const apiToDbResult = enforcer.enforceApiToDb(projectApiData, 'project');
      expect(apiToDbResult.valid).toBe(true);
      expect(apiToDbResult.correctedData).toHaveProperty('full_description');

      // Simulate database response
      const dbResponseData = {
        id: 1,
        ...apiToDbResult.correctedData,
        created_by: 1,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const dbToApiResult = enforcer.enforceDbToApi(dbResponseData, 'project');
      expect(dbToApiResult.valid).toBe(true);
      expect(dbToApiResult.correctedData).toHaveProperty('fullDescription');
      expect(dbToApiResult.correctedData).toHaveProperty('createdBy');
    });

    it('should detect and report type safety violations', () => {
      const enforcer = new TypeSafetyEnforcer({
        strictMode: true,
        enforceFieldNaming: true,
        throwOnMismatch: false,
        logWarnings: false
      });

      const invalidData = {
        title: 'Test',
        invalid_snake_case: 'should be camelCase',
        'kebab-case-field': 'not allowed',
        123: 'numeric key not allowed'
      };

      const result = enforcer.enforceApiToDb(invalidData, 'project');
      
      expect(result.valid).toBe(false);
      expect(result.violations.length).toBeGreaterThan(0);
      
      const namingViolations = result.violations.filter(v => v.type === 'field_naming');
      expect(namingViolations.length).toBeGreaterThan(0);
    });
  });

  describe('Cross-System Validation', () => {
    it('should validate consistency between schema, mappings, and API contracts', () => {
      // Get actual database schema
      const projectSchema = schemaValidator.validateTableSchema('projects');
      const taskSchema = schemaValidator.validateTableSchema('tasks');
      
      expect(projectSchema.valid).toBe(true);
      expect(taskSchema.valid).toBe(true);

      // Check that all mapped fields exist in schema
      const projectDbFields = projectSchema.fieldInfo.map(f => f.name);
      const taskDbFields = taskSchema.fieldInfo.map(f => f.name);

      // Verify key mappings exist in actual schema
      expect(projectDbFields).toContain('full_description');
      expect(projectDbFields).toContain('event_log');
      expect(projectDbFields).toContain('due_date');
      expect(projectDbFields).toContain('created_by');
      expect(projectDbFields).toContain('assigned_to');

      expect(taskDbFields).toContain('project_id');
      expect(taskDbFields).toContain('parent_task_id');
      expect(taskDbFields).toContain('due_date');
      expect(taskDbFields).toContain('created_by');
      expect(taskDbFields).toContain('assigned_to');
    });

    it('should validate that API contracts match actual schema capabilities', () => {
      const testSuite = ApiValidator.generateContractTests();
      
      // Verify that generated tests cover all major entities
      const testEndpoints = testSuite.tests.map(t => t.endpoint);
      
      expect(testEndpoints.some(e => e.includes('projects'))).toBe(true);
      expect(testEndpoints.some(e => e.includes('tasks'))).toBe(true);
      expect(testEndpoints.some(e => e.includes('comments'))).toBe(true);
      expect(testEndpoints.some(e => e.includes('files'))).toBe(true);
      expect(testEndpoints.some(e => e.includes('team'))).toBe(true);

      // Verify test structure
      for (const test of testSuite.tests) {
        expect(test).toHaveProperty('name');
        expect(test).toHaveProperty('description');
        expect(test).toHaveProperty('endpoint');
        expect(test).toHaveProperty('method');
        expect(test).toHaveProperty('expectedFields');
        expect(test).toHaveProperty('testCode');
      }
    });

    it('should ensure all field mappings are bidirectional', () => {
      const testMappings = [
        ['fullDescription', 'full_description'],
        ['eventLog', 'event_log'],
        ['dueDate', 'due_date'],
        ['createdBy', 'created_by'],
        ['assignedTo', 'assigned_to'],
        ['projectId', 'project_id'],
        ['parentTaskId', 'parent_task_id'],
        ['parentCommentId', 'parent_comment_id'],
        ['fileName', 'file_name'],
        ['fileSize', 'file_size'],
        ['mimeType', 'mime_type'],
        ['uploadedBy', 'uploaded_by'],
        ['uploadedAt', 'uploaded_at'],
        ['userId', 'user_id'],
        ['joinedAt', 'joined_at']
      ];

      for (const [apiField, dbField] of testMappings) {
        // Test API to DB mapping
        const mappedDbField = FieldMapper.getDbFieldName(apiField);
        expect(mappedDbField).toBe(dbField);

        // Test DB to API mapping
        const mappedApiField = FieldMapper.getApiFieldName(dbField);
        expect(mappedApiField).toBe(apiField);
      }
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle large datasets efficiently', () => {
      const startTime = Date.now();
      
      // Test with larger dataset
      const largeApiData = Array.from({ length: 100 }, (_, i) => ({
        title: `Project ${i}`,
        fullDescription: `Full description for project ${i}`,
        eventLog: `Event log for project ${i}`,
        dueDate: '2024-12-31T23:59:59Z',
        assignedTo: i % 10 + 1
      }));

      for (const data of largeApiData) {
        const dbData = FieldMapper.apiToDb(data);
        const apiData = FieldMapper.dbToApi(dbData);
        
        expect(apiData).toHaveProperty('fullDescription');
        expect(dbData).toHaveProperty('full_description');
      }

      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(1000); // 1 second
    });

    it('should cache validation results for repeated operations', () => {
      const enforcer = new TypeSafetyEnforcer();
      
      const testData = {
        title: 'Cache Test',
        fullDescription: 'Description',
        eventLog: 'Log'
      };

      // First validation
      const start1 = Date.now();
      const result1 = enforcer.enforceApiToDb(testData, 'project');
      const duration1 = Date.now() - start1;

      // Second validation (should be faster due to caching)
      const start2 = Date.now();
      const result2 = enforcer.enforceApiToDb(testData, 'project');
      const duration2 = Date.now() - start2;

      expect(result1.valid).toBe(result2.valid);
      expect(result1.correctedData).toEqual(result2.correctedData);
      
      // Note: Actual caching implementation would need to be added to see performance improvement
    });
  });
});
