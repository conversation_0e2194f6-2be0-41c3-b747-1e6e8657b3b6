"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { Database, Users, Settings, RefreshCw, AlertTriangle, UserPlus, Shield, ArrowLeft } from 'lucide-react';
import { useRouter } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import { UserManagement } from '@/components/admin/user-management';
import { AccessRequestManagement } from '@/components/admin/access-request-management';

interface DatabaseStatus {
  healthy: boolean;
  stats: {
    users: number;
    projects: number;
    activeSessions: number;
  } | null;
  initialized: boolean;
  error?: string;
}

export default function AdminPage() {
  const { data: session } = useSession();
  const currentUser = session?.user;
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations('admin');
  const tSystem = useTranslations('admin.system');
  const [dbStatus, setDbStatus] = useState<DatabaseStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Check if user has admin access
  useEffect(() => {
    if (currentUser && currentUser.role !== 'admin' && currentUser.role !== 'moderator') {
      router.push('/dashboard');
      toast({
        title: "Access Denied",
        description: "You don't have permission to access the admin panel",
        variant: "destructive"
      });
    }
  }, [currentUser, router, toast]);

  const fetchDatabaseStatus = async () => {
    try {
      const response = await fetch('/api/admin/init-db');
      const data = await response.json();
      setDbStatus(data);
    } catch (error) {
      console.error('Failed to fetch database status:', error);
      setDbStatus({
        healthy: false,
        stats: null,
        initialized: false,
        error: 'Failed to connect to database'
      });
    }
  };



  const resetDatabase = async () => {
    if (!confirm('⚠️ DANGER: This will completely delete the database and recreate it from scratch!\n\nThis will permanently remove:\n• All user accounts\n• All Task Master projects\n• All Paper Cost Estimator data\n• All sessions and settings\n\nA backup will be created automatically before reset. Continue?')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/init-db', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'reset' })
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: "Database Reset Complete",
          description: result.backupName ? `Database reset. Backup saved as: ${result.backupName}` : "Database reset successfully"
        });
        await fetchDatabaseStatus();
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to reset database",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const seedSampleData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/seed-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: "Success",
          description: `Created ${result.projects} sample projects for Task Master`
        });
        await fetchDatabaseStatus();
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to create sample data",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const backupGlobalDatabase = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/backup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: "Global System Backup Created",
          description: `Comprehensive backup saved as: ${result.backupName}`
        });
        await fetchDatabaseStatus();
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to create global backup",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const backupTaskMaster = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/backup/task-master', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: "Task Master Backup Created",
          description: `Selective backup saved: ${result.backupName} (${result.recordCount} records)`
        });
        await fetchDatabaseStatus();
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to create Task Master backup",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToSettings = () => {
    router.push('/settings');
  };

  useEffect(() => {
    fetchDatabaseStatus();
  }, []);

  if (!currentUser || (currentUser.role !== 'admin' && currentUser.role !== 'moderator')) {
    return null;
  }

  return (
    <div className="container max-w-6xl mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBackToSettings}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Settings className="h-6 w-6 text-[#5E6AD2]" />
          <h1 className="text-2xl font-bold">{t('title')}</h1>
        </div>
        <Button
          onClick={fetchDatabaseStatus}
          variant="outline"
          size="sm"
          disabled={isLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            {t('users.title')}
          </TabsTrigger>
          <TabsTrigger value="access-requests" className="flex items-center gap-2">
            <UserPlus className="h-4 w-4" />
            Access Requests
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            System
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="space-y-6">
            {/* Database Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  {t('database.title')}
                </CardTitle>
                <CardDescription>
                  Monitor database health and create comprehensive system backups including all modules (Task Master, Paper Calculator, User Data, Sessions)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {dbStatus ? (
                  <>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{tSystem('status')}:</span>
                        <Badge variant={dbStatus.healthy ? "success" : "destructive"}>
                          {dbStatus.healthy ? tSystem('healthy') : "Unhealthy"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">Initialized:</span>
                        <Badge variant={dbStatus.initialized ? "success" : "secondary"}>
                          {dbStatus.initialized ? "Yes" : "No"}
                        </Badge>
                      </div>
                    </div>

                    {dbStatus.stats && (
                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <div className="text-2xl font-bold text-[#5E6AD2]">{dbStatus.stats.users}</div>
                          <div className="text-sm text-muted-foreground">{t('users.totalUsers')}</div>
                        </div>
                        <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <div className="text-2xl font-bold text-[#5E6AD2]">{dbStatus.stats.projects}</div>
                          <div className="text-sm text-muted-foreground">Projects</div>
                        </div>
                        <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <div className="text-2xl font-bold text-[#5E6AD2]">{dbStatus.stats.activeSessions}</div>
                          <div className="text-sm text-muted-foreground">Active Sessions</div>
                        </div>
                      </div>
                    )}

                    {dbStatus.error && (
                      <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <span className="text-sm text-red-600 dark:text-red-400">{dbStatus.error}</span>
                      </div>
                    )}

                    <div className="space-y-3">
                      <div className="text-sm text-muted-foreground">
                        Global System Management Actions
                      </div>
                      <div className="flex gap-2 flex-wrap">
                        <Button
                          onClick={seedSampleData}
                          disabled={isLoading}
                          variant="outline"
                        >
                          {isLoading ? 'Creating...' : 'Add Sample Projects'}
                        </Button>
                        <Button
                          onClick={backupGlobalDatabase}
                          disabled={isLoading}
                          variant="outline"
                        >
                          {isLoading ? 'Creating Global Backup...' : 'Global System Backup'}
                        </Button>
                        {process.env.NODE_ENV !== 'production' && (
                          <Button
                            onClick={resetDatabase}
                            disabled={isLoading}
                            variant="destructive"
                          >
                            {isLoading ? 'Resetting...' : 'Reset Database'}
                          </Button>
                        )}
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#5E6AD2] mx-auto"></div>
                    <p className="text-sm text-muted-foreground mt-2">Loading database status...</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    {t('users.title')}
                  </CardTitle>
                  <CardDescription>
                    Manage user accounts and access requests
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Button className="w-full" variant="outline">
                      View All Users
                    </Button>
                    <Button className="w-full" variant="outline">
                      Pending Access Requests
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Task Master Data
                  </CardTitle>
                  <CardDescription>
                    Manage Task Master projects and create selective backups of Task Master data only (projects, comments, activities)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Button
                      className="w-full"
                      variant="outline"
                      onClick={seedSampleData}
                      disabled={isLoading}
                    >
                      Add Sample Projects
                    </Button>
                    <Button
                      className="w-full"
                      variant="outline"
                      onClick={backupTaskMaster}
                      disabled={isLoading}
                    >
                      {isLoading ? 'Creating Task Master Backup...' : 'Task Master Backup'}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>System Information</CardTitle>
                  <CardDescription>
                    Application and system details
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Environment:</span>
                      <Badge variant="secondary">{process.env.NODE_ENV || 'development'}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Version:</span>
                      <span>1.0.0</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Database:</span>
                      <span>SQLite</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="users">
          <UserManagement />
        </TabsContent>

        <TabsContent value="access-requests">
          <AccessRequestManagement />
        </TabsContent>

        <TabsContent value="system">
          <Card>
            <CardHeader>
              <CardTitle>System Management</CardTitle>
              <CardDescription>
                Advanced system administration and maintenance tools
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Session Management</h4>
                    <p className="text-sm text-muted-foreground">
                      Monitor and manage user sessions across the platform
                    </p>
                    <Button variant="outline" size="sm">
                      View Active Sessions
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">System Logs</h4>
                    <p className="text-sm text-muted-foreground">
                      Access system logs and audit trails
                    </p>
                    <Button variant="outline" size="sm">
                      View Logs
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Backup & Recovery</h4>
                    <p className="text-sm text-muted-foreground">
                      Manage database backups and recovery procedures
                    </p>
                    <Button variant="outline" size="sm">
                      Manage Backups
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Performance Monitoring</h4>
                    <p className="text-sm text-muted-foreground">
                      Monitor system performance and resource usage
                    </p>
                    <Button variant="outline" size="sm">
                      View Metrics
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
