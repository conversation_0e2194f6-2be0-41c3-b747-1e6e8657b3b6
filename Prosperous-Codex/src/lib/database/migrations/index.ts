import { Database } from 'better-sqlite3';
import { addEventLogColumn } from './add-event-log-column';
import { addPerformanceIndexes } from './add-performance-indexes';

/**
 * Apply all pending migrations
 */
export function applyMigrations(db: Database) {
  console.log('🔄 Applying database migrations...');

  try {
    // Apply migrations in order
    addEventLogColumn(db);
    addPerformanceIndexes(db);

    console.log('✅ All migrations applied successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}
