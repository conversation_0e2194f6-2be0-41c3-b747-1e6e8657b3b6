/**
 * Custom error classes for Task Master system
 * Based on TypeScript best practices for error handling
 */

/**
 * Base error class for all Task Master errors
 * Provides consistent error structure with type discrimination
 */
export abstract class TaskMasterError extends Error {
  abstract readonly type: string;
  readonly timestamp: string;
  readonly context?: Record<string, any>;

  constructor(message: string, options?: { cause?: Error; context?: Record<string, any> }) {
    super(message, { cause: options?.cause });
    this.name = this.constructor.name;
    this.timestamp = new Date().toISOString();
    this.context = options?.context;

    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, new.target.prototype);
  }

  /**
   * Serialize error for logging/API responses
   */
  toJSON() {
    return {
      type: this.type,
      name: this.name,
      message: this.message,
      timestamp: this.timestamp,
      context: this.context,
      stack: this.stack,
      cause: this.cause instanceof Error ? {
        name: this.cause.name,
        message: this.cause.message,
        stack: this.cause.stack
      } : this.cause
    };
  }
}

/**
 * Authorization/Permission related errors
 */
export class AuthorizationError extends TaskMasterError {
  readonly type = 'authorization-error' as const;

  constructor(message: string = 'Access denied', options?: { cause?: Error; context?: Record<string, any> }) {
    super(message, options);
  }
}

/**
 * Input validation errors
 */
export class ValidationError extends TaskMasterError {
  readonly type = 'validation-error' as const;
  readonly field?: string;

  constructor(
    message: string, 
    options?: { 
      cause?: Error; 
      context?: Record<string, any>; 
      field?: string;
    }
  ) {
    super(message, options);
    this.field = options?.field;
  }
}

/**
 * Database operation errors
 */
export class DatabaseError extends TaskMasterError {
  readonly type = 'database-error' as const;
  readonly operation?: string;

  constructor(
    message: string, 
    options?: { 
      cause?: Error; 
      context?: Record<string, any>; 
      operation?: string;
    }
  ) {
    super(message, options);
    this.operation = options?.operation;
  }
}

/**
 * Resource not found errors
 */
export class NotFoundError extends TaskMasterError {
  readonly type = 'not-found-error' as const;
  readonly resource?: string;
  readonly resourceId?: string | number;

  constructor(
    message: string, 
    options?: { 
      cause?: Error; 
      context?: Record<string, any>; 
      resource?: string;
      resourceId?: string | number;
    }
  ) {
    super(message, options);
    this.resource = options?.resource;
    this.resourceId = options?.resourceId;
  }
}

/**
 * Business logic constraint errors
 */
export class BusinessLogicError extends TaskMasterError {
  readonly type = 'business-logic-error' as const;
  readonly constraint?: string;

  constructor(
    message: string, 
    options?: { 
      cause?: Error; 
      context?: Record<string, any>; 
      constraint?: string;
    }
  ) {
    super(message, options);
    this.constraint = options?.constraint;
  }
}

/**
 * File operation errors
 */
export class FileOperationError extends TaskMasterError {
  readonly type = 'file-operation-error' as const;
  readonly operation?: 'upload' | 'delete' | 'read' | 'write';
  readonly fileName?: string;

  constructor(
    message: string, 
    options?: { 
      cause?: Error; 
      context?: Record<string, any>; 
      operation?: 'upload' | 'delete' | 'read' | 'write';
      fileName?: string;
    }
  ) {
    super(message, options);
    this.operation = options?.operation;
    this.fileName = options?.fileName;
  }
}

/**
 * Type union for all Task Master errors
 * Used for exhaustive error handling
 */
export type TaskMasterErrorType = 
  | AuthorizationError
  | ValidationError
  | DatabaseError
  | NotFoundError
  | BusinessLogicError
  | FileOperationError;

/**
 * Type guard to check if an error is a Task Master error
 */
export function isTaskMasterError(error: unknown): error is TaskMasterErrorType {
  return error instanceof TaskMasterError;
}

/**
 * Utility function for exhaustive error type checking
 * Ensures all error types are handled at compile time
 */
export function assertUnreachableError(error: never): never {
  throw new Error(`Unhandled error type: ${JSON.stringify(error)}`);
}

/**
 * Error factory functions for common scenarios
 */
export const ErrorFactory = {
  /**
   * Create authorization error for insufficient permissions
   */
  unauthorized(resource?: string, action?: string): AuthorizationError {
    const message = resource && action
      ? `Insufficient permissions to ${action} ${resource}`
      : 'Insufficient permissions';

    return new AuthorizationError(message, {
      context: { resource, action }
    });
  },

  /**
   * Create specific permission error messages for better user experience
   */
  permissionDenied(scenario: 'project_access' | 'task_edit' | 'team_management' | 'file_upload' | 'role_required', requiredRole?: string): AuthorizationError {
    let message: string;

    switch (scenario) {
      case 'project_access':
        message = "You don't have access to this project. Contact the project owner to request team access.";
        break;
      case 'task_edit':
        message = "You can only edit tasks that you created or are assigned to. Contact the project owner or task creator for changes.";
        break;
      case 'team_management':
        message = "Only project owners and team admins can manage team members. Contact the project owner to request role changes.";
        break;
      case 'file_upload':
        message = "You need at least 'Member' role to upload files. Contact the project owner to upgrade your permissions.";
        break;
      case 'role_required':
        message = requiredRole
          ? `This action requires ${requiredRole} role. Contact your system administrator for role upgrade.`
          : "This action requires elevated permissions. Contact your system administrator for role upgrade.";
        break;
      default:
        message = 'Insufficient permissions';
    }

    return new AuthorizationError(message, {
      context: { scenario, requiredRole }
    });
  },

  /**
   * Create not found error for missing resources
   */
  notFound(resource: string, id: string | number): NotFoundError {
    return new NotFoundError(`${resource} not found`, {
      context: { resource, id },
      resource,
      resourceId: id
    });
  },

  /**
   * Create validation error for invalid input
   */
  invalidInput(field: string, value: any, reason?: string): ValidationError {
    const message = reason 
      ? `Invalid ${field}: ${reason}`
      : `Invalid ${field}`;
    
    return new ValidationError(message, {
      context: { field, value, reason },
      field
    });
  },

  /**
   * Create database error for operation failures
   */
  databaseOperation(operation: string, details?: string): DatabaseError {
    const message = details 
      ? `Database ${operation} failed: ${details}`
      : `Database ${operation} failed`;
    
    return new DatabaseError(message, {
      context: { operation, details },
      operation
    });
  },

  /**
   * Create business logic error for constraint violations
   */
  constraintViolation(constraint: string, details?: string): BusinessLogicError {
    const message = details 
      ? `Business constraint violated (${constraint}): ${details}`
      : `Business constraint violated: ${constraint}`;
    
    return new BusinessLogicError(message, {
      context: { constraint, details },
      constraint
    });
  }
};
