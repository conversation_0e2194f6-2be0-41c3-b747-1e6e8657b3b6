// Paper Options Database
// This file contains the structured paper options data for the paper cost estimator

export interface PaperOption {
  id: string;
  name: string;
  source: string; // Source (Pre-Cut, Roll)
  sheetHeight: number | null; // Sheet H (mm) - null for rolls
  sheetWidth: number; // Sheet W (mm)
  grainDirection: string; // Grain direction (Height, Width)
  caliper: number; // Caliper (µm)
  costPerReam: number | null; // Cost/Ream ($) - null for rolls
  gsm: number; // GSM (g/m²)
  costPerTon?: number | null; // Cost/Ton ($) - used for rolls
  category: 'Inner Text' | 'Cover' | 'Endpapers';
}

export interface PaperCategory {
  category: string;
  papers: PaperOption[];
}

// Inner Text Papers
const innerTextPapers: PaperOption[] = [
  {
    id: 'inner-1',
    name: 'Halved 31 x 43" 对开',
    source: 'Pre-Cut',
    sheetHeight: 546.1,
    sheetWidth: 787.4,
    grainDirection: 'Height',
    caliper: 100,
    costPerReam: 50.00,
    gsm: 80,
    costPerTon: null,
    category: 'Inner Text'
  },
  {
    id: 'inner-2',
    name: 'Halved 35 x 47" 对开',
    source: 'Pre-Cut',
    sheetHeight: 596.9,
    sheetWidth: 889.0,
    grainDirection: 'Height',
    caliper: 100,
    costPerReam: 22.50,
    gsm: 80,
    costPerTon: null,
    category: 'Inner Text'
  },
  {
    id: 'inner-3',
    name: 'Quarter 35 x 47" 四开',
    source: 'Pre-Cut',
    sheetHeight: 444.5,
    sheetWidth: 596.9,
    grainDirection: 'Width',
    caliper: 100,
    costPerReam: 11.25,
    gsm: 80,
    costPerTon: null,
    category: 'Inner Text'
  },
  {
    id: 'inner-4',
    name: 'Special 25 x 38"',
    source: 'Pre-Cut',
    sheetHeight: 635.0,
    sheetWidth: 965.2,
    grainDirection: 'Width',
    caliper: 100,
    costPerReam: 40.00,
    gsm: 80,
    costPerTon: null,
    category: 'Inner Text'
  },
  {
    id: 'inner-5',
    name: 'Custom 25" Roll',
    source: 'Roll',
    sheetHeight: null,
    sheetWidth: 635.0,
    grainDirection: 'Height',
    caliper: 100,
    costPerReam: null,
    gsm: 80,
    costPerTon: 1200.00,
    category: 'Inner Text'
  },
  {
    id: 'inner-6',
    name: 'Custom 31" Roll',
    source: 'Roll',
    sheetHeight: null,
    sheetWidth: 787.4,
    grainDirection: 'Height',
    caliper: 100,
    costPerReam: null,
    gsm: 80,
    costPerTon: 1200.00,
    category: 'Inner Text'
  },
  {
    id: 'inner-7',
    name: 'Custom 35" Roll',
    source: 'Roll',
    sheetHeight: null,
    sheetWidth: 889.0,
    grainDirection: 'Height',
    caliper: 100,
    costPerReam: null,
    gsm: 80,
    costPerTon: 1200.00,
    category: 'Inner Text'
  },
  {
    id: 'inner-8',
    name: 'Custom 38" Roll',
    source: 'Roll',
    sheetHeight: null,
    sheetWidth: 965.2,
    grainDirection: 'Height',
    caliper: 100,
    costPerReam: null,
    gsm: 80,
    costPerTon: 1200.00,
    category: 'Inner Text'
  }
];

// Cover Papers
const coverPapers: PaperOption[] = [
  {
    id: 'cover-1',
    name: 'Art Card 250gsm',
    source: 'Pre-Cut',
    sheetHeight: 635,
    sheetWidth: 965,
    grainDirection: 'Height',
    caliper: 280,
    costPerReam: 120.00,
    gsm: 250,
    costPerTon: null,
    category: 'Cover'
  },
  {
    id: 'cover-2',
    name: 'Coated Board 300gsm',
    source: 'Pre-Cut',
    sheetHeight: 700,
    sheetWidth: 1000,
    grainDirection: 'Width',
    caliper: 350,
    costPerReam: 150.00,
    gsm: 300,
    costPerTon: null,
    category: 'Cover'
  }
];

// Endpaper Papers
const endpaperPapers: PaperOption[] = [
  {
    id: 'endpaper-1',
    name: 'Woodfree 120gsm',
    source: 'Pre-Cut',
    sheetHeight: 787.4,
    sheetWidth: 1092.2,
    grainDirection: 'Height',
    caliper: 140,
    costPerReam: 65.00,
    gsm: 120,
    costPerTon: null,
    category: 'Endpapers'
  },
  {
    id: 'endpaper-2',
    name: 'Specialty Endpaper 140gsm',
    source: 'Pre-Cut',
    sheetHeight: 650,
    sheetWidth: 900,
    grainDirection: 'Width',
    caliper: 160,
    costPerReam: 80.00,
    gsm: 140,
    costPerTon: null,
    category: 'Endpapers'
  }
];

// Combined paper database
export const paperDatabase: PaperCategory[] = [
  {
    category: 'Inner Text',
    papers: innerTextPapers
  },
  {
    category: 'Cover',
    papers: coverPapers
  },
  {
    category: 'Endpapers',
    papers: endpaperPapers
  }
];

// Utility functions for paper data management
export const paperDataUtils = {
  // Get all papers from all categories
  getAllPapers: (): PaperOption[] => {
    return paperDatabase.flatMap(category => category.papers);
  },

  // Get papers by category
  getPapersByCategory: (category: string): PaperOption[] => {
    const categoryData = paperDatabase.find(cat => cat.category === category);
    return categoryData ? categoryData.papers : [];
  },

  // Get paper by ID
  getPaperById: (id: string): PaperOption | undefined => {
    return paperDataUtils.getAllPapers().find(paper => paper.id === id);
  },

  // Get all available categories
  getCategories: (): string[] => {
    return paperDatabase.map(category => category.category);
  },

  // Get all paper types (sources)
  getPaperTypes: (): string[] => {
    const types = new Set(paperDataUtils.getAllPapers().map(paper => paper.source));
    return Array.from(types);
  },

  // Get all grain directions
  getGrainDirections: (): string[] => {
    const grains = new Set(paperDataUtils.getAllPapers().map(paper => paper.grainDirection));
    return Array.from(grains);
  },

  // Search papers by name
  searchPapersByName: (searchTerm: string): PaperOption[] => {
    const term = searchTerm.toLowerCase();
    return paperDataUtils.getAllPapers().filter(paper => 
      paper.name.toLowerCase().includes(term)
    );
  },

  // Filter papers by criteria
  filterPapers: (criteria: {
    category?: string;
    source?: string;
    minGsm?: number;
    maxGsm?: number;
    grainDirection?: string;
  }): PaperOption[] => {
    let papers = paperDataUtils.getAllPapers();

    if (criteria.category) {
      papers = papers.filter(paper => paper.category === criteria.category);
    }

    if (criteria.source) {
      papers = papers.filter(paper => paper.source === criteria.source);
    }

    if (criteria.minGsm !== undefined) {
      papers = papers.filter(paper => paper.gsm >= criteria.minGsm!);
    }

    if (criteria.maxGsm !== undefined) {
      papers = papers.filter(paper => paper.gsm <= criteria.maxGsm!);
    }

    if (criteria.grainDirection) {
      papers = papers.filter(paper => paper.grainDirection === criteria.grainDirection);
    }

    return papers;
  },

  // Add new paper to a category
  addPaper: (categoryName: string, paper: Omit<PaperOption, 'id'>): PaperOption => {
    const newPaper: PaperOption = {
      ...paper,
      id: `${categoryName.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`,
      category: categoryName as 'Inner Text' | 'Cover' | 'Endpapers'
    };

    const category = paperDatabase.find(cat => cat.category === categoryName);
    if (category) {
      category.papers.push(newPaper);
    }

    return newPaper;
  },

  // Update paper
  updatePaper: (id: string, updates: Partial<PaperOption>): PaperOption | null => {
    for (const category of paperDatabase) {
      const paperIndex = category.papers.findIndex(paper => paper.id === id);
      if (paperIndex !== -1) {
        category.papers[paperIndex] = { ...category.papers[paperIndex], ...updates };
        return category.papers[paperIndex];
      }
    }
    return null;
  },

  // Delete paper
  deletePaper: (id: string): boolean => {
    for (const category of paperDatabase) {
      const paperIndex = category.papers.findIndex(paper => paper.id === id);
      if (paperIndex !== -1) {
        category.papers.splice(paperIndex, 1);
        return true;
      }
    }
    return false;
  }
};

// Export individual categories for convenience
export { innerTextPapers, coverPapers, endpaperPapers };