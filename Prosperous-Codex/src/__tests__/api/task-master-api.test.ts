/**
 * Task Master API Integration Tests
 * 
 * Integration tests for all Task Master API endpoints
 * verifying authentication, validation, and response formats
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { createMocks } from 'node-mocks-http';
import { NextRequest } from 'next/server';
import Database from 'better-sqlite3';

// Import API handlers
import { GET as getProjects, POST as createProject } from '@/app/api/task-master/projects/route';
import { GET as getProject, PUT as updateProject, DELETE as deleteProject } from '@/app/api/task-master/projects/[id]/route';
import { GET as getTasks, POST as createTask } from '@/app/api/task-master/projects/[id]/tasks/route';
// Task handlers are imported but not used in current tests

describe('Task Master API Integration Tests', () => {
  let db: Database.Database;
  let testUserId: number;
  let testProjectId: number;
  let testTaskId: number;

  beforeAll(async () => {
    // Setup test database
    db = new Database(':memory:');
    
    // Create tables
    db.exec(`
      CREATE TABLE projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        full_description TEXT,
        event_log TEXT,
        status TEXT NOT NULL DEFAULT 'todo',
        priority TEXT NOT NULL DEFAULT 'medium',
        progress INTEGER NOT NULL DEFAULT 0,
        due_date TEXT,
        completed_date TEXT,
        created_by INTEGER NOT NULL,
        assigned_to INTEGER,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE tasks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        status TEXT NOT NULL DEFAULT 'todo',
        priority TEXT NOT NULL DEFAULT 'medium',
        progress INTEGER NOT NULL DEFAULT 0,
        due_date TEXT,
        completed_date TEXT,
        project_id INTEGER NOT NULL,
        parent_task_id INTEGER,
        created_by INTEGER NOT NULL,
        assigned_to INTEGER,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id),
        FOREIGN KEY (parent_task_id) REFERENCES tasks(id)
      );

      CREATE TABLE project_team_members (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        role TEXT NOT NULL DEFAULT 'member',
        joined_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id),
        UNIQUE(project_id, user_id)
      );

      CREATE TABLE activity_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        activity_type TEXT NOT NULL,
        entity_type TEXT NOT NULL,
        entity_id INTEGER NOT NULL,
        old_value TEXT,
        new_value TEXT,
        project_id INTEGER NOT NULL,
        performed_by INTEGER NOT NULL,
        performed_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id)
      );
    `);

    testUserId = 1;
  });

  afterAll(() => {
    if (db) {
      db.close();
    }
  });

  beforeEach(() => {
    // Clear test data
    db.exec(`
      DELETE FROM activity_log;
      DELETE FROM project_team_members;
      DELETE FROM tasks;
      DELETE FROM projects;
    `);
  });

  // Helper function to create authenticated request
  function createAuthenticatedRequest(method: string, url: string, body?: any) {
    const { req, res } = createMocks({
      method,
      url,
      headers: {
        'content-type': 'application/json',
        'authorization': `Bearer test-token-user-${testUserId}`,
      },
      body: body ? JSON.stringify(body) : undefined,
    });

    // Mock user session
    (req as any).user = { id: testUserId };
    
    return { req, res };
  }

  // Helper function to parse response
  async function parseResponse(response: Response) {
    const text = await response.text();
    try {
      return JSON.parse(text);
    } catch {
      return text;
    }
  }

  describe('Projects API', () => {
    describe('POST /api/task-master/projects', () => {
      it('should create a project successfully', async () => {
        const projectData = {
          title: 'Test Project',
          description: 'Test description',
          fullDescription: 'Full test description',
          status: 'todo',
          priority: 'medium',
          dueDate: '2024-12-31T23:59:59Z',
        };

        const request = new NextRequest('http://localhost/api/task-master/projects', {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify(projectData),
        });

        // Mock authentication
        (request as any).user = { id: testUserId };

        const response = await createProject(request);
        const data = await parseResponse(response);

        expect(response.status).toBe(201);
        expect(data).toHaveProperty('id');
        expect(data.title).toBe(projectData.title);
        expect(data.fullDescription).toBe(projectData.fullDescription);
        expect(data.createdBy).toBe(testUserId);

        testProjectId = data.id;
      });

      it('should return 400 for invalid data', async () => {
        const invalidData = {
          title: '', // Empty title
          status: 'invalid-status',
          priority: 'invalid-priority',
        };

        const request = new NextRequest('http://localhost/api/task-master/projects', {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify(invalidData),
        });

        (request as any).user = { id: testUserId };

        const response = await createProject(request);
        const data = await parseResponse(response);

        expect(response.status).toBe(400);
        expect(data).toHaveProperty('error');
      });

      it('should return 401 for unauthenticated request', async () => {
        const request = new NextRequest('http://localhost/api/task-master/projects', {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({ title: 'Test' }),
        });

        const response = await createProject(request);

        expect(response.status).toBe(401);
      });
    });

    describe('GET /api/task-master/projects', () => {
      beforeEach(async () => {
        // Create test project
        const projectData = {
          title: 'Test Project',
          status: 'todo',
          priority: 'medium',
        };

        const request = new NextRequest('http://localhost/api/task-master/projects', {
          method: 'POST',
          headers: { 'content-type': 'application/json' },
          body: JSON.stringify(projectData),
        });

        (request as any).user = { id: testUserId };
        const response = await createProject(request);
        const data = await parseResponse(response);
        testProjectId = data.id;
      });

      it('should return user projects', async () => {
        const request = new NextRequest('http://localhost/api/task-master/projects');
        (request as any).user = { id: testUserId };

        const response = await getProjects(request);
        const data = await parseResponse(response);

        expect(response.status).toBe(200);
        expect(Array.isArray(data)).toBe(true);
        expect(data.length).toBeGreaterThan(0);
        expect(data[0]).toHaveProperty('id');
        expect(data[0]).toHaveProperty('title');
      });

      it('should support pagination', async () => {
        const request = new NextRequest('http://localhost/api/task-master/projects?page=1&limit=10');
        (request as any).user = { id: testUserId };

        const response = await getProjects(request);
        const data = await parseResponse(response);

        expect(response.status).toBe(200);
        expect(Array.isArray(data)).toBe(true);
      });

      it('should support field filtering', async () => {
        const request = new NextRequest('http://localhost/api/task-master/projects?fields=id,title');
        (request as any).user = { id: testUserId };

        const response = await getProjects(request);
        const data = await parseResponse(response);

        expect(response.status).toBe(200);
        if (data.length > 0) {
          expect(data[0]).toHaveProperty('id');
          expect(data[0]).toHaveProperty('title');
          expect(data[0]).not.toHaveProperty('description');
        }
      });
    });

    describe('GET /api/task-master/projects/[id]', () => {
      beforeEach(async () => {
        // Create test project
        const stmt = db.prepare(`
          INSERT INTO projects (title, created_by) VALUES (?, ?)
        `);
        const result = stmt.run('Test Project', testUserId);
        testProjectId = result.lastInsertRowid as number;

        // Add team member
        db.prepare(`
          INSERT INTO project_team_members (project_id, user_id, role) VALUES (?, ?, ?)
        `).run(testProjectId, testUserId, 'owner');
      });

      it('should return project details', async () => {
        const request = new NextRequest(`http://localhost/api/task-master/projects/${testProjectId}`);
        (request as any).user = { id: testUserId };

        const response = await getProject(request, { params: { id: testProjectId.toString() } });
        const data = await parseResponse(response);

        expect(response.status).toBe(200);
        expect(data).toHaveProperty('id', testProjectId);
        expect(data).toHaveProperty('title', 'Test Project');
        expect(data).toHaveProperty('createdBy', testUserId);
      });

      it('should return 403 for unauthorized access', async () => {
        const request = new NextRequest(`http://localhost/api/task-master/projects/${testProjectId}`);
        (request as any).user = { id: 999 }; // Different user

        const response = await getProject(request, { params: { id: testProjectId.toString() } });

        expect(response.status).toBe(403);
      });

      it('should return 404 for non-existent project', async () => {
        const request = new NextRequest('http://localhost/api/task-master/projects/999');
        (request as any).user = { id: testUserId };

        const response = await getProject(request, { params: { id: '999' } });

        expect(response.status).toBe(404);
      });
    });

    describe('PUT /api/task-master/projects/[id]', () => {
      beforeEach(async () => {
        // Create test project
        const stmt = db.prepare(`
          INSERT INTO projects (title, created_by) VALUES (?, ?)
        `);
        const result = stmt.run('Test Project', testUserId);
        testProjectId = result.lastInsertRowid as number;

        db.prepare(`
          INSERT INTO project_team_members (project_id, user_id, role) VALUES (?, ?, ?)
        `).run(testProjectId, testUserId, 'owner');
      });

      it('should update project successfully', async () => {
        const updateData = {
          title: 'Updated Project',
          description: 'Updated description',
          status: 'in_progress',
        };

        const request = new NextRequest(`http://localhost/api/task-master/projects/${testProjectId}`, {
          method: 'PUT',
          headers: { 'content-type': 'application/json' },
          body: JSON.stringify(updateData),
        });

        (request as any).user = { id: testUserId };

        const response = await updateProject(request, { params: { id: testProjectId.toString() } });
        const data = await parseResponse(response);

        expect(response.status).toBe(200);
        expect(data.title).toBe('Updated Project');
        expect(data.description).toBe('Updated description');
        expect(data.status).toBe('in_progress');
      });

      it('should return 403 for unauthorized update', async () => {
        const request = new NextRequest(`http://localhost/api/task-master/projects/${testProjectId}`, {
          method: 'PUT',
          headers: { 'content-type': 'application/json' },
          body: JSON.stringify({ title: 'Hacked' }),
        });

        (request as any).user = { id: 999 };

        const response = await updateProject(request, { params: { id: testProjectId.toString() } });

        expect(response.status).toBe(403);
      });
    });

    describe('DELETE /api/task-master/projects/[id]', () => {
      beforeEach(async () => {
        // Create test project
        const stmt = db.prepare(`
          INSERT INTO projects (title, created_by) VALUES (?, ?)
        `);
        const result = stmt.run('Test Project', testUserId);
        testProjectId = result.lastInsertRowid as number;

        db.prepare(`
          INSERT INTO project_team_members (project_id, user_id, role) VALUES (?, ?, ?)
        `).run(testProjectId, testUserId, 'owner');
      });

      it('should delete project successfully', async () => {
        const request = new NextRequest(`http://localhost/api/task-master/projects/${testProjectId}`, {
          method: 'DELETE',
        });

        (request as any).user = { id: testUserId };

        const response = await deleteProject(request, { params: { id: testProjectId.toString() } });

        expect(response.status).toBe(204);

        // Verify project is deleted
        const project = db.prepare('SELECT * FROM projects WHERE id = ?').get(testProjectId);
        expect(project).toBeUndefined();
      });

      it('should return 403 for unauthorized deletion', async () => {
        const request = new NextRequest(`http://localhost/api/task-master/projects/${testProjectId}`, {
          method: 'DELETE',
        });

        (request as any).user = { id: 999 };

        const response = await deleteProject(request, { params: { id: testProjectId.toString() } });

        expect(response.status).toBe(403);
      });
    });
  });

  describe('Tasks API', () => {
    beforeEach(async () => {
      // Create test project
      const stmt = db.prepare(`
        INSERT INTO projects (title, created_by) VALUES (?, ?)
      `);
      const result = stmt.run('Test Project', testUserId);
      testProjectId = result.lastInsertRowid as number;

      db.prepare(`
        INSERT INTO project_team_members (project_id, user_id, role) VALUES (?, ?, ?)
      `).run(testProjectId, testUserId, 'owner');
    });

    describe('POST /api/task-master/projects/[id]/tasks', () => {
      it('should create task successfully', async () => {
        const taskData = {
          title: 'Test Task',
          description: 'Test description',
          status: 'todo',
          priority: 'high',
          dueDate: '2024-12-31T23:59:59Z',
        };

        const request = new NextRequest(`http://localhost/api/task-master/projects/${testProjectId}/tasks`, {
          method: 'POST',
          headers: { 'content-type': 'application/json' },
          body: JSON.stringify(taskData),
        });

        (request as any).user = { id: testUserId };

        const response = await createTask(request, { params: { id: testProjectId.toString() } });
        const data = await parseResponse(response);

        expect(response.status).toBe(201);
        expect(data).toHaveProperty('id');
        expect(data.title).toBe(taskData.title);
        expect(data.projectId).toBe(testProjectId);
        expect(data.createdBy).toBe(testUserId);

        testTaskId = data.id;
      });

      it('should return 400 for invalid task data', async () => {
        const invalidData = {
          title: '', // Empty title
          status: 'invalid-status',
        };

        const request = new NextRequest(`http://localhost/api/task-master/projects/${testProjectId}/tasks`, {
          method: 'POST',
          headers: { 'content-type': 'application/json' },
          body: JSON.stringify(invalidData),
        });

        (request as any).user = { id: testUserId };

        const response = await createTask(request, { params: { id: testProjectId.toString() } });

        expect(response.status).toBe(400);
      });
    });

    describe('GET /api/task-master/projects/[id]/tasks', () => {
      beforeEach(async () => {
        // Create test task
        const stmt = db.prepare(`
          INSERT INTO tasks (title, project_id, created_by) VALUES (?, ?, ?)
        `);
        const result = stmt.run('Test Task', testProjectId, testUserId);
        testTaskId = result.lastInsertRowid as number;
      });

      it('should return project tasks', async () => {
        const request = new NextRequest(`http://localhost/api/task-master/projects/${testProjectId}/tasks`);
        (request as any).user = { id: testUserId };

        const response = await getTasks(request, { params: { id: testProjectId.toString() } });
        const data = await parseResponse(response);

        expect(response.status).toBe(200);
        expect(Array.isArray(data)).toBe(true);
        expect(data.length).toBeGreaterThan(0);
        expect(data[0]).toHaveProperty('id');
        expect(data[0]).toHaveProperty('title');
        expect(data[0]).toHaveProperty('projectId', testProjectId);
      });
    });
  });

  describe('Field Name Consistency', () => {
    it('should use camelCase in API responses', async () => {
      // Create project with snake_case database fields
      const stmt = db.prepare(`
        INSERT INTO projects (title, full_description, event_log, due_date, created_by) 
        VALUES (?, ?, ?, ?, ?)
      `);
      const result = stmt.run('Test', 'Full desc', 'Event log', '2024-12-31T23:59:59Z', testUserId);
      testProjectId = result.lastInsertRowid as number;

      db.prepare(`
        INSERT INTO project_team_members (project_id, user_id, role) VALUES (?, ?, ?)
      `).run(testProjectId, testUserId, 'owner');

      const request = new NextRequest(`http://localhost/api/task-master/projects/${testProjectId}`);
      (request as any).user = { id: testUserId };

      const response = await getProject(request, { params: { id: testProjectId.toString() } });
      const data = await parseResponse(response);

      // Verify camelCase field names in response
      expect(data).toHaveProperty('fullDescription', 'Full desc');
      expect(data).toHaveProperty('eventLog', 'Event log');
      expect(data).toHaveProperty('dueDate', '2024-12-31T23:59:59Z');
      expect(data).toHaveProperty('createdBy', testUserId);

      // Verify no snake_case fields in response
      expect(data).not.toHaveProperty('full_description');
      expect(data).not.toHaveProperty('event_log');
      expect(data).not.toHaveProperty('due_date');
      expect(data).not.toHaveProperty('created_by');
    });
  });

  describe('Error Handling', () => {
    it('should return consistent error format', async () => {
      const request = new NextRequest('http://localhost/api/task-master/projects/999');
      (request as any).user = { id: testUserId };

      const response = await getProject(request, { params: { id: '999' } });
      const data = await parseResponse(response);

      expect(response.status).toBe(404);
      expect(data).toHaveProperty('error');
      expect(typeof data.error).toBe('string');
    });

    it('should handle validation errors consistently', async () => {
      const invalidData = {
        title: '',
        status: 'invalid',
      };

      const request = new NextRequest('http://localhost/api/task-master/projects', {
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(invalidData),
      });

      (request as any).user = { id: testUserId };

      const response = await createProject(request);
      const data = await parseResponse(response);

      expect(response.status).toBe(400);
      expect(data).toHaveProperty('error');
    });
  });
});
