#!/usr/bin/env node

/**
 * Performance Benchmark Suite for Task Master Optimizations
 * Measures and compares performance before/after optimization
 */

const { performance } = require('perf_hooks');
const fs = require('fs');
const path = require('path');

const BENCHMARK_CONFIG = {
  baseUrl: 'http://localhost:3001',
  testUser: {
    email: '<EMAIL>',
    password: 'moderator123'
  },
  iterations: 10,
  warmupIterations: 3,
  concurrentUsers: 5
};

class PerformanceBenchmark {
  constructor() {
    this.authToken = null;
    this.results = {
      timestamp: new Date().toISOString(),
      config: BENCHMARK_CONFIG,
      benchmarks: []
    };
  }

  async authenticate() {
    console.log('🔐 Authenticating for benchmarks...');
    
    const response = await fetch(`${BENCHMARK_CONFIG.baseUrl}/api/auth/signin`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(BENCHMARK_CONFIG.testUser)
    });

    if (!response.ok) {
      throw new Error(`Authentication failed: ${response.status}`);
    }

    const setCookieHeader = response.headers.get('set-cookie');
    if (setCookieHeader) {
      this.authToken = setCookieHeader;
    }

    console.log('✅ Authentication successful');
  }

  async makeRequest(endpoint, options = {}) {
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    if (this.authToken) {
      headers['Cookie'] = this.authToken;
    }

    const startTime = performance.now();
    
    const response = await fetch(`${BENCHMARK_CONFIG.baseUrl}${endpoint}`, {
      ...options,
      headers
    });

    const endTime = performance.now();
    const responseTime = endTime - startTime;

    return {
      response,
      responseTime,
      data: response.ok ? await response.json() : null,
      size: response.headers.get('content-length') || 0
    };
  }

  async runBenchmark(name, testFunction, iterations = BENCHMARK_CONFIG.iterations) {
    console.log(`\n📊 Running benchmark: ${name}`);
    console.log(`   Iterations: ${iterations} (+ ${BENCHMARK_CONFIG.warmupIterations} warmup)`);

    const times = [];
    const sizes = [];
    let errors = 0;

    // Warmup iterations
    for (let i = 0; i < BENCHMARK_CONFIG.warmupIterations; i++) {
      try {
        await testFunction();
      } catch (error) {
        console.warn(`   Warmup ${i + 1} failed:`, error.message);
      }
    }

    // Actual benchmark iterations
    for (let i = 0; i < iterations; i++) {
      try {
        const result = await testFunction();
        times.push(result.responseTime);
        sizes.push(result.size || 0);
        
        process.stdout.write('.');
      } catch (error) {
        errors++;
        process.stdout.write('x');
      }
    }

    console.log(''); // New line after progress dots

    // Calculate statistics
    const stats = this.calculateStats(times);
    const avgSize = sizes.reduce((sum, size) => sum + size, 0) / sizes.length;

    const benchmark = {
      name,
      iterations,
      errors,
      responseTime: stats,
      averageSize: Math.round(avgSize),
      timestamp: new Date().toISOString()
    };

    this.results.benchmarks.push(benchmark);

    console.log(`   ✅ ${name} completed`);
    console.log(`   📈 Avg: ${stats.mean.toFixed(2)}ms, Min: ${stats.min.toFixed(2)}ms, Max: ${stats.max.toFixed(2)}ms`);
    console.log(`   📦 Avg Size: ${Math.round(avgSize)} bytes`);
    
    if (errors > 0) {
      console.log(`   ⚠️  Errors: ${errors}/${iterations}`);
    }

    return benchmark;
  }

  calculateStats(times) {
    if (times.length === 0) return { mean: 0, min: 0, max: 0, median: 0, p95: 0, p99: 0 };

    const sorted = times.slice().sort((a, b) => a - b);
    const mean = times.reduce((sum, time) => sum + time, 0) / times.length;
    const min = sorted[0];
    const max = sorted[sorted.length - 1];
    const median = sorted[Math.floor(sorted.length / 2)];
    const p95 = sorted[Math.floor(sorted.length * 0.95)];
    const p99 = sorted[Math.floor(sorted.length * 0.99)];

    return { mean, min, max, median, p95, p99 };
  }

  async benchmarkProjectList() {
    return await this.runBenchmark('Project List (Basic)', async () => {
      return await this.makeRequest('/api/task-master/projects?limit=20');
    });
  }

  async benchmarkProjectListWithDetails() {
    return await this.runBenchmark('Project List (With Details)', async () => {
      return await this.makeRequest('/api/task-master/projects?includeDetails=true&limit=20');
    });
  }

  async benchmarkProjectListPaginated() {
    return await this.runBenchmark('Project List (Paginated)', async () => {
      return await this.makeRequest('/api/task-master/projects?page=1&limit=10');
    });
  }

  async benchmarkPartialResponse() {
    return await this.runBenchmark('Partial Response (Basic Fields)', async () => {
      return await this.makeRequest('/api/task-master/projects?fields=basic&limit=20');
    });
  }

  async benchmarkProjectDetail() {
    // First get a project ID
    const { data } = await this.makeRequest('/api/task-master/projects?limit=1');
    if (!data?.projects?.length) {
      throw new Error('No projects available for benchmarking');
    }

    const projectId = data.projects[0].id;

    return await this.runBenchmark('Project Detail (Full)', async () => {
      return await this.makeRequest(`/api/task-master/projects/${projectId}?fields=withRelations`);
    });
  }

  async benchmarkProjectDetailPartial() {
    const { data } = await this.makeRequest('/api/task-master/projects?limit=1');
    if (!data?.projects?.length) {
      throw new Error('No projects available for benchmarking');
    }

    const projectId = data.projects[0].id;

    return await this.runBenchmark('Project Detail (Partial)', async () => {
      return await this.makeRequest(`/api/task-master/projects/${projectId}?fields=summary`);
    });
  }

  async benchmarkConcurrentRequests() {
    console.log(`\n🚀 Running concurrent request benchmark (${BENCHMARK_CONFIG.concurrentUsers} users)`);

    const concurrentTimes = [];
    const iterations = 5;

    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();
      
      const promises = Array.from({ length: BENCHMARK_CONFIG.concurrentUsers }, () =>
        this.makeRequest('/api/task-master/projects?limit=10')
      );

      const results = await Promise.allSettled(promises);
      const endTime = performance.now();
      
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const totalTime = endTime - startTime;
      
      concurrentTimes.push(totalTime);
      
      console.log(`   Iteration ${i + 1}: ${totalTime.toFixed(2)}ms (${successCount}/${BENCHMARK_CONFIG.concurrentUsers} successful)`);
    }

    const stats = this.calculateStats(concurrentTimes);
    
    const benchmark = {
      name: 'Concurrent Requests',
      concurrentUsers: BENCHMARK_CONFIG.concurrentUsers,
      iterations,
      responseTime: stats,
      timestamp: new Date().toISOString()
    };

    this.results.benchmarks.push(benchmark);
    
    console.log(`   ✅ Concurrent requests completed`);
    console.log(`   📈 Avg: ${stats.mean.toFixed(2)}ms, Min: ${stats.min.toFixed(2)}ms, Max: ${stats.max.toFixed(2)}ms`);

    return benchmark;
  }

  async benchmarkDatabaseOperations() {
    return await this.runBenchmark('Database Operations (Create/Update/Delete)', async () => {
      // Create project
      const createResult = await this.makeRequest('/api/task-master/projects', {
        method: 'POST',
        body: JSON.stringify({
          title: `Benchmark Project ${Date.now()}`,
          description: 'Performance benchmark test project',
          status: 'todo',
          priority: 'medium'
        })
      });

      if (!createResult.response.ok) {
        throw new Error('Project creation failed');
      }

      const projectId = createResult.data.project.id;

      // Update project
      const updateResult = await this.makeRequest(`/api/task-master/projects/${projectId}`, {
        method: 'PUT',
        body: JSON.stringify({
          status: 'inProgress',
          progress: 50
        })
      });

      if (!updateResult.response.ok) {
        throw new Error('Project update failed');
      }

      // Delete project
      const deleteResult = await this.makeRequest(`/api/task-master/projects/${projectId}`, {
        method: 'DELETE'
      });

      if (!deleteResult.response.ok) {
        throw new Error('Project deletion failed');
      }

      return {
        responseTime: createResult.responseTime + updateResult.responseTime + deleteResult.responseTime,
        size: 0
      };
    }, 5); // Fewer iterations for destructive operations
  }

  generateReport() {
    const report = {
      summary: {
        totalBenchmarks: this.results.benchmarks.length,
        timestamp: this.results.timestamp,
        config: this.results.config
      },
      performance: {},
      recommendations: []
    };

    // Calculate performance metrics
    this.results.benchmarks.forEach(benchmark => {
      report.performance[benchmark.name] = {
        averageResponseTime: benchmark.responseTime?.mean || 0,
        p95ResponseTime: benchmark.responseTime?.p95 || 0,
        averageSize: benchmark.averageSize || 0,
        errors: benchmark.errors || 0
      };
    });

    // Generate recommendations
    const projectListBasic = this.results.benchmarks.find(b => b.name === 'Project List (Basic)');
    const projectListDetails = this.results.benchmarks.find(b => b.name === 'Project List (With Details)');
    const partialResponse = this.results.benchmarks.find(b => b.name === 'Partial Response (Basic Fields)');

    if (projectListBasic && projectListDetails) {
      const improvement = ((projectListDetails.responseTime.mean - projectListBasic.responseTime.mean) / projectListDetails.responseTime.mean) * 100;
      report.recommendations.push({
        type: 'performance',
        message: `Lazy loading provides ${improvement.toFixed(1)}% performance improvement`,
        impact: improvement > 30 ? 'high' : improvement > 15 ? 'medium' : 'low'
      });
    }

    if (partialResponse && projectListBasic) {
      const sizeReduction = ((projectListBasic.averageSize - partialResponse.averageSize) / projectListBasic.averageSize) * 100;
      if (sizeReduction > 0) {
        report.recommendations.push({
          type: 'bandwidth',
          message: `Partial responses reduce payload size by ${sizeReduction.toFixed(1)}%`,
          impact: sizeReduction > 50 ? 'high' : sizeReduction > 25 ? 'medium' : 'low'
        });
      }
    }

    return report;
  }

  async saveResults() {
    const reportPath = path.join(__dirname, 'performance-benchmark-results.json');
    const report = this.generateReport();
    
    fs.writeFileSync(reportPath, JSON.stringify({
      ...this.results,
      report
    }, null, 2));

    console.log(`\n📄 Benchmark results saved to: ${reportPath}`);
    return reportPath;
  }
}

async function runPerformanceBenchmarks() {
  console.log('⚡ TASK MASTER PERFORMANCE BENCHMARKS');
  console.log('====================================');
  console.log(`Target: ${BENCHMARK_CONFIG.baseUrl}`);
  console.log(`Iterations: ${BENCHMARK_CONFIG.iterations} (+ ${BENCHMARK_CONFIG.warmupIterations} warmup)`);
  console.log('');

  const benchmark = new PerformanceBenchmark();

  try {
    await benchmark.authenticate();

    // Run all benchmarks
    await benchmark.benchmarkProjectList();
    await benchmark.benchmarkProjectListWithDetails();
    await benchmark.benchmarkProjectListPaginated();
    await benchmark.benchmarkPartialResponse();
    await benchmark.benchmarkProjectDetail();
    await benchmark.benchmarkProjectDetailPartial();
    await benchmark.benchmarkConcurrentRequests();
    await benchmark.benchmarkDatabaseOperations();

    // Generate and save report
    const report = benchmark.generateReport();
    await benchmark.saveResults();

    // Display summary
    console.log('\n📊 BENCHMARK SUMMARY');
    console.log('===================');
    
    Object.entries(report.performance).forEach(([name, metrics]) => {
      console.log(`${name}:`);
      console.log(`  Avg Response: ${metrics.averageResponseTime.toFixed(2)}ms`);
      console.log(`  P95 Response: ${metrics.p95ResponseTime.toFixed(2)}ms`);
      if (metrics.averageSize > 0) {
        console.log(`  Avg Size: ${metrics.averageSize} bytes`);
      }
      if (metrics.errors > 0) {
        console.log(`  Errors: ${metrics.errors}`);
      }
      console.log('');
    });

    // Display recommendations
    if (report.recommendations.length > 0) {
      console.log('💡 RECOMMENDATIONS');
      console.log('==================');
      report.recommendations.forEach(rec => {
        const impact = rec.impact === 'high' ? '🔴' : rec.impact === 'medium' ? '🟡' : '🟢';
        console.log(`${impact} ${rec.message}`);
      });
    }

    console.log('\n✅ Performance benchmarks completed successfully');

  } catch (error) {
    console.error('\n❌ Benchmark execution failed:', error.message);
    process.exit(1);
  }
}

// Run benchmarks if this file is executed directly
if (require.main === module) {
  runPerformanceBenchmarks().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = {
  PerformanceBenchmark,
  runPerformanceBenchmarks,
  BENCHMARK_CONFIG
};
