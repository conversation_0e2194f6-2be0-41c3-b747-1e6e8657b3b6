#!/usr/bin/env tsx

/**
 * Comprehensive Field Mapping Audit Script
 * 
 * Systematically audits all Task Master API routes for field mapping compliance
 * and identifies routes that need fixes for proper camelCase/snake_case handling.
 */

import { readFileSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

interface RouteAuditResult {
  path: string;
  hasFieldMapping: boolean;
  usesMiddleware: boolean;
  hasValidation: boolean;
  issues: string[];
  recommendations: string[];
  status: 'compliant' | 'needs_fixes' | 'critical_issues';
}

interface AuditSummary {
  totalRoutes: number;
  compliantRoutes: number;
  routesNeedingFixes: number;
  criticalIssues: number;
  routes: RouteAuditResult[];
}

class FieldMappingAuditor {
  private apiBasePath: string;
  private results: RouteAuditResult[] = [];

  constructor() {
    this.apiBasePath = join(process.cwd(), 'src/app/api/task-master');
  }

  /**
   * Run comprehensive audit of all Task Master API routes
   */
  async audit(): Promise<AuditSummary> {
    console.log('🔍 Starting comprehensive field mapping audit...\n');

    // Find all route files
    const routeFiles = this.findAllRouteFiles(this.apiBasePath);
    
    console.log(`📋 Found ${routeFiles.length} API route files to audit:\n`);
    
    // Audit each route file
    for (const routeFile of routeFiles) {
      const result = await this.auditRouteFile(routeFile);
      this.results.push(result);
      
      // Print immediate feedback
      const statusIcon = result.status === 'compliant' ? '✅' : 
                        result.status === 'needs_fixes' ? '⚠️' : '🚨';
      console.log(`${statusIcon} ${result.path}`);
      
      if (result.issues.length > 0) {
        result.issues.forEach(issue => console.log(`   - ${issue}`));
      }
      console.log();
    }

    // Generate summary
    const summary: AuditSummary = {
      totalRoutes: this.results.length,
      compliantRoutes: this.results.filter(r => r.status === 'compliant').length,
      routesNeedingFixes: this.results.filter(r => r.status === 'needs_fixes').length,
      criticalIssues: this.results.filter(r => r.status === 'critical_issues').length,
      routes: this.results
    };

    this.printDetailedReport(summary);
    return summary;
  }

  /**
   * Find all route.ts files in the API directory
   */
  private findAllRouteFiles(dir: string, basePath: string = ''): string[] {
    const files: string[] = [];
    
    try {
      const items = readdirSync(dir);
      
      for (const item of items) {
        const fullPath = join(dir, item);
        const relativePath = join(basePath, item);
        const stat = statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Recursively search subdirectories
          files.push(...this.findAllRouteFiles(fullPath, relativePath));
        } else if (item === 'route.ts') {
          files.push(relativePath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not read directory ${dir}`);
    }
    
    return files;
  }

  /**
   * Audit a specific route file
   */
  private async auditRouteFile(relativePath: string): Promise<RouteAuditResult> {
    const fullPath = join(this.apiBasePath, relativePath);
    const apiPath = `/api/task-master/${relativePath.replace('/route.ts', '')}`;
    
    const result: RouteAuditResult = {
      path: apiPath,
      hasFieldMapping: false,
      usesMiddleware: false,
      hasValidation: false,
      issues: [],
      recommendations: [],
      status: 'compliant'
    };

    try {
      const content = readFileSync(fullPath, 'utf-8');
      
      // Check for field mapping
      result.hasFieldMapping = this.checkFieldMapping(content);
      
      // Check for middleware usage
      result.usesMiddleware = this.checkMiddlewareUsage(content);
      
      // Check for validation
      result.hasValidation = this.checkValidation(content);
      
      // Analyze issues and determine status
      this.analyzeIssues(content, result);
      
    } catch (error) {
      result.issues.push(`Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`);
      result.status = 'critical_issues';
    }

    return result;
  }

  /**
   * Check if route has proper field mapping
   */
  private checkFieldMapping(content: string): boolean {
    return content.includes('FieldMapper.apiToDb') || content.includes('FieldMapper.dbToApi');
  }

  /**
   * Check if route uses the standardized middleware
   */
  private checkMiddlewareUsage(content: string): boolean {
    return content.includes('withTaskMasterMiddleware');
  }

  /**
   * Check if route has validation schemas
   */
  private checkValidation(content: string): boolean {
    return content.includes('Schemas.') || content.includes('validateBody') || content.includes('validateQuery');
  }

  /**
   * Analyze content for specific issues and recommendations
   */
  private analyzeIssues(content: string, result: RouteAuditResult): void {
    // Check for manual field access patterns that indicate missing field mapping
    const manualFieldPatterns = [
      /\.full_description/g,
      /\.due_date/g,
      /\.created_at/g,
      /\.updated_at/g,
      /\.assigned_to/g,
      /\.parent_task_id/g,
      /\.project_id/g
    ];

    let hasManualFieldAccess = false;
    for (const pattern of manualFieldPatterns) {
      if (pattern.test(content)) {
        hasManualFieldAccess = true;
        break;
      }
    }

    // Check for mixed field naming (both camelCase and snake_case in same file)
    const hasCamelCase = /\.(fullDescription|dueDate|createdAt|updatedAt|assignedTo|parentTaskId|projectId)/.test(content);
    const hasSnakeCase = /\.(full_description|due_date|created_at|updated_at|assigned_to|parent_task_id|project_id)/.test(content);

    // Determine issues
    if (!result.usesMiddleware) {
      result.issues.push('Not using withTaskMasterMiddleware');
      result.recommendations.push('Migrate to use withTaskMasterMiddleware for consistent auth and validation');
    }

    if (!result.hasValidation && result.usesMiddleware) {
      result.issues.push('Missing validation schemas');
      result.recommendations.push('Add appropriate validation schemas for request body/query parameters');
    }

    if (hasManualFieldAccess && !result.hasFieldMapping) {
      result.issues.push('Manual field access without field mapping');
      result.recommendations.push('Add FieldMapper.apiToDb() for incoming data and FieldMapper.dbToApi() for outgoing data');
    }

    if (hasCamelCase && hasSnakeCase) {
      result.issues.push('Mixed field naming conventions detected');
      result.recommendations.push('Use consistent field mapping to separate API (camelCase) and database (snake_case) layers');
    }

    // Check for wrong field mapping direction
    if (content.includes('FieldMapper.dbToApi') && content.includes('body')) {
      const lines = content.split('\n');
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes('FieldMapper.dbToApi') && lines[i].includes('body')) {
          result.issues.push('Wrong field mapping direction detected (dbToApi used on request body)');
          result.recommendations.push('Use FieldMapper.apiToDb() for incoming request data');
          break;
        }
      }
    }

    // Determine overall status
    if (result.issues.length === 0) {
      result.status = 'compliant';
    } else if (result.issues.some(issue => 
      issue.includes('Wrong field mapping direction') || 
      issue.includes('Not using withTaskMasterMiddleware') ||
      issue.includes('Manual field access without field mapping')
    )) {
      result.status = 'critical_issues';
    } else {
      result.status = 'needs_fixes';
    }
  }

  /**
   * Print detailed audit report
   */
  private printDetailedReport(summary: AuditSummary): void {
    console.log('\n' + '='.repeat(80));
    console.log('📊 COMPREHENSIVE FIELD MAPPING AUDIT REPORT');
    console.log('='.repeat(80));
    
    console.log(`\n📈 Summary:`);
    console.log(`   Total Routes Audited: ${summary.totalRoutes}`);
    console.log(`   ✅ Compliant Routes: ${summary.compliantRoutes}`);
    console.log(`   ⚠️  Routes Needing Fixes: ${summary.routesNeedingFixes}`);
    console.log(`   🚨 Critical Issues: ${summary.criticalIssues}`);
    
    const complianceRate = ((summary.compliantRoutes / summary.totalRoutes) * 100).toFixed(1);
    console.log(`   📊 Compliance Rate: ${complianceRate}%`);

    // Critical issues section
    const criticalRoutes = summary.routes.filter(r => r.status === 'critical_issues');
    if (criticalRoutes.length > 0) {
      console.log(`\n🚨 CRITICAL ISSUES (${criticalRoutes.length} routes):`);
      criticalRoutes.forEach(route => {
        console.log(`\n   ${route.path}:`);
        route.issues.forEach(issue => console.log(`     - ${issue}`));
        route.recommendations.forEach(rec => console.log(`     → ${rec}`));
      });
    }

    // Routes needing fixes
    const fixRoutes = summary.routes.filter(r => r.status === 'needs_fixes');
    if (fixRoutes.length > 0) {
      console.log(`\n⚠️  ROUTES NEEDING FIXES (${fixRoutes.length} routes):`);
      fixRoutes.forEach(route => {
        console.log(`\n   ${route.path}:`);
        route.issues.forEach(issue => console.log(`     - ${issue}`));
        route.recommendations.forEach(rec => console.log(`     → ${rec}`));
      });
    }

    // Compliant routes
    const compliantRoutes = summary.routes.filter(r => r.status === 'compliant');
    if (compliantRoutes.length > 0) {
      console.log(`\n✅ COMPLIANT ROUTES (${compliantRoutes.length} routes):`);
      compliantRoutes.forEach(route => {
        console.log(`   ${route.path}`);
      });
    }

    console.log('\n' + '='.repeat(80));
    console.log('🎯 RECOMMENDATIONS FOR ACHIEVING ZERO FIELD MAPPING BUGS:');
    console.log('='.repeat(80));
    
    if (summary.criticalIssues > 0) {
      console.log('\n1. 🚨 IMMEDIATE ACTION REQUIRED:');
      console.log('   - Fix critical field mapping issues in routes listed above');
      console.log('   - Ensure all routes use FieldMapper.apiToDb() for incoming data');
      console.log('   - Migrate all routes to use withTaskMasterMiddleware');
    }
    
    if (summary.routesNeedingFixes > 0) {
      console.log('\n2. ⚠️  IMPROVEMENTS NEEDED:');
      console.log('   - Add missing validation schemas');
      console.log('   - Implement consistent field mapping patterns');
      console.log('   - Remove manual field access patterns');
    }
    
    console.log('\n3. 📋 STANDARDS COMPLIANCE:');
    console.log('   - API layer: Only camelCase field names');
    console.log('   - Database layer: Only snake_case field names');
    console.log('   - Automatic conversion: FieldMapper between layers');
    console.log('   - Validation: Schemas enforce correct naming per layer');
    
    console.log('\n4. 🔧 NEXT STEPS:');
    console.log('   - Run field mapping fixes for critical issues');
    console.log('   - Update remaining routes to use middleware pattern');
    console.log('   - Add comprehensive validation schemas');
    console.log('   - Test all routes after fixes');
    
    console.log('\n✅ Audit completed successfully!');
  }
}

// Main execution
async function main() {
  const auditor = new FieldMappingAuditor();
  const summary = await auditor.audit();
  
  // Exit with appropriate code
  process.exit(summary.criticalIssues > 0 ? 1 : 0);
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Audit failed:', error);
    process.exit(1);
  });
}

export { FieldMappingAuditor };
export type { RouteAuditResult, AuditSummary };
