openapi: 3.0.3
info:
  title: Task Master API
  description: |
    Comprehensive API for the Task Master project management system.
    
    ## Authentication
    All endpoints require authentication via <PERSON><PERSON> token in the Authorization header.
    
    ## Field Naming Convention
    - API requests and responses use camelCase field names
    - Database internally uses snake_case but this is transparent to API consumers
    
    ## Error Handling
    All endpoints return consistent error responses with appropriate HTTP status codes.
    
  version: 1.0.0
  contact:
    name: Task Master API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3000/api/task-master
    description: Development server
  - url: https://prosperous.com/api/task-master
    description: Production server

security:
  - bearerAuth: []

paths:
  /projects:
    get:
      summary: Get user projects
      description: Retrieve all projects accessible to the authenticated user
      tags:
        - Projects
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
        - name: status
          in: query
          description: Filter by project status
          schema:
            type: string
            enum: [todo, in_progress, completed]
        - name: priority
          in: query
          description: Filter by project priority
          schema:
            type: string
            enum: [low, medium, high]
        - name: fields
          in: query
          description: Comma-separated list of fields to include in response
          schema:
            type: string
            example: "id,title,status,priority"
      responses:
        '200':
          description: List of projects
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Project'
              examples:
                projects_list:
                  summary: Example projects list
                  value:
                    - id: 1
                      title: "Website Redesign"
                      description: "Complete redesign of company website"
                      status: "in_progress"
                      priority: "high"
                      progress: 65
                      createdBy: 1
                      createdAt: "2024-01-01T00:00:00Z"
                    - id: 2
                      title: "Mobile App Development"
                      description: "Develop mobile app for iOS and Android"
                      status: "todo"
                      priority: "medium"
                      progress: 0
                      createdBy: 1
                      createdAt: "2024-01-02T00:00:00Z"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create new project
      description: Create a new project with the authenticated user as owner
      tags:
        - Projects
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProjectRequest'
            examples:
              create_project:
                summary: Create project example
                value:
                  title: "New Project"
                  description: "Project description"
                  fullDescription: "Detailed project description with requirements"
                  status: "todo"
                  priority: "medium"
                  dueDate: "2024-12-31T23:59:59Z"
                  assignedTo: 2
      responses:
        '201':
          description: Project created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /projects/{id}:
    get:
      summary: Get project by ID
      description: Retrieve detailed information about a specific project
      tags:
        - Projects
      parameters:
        - name: id
          in: path
          required: true
          description: Project ID
          schema:
            type: integer
            minimum: 1
        - name: fields
          in: query
          description: Comma-separated list of fields to include
          schema:
            type: string
      responses:
        '200':
          description: Project details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDetailed'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      summary: Update project
      description: Update an existing project (requires ownership or admin role)
      tags:
        - Projects
      parameters:
        - name: id
          in: path
          required: true
          description: Project ID
          schema:
            type: integer
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProjectRequest'
      responses:
        '200':
          description: Project updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      summary: Delete project
      description: Delete a project and all associated tasks (requires ownership)
      tags:
        - Projects
      parameters:
        - name: id
          in: path
          required: true
          description: Project ID
          schema:
            type: integer
            minimum: 1
      responses:
        '204':
          description: Project deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /projects/{id}/tasks:
    get:
      summary: Get project tasks
      description: Retrieve all tasks for a specific project
      tags:
        - Tasks
      parameters:
        - name: id
          in: path
          required: true
          description: Project ID
          schema:
            type: integer
            minimum: 1
        - name: status
          in: query
          description: Filter by task status
          schema:
            type: string
            enum: [todo, in_progress, completed]
        - name: assignedTo
          in: query
          description: Filter by assigned user ID
          schema:
            type: integer
        - name: parentTaskId
          in: query
          description: Filter by parent task ID (for subtasks)
          schema:
            type: integer
      responses:
        '200':
          description: List of tasks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Task'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create new task
      description: Create a new task within a project
      tags:
        - Tasks
      parameters:
        - name: id
          in: path
          required: true
          description: Project ID
          schema:
            type: integer
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTaskRequest'
      responses:
        '201':
          description: Task created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /tasks/{id}:
    get:
      summary: Get task by ID
      description: Retrieve detailed information about a specific task
      tags:
        - Tasks
      parameters:
        - name: id
          in: path
          required: true
          description: Task ID
          schema:
            type: integer
            minimum: 1
      responses:
        '200':
          description: Task details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskDetailed'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      summary: Update task
      description: Update an existing task
      tags:
        - Tasks
      parameters:
        - name: id
          in: path
          required: true
          description: Task ID
          schema:
            type: integer
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTaskRequest'
      responses:
        '200':
          description: Task updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      summary: Delete task
      description: Delete a task and all associated comments
      tags:
        - Tasks
      parameters:
        - name: id
          in: path
          required: true
          description: Task ID
          schema:
            type: integer
            minimum: 1
      responses:
        '204':
          description: Task deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Project:
      type: object
      properties:
        id:
          type: integer
          description: Unique project identifier
          example: 1
        title:
          type: string
          description: Project title
          example: "Website Redesign"
        description:
          type: string
          nullable: true
          description: Short project description
          example: "Complete redesign of company website"
        fullDescription:
          type: string
          nullable: true
          description: Detailed project description
          example: "Complete redesign including UX research, wireframes, and implementation"
        eventLog:
          type: string
          nullable: true
          description: Project event log content
          example: "Project kickoff meeting completed"
        status:
          type: string
          enum: [todo, in_progress, completed]
          description: Current project status
          example: "in_progress"
        priority:
          type: string
          enum: [low, medium, high]
          description: Project priority level
          example: "high"
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: Project completion percentage
          example: 65
        dueDate:
          type: string
          format: date-time
          nullable: true
          description: Project due date
          example: "2024-12-31T23:59:59Z"
        completedDate:
          type: string
          format: date-time
          nullable: true
          description: Project completion date
          example: "2024-06-15T14:30:00Z"
        createdBy:
          type: integer
          description: ID of user who created the project
          example: 1
        assignedTo:
          type: integer
          nullable: true
          description: ID of user assigned to the project
          example: 2
        createdAt:
          type: string
          format: date-time
          description: Project creation timestamp
          example: "2024-01-01T00:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-15T10:30:00Z"
      required:
        - id
        - title
        - status
        - priority
        - progress
        - createdBy
        - createdAt
        - updatedAt

    ProjectDetailed:
      allOf:
        - $ref: '#/components/schemas/Project'
        - type: object
          properties:
            tasks:
              type: array
              items:
                $ref: '#/components/schemas/Task'
              description: Project tasks
            teamMembers:
              type: array
              items:
                $ref: '#/components/schemas/TeamMember'
              description: Project team members
            activityLog:
              type: array
              items:
                $ref: '#/components/schemas/ActivityLogEntry'
              description: Recent project activity

    CreateProjectRequest:
      type: object
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 255
          description: Project title
          example: "New Project"
        description:
          type: string
          nullable: true
          maxLength: 1000
          description: Short project description
          example: "Project description"
        fullDescription:
          type: string
          nullable: true
          description: Detailed project description
          example: "Detailed project description with requirements"
        eventLog:
          type: string
          nullable: true
          description: Initial event log content
          example: "Project created"
        status:
          type: string
          enum: [todo, in_progress, completed]
          default: todo
          description: Initial project status
          example: "todo"
        priority:
          type: string
          enum: [low, medium, high]
          default: medium
          description: Project priority level
          example: "medium"
        dueDate:
          type: string
          format: date-time
          nullable: true
          description: Project due date
          example: "2024-12-31T23:59:59Z"
        assignedTo:
          type: integer
          nullable: true
          description: ID of user to assign the project to
          example: 2
      required:
        - title

    UpdateProjectRequest:
      type: object
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 255
          description: Project title
        description:
          type: string
          nullable: true
          maxLength: 1000
          description: Short project description
        fullDescription:
          type: string
          nullable: true
          description: Detailed project description
        eventLog:
          type: string
          nullable: true
          description: Event log content
        status:
          type: string
          enum: [todo, in_progress, completed]
          description: Project status
        priority:
          type: string
          enum: [low, medium, high]
          description: Project priority level
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: Project completion percentage
        dueDate:
          type: string
          format: date-time
          nullable: true
          description: Project due date
        assignedTo:
          type: integer
          nullable: true
          description: ID of user to assign the project to

    Task:
      type: object
      properties:
        id:
          type: integer
          description: Unique task identifier
          example: 1
        title:
          type: string
          description: Task title
          example: "Design homepage mockup"
        description:
          type: string
          nullable: true
          description: Task description
          example: "Create wireframes and mockups for the new homepage"
        status:
          type: string
          enum: [todo, in_progress, completed]
          description: Current task status
          example: "in_progress"
        priority:
          type: string
          enum: [low, medium, high]
          description: Task priority level
          example: "high"
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: Task completion percentage
          example: 75
        dueDate:
          type: string
          format: date-time
          nullable: true
          description: Task due date
          example: "2024-06-30T23:59:59Z"
        completedDate:
          type: string
          format: date-time
          nullable: true
          description: Task completion date
          example: "2024-06-15T14:30:00Z"
        projectId:
          type: integer
          description: ID of the parent project
          example: 1
        parentTaskId:
          type: integer
          nullable: true
          description: ID of the parent task (for subtasks)
          example: null
        createdBy:
          type: integer
          description: ID of user who created the task
          example: 1
        assignedTo:
          type: integer
          nullable: true
          description: ID of user assigned to the task
          example: 2
        createdAt:
          type: string
          format: date-time
          description: Task creation timestamp
          example: "2024-01-01T00:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-15T10:30:00Z"
      required:
        - id
        - title
        - status
        - priority
        - progress
        - projectId
        - createdBy
        - createdAt
        - updatedAt

    TaskDetailed:
      allOf:
        - $ref: '#/components/schemas/Task'
        - type: object
          properties:
            comments:
              type: array
              items:
                $ref: '#/components/schemas/Comment'
              description: Task comments
            subtasks:
              type: array
              items:
                $ref: '#/components/schemas/Task'
              description: Subtasks

    CreateTaskRequest:
      type: object
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 255
          description: Task title
          example: "Design homepage mockup"
        description:
          type: string
          nullable: true
          maxLength: 1000
          description: Task description
          example: "Create wireframes and mockups for the new homepage"
        status:
          type: string
          enum: [todo, in_progress, completed]
          default: todo
          description: Initial task status
          example: "todo"
        priority:
          type: string
          enum: [low, medium, high]
          default: medium
          description: Task priority level
          example: "high"
        dueDate:
          type: string
          format: date-time
          nullable: true
          description: Task due date
          example: "2024-06-30T23:59:59Z"
        parentTaskId:
          type: integer
          nullable: true
          description: ID of the parent task (for subtasks)
          example: null
        assignedTo:
          type: integer
          nullable: true
          description: ID of user to assign the task to
          example: 2
      required:
        - title

    UpdateTaskRequest:
      type: object
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 255
          description: Task title
        description:
          type: string
          nullable: true
          maxLength: 1000
          description: Task description
        status:
          type: string
          enum: [todo, in_progress, completed]
          description: Task status
        priority:
          type: string
          enum: [low, medium, high]
          description: Task priority level
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: Task completion percentage
        dueDate:
          type: string
          format: date-time
          nullable: true
          description: Task due date
        parentTaskId:
          type: integer
          nullable: true
          description: ID of the parent task (for subtasks)
        assignedTo:
          type: integer
          nullable: true
          description: ID of user to assign the task to

    Comment:
      type: object
      properties:
        id:
          type: integer
          description: Unique comment identifier
          example: 1
        content:
          type: string
          description: Comment content
          example: "This looks great! Just need to adjust the color scheme."
        taskId:
          type: integer
          description: ID of the parent task
          example: 1
        parentCommentId:
          type: integer
          nullable: true
          description: ID of the parent comment (for replies)
          example: null
        createdBy:
          type: integer
          description: ID of user who created the comment
          example: 2
        createdAt:
          type: string
          format: date-time
          description: Comment creation timestamp
          example: "2024-01-15T10:30:00Z"
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-15T10:30:00Z"
      required:
        - id
        - content
        - taskId
        - createdBy
        - createdAt
        - updatedAt

    TeamMember:
      type: object
      properties:
        id:
          type: integer
          description: Unique team member identifier
          example: 1
        projectId:
          type: integer
          description: ID of the project
          example: 1
        userId:
          type: integer
          description: ID of the user
          example: 2
        role:
          type: string
          enum: [owner, admin, member]
          description: Team member role
          example: "member"
        joinedAt:
          type: string
          format: date-time
          description: When the user joined the project
          example: "2024-01-01T00:00:00Z"
      required:
        - id
        - projectId
        - userId
        - role
        - joinedAt

    ActivityLogEntry:
      type: object
      properties:
        id:
          type: integer
          description: Unique activity log entry identifier
          example: 1
        activityType:
          type: string
          description: Type of activity
          example: "task_created"
        entityType:
          type: string
          description: Type of entity affected
          example: "task"
        entityId:
          type: integer
          description: ID of the affected entity
          example: 5
        oldValue:
          type: string
          nullable: true
          description: Previous value (for updates)
          example: "todo"
        newValue:
          type: string
          nullable: true
          description: New value (for updates)
          example: "in_progress"
        projectId:
          type: integer
          description: ID of the related project
          example: 1
        performedBy:
          type: integer
          description: ID of user who performed the action
          example: 2
        performedAt:
          type: string
          format: date-time
          description: When the activity occurred
          example: "2024-01-15T10:30:00Z"
      required:
        - id
        - activityType
        - entityType
        - entityId
        - projectId
        - performedBy
        - performedAt

    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "Resource not found"
        code:
          type: string
          description: Error code
          example: "NOT_FOUND"
        details:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
                description: Field that caused the error
                example: "title"
              message:
                type: string
                description: Field-specific error message
                example: "Title is required"
              code:
                type: string
                description: Field-specific error code
                example: "REQUIRED"
          description: Detailed error information
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
          example: "2024-01-15T10:30:00Z"
      required:
        - error

  responses:
    BadRequest:
      description: Bad request - validation error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "Validation failed"
            code: "VALIDATION_ERROR"
            details:
              - field: "title"
                message: "Title is required"
                code: "REQUIRED"
            timestamp: "2024-01-15T10:30:00Z"

    Unauthorized:
      description: Unauthorized - authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "Authentication required"
            code: "UNAUTHORIZED"
            timestamp: "2024-01-15T10:30:00Z"

    Forbidden:
      description: Forbidden - insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "Access denied to this resource"
            code: "FORBIDDEN"
            timestamp: "2024-01-15T10:30:00Z"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "Resource not found"
            code: "NOT_FOUND"
            timestamp: "2024-01-15T10:30:00Z"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "An unexpected error occurred"
            code: "INTERNAL_ERROR"
            timestamp: "2024-01-15T10:30:00Z"

tags:
  - name: Projects
    description: Project management operations
  - name: Tasks
    description: Task management operations
  - name: Comments
    description: Comment management operations
  - name: Team
    description: Team member management operations
