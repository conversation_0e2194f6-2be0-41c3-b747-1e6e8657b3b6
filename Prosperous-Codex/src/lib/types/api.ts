/**
 * API Types (camelCase)
 * 
 * These types represent the API layer structure with camelCase field names.
 * They should be used for API request/response handling and service layer outputs.
 * 
 * These types are automatically mapped from database types using the FieldMapper.
 */

import { TaskStatus, TaskPriority, ProjectVisibility, TeamMemberRole, ActivityType } from './database';

/**
 * Base API entity structure
 */
export interface BaseApiEntity {
  id: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * API Project structure (camelCase)
 */
export interface Project extends BaseApiEntity {
  title: string;
  description?: string;
  fullDescription?: string;
  eventLog?: string;
  status: TaskStatus;
  priority: TaskPriority;
  progress: number;
  dueDate?: string;
  completedDate?: string;
  visibility: ProjectVisibility;
  createdBy: number;
  assignedTo?: number;
  
  // Edit tracking fields
  fullDescriptionEditCount?: number;
  fullDescriptionLastEditedAt?: string;
  fullDescriptionLastEditedBy?: number;
  eventLogEditCount?: number;
  eventLogLastEditedAt?: string;
  eventLogLastEditedBy?: number;
  
  // Joined data
  createdByUsername?: string;
  assignedToUsername?: string;
  fullDescriptionLastEditedByUsername?: string;
  eventLogLastEditedByUsername?: string;
  
  // Related entities (populated by service layer)
  tags?: string[];
  teamMembers?: ProjectTeamMember[];
  files?: ProjectFile[];
  comments?: ProjectComment[];
  tasks?: Task[];
  activity?: ActivityLogEntry[];
}

/**
 * API Task structure (camelCase)
 */
export interface Task extends BaseApiEntity {
  projectId: number;
  parentTaskId?: number;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  dueDate?: string;
  completedDate?: string;
  assignedTo?: number;
  createdBy: number;
  
  // Joined data
  assignedToUsername?: string;
  createdByUsername?: string;
  
  // Related entities
  subtasks?: Task[];
}

/**
 * API Project Team Member structure (camelCase)
 */
export interface ProjectTeamMember extends BaseApiEntity {
  projectId: number;
  userId: number;
  role: TeamMemberRole;
  addedBy?: number;
  addedAt: string;
  
  // Joined user data
  username?: string;
  email?: string;
  name: string; // Required - derived from username || email
  avatar?: string;
}

/**
 * API Project File structure (camelCase)
 */
export interface ProjectFile extends BaseApiEntity {
  projectId: number;
  fileName: string;
  fileType?: string;
  fileSize?: number;
  filePath: string;
  thumbnailPath?: string;
  uploadedBy: number;
  uploadedAt: string;
  
  // Joined data
  uploadedByUsername?: string;
}

/**
 * API Project Comment structure (camelCase)
 */
export interface ProjectComment extends BaseApiEntity {
  projectId: number;
  parentCommentId?: number;
  authorId: number;
  content: string;
  
  // Edit tracking fields
  isEdited?: boolean;
  editCount?: number;
  lastEditedAt?: string;
  lastEditedBy?: number;
  
  // Joined data
  authorUsername?: string;
  authorEmail?: string;
  lastEditedByUsername?: string;
  
  // Related entities
  replies?: ProjectComment[];
  editHistory?: CommentEditHistory[];
}

/**
 * API Comment Edit History structure (camelCase)
 */
export interface CommentEditHistory extends BaseApiEntity {
  commentId: number;
  versionNumber: number;
  previousContent: string;
  editedBy: number;
  editedAt: string;
  
  // Joined data
  editedByUsername?: string;
}

/**
 * API Project Edit History structure (camelCase)
 */
export interface ProjectEditHistory extends BaseApiEntity {
  projectId: number;
  fieldName: 'fullDescription' | 'eventLog';
  versionNumber: number;
  previousContent: string;
  editedBy: number;
  editedAt: string;
  
  // Joined data
  editedByUsername?: string;
}

/**
 * API Activity Log Entry structure (camelCase)
 */
export interface ActivityLogEntry extends BaseApiEntity {
  projectId?: number;
  taskId?: number;
  userId: number;
  activityType: ActivityType;
  description: string;
  metadata?: string;
  
  // Joined data
  username?: string;
}

/**
 * API User structure (camelCase)
 */
export interface User extends BaseApiEntity {
  email: string;
  username?: string;
  role: string;
  isActive: boolean;
  lastLogin?: string;
  avatar?: string;
  language?: string;
  // Note: passwordHash is excluded from API responses for security
}

/**
 * API Request/Response DTOs
 */

/**
 * Create project request
 */
export interface CreateProjectRequest {
  title: string;
  description?: string;
  fullDescription?: string;
  eventLog?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  progress?: number;
  dueDate?: string;
  assignedTo?: number;
  visibility?: ProjectVisibility;
  tags?: string[];
}

/**
 * Update project request
 */
export interface UpdateProjectRequest {
  title?: string;
  description?: string;
  fullDescription?: string;
  eventLog?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  progress?: number;
  dueDate?: string;
  assignedTo?: number;
  completedDate?: string;
  visibility?: ProjectVisibility;
}

/**
 * Create task request
 */
export interface CreateTaskRequest {
  projectId: number;
  title: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  dueDate?: string;
  assignedTo?: number;
  parentTaskId?: number;
}

/**
 * Update task request
 */
export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  dueDate?: string;
  assignedTo?: number;
  completedDate?: string;
}

/**
 * Add team member request
 */
export interface AddTeamMemberRequest {
  userEmail: string;
  role?: TeamMemberRole;
}

/**
 * Create comment request
 */
export interface CreateCommentRequest {
  content: string;
  parentCommentId?: number;
}

/**
 * Update comment request
 */
export interface UpdateCommentRequest {
  content: string;
}

/**
 * API Response wrapper
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  requestId?: string;
}

/**
 * Paginated response
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    offset: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

/**
 * API Error response
 */
export interface ApiError {
  error: string;
  code?: string;
  details?: Array<{
    field: string;
    message: string;
    code?: string;
  }>;
  timestamp?: string;
  requestId?: string;
}

// Re-export database enums for API layer
export { TaskStatus, TaskPriority, ProjectVisibility, TeamMemberRole, ActivityType } from './database';
