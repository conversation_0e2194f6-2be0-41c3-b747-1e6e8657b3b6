// Main Calculation Orchestrator
// Coordinates all calculation phases and provides the main calculation interface

import {
  JobInputs,
  InnerTextJobInputs,
  CoverJobInputs,
  EndpaperJobInputs,
  PaperCandidate,
  CalculationRequest,
  CalculationResponse,
  BleedDimensions,
  PRESS_CONSTRAINTS,
  ERROR_KEYS
} from '../types/calculation';

import {
  validateJobInputs,
  validateInnerTextInputs,
  calculateBleedDimensions_InnerText,
  calculateBleedDimensions_Cover,
  calculateBleedDimensions_Endpapers,
  determineOptimalLayoutBenchmark_InnerText,
  determineOptimalLayoutBenchmark_Cover,
  determineOptimalLayoutBenchmark_Endpapers,
  getPaperOptionDataAndValidate,
  determineInitialPressFit,
  selectWinningCandidate,
  applyPressLimitsAndGetGrain,
  calculateUsableArea,
  calculateSingleLayoutFit_ComponentSpecific
} from './core';

// The functions previously imported from './layout' are likely now part of './core' or './metrics'.
// I will remove this import and search for the correct location of these functions.

import {
  finalizeRollDimensions,
  calculateFinalMetrics_ComponentSpecific,
  sortAndCategorizeResults
} from './metrics';

// Main calculation function
export async function calculatePaperCosts(request: CalculationRequest): Promise<CalculationResponse> {
  const { tabId, jobInputs, paperOptions } = request;
  const results: PaperCandidate[] = [];
  const errors: string[] = [];

  try {
    // Phase 1: Validate job inputs
    let validationResult;
    if (tabId === 'innerText') {
      validationResult = validateInnerTextInputs(jobInputs as InnerTextJobInputs);
    } else {
      validationResult = validateJobInputs(jobInputs);
    }

    if (!validationResult.isValid) {
      return {
        results: [],
        benchmarkValue: 0,
        errors: validationResult.errors
      };
    }

    // Phase 2: Calculate bleed dimensions
    let bleedDims: BleedDimensions | null = null;
    if (tabId === 'innerText') {
      const innerInputs = jobInputs as InnerTextJobInputs;
      bleedDims = calculateBleedDimensions_InnerText(
        innerInputs.trimW,
        innerInputs.trimH,
        innerInputs.bleed,
        innerInputs.bindingMethod
      );
    } else if (tabId === 'cover') {
      const coverInputs = jobInputs as CoverJobInputs;
      bleedDims = calculateBleedDimensions_Cover(
        coverInputs.trimH,
        coverInputs.trimW,
        coverInputs.spineThickness,
        coverInputs.bleed,
        coverInputs.coverType,
        coverInputs.turnInAllowance,
        coverInputs.flapWidth
      );
    } else if (tabId === 'endpapers') {
      const endpaperInputs = jobInputs as EndpaperJobInputs;
      bleedDims = calculateBleedDimensions_Endpapers(
        endpaperInputs.trimH,
        endpaperInputs.trimW,
        endpaperInputs.bleed
      );
    }

    if (!bleedDims) {
      errors.push(ERROR_KEYS.PAGE_DIMS_INVALID);
      return { results: [], benchmarkValue: 0, errors };
    }

    // Phase 3: Determine optimal layout benchmark
    let benchmarkValue = 0;
    if (tabId === 'innerText') {
      const innerInputs = jobInputs as InnerTextJobInputs;
      benchmarkValue = determineOptimalLayoutBenchmark_InnerText(
        PRESS_CONSTRAINTS.MAX_PRESS_H,
        PRESS_CONSTRAINTS.MAX_PRESS_W,
        innerInputs.gripper,
        innerInputs.colorBar,
        innerInputs.lip,
        innerInputs.bleed,
        innerInputs.trimW,
        innerInputs.trimH,
        innerInputs.bindingMethod
      );
    } else if (tabId === 'cover') {
      benchmarkValue = determineOptimalLayoutBenchmark_Cover(
        PRESS_CONSTRAINTS.MAX_PRESS_H,
        PRESS_CONSTRAINTS.MAX_PRESS_W,
        jobInputs.gripper,
        jobInputs.colorBar,
        bleedDims.layoutPageH,
        bleedDims.layoutPageW
      );
    } else if (tabId === 'endpapers') {
      benchmarkValue = determineOptimalLayoutBenchmark_Endpapers(
        PRESS_CONSTRAINTS.MAX_PRESS_H,
        PRESS_CONSTRAINTS.MAX_PRESS_W,
        jobInputs.gripper,
        jobInputs.colorBar,
        bleedDims.layoutPageH,
        bleedDims.layoutPageW
      );
    }

    // Phase 4: Evaluate each paper stock
    for (const paperOption of paperOptions) {
      const paperData = getPaperOptionDataAndValidate(paperOption, tabId);
      if (paperData.error) {
        // Add bleed dimensions to error results for display
        paperData.layoutPageH = bleedDims.layoutPageH;
        paperData.layoutPageW = bleedDims.layoutPageW;
        results.push(paperData);
        continue;
      }

      // Set initial press dimensions based on paper size
      paperData.pressH = paperData.sheetH || PRESS_CONSTRAINTS.MAX_PRESS_H;
      paperData.pressW = paperData.sheetW;

      // Set roll optimization flags for inferred dimensions
      // CRITICAL FIX A: Use Math.max(MAX_PRESS_H, MAX_PRESS_W) for roll dimension maximization
      if (paperData.source === 'Roll') {
        const maxPressDimension = Math.max(PRESS_CONSTRAINTS.MAX_PRESS_H, PRESS_CONSTRAINTS.MAX_PRESS_W);

        if (!paperData.sheetH) {
          // Height was inferred (set to maximum possible press dimension for optimal layout testing)
          paperData.pressH = maxPressDimension;
          paperData.rollDimensionOptimizedForFitting = true;
          paperData.inputNoteKey = 'resNoteHeightInferred';
        } else if (!paperData.sheetW || paperData.sheetW === 0) {
          // Width was inferred (set to maximum possible press dimension for optimal layout testing)
          paperData.pressW = maxPressDimension;
          paperData.rollDimensionOptimizedForFitting = true;
          paperData.inputNoteKey = 'resNoteWidthInferred';
        }
      }

      const initialFit = determineInitialPressFit(
        paperData,
        PRESS_CONSTRAINTS.MAX_PRESS_H,
        PRESS_CONSTRAINTS.MAX_PRESS_W
      );
      if (initialFit.error) {
        // Add bleed dimensions to error results for display
        initialFit.layoutPageH = bleedDims.layoutPageH;
        initialFit.layoutPageW = bleedDims.layoutPageW;
        results.push(initialFit);
        continue;
      }

      // Calculate both orientations using reference implementation approach

      // Candidate A: layoutPageH x layoutPageW (normal orientation)
      // For innerText: applyEvenRuleToHeight = false, applyEvenRuleToWidth = true
      const candidateA = processOrientation(
        initialFit,
        bleedDims,
        jobInputs,
        tabId,
        false, // not rotated
        false, // applyEvenRuleToHeight
        tabId === 'innerText' // applyEvenRuleToWidth
      );

      // Candidate B: layoutPageW x layoutPageH (rotated orientation)
      // For innerText: applyEvenRuleToHeight = true, applyEvenRuleToWidth = false
      const candidateB = processOrientation(
        initialFit,
        bleedDims,
        jobInputs,
        tabId,
        true,  // rotated
        tabId === 'innerText', // applyEvenRuleToHeight
        false  // applyEvenRuleToWidth
      );

      // Select winning candidate based on alignment mode (with fallback)
      const winningCandidate = selectWinningCandidate(
        candidateA,
        candidateB,
        jobInputs.alignmentMode
      );

      if (winningCandidate) {
        // Store bleed dimensions in the candidate
        winningCandidate.layoutPageH = bleedDims.layoutPageH;
        winningCandidate.layoutPageW = bleedDims.layoutPageW;

        // Finalize roll dimensions if needed
        const lip = tabId === 'innerText' ? (jobInputs as InnerTextJobInputs).lip : 0;
        const finalizedCandidate = finalizeRollDimensions(
          winningCandidate,
          jobInputs.gripper,
          jobInputs.colorBar,
          lip
        );

        // Calculate final metrics
        const finalResult = calculateFinalMetrics_ComponentSpecific(
          tabId,
          finalizedCandidate,
          jobInputs,
          benchmarkValue
        );

        results.push(finalResult);
      } else {
        // Only create error result if paper physically cannot fit at all
        if (!candidateA && !candidateB) {
          const errorResult: PaperCandidate = {
            ...paperData,
            ...initialFit,
            layoutPageH: bleedDims.layoutPageH,
            layoutPageW: bleedDims.layoutPageW,
            error: true,
            errorMessageKey: 'errorCannotFit',
            totalCost: Infinity,
            wastePercent: 1.1,
            isOptimalCandidate: false
          };
          results.push(errorResult);
        }
        // Note: Removed errorAlignmentMismatch case - these are now handled as suboptimal
      }
    }

    return {
      results,
      benchmarkValue,
      errors: errors.length > 0 ? errors : undefined
    };

  } catch (error) {
    console.error('Calculation error:', error);
    return {
      results: [],
      benchmarkValue: 0,
      errors: ['Unexpected calculation error occurred']
    };
  }
}

// Helper function to process a single orientation
function processOrientation(
  candidate: PaperCandidate,
  bleedDims: BleedDimensions,
  jobInputs: JobInputs,
  tabId: 'innerText' | 'cover' | 'endpapers',
  forceRotation: boolean,
  applyEvenRuleToHeight: boolean = false,
  applyEvenRuleToWidth: boolean = true
): PaperCandidate | null {
  try {
    let workingCandidate = { ...candidate };

    // Apply rotation if forced
    if (forceRotation && workingCandidate.pressH && workingCandidate.pressW) {
      const temp = workingCandidate.pressH;
      workingCandidate.pressH = workingCandidate.pressW;
      workingCandidate.pressW = temp;
      workingCandidate.paperWasRotated = !workingCandidate.paperWasRotated;
    }

    // Apply press limits and get grain alignment
    workingCandidate = applyPressLimitsAndGetGrain(workingCandidate, jobInputs.alignmentMode);
    if (workingCandidate.error) return null;

    // Calculate usable area
    const lip = tabId === 'innerText' ? (jobInputs as InnerTextJobInputs).lip : 0;
    workingCandidate = calculateUsableArea(
      workingCandidate,
      jobInputs.gripper,
      jobInputs.colorBar,
      lip
    );
    if (workingCandidate.error) return null;

    // Calculate layout fit with even rule parameters
    workingCandidate = calculateSingleLayoutFit_ComponentSpecific(
      workingCandidate,
      bleedDims,
      tabId,
      jobInputs,
      applyEvenRuleToHeight,
      applyEvenRuleToWidth
    );

    if (!workingCandidate || workingCandidate.error) return null;

    // Add layout description key for inner text (matches reference implementation)
    if (tabId === 'innerText' && (workingCandidate.maxItemsPerSide || 0) > 0) {
      if (forceRotation) {
        workingCandidate.layoutDescKey = 'layoutDescSpreadDown';
      } else {
        workingCandidate.layoutDescKey = 'layoutDescSpreadAcross';
      }
    } else {
      workingCandidate.layoutDescKey = '';
    }

    // Add layout orientation identifier
    workingCandidate.layoutOrientation = forceRotation ? 'B' : 'A';

    // Add layout down/across page counts
    workingCandidate.winningLayoutDownPages = workingCandidate.layoutDown;
    workingCandidate.winningLayoutAcrossPages = workingCandidate.layoutAcross;

    return workingCandidate;
  } catch (error) {
    console.error('Error processing orientation:', error);
    return null;
  }
}

// Export sorted results for display
export function getSortedResults(results: PaperCandidate[]) {
  return sortAndCategorizeResults(results);
}
