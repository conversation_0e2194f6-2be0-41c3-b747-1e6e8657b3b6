<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Line Break Preservation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .input-section {
            background-color: #f5f5f5;
        }
        .output-section {
            background-color: #e8f5e8;
        }
        textarea {
            width: 100%;
            height: 150px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
        .formatted-output {
            border: 1px solid #ddd;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            min-height: 100px;
            white-space: pre-wrap;
        }
        .formatted-output-html {
            border: 1px solid #ddd;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            min-height: 100px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px 10px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>Line Break Preservation Test</h1>
    <p>This page tests the line break preservation functionality that was fixed in the FormattedText component.</p>

    <div class="test-container input-section">
        <h2>Interactive Test</h2>
        <p>Enter text with line breaks in the textarea below:</p>
        <textarea id="userInput" placeholder="Enter text with line breaks here...
For example:
Project Overview:
• Phase 1: Planning
• Phase 2: Development

Important Notes:
- Client meeting next week
- Budget pending approval"></textarea>
        <br>
        <button onclick="testFormatting()">Test Formatting</button>
        <button onclick="loadSampleText()">Load Sample Text</button>
    </div>

    <div class="test-container output-section">
        <h2>Results</h2>
        
        <h3>Raw Text (with whitespace-pre-wrap):</h3>
        <div id="rawOutput" class="formatted-output"></div>
        
        <h3>Formatted HTML (with &lt;br&gt; tags):</h3>
        <div id="htmlOutput" class="formatted-output-html"></div>
        
        <h3>HTML Source:</h3>
        <pre id="htmlSource" style="background-color: #f8f8f8; padding: 10px; border-radius: 4px; overflow-x: auto;"></pre>
    </div>

    <div class="test-container">
        <h2>Predefined Test Cases</h2>
        
        <div class="test-case">
            <h3>Test Case 1: Simple Line Breaks</h3>
            <button onclick="runTestCase1()">Run Test</button>
            <div id="testCase1Result"></div>
        </div>
        
        <div class="test-case">
            <h3>Test Case 2: Complex Formatting</h3>
            <button onclick="runTestCase2()">Run Test</button>
            <div id="testCase2Result"></div>
        </div>
        
        <div class="test-case">
            <h3>Test Case 3: Mixed Line Break Types</h3>
            <button onclick="runTestCase3()">Run Test</button>
            <div id="testCase3Result"></div>
        </div>
    </div>

    <script>
        // Simplified version of the formatting functions for testing
        function formatTextForDisplay(text) {
            if (!text || typeof text !== 'string') {
                return '';
            }

            let formatted = text.trim();

            // Preserve line breaks by temporarily replacing them with placeholders
            const withPlaceholders = formatted
                .replace(/\r\n/g, '___CRLF___')
                .replace(/\n/g, '___LF___')
                .replace(/\r/g, '___CR___');

            // Simple sanitization (in real app, use DOMPurify)
            const sanitized = withPlaceholders
                .replace(/<script[^>]*>.*?<\/script>/gi, '')
                .replace(/<[^>]*>/g, '');

            // Restore line breaks and convert them to <br> tags
            const withLineBreaks = sanitized
                .replace(/___CRLF___/g, '<br>')
                .replace(/___LF___/g, '<br>')
                .replace(/___CR___/g, '<br>');

            return withLineBreaks;
        }

        function hasFormatting(text) {
            if (!text || typeof text !== 'string') {
                return false;
            }
            return text.includes('\n') || text.includes('\r') || 
                   /^[\s]*[•\-\*][\s]+/m.test(text) || 
                   /^[\s]*\d+\.[\s]+/m.test(text);
        }

        function testFormatting() {
            const input = document.getElementById('userInput').value;
            
            // Test raw text with whitespace-pre-wrap
            document.getElementById('rawOutput').textContent = input;
            
            // Test formatted HTML
            const formatted = formatTextForDisplay(input);
            document.getElementById('htmlOutput').innerHTML = formatted;
            document.getElementById('htmlSource').textContent = formatted;
        }

        function loadSampleText() {
            const sampleText = `Project Overview:
• Phase 1: Planning
• Phase 2: Development

Important Notes:
- Client meeting next week
- Budget pending approval

Additional Details:
1. Timeline: 6 months
2. Budget: $50,000
3. Team size: 5 people`;
            
            document.getElementById('userInput').value = sampleText;
            testFormatting();
        }

        function runTestCase1() {
            const testText = "Line 1\nLine 2\nLine 3";
            const result = formatTextForDisplay(testText);
            const expected = "Line 1<br>Line 2<br>Line 3";
            const passed = result === expected;
            
            document.getElementById('testCase1Result').innerHTML = `
                <p><strong>Input:</strong> ${JSON.stringify(testText)}</p>
                <p><strong>Expected:</strong> ${expected}</p>
                <p><strong>Actual:</strong> ${result}</p>
                <p><strong>Result:</strong> <span style="color: ${passed ? 'green' : 'red'}">${passed ? 'PASS' : 'FAIL'}</span></p>
                <div style="border: 1px solid #ddd; padding: 10px; margin-top: 10px;">
                    <strong>Rendered:</strong><br>
                    <div style="border: 1px solid #ccc; padding: 5px; margin-top: 5px;">${result}</div>
                </div>
            `;
        }

        function runTestCase2() {
            const testText = `Project Overview:
• Phase 1: Planning
• Phase 2: Development

Important Notes:
- Client meeting next week
- Budget pending approval`;
            
            const result = formatTextForDisplay(testText);
            const hasLineBreaks = result.includes('<br>');
            
            document.getElementById('testCase2Result').innerHTML = `
                <p><strong>Input:</strong></p>
                <pre style="background-color: #f8f8f8; padding: 5px;">${testText}</pre>
                <p><strong>Has line breaks:</strong> <span style="color: ${hasLineBreaks ? 'green' : 'red'}">${hasLineBreaks ? 'YES' : 'NO'}</span></p>
                <p><strong>Formatted result:</strong></p>
                <div style="border: 1px solid #ccc; padding: 10px; margin-top: 5px;">${result}</div>
            `;
        }

        function runTestCase3() {
            const testText = "Line 1\r\nLine 2\rLine 3\nLine 4";
            const result = formatTextForDisplay(testText);
            const expected = "Line 1<br>Line 2<br>Line 3<br>Line 4";
            const passed = result === expected;
            
            document.getElementById('testCase3Result').innerHTML = `
                <p><strong>Input:</strong> ${JSON.stringify(testText)}</p>
                <p><strong>Expected:</strong> ${expected}</p>
                <p><strong>Actual:</strong> ${result}</p>
                <p><strong>Result:</strong> <span style="color: ${passed ? 'green' : 'red'}">${passed ? 'PASS' : 'FAIL'}</span></p>
                <div style="border: 1px solid #ddd; padding: 10px; margin-top: 10px;">
                    <strong>Rendered:</strong><br>
                    <div style="border: 1px solid #ccc; padding: 5px; margin-top: 5px;">${result}</div>
                </div>
            `;
        }

        // Load sample text on page load
        window.onload = function() {
            loadSampleText();
        };
    </script>
</body>
</html>
