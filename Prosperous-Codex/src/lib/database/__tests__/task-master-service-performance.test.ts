import { TaskMasterService } from '../task-master-service';
import Database from 'better-sqlite3';

describe('TaskMasterService Performance Tests', () => {
  let db: Database.Database;
  let service: TaskMasterService;

  beforeAll(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Initialize schema
    const schema = `
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        full_description TEXT,
        status TEXT CHECK(status IN ('todo', 'inProgress', 'completed')) DEFAULT 'todo',
        priority TEXT CHECK(priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
        progress INTEGER DEFAULT 0 CHECK(progress >= 0 AND progress <= 100),
        due_date DATETIME,
        created_by INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users (id)
      );

      CREATE INDEX idx_projects_created_by ON projects(created_by);
      CREATE INDEX idx_projects_status ON projects(status);
      CREATE INDEX idx_projects_priority ON projects(priority);
      CREATE INDEX idx_projects_due_date ON projects(due_date);
      CREATE INDEX idx_projects_updated_at ON projects(updated_at);
      CREATE INDEX idx_projects_progress ON projects(progress);
    `;
    
    db.exec(schema);
    
    // Create test user
    db.prepare('INSERT INTO users (email, password_hash) VALUES (?, ?)').run(
      '<EMAIL>',
      'hashed_password'
    );
    
    service = new TaskMasterService(db);
  });

  afterAll(() => {
    db.close();
  });

  const createTestProjects = (count: number, userId: number) => {
    const priorities = ['low', 'medium', 'high'];
    const statuses = ['todo', 'inProgress', 'completed'];
    
    const stmt = db.prepare(`
      INSERT INTO projects (title, description, status, priority, progress, due_date, created_by, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const projects = [];
    for (let i = 0; i < count; i++) {
      const priority = priorities[i % priorities.length];
      const status = statuses[i % statuses.length];
      const progress = Math.floor(Math.random() * 101);
      const dueDate = new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString();
      const updatedAt = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString();
      
      stmt.run(
        `Test Project ${i + 1}`,
        `Description for project ${i + 1}`,
        status,
        priority,
        progress,
        dueDate,
        userId,
        updatedAt
      );
      
      projects.push({
        id: i + 1,
        title: `Test Project ${i + 1}`,
        priority,
        status,
        progress,
        dueDate,
        updatedAt
      });
    }
    
    return projects;
  };

  describe('Filter Performance with Large Datasets', () => {
    beforeEach(() => {
      // Clear projects table
      db.prepare('DELETE FROM projects').run();
    });

    it('should handle 100 projects with priority filter in under 100ms', () => {
      createTestProjects(100, 1);
      
      const startTime = performance.now();
      
      const results = service.getUserProjects(1, {
        sortBy: 'priority',
        order: 'asc',
        limit: 50
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(100);
      expect(results).toBeDefined();
      expect(results.length).toBeLessThanOrEqual(50);
    });

    it('should handle 500 projects with multiple filters in under 200ms', () => {
      createTestProjects(500, 1);
      
      const startTime = performance.now();
      
      const results = service.getUserProjects(1, {
        sortBy: 'priority,dueDate,lastUpdated',
        order: 'desc',
        limit: 20
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(200);
      expect(results).toBeDefined();
      expect(results.length).toBeLessThanOrEqual(20);
    });

    it('should handle 1000 projects with completion filter in under 300ms', () => {
      createTestProjects(1000, 1);
      
      const startTime = performance.now();
      
      const results = service.getUserProjects(1, {
        sortBy: 'completion',
        order: 'asc',
        limit: 100
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(300);
      expect(results).toBeDefined();
      expect(results.length).toBeLessThanOrEqual(100);
    });

    it('should maintain performance with pagination', () => {
      createTestProjects(1000, 1);
      
      const durations: number[] = [];
      
      // Test multiple pages
      for (let page = 0; page < 5; page++) {
        const startTime = performance.now();
        
        const results = service.getUserProjects(1, {
          sortBy: 'priority,dueDate',
          order: 'desc',
          limit: 20,
          offset: page * 20
        });
        
        const endTime = performance.now();
        durations.push(endTime - startTime);
        
        expect(results).toBeDefined();
        expect(results.length).toBeLessThanOrEqual(20);
      }
      
      // All page loads should be fast
      durations.forEach(duration => {
        expect(duration).toBeLessThan(150);
      });
      
      // Performance should be consistent across pages
      const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
      const maxDuration = Math.max(...durations);
      const minDuration = Math.min(...durations);
      
      // Variance should be reasonable (max shouldn't be more than 3x min)
      expect(maxDuration / minDuration).toBeLessThan(3);
    });

    it('should handle concurrent filter requests efficiently', async () => {
      createTestProjects(500, 1);
      
      const concurrentRequests = 10;
      const promises: Promise<any>[] = [];
      
      const startTime = performance.now();
      
      for (let i = 0; i < concurrentRequests; i++) {
        const promise = new Promise((resolve) => {
          const results = service.getUserProjects(1, {
            sortBy: i % 2 === 0 ? 'priority' : 'dueDate',
            order: i % 2 === 0 ? 'asc' : 'desc',
            limit: 20,
            offset: i * 20
          });
          resolve(results);
        });
        promises.push(promise);
      }
      
      const results = await Promise.all(promises);
      
      const endTime = performance.now();
      const totalDuration = endTime - startTime;
      
      // All concurrent requests should complete in reasonable time
      expect(totalDuration).toBeLessThan(500);
      
      // All requests should return results
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
      });
    });

    it('should optimize memory usage with large result sets', () => {
      createTestProjects(1000, 1);
      
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform multiple filter operations
      for (let i = 0; i < 10; i++) {
        const results = service.getUserProjects(1, {
          sortBy: 'priority,dueDate,lastUpdated,completion',
          order: 'desc',
          limit: 100,
          offset: i * 100
        });
        
        expect(results).toBeDefined();
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });

    it('should handle edge cases efficiently', () => {
      createTestProjects(100, 1);
      
      // Test empty results
      const startTime1 = performance.now();
      const emptyResults = service.getUserProjects(999, { // Non-existent user
        sortBy: 'priority',
        order: 'asc'
      });
      const duration1 = performance.now() - startTime1;
      
      expect(duration1).toBeLessThan(50);
      expect(emptyResults).toEqual([]);
      
      // Test large offset
      const startTime2 = performance.now();
      const offsetResults = service.getUserProjects(1, {
        sortBy: 'priority',
        order: 'asc',
        limit: 10,
        offset: 1000 // Beyond available data
      });
      const duration2 = performance.now() - startTime2;
      
      expect(duration2).toBeLessThan(50);
      expect(offsetResults).toEqual([]);
    });
  });

  describe('Index Effectiveness', () => {
    beforeEach(() => {
      db.prepare('DELETE FROM projects').run();
      createTestProjects(1000, 1);
    });

    it('should use indexes for priority sorting', () => {
      const explain = db.prepare(`
        EXPLAIN QUERY PLAN 
        SELECT * FROM projects 
        WHERE created_by = ? 
        ORDER BY CASE priority 
          WHEN 'high' THEN 1 
          WHEN 'medium' THEN 2 
          WHEN 'low' THEN 3 
          ELSE 4 
        END ASC
        LIMIT 20
      `).all(1);
      
      // Should use index for created_by
      const hasCreatedByIndex = explain.some(row => 
        row.detail.includes('idx_projects_created_by')
      );
      expect(hasCreatedByIndex).toBe(true);
    });

    it('should use indexes for due date sorting', () => {
      const explain = db.prepare(`
        EXPLAIN QUERY PLAN 
        SELECT * FROM projects 
        WHERE created_by = ? 
        ORDER BY due_date IS NULL, due_date ASC
        LIMIT 20
      `).all(1);
      
      // Should use appropriate indexes
      const usesIndex = explain.some(row => 
        row.detail.includes('idx_projects') || row.detail.includes('USING INDEX')
      );
      expect(usesIndex).toBe(true);
    });
  });
});
