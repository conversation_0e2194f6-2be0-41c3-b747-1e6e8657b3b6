#!/usr/bin/env node

/**
 * Database Schema Validation Script
 * 
 * Validates database schema consistency and field naming conventions
 */

const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');

/**
 * Logger utility
 */
const logger = {
  info: (message) => console.log(`ℹ️  ${message}`),
  success: (message) => console.log(`✅ ${message}`),
  error: (message) => console.error(`❌ ${message}`),
  warn: (message) => console.warn(`⚠️  ${message}`),
};

/**
 * Get database schema information
 */
function getDatabaseSchema(dbPath) {
  if (!fs.existsSync(dbPath)) {
    throw new Error(`Database file not found: ${dbPath}`);
  }

  const db = new Database(dbPath, { readonly: true });
  
  try {
    // Get all tables
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `).all();

    const schema = {};

    for (const table of tables) {
      // Get table info
      const columns = db.prepare(`PRAGMA table_info(${table.name})`).all();
      
      // Get foreign keys
      const foreignKeys = db.prepare(`PRAGMA foreign_key_list(${table.name})`).all();
      
      // Get indexes
      const indexes = db.prepare(`PRAGMA index_list(${table.name})`).all();

      schema[table.name] = {
        columns: columns.map(col => ({
          name: col.name,
          type: col.type,
          nullable: !col.notnull,
          defaultValue: col.dflt_value,
          primaryKey: col.pk === 1
        })),
        foreignKeys: foreignKeys.map(fk => ({
          column: fk.from,
          referencedTable: fk.table,
          referencedColumn: fk.to
        })),
        indexes: indexes.map(idx => ({
          name: idx.name,
          unique: idx.unique === 1
        }))
      };
    }

    return schema;
  } finally {
    db.close();
  }
}

/**
 * Validate table and column naming conventions
 */
function validateNamingConventions(schema) {
  const errors = [];
  const warnings = [];

  for (const [tableName, tableInfo] of Object.entries(schema)) {
    // Validate table name (should be snake_case, plural)
    if (!/^[a-z][a-z0-9_]*$/.test(tableName)) {
      errors.push(`Table '${tableName}' does not follow snake_case convention`);
    }

    // Validate column names
    for (const column of tableInfo.columns) {
      if (!/^[a-z][a-z0-9_]*$/.test(column.name)) {
        errors.push(`Column '${tableName}.${column.name}' does not follow snake_case convention`);
      }

      // Check for common naming patterns
      if (column.name.endsWith('_id') && !column.name.includes('_')) {
        warnings.push(`Column '${tableName}.${column.name}' might be a foreign key but doesn't follow naming pattern`);
      }

      if (column.name.includes('date') && !column.name.endsWith('_at') && !column.name.endsWith('_date')) {
        warnings.push(`Date column '${tableName}.${column.name}' should end with '_at' or '_date'`);
      }
    }
  }

  return { errors, warnings };
}

/**
 * Validate foreign key relationships
 */
function validateForeignKeys(schema) {
  const errors = [];
  const warnings = [];

  for (const [tableName, tableInfo] of Object.entries(schema)) {
    for (const fk of tableInfo.foreignKeys) {
      // Check if referenced table exists
      if (!schema[fk.referencedTable]) {
        errors.push(`Foreign key '${tableName}.${fk.column}' references non-existent table '${fk.referencedTable}'`);
        continue;
      }

      // Check if referenced column exists
      const referencedTable = schema[fk.referencedTable];
      const referencedColumn = referencedTable.columns.find(col => col.name === fk.referencedColumn);
      
      if (!referencedColumn) {
        errors.push(`Foreign key '${tableName}.${fk.column}' references non-existent column '${fk.referencedTable}.${fk.referencedColumn}'`);
      }

      // Check naming convention for foreign keys
      if (!fk.column.endsWith('_id')) {
        warnings.push(`Foreign key column '${tableName}.${fk.column}' should end with '_id'`);
      }
    }
  }

  return { errors, warnings };
}

/**
 * Validate required columns
 */
function validateRequiredColumns(schema) {
  const errors = [];
  const warnings = [];

  const requiredColumns = {
    // All tables should have these
    '*': ['id', 'created_at', 'updated_at'],
    
    // Specific table requirements
    'projects': ['title', 'status', 'priority', 'progress', 'created_by'],
    'tasks': ['title', 'status', 'priority', 'progress', 'project_id', 'created_by'],
    'comments': ['content', 'task_id', 'created_by'],
    'project_team_members': ['project_id', 'user_id', 'role'],
    'activity_log': ['activity_type', 'entity_type', 'entity_id', 'project_id', 'performed_by', 'performed_at']
  };

  for (const [tableName, tableInfo] of Object.entries(schema)) {
    const columnNames = tableInfo.columns.map(col => col.name);

    // Check universal required columns
    for (const requiredCol of requiredColumns['*']) {
      if (!columnNames.includes(requiredCol)) {
        errors.push(`Table '${tableName}' missing required column '${requiredCol}'`);
      }
    }

    // Check table-specific required columns
    if (requiredColumns[tableName]) {
      for (const requiredCol of requiredColumns[tableName]) {
        if (!columnNames.includes(requiredCol)) {
          errors.push(`Table '${tableName}' missing required column '${requiredCol}'`);
        }
      }
    }
  }

  return { errors, warnings };
}

/**
 * Validate data types
 */
function validateDataTypes(schema) {
  const errors = [];
  const warnings = [];

  const typePatterns = {
    '_id': ['INTEGER'],
    '_at': ['TEXT', 'DATETIME'],
    '_date': ['TEXT', 'DATETIME'],
    'progress': ['INTEGER'],
    'status': ['TEXT'],
    'priority': ['TEXT'],
    'title': ['TEXT'],
    'description': ['TEXT'],
    'content': ['TEXT']
  };

  for (const [tableName, tableInfo] of Object.entries(schema)) {
    for (const column of tableInfo.columns) {
      // Check type patterns
      for (const [suffix, expectedTypes] of Object.entries(typePatterns)) {
        if (column.name.endsWith(suffix) || column.name === suffix.substring(1)) {
          if (!expectedTypes.includes(column.type)) {
            warnings.push(`Column '${tableName}.${column.name}' has type '${column.type}', expected one of: ${expectedTypes.join(', ')}`);
          }
        }
      }

      // Check for common issues
      if (column.name === 'id' && column.type !== 'INTEGER') {
        errors.push(`Primary key '${tableName}.id' should be INTEGER, got '${column.type}'`);
      }

      if (column.name === 'id' && !column.primaryKey) {
        errors.push(`Column '${tableName}.id' should be primary key`);
      }
    }
  }

  return { errors, warnings };
}

/**
 * Generate validation report
 */
function generateReport(results) {
  const totalErrors = results.reduce((sum, result) => sum + result.errors.length, 0);
  const totalWarnings = results.reduce((sum, result) => sum + result.warnings.length, 0);

  console.log('\n📊 Database Schema Validation Report');
  console.log('====================================\n');

  for (const result of results) {
    if (result.section) {
      console.log(`\n🔍 ${result.section}`);
      console.log('-'.repeat(result.section.length + 4));
    }

    if (result.errors.length > 0) {
      console.log('\n❌ Errors:');
      result.errors.forEach(error => console.log(`   • ${error}`));
    }

    if (result.warnings.length > 0) {
      console.log('\n⚠️  Warnings:');
      result.warnings.forEach(warning => console.log(`   • ${warning}`));
    }

    if (result.errors.length === 0 && result.warnings.length === 0) {
      console.log('   ✅ No issues found');
    }
  }

  console.log('\n📈 Summary');
  console.log('===========');
  console.log(`Total Errors: ${totalErrors}`);
  console.log(`Total Warnings: ${totalWarnings}`);

  if (totalErrors === 0) {
    logger.success('Schema validation passed!');
  } else {
    logger.error(`Schema validation failed with ${totalErrors} errors`);
  }

  return totalErrors === 0;
}

/**
 * Main validation function
 */
function validateSchema() {
  try {
    logger.info('Starting database schema validation...');

    // Find database file
    const dbPath = path.join(__dirname, '../data/prosperous-codex.db');
    
    if (!fs.existsSync(dbPath)) {
      logger.warn('Database file not found, creating temporary database for validation...');
      
      // Create temporary database with schema
      const tempDb = new Database(':memory:');
      
      // Read and execute schema from init script
      const initScript = path.join(__dirname, 'init-db.js');
      if (fs.existsSync(initScript)) {
        // This is a simplified approach - in practice, you'd extract the schema
        logger.warn('Using in-memory database for validation');
        tempDb.close();
        return;
      } else {
        throw new Error('No database found and no init script available');
      }
    }

    // Get schema
    const schema = getDatabaseSchema(dbPath);
    const tableCount = Object.keys(schema).length;
    logger.info(`Analyzing ${tableCount} tables...`);

    // Run validations
    const results = [
      {
        section: 'Naming Conventions',
        ...validateNamingConventions(schema)
      },
      {
        section: 'Foreign Key Relationships',
        ...validateForeignKeys(schema)
      },
      {
        section: 'Required Columns',
        ...validateRequiredColumns(schema)
      },
      {
        section: 'Data Types',
        ...validateDataTypes(schema)
      }
    ];

    // Generate report
    const success = generateReport(results);

    if (!success) {
      process.exit(1);
    }

  } catch (error) {
    logger.error(`Schema validation failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  validateSchema();
}

module.exports = { validateSchema };
