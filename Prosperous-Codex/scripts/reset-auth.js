#!/usr/bin/env node

/**
 * Reset authentication and create fresh users
 * This script will reset the database and create new users with known passwords
 */

const path = require('path');
const fs = require('fs');

// Set up the environment
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

async function resetAuthentication() {
  try {
    console.log('🔄 Resetting authentication system...');
    
    // Import database modules
    const Database = require('better-sqlite3');
    const bcrypt = require('bcryptjs');
    
    // Database path
    const dbPath = path.join(process.cwd(), 'data', 'prosperous-codex.db');
    
    // Create backup first
    if (fs.existsSync(dbPath)) {
      const backupPath = `${dbPath}.backup.${Date.now()}`;
      fs.copyFileSync(dbPath, backupPath);
      console.log(`📦 Backup created: ${backupPath}`);
    }
    
    // Initialize database
    const db = new Database(dbPath);

    // Disable foreign key constraints temporarily
    db.pragma('foreign_keys = OFF');

    // Clear existing users and related data
    console.log('🗑️ Clearing existing users and related data...');
    db.prepare('DELETE FROM user_sessions').run();
    db.prepare('DELETE FROM user_preferences').run();
    db.prepare('DELETE FROM access_requests').run();
    db.prepare('DELETE FROM users').run();

    // Re-enable foreign key constraints
    db.pragma('foreign_keys = ON');
    
    // Create admin user
    console.log('👤 Creating admin user...');
    const adminPasswordHash = await bcrypt.hash('admin123', 12);
    db.prepare(`
      INSERT INTO users (email, username, password_hash, role, is_active, created_at)
      VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `).run('<EMAIL>', 'admin', adminPasswordHash, 'admin', 1);
    
    // Create moderator user
    console.log('👤 Creating moderator user...');
    const moderatorPasswordHash = await bcrypt.hash('moderator123', 12);
    db.prepare(`
      INSERT INTO users (email, username, password_hash, role, is_active, created_at)
      VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `).run('<EMAIL>', 'moderator', moderatorPasswordHash, 'moderator', 1);
    
    // Create test user
    console.log('👤 Creating test user...');
    const userPasswordHash = await bcrypt.hash('user123', 12);
    db.prepare(`
      INSERT INTO users (email, username, password_hash, role, is_active, created_at)
      VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `).run('<EMAIL>', 'testuser', userPasswordHash, 'user', 1);
    
    // Verify users were created
    const users = db.prepare('SELECT email, username, role FROM users').all();
    console.log('✅ Users created successfully:');
    users.forEach(user => {
      console.log(`  - ${user.email} (${user.username}) - Role: ${user.role}`);
    });
    
    db.close();
    
    console.log('');
    console.log('🎉 Authentication reset complete!');
    console.log('');
    console.log('📝 New login credentials:');
    console.log('  Admin:     <EMAIL> / admin123');
    console.log('  Moderator: <EMAIL> / moderator123');
    console.log('  User:      <EMAIL> / user123');
    console.log('');
    console.log('⚠️  Please change these passwords in production!');
    
  } catch (error) {
    console.error('❌ Error during authentication reset:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  resetAuthentication();
}

module.exports = { resetAuthentication };
