"use client";

import { useState, useCallback, useRef } from 'react';
import { FilterState } from '@/components/task-master/filter-dropdown';

interface CacheEntry {
  data: any[];
  timestamp: number;
  filters: FilterState;
}

interface UseFilterCacheOptions {
  maxCacheSize?: number;
  cacheExpiryMs?: number;
}

export function useFilterCache(options: UseFilterCacheOptions = {}) {
  const { maxCacheSize = 10, cacheExpiryMs = 5 * 60 * 1000 } = options; // 5 minutes default
  
  const cacheRef = useRef<Map<string, CacheEntry>>(new Map());
  const [cacheStats, setCacheStats] = useState({
    hits: 0,
    misses: 0,
    size: 0
  });

  // Generate cache key from filters
  const generateCacheKey = useCallback((filters: FilterState): string => {
    const sortedEntries = Object.entries(filters)
      .filter(([_, value]) => value !== undefined && value !== false)
      .sort(([a], [b]) => a.localeCompare(b));
    
    return JSON.stringify(sortedEntries);
  }, []);

  // Check if cache entry is valid
  const isValidCacheEntry = useCallback((entry: CacheEntry): boolean => {
    const now = Date.now();
    return (now - entry.timestamp) < cacheExpiryMs;
  }, [cacheExpiryMs]);

  // Get cached data
  const getCachedData = useCallback((filters: FilterState): any[] | null => {
    const key = generateCacheKey(filters);
    const entry = cacheRef.current.get(key);
    
    if (entry && isValidCacheEntry(entry)) {
      setCacheStats(prev => ({ ...prev, hits: prev.hits + 1 }));
      return entry.data;
    }
    
    // Remove expired entry if it exists
    if (entry) {
      cacheRef.current.delete(key);
      setCacheStats(prev => ({ ...prev, size: prev.size - 1 }));
    }
    
    setCacheStats(prev => ({ ...prev, misses: prev.misses + 1 }));
    return null;
  }, [generateCacheKey, isValidCacheEntry]);

  // Set cached data
  const setCachedData = useCallback((filters: FilterState, data: any[]): void => {
    const key = generateCacheKey(filters);
    const entry: CacheEntry = {
      data: [...data], // Create a copy to prevent mutations
      timestamp: Date.now(),
      filters: { ...filters }
    };

    // Implement LRU eviction if cache is full
    if (cacheRef.current.size >= maxCacheSize && !cacheRef.current.has(key)) {
      // Remove oldest entry
      const oldestKey = cacheRef.current.keys().next().value;
      if (oldestKey) {
        cacheRef.current.delete(oldestKey);
        setCacheStats(prev => ({ ...prev, size: prev.size - 1 }));
      }
    }

    cacheRef.current.set(key, entry);
    setCacheStats(prev => ({ 
      ...prev, 
      size: cacheRef.current.size 
    }));
  }, [generateCacheKey, maxCacheSize]);

  // Clear cache
  const clearCache = useCallback(() => {
    cacheRef.current.clear();
    setCacheStats({ hits: 0, misses: 0, size: 0 });
  }, []);

  // Clear expired entries
  const clearExpiredEntries = useCallback(() => {
    const now = Date.now();
    let removedCount = 0;
    
    for (const [key, entry] of cacheRef.current.entries()) {
      if ((now - entry.timestamp) >= cacheExpiryMs) {
        cacheRef.current.delete(key);
        removedCount++;
      }
    }
    
    if (removedCount > 0) {
      setCacheStats(prev => ({ 
        ...prev, 
        size: prev.size - removedCount 
      }));
    }
  }, [cacheExpiryMs]);

  // Get cache hit ratio
  const getCacheHitRatio = useCallback((): number => {
    const total = cacheStats.hits + cacheStats.misses;
    return total > 0 ? cacheStats.hits / total : 0;
  }, [cacheStats]);

  // Prefetch data for likely filter combinations
  const prefetchData = useCallback(async (
    filterCombinations: FilterState[],
    fetchFunction: (filters: FilterState) => Promise<any[]>
  ): Promise<void> => {
    const prefetchPromises = filterCombinations.map(async (filters) => {
      const key = generateCacheKey(filters);
      
      // Only prefetch if not already cached
      if (!cacheRef.current.has(key)) {
        try {
          const data = await fetchFunction(filters);
          setCachedData(filters, data);
        } catch (error) {
          console.warn('Prefetch failed for filters:', filters, error);
        }
      }
    });

    await Promise.allSettled(prefetchPromises);
  }, [generateCacheKey, setCachedData]);

  return {
    getCachedData,
    setCachedData,
    clearCache,
    clearExpiredEntries,
    getCacheHitRatio,
    prefetchData,
    cacheStats
  };
}
