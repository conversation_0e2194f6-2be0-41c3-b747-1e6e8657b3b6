/**
 * Type Utilities
 * 
 * Common type utilities and helpers
 */

/**
 * Make all properties optional recursively
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * Make all properties required recursively
 */
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

/**
 * Extract keys of a specific type
 */
export type KeysOfType<T, U> = {
  [K in keyof T]: T[K] extends U ? K : never;
}[keyof T];

/**
 * Omit properties by type
 */
export type OmitByType<T, U> = Omit<T, KeysOfType<T, U>>;

/**
 * Pick properties by type
 */
export type PickByType<T, U> = Pick<T, KeysOfType<T, U>>;

/**
 * Make specific properties optional
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Make specific properties required
 */
export type RequiredBy<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;

/**
 * Flatten nested object types
 */
export type Flatten<T> = T extends object ? {
  [K in keyof T]: T[K];
} : T;

/**
 * Get the type of array elements
 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never;

/**
 * Get the return type of a promise
 */
export type PromiseType<T> = T extends Promise<infer U> ? U : never;

/**
 * Create a union of all possible paths in an object
 */
export type Paths<T> = T extends object ? {
  [K in keyof T]: K extends string ? 
    T[K] extends object ? 
      K | `${K}.${Paths<T[K]>}` 
      : K 
    : never;
}[keyof T] : never;

/**
 * Get the type at a specific path
 */
export type PathValue<T, P extends string> = 
  P extends `${infer K}.${infer Rest}` 
    ? K extends keyof T 
      ? PathValue<T[K], Rest>
      : never
    : P extends keyof T 
      ? T[P] 
      : never;

/**
 * Create a type with all string keys
 */
export type StringKeys<T> = Extract<keyof T, string>;

/**
 * Create a type with all number keys
 */
export type NumberKeys<T> = Extract<keyof T, number>;

/**
 * Create a type with all symbol keys
 */
export type SymbolKeys<T> = Extract<keyof T, symbol>;

/**
 * Check if a type is never
 */
export type IsNever<T> = [T] extends [never] ? true : false;

/**
 * Check if a type is any
 */
export type IsAny<T> = 0 extends (1 & T) ? true : false;

/**
 * Check if a type is unknown
 */
export type IsUnknown<T> = IsAny<T> extends true ? false : unknown extends T ? true : false;

/**
 * Create a branded type
 */
export type Brand<T, B> = T & { __brand: B };

/**
 * Extract the brand from a branded type
 */
export type UnBrand<T> = T extends Brand<infer U, any> ? U : T;

/**
 * Create a nominal type
 */
export type Nominal<T, N extends string> = T & { readonly __nominal: N };

/**
 * Function type utilities
 */
export type FunctionKeys<T> = KeysOfType<T, Function>;
export type NonFunctionKeys<T> = Exclude<keyof T, FunctionKeys<T>>;
export type FunctionProperties<T> = Pick<T, FunctionKeys<T>>;
export type NonFunctionProperties<T> = Pick<T, NonFunctionKeys<T>>;

/**
 * Async function utilities
 */
export type AsyncFunction<T extends any[] = any[], R = any> = (...args: T) => Promise<R>;
export type SyncFunction<T extends unknown[] = unknown[], R = unknown> = (...args: T) => R;

/**
 * Constructor type
 */
export type Constructor<T = Record<string, unknown>> = new (...args: unknown[]) => T;

/**
 * Abstract constructor type
 */
export type AbstractConstructor<T = Record<string, unknown>> = abstract new (...args: unknown[]) => T;

/**
 * Mixin type
 */
export type Mixin<T extends Constructor> = T & Constructor;

/**
 * Event handler type
 */
export type EventHandler<T = any> = (event: T) => void | Promise<void>;

/**
 * Callback type
 */
export type Callback<T = any, R = void> = (data: T) => R;

/**
 * Predicate type
 */
export type Predicate<T = any> = (value: T) => boolean;

/**
 * Comparator type
 */
export type Comparator<T = any> = (a: T, b: T) => number;

/**
 * Transformer type
 */
export type Transformer<T, U> = (input: T) => U;

/**
 * Validator type
 */
export type Validator<T> = (value: T) => boolean | string | string[];

/**
 * Serializer type
 */
export type Serializer<T> = {
  serialize: (value: T) => string;
  deserialize: (value: string) => T;
};

/**
 * Type guard utilities
 */
export type TypeGuard<T> = (value: unknown) => value is T;

/**
 * Create a type guard for a specific type
 */
export function createTypeGuard<T>(
  predicate: (value: unknown) => boolean
): TypeGuard<T> {
  return (value: unknown): value is T => predicate(value);
}

/**
 * Utility to check if value is of specific type
 */
export function isOfType<T>(
  value: unknown,
  guard: TypeGuard<T>
): value is T {
  return guard(value);
}

/**
 * Utility to assert type
 */
export function assertType<T>(
  value: unknown,
  guard: TypeGuard<T>,
  message?: string
): asserts value is T {
  if (!guard(value)) {
    throw new Error(message || `Type assertion failed`);
  }
}

/**
 * Utility to safely cast type
 */
export function safeCast<T>(
  value: unknown,
  guard: TypeGuard<T>
): T | null {
  return guard(value) ? value : null;
}

/**
 * Utility to create a readonly version of a type
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * Utility to create a mutable version of a readonly type
 */
export type Mutable<T> = {
  -readonly [P in keyof T]: T[P];
};

/**
 * Utility to create a deep mutable version
 */
export type DeepMutable<T> = {
  -readonly [P in keyof T]: T[P] extends object ? DeepMutable<T[P]> : T[P];
};
