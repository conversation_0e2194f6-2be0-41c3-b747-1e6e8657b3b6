/**
 * Database Schema Validator for Task Master
 * 
 * Validates database schema consistency and field name conventions
 * to ensure proper mapping between database and API layers.
 */

import Database from 'better-sqlite3';
import { FieldMapper, REVERSE_FIELD_MAPPINGS } from './field-mapping';
import { ValidationError } from './errors';

/**
 * Schema validation result
 */
export interface SchemaValidationResult {
  valid: boolean;
  tableName: string;
  errors: string[];
  warnings: string[];
  suggestions: string[];
  fieldInfo: FieldInfo[];
}

/**
 * Field information from database schema
 */
export interface FieldInfo {
  name: string;
  type: string;
  nullable: boolean;
  defaultValue: any;
  primaryKey: boolean;
  hasMapping: boolean;
  suggestedApiName?: string;
}

/**
 * Consistency report for entire schema
 */
export interface ConsistencyReport {
  overallValid: boolean;
  tablesChecked: number;
  totalFields: number;
  mappedFields: number;
  unmappedFields: number;
  inconsistencies: SchemaInconsistency[];
  recommendations: string[];
  summary: {
    projects: SchemaValidationResult;
    tasks: SchemaValidationResult;
    comments: SchemaValidationResult;
    files: SchemaValidationResult;
    project_team_members: SchemaValidationResult;
    activity_log: SchemaValidationResult;
  };
}

/**
 * Schema inconsistency details
 */
export interface SchemaInconsistency {
  table: string;
  field: string;
  issue: string;
  severity: 'error' | 'warning';
  suggestion?: string;
}

/**
 * Migration plan for schema changes
 */
export interface MigrationPlan {
  requiredChanges: MigrationChange[];
  optionalChanges: MigrationChange[];
  riskLevel: 'low' | 'medium' | 'high';
  estimatedEffort: string;
}

/**
 * Individual migration change
 */
export interface MigrationChange {
  type: 'add_mapping' | 'rename_field' | 'add_field' | 'remove_field';
  table: string;
  field: string;
  description: string;
  sqlCommand?: string;
  riskLevel: 'low' | 'medium' | 'high';
}

/**
 * Database Schema Validator Class
 */
export class SchemaValidator {
  private db: Database.Database;

  constructor(database: Database.Database) {
    this.db = database;
  }

  /**
   * Validate schema for a specific table
   */
  validateTableSchema(tableName: string): SchemaValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];
    const fieldInfo: FieldInfo[] = [];

    try {
      // Get table schema information
      const tableInfo = this.getTableInfo(tableName);
      
      if (!tableInfo.length) {
        errors.push(`Table '${tableName}' does not exist`);
        return {
          valid: false,
          tableName,
          errors,
          warnings,
          suggestions,
          fieldInfo: [],
        };
      }

      // Analyze each field
      for (const column of tableInfo) {
        const hasMapping = column.name in REVERSE_FIELD_MAPPINGS;
        const suggestedApiName = hasMapping 
          ? REVERSE_FIELD_MAPPINGS[column.name]
          : this.suggestApiFieldName(column.name);

        fieldInfo.push({
          name: column.name,
          type: column.type,
          nullable: !column.notnull,
          defaultValue: column.dflt_value,
          primaryKey: column.pk === 1,
          hasMapping,
          suggestedApiName: hasMapping ? undefined : suggestedApiName,
        });

        // Check for naming convention issues
        if (this.shouldBeMapped(column.name) && !hasMapping) {
          warnings.push(`Field '${column.name}' uses snake_case but has no API mapping`);
          suggestions.push(`Add mapping: ${suggestedApiName} -> ${column.name}`);
        }

        // Check for potential issues
        if (column.name.includes('__')) {
          warnings.push(`Field '${column.name}' contains double underscores`);
        }

        if (column.name.length > 50) {
          warnings.push(`Field '${column.name}' has very long name (${column.name.length} chars)`);
        }
      }

      return {
        valid: errors.length === 0,
        tableName,
        errors,
        warnings,
        suggestions,
        fieldInfo,
      };

    } catch (error) {
      errors.push(`Failed to validate table schema: ${error instanceof Error ? error.message : String(error)}`);
      return {
        valid: false,
        tableName,
        errors,
        warnings,
        suggestions,
        fieldInfo: [],
      };
    }
  }

  /**
   * Check field name consistency across all tables
   */
  checkFieldNameConsistency(): ConsistencyReport {
    const tables = ['projects', 'tasks', 'comments', 'files', 'project_team_members', 'activity_log'];
    const inconsistencies: SchemaInconsistency[] = [];
    const recommendations: string[] = [];
    
    let totalFields = 0;
    let mappedFields = 0;
    let unmappedFields = 0;

    // Validate each table
    const summary = {} as ConsistencyReport['summary'];
    
    for (const table of tables) {
      const result = this.validateTableSchema(table);
      summary[table as keyof typeof summary] = result;
      
      totalFields += result.fieldInfo.length;
      mappedFields += result.fieldInfo.filter(f => f.hasMapping).length;
      unmappedFields += result.fieldInfo.filter(f => !f.hasMapping && this.shouldBeMapped(f.name)).length;

      // Collect inconsistencies
      for (const error of result.errors) {
        inconsistencies.push({
          table,
          field: '',
          issue: error,
          severity: 'error',
        });
      }

      for (const warning of result.warnings) {
        inconsistencies.push({
          table,
          field: '',
          issue: warning,
          severity: 'warning',
        });
      }

      recommendations.push(...result.suggestions);
    }

    // Check for cross-table consistency
    this.checkCrossTableConsistency(summary, inconsistencies);

    const overallValid = inconsistencies.filter(i => i.severity === 'error').length === 0;

    return {
      overallValid,
      tablesChecked: tables.length,
      totalFields,
      mappedFields,
      unmappedFields,
      inconsistencies,
      recommendations: [...new Set(recommendations)], // Remove duplicates
      summary,
    };
  }

  /**
   * Generate migration suggestions based on schema analysis
   */
  generateMigrationSuggestions(): MigrationPlan {
    const consistencyReport = this.checkFieldNameConsistency();
    const requiredChanges: MigrationChange[] = [];
    const optionalChanges: MigrationChange[] = [];

    // Analyze inconsistencies and generate migration changes
    for (const inconsistency of consistencyReport.inconsistencies) {
      if (inconsistency.severity === 'error') {
        requiredChanges.push({
          type: 'add_mapping',
          table: inconsistency.table,
          field: inconsistency.field,
          description: inconsistency.issue,
          riskLevel: 'medium',
        });
      } else {
        optionalChanges.push({
          type: 'add_mapping',
          table: inconsistency.table,
          field: inconsistency.field,
          description: inconsistency.issue,
          riskLevel: 'low',
        });
      }
    }

    // Determine overall risk level
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    if (requiredChanges.length > 5) {
      riskLevel = 'high';
    } else if (requiredChanges.length > 0) {
      riskLevel = 'medium';
    }

    const estimatedEffort = this.estimateEffort(requiredChanges.length, optionalChanges.length);

    return {
      requiredChanges,
      optionalChanges,
      riskLevel,
      estimatedEffort,
    };
  }

  /**
   * Get table schema information from SQLite
   */
  private getTableInfo(tableName: string): any[] {
    try {
      const stmt = this.db.prepare(`PRAGMA table_info(${tableName})`);
      return stmt.all();
    } catch (error) {
      throw new ValidationError(`Failed to get table info for '${tableName}': ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Check if a field name should be mapped
   */
  private shouldBeMapped(fieldName: string): boolean {
    // Skip common fields that don't need mapping
    const skipFields = ['id', 'title', 'description', 'status', 'priority', 'progress', 'role', 'content'];
    if (skipFields.includes(fieldName)) {
      return false;
    }

    // Check for snake_case (contains underscore)
    return fieldName.includes('_');
  }

  /**
   * Suggest API field name for database field
   */
  private suggestApiFieldName(dbField: string): string {
    return dbField.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  /**
   * Check consistency across tables
   */
  private checkCrossTableConsistency(
    summary: ConsistencyReport['summary'],
    inconsistencies: SchemaInconsistency[]
  ): void {
    // Check for similar field names across tables that should be consistent
    const commonFields = ['created_at', 'updated_at', 'created_by', 'assigned_to', 'due_date'];
    
    for (const field of commonFields) {
      const tablesWithField = Object.entries(summary).filter(([, result]) =>
        result.fieldInfo.some(f => f.name === field)
      );

      if (tablesWithField.length > 1) {
        const mappingStatus = tablesWithField.map(([table, result]) => ({
          table,
          hasMapping: result.fieldInfo.find(f => f.name === field)?.hasMapping || false,
        }));

        const inconsistentMappings = mappingStatus.filter(m => !m.hasMapping);
        if (inconsistentMappings.length > 0 && inconsistentMappings.length < mappingStatus.length) {
          for (const { table } of inconsistentMappings) {
            inconsistencies.push({
              table,
              field,
              issue: `Field '${field}' is mapped in some tables but not in '${table}'`,
              severity: 'warning',
              suggestion: `Add mapping for consistency across tables`,
            });
          }
        }
      }
    }
  }

  /**
   * Estimate effort for migration changes
   */
  private estimateEffort(requiredChanges: number, optionalChanges: number): string {
    const totalChanges = requiredChanges + optionalChanges;
    
    if (totalChanges === 0) {
      return 'No changes needed';
    } else if (totalChanges <= 5) {
      return '1-2 hours';
    } else if (totalChanges <= 15) {
      return '4-6 hours';
    } else {
      return '1-2 days';
    }
  }

  /**
   * Validate specific field mappings
   */
  validateFieldMappings(tableName: string, apiFields: string[]): SchemaValidationResult {
    const result = this.validateTableSchema(tableName);
    const dbFields = result.fieldInfo.map(f => f.name);
    
    const validation = FieldMapper.validateConsistency(apiFields, dbFields);
    
    return {
      ...result,
      valid: result.valid && validation.valid,
      errors: [...result.errors, ...validation.errors],
      warnings: [...result.warnings, ...validation.warnings],
      suggestions: [...result.suggestions, ...Object.entries(validation.suggestions).map(([field, suggestion]) => 
        `${field} -> ${suggestion}`
      )],
    };
  }
}

/**
 * Utility function to create schema validator instance
 */
export function createSchemaValidator(database: Database.Database): SchemaValidator {
  return new SchemaValidator(database);
}

/**
 * Quick validation function for common use cases
 */
export function validateSchema(database: Database.Database): ConsistencyReport {
  const validator = new SchemaValidator(database);
  return validator.checkFieldNameConsistency();
}
