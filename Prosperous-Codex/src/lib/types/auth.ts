export interface User {
  id: string;
  email: string;
  username?: string;
  createdAt?: string;
  role?: 'user' | 'moderator' | 'admin';
  isActive?: boolean;
  // Add other user properties as needed
}

export interface AuthState {
  currentUser: User | null;
  isLoading: boolean;
}

export interface AuthContextType extends AuthState {
  login: (data: LoginFormData) => Promise<void>;
  logout: () => Promise<void>;
}

export interface LoginFormData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AccessRequestFormData {
  email: string;
  name?: string;
  reason?: string;
}
