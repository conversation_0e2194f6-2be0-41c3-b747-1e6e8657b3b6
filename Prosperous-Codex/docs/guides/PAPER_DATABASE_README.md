# Paper Options Database Storage System

This document describes the comprehensive database storage system for paper options in the Paper Cost Estimator application.

## Overview

The paper database storage system provides a complete solution for managing paper options data with the following features:

- **Structured Data Storage**: Organized paper options by category (Inner Text, Cover, Endpapers)
- **Local Storage Persistence**: Automatic saving and loading from browser localStorage
- **CRUD Operations**: Create, Read, Update, Delete operations for paper options
- **React Integration**: Custom hooks for seamless React component integration
- **Type Safety**: Full TypeScript support with proper interfaces
- **Import/Export**: JSON-based data import and export functionality

## File Structure

```
src/
├── lib/
│   ├── paper-data.ts           # Core data structures and initial data
│   ├── paper-configs.ts        # Configuration objects for PaperOptionsCard
│   ├── paper-storage.ts        # Storage service with CRUD operations
│   └── hooks/
│       └── use-paper-data.ts   # React hooks for data management
└── components/
    └── paper-options-example.tsx # Example usage components
```

## Core Components

### 1. Data Structure (`paper-data.ts`)

Defines the core `PaperOption` interface and provides initial data for all paper categories:

```typescript
export interface PaperOption {
  id: string;
  name: string;
  paperType: string;      // Source (Pre-Cut, Roll)
  weight: number;         // GSM (g/m²)
  color: string;          // Sheet H (mm)
  finish: string;         // Sheet W (mm)
  printingSide: string;   // Grain direction (Height, Width)
  quantity: number;       // Caliper (µm)
  notes: string;          // Cost/Ream ($)
  costTon?: number;       // Cost/Ton ($)
  category: 'Inner Text' | 'Cover' | 'Endpapers';
}
```

**Note**: The field names in the interface correspond to the original paper options card structure, but represent different data:
- `color` = Sheet Height (mm)
- `finish` = Sheet Width (mm)
- `printingSide` = Grain direction
- `quantity` = Caliper (µm)
- `notes` = Cost/Ream ($)

### 2. Configuration (`paper-configs.ts`)

Provides configuration objects for the `PaperOptionsCard` component:

```typescript
export const innerTextConfig: PaperOptionsConfig = {
  title: 'Inner Text Paper Options',
  paperTypes: ['Pre-Cut', 'Roll'],
  finishes: ['546.1', '596.9', '444.5', ...],
  printingSides: ['Height', 'Width'],
  defaultValues: { /* ... */ },
  initialData: innerTextPapers
};
```

### 3. Storage Service (`paper-storage.ts`)

Provides a singleton service for managing paper data with localStorage persistence:

```typescript
const paperStorage = PaperStorageService.getInstance();

// Basic CRUD operations
paperStorage.loadPapers('Inner Text');
paperStorage.addPaper('Inner Text', paperData);
paperStorage.updatePaper('Inner Text', paperId, updates);
paperStorage.deletePaper('Inner Text', paperId);

// Advanced operations
paperStorage.searchPapers('Inner Text', 'searchTerm');
paperStorage.filterPapers('Inner Text', filters);
paperStorage.exportAllData();
paperStorage.importData(data);
```

### 4. React Hooks (`use-paper-data.ts`)

Custom hooks for React component integration:

```typescript
// Hook for managing a specific category
const {
  papers,
  loading,
  error,
  addPaper,
  updatePaper,
  deletePaper,
  resetToDefaults
} = usePaperData('Inner Text');

// Hook for statistics
const { stats } = usePaperStats();

// Hook for managing all categories
const { allData, importData, resetAllCategories } = useAllPaperData();
```

## Usage Examples

### Basic Usage with Existing Component

```typescript
import React from 'react';
import PaperOptionsCard from './paper-options-card';
import { usePaperData } from '@/lib/hooks/use-paper-data';
import { innerTextConfig } from '@/lib/paper-configs';

const InnerTextPaperCard: React.FC = () => {
  const { papers, loading, error } = usePaperData('Inner Text');
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  
  const config = {
    ...innerTextConfig,
    initialData: papers
  };
  
  return <PaperOptionsCard config={config} />;
};
```

### Advanced Usage with Management Features

```typescript
import { PaperManagementDashboard } from '@/components/paper-options-example';

// This component provides:
// - All three paper category cards
// - Statistics dashboard
// - Import/Export functionality
// - Reset and clear operations
const App = () => {
  return <PaperManagementDashboard />;
};
```

## Initial Data

The system comes pre-loaded with the paper options data you provided:

### Inner Text Papers (8 options)
- Halved 31 x 43" (Pre-Cut)
- Halved 35 x 47" (Pre-Cut)
- Quarter 35 x 47" (Pre-Cut)
- Special 25 x 38" (Pre-Cut)
- Custom 25" Roll
- Custom 31" Roll
- Custom 35" Roll
- Custom 38" Roll

### Cover Papers (2 options)
- Art Card 250gsm (Pre-Cut)
- Coated Board 300gsm (Pre-Cut)

### Endpapers (2 options)
- Woodfree 120gsm (Pre-Cut)
- Specialty Endpaper 140gsm (Pre-Cut)

## Data Persistence

The system automatically saves all changes to browser localStorage:

- **Storage Keys**: 
  - `paper_options_inner_text`
  - `paper_options_cover`
  - `paper_options_endpapers`
  - `paper_options_last_updated`

- **Automatic Saving**: All CRUD operations automatically persist to localStorage
- **Fallback**: If localStorage is unavailable, falls back to in-memory storage
- **Error Handling**: Graceful handling of storage errors with user feedback

## API Reference

### PaperStorageService Methods

| Method | Description | Parameters | Returns |
|--------|-------------|------------|----------|
| `loadPapers(category)` | Load papers for a category | `StorageCategory` | `PaperOption[]` |
| `savePapers(category, papers)` | Save papers for a category | `StorageCategory, PaperOption[]` | `boolean` |
| `addPaper(category, paperData)` | Add a new paper | `StorageCategory, Omit<PaperOption, 'id' \| 'category'>` | `PaperOption \| null` |
| `updatePaper(category, id, updates)` | Update existing paper | `StorageCategory, string, Partial<PaperOption>` | `PaperOption \| null` |
| `deletePaper(category, id)` | Delete a paper | `StorageCategory, string` | `boolean` |
| `searchPapers(category, term)` | Search papers by name | `StorageCategory, string` | `PaperOption[]` |
| `filterPapers(category, filters)` | Filter papers by criteria | `StorageCategory, FilterObject` | `PaperOption[]` |
| `exportAllData()` | Export all data | None | `{[key: string]: PaperOption[]}` |
| `importData(data)` | Import data | `{[key: string]: PaperOption[]}` | `boolean` |
| `resetCategory(category)` | Reset category to defaults | `StorageCategory` | `boolean` |
| `resetAllCategories()` | Reset all categories | None | `boolean` |
| `clearAllData()` | Clear all stored data | None | `boolean` |

### React Hooks

#### `usePaperData(category)`
Returns an object with:
- `papers`: Current papers array
- `loading`: Loading state
- `error`: Error message (if any)
- `addPaper`: Function to add a paper
- `updatePaper`: Function to update a paper
- `deletePaper`: Function to delete a paper
- `searchPapers`: Function to search papers
- `filterPapers`: Function to filter papers
- `resetToDefaults`: Function to reset to default data
- `reload`: Function to reload data
- `clearError`: Function to clear error state

#### `usePaperStats()`
Returns statistics about all paper data:
- `stats.totalPapers`: Total number of papers
- `stats.categoryCounts`: Papers count by category
- `stats.paperTypeCounts`: Papers count by type
- `stats.averageWeight`: Average paper weight
- `stats.weightRange`: Min/max weight range
- `stats.lastUpdated`: Last update timestamp

#### `useAllPaperData()`
Returns data for all categories:
- `allData`: All paper data
- `loading`: Loading state
- `error`: Error message
- `importData`: Function to import data
- `resetAllCategories`: Function to reset all
- `clearAllData`: Function to clear all

## Integration Steps

1. **Import the required modules**:
   ```typescript
   import { usePaperData } from '@/lib/hooks/use-paper-data';
   import { innerTextConfig } from '@/lib/paper-configs';
   ```

2. **Use the hook in your component**:
   ```typescript
   const { papers, loading, error } = usePaperData('Inner Text');
   ```

3. **Update your PaperOptionsCard config**:
   ```typescript
   const config = {
     ...innerTextConfig,
     initialData: papers
   };
   ```

4. **Handle loading and error states**:
   ```typescript
   if (loading) return <LoadingComponent />;
   if (error) return <ErrorComponent error={error} />;
   ```

5. **Render the component**:
   ```typescript
   return <PaperOptionsCard config={config} />;
   ```

## Best Practices

1. **Error Handling**: Always handle loading and error states in your components
2. **Performance**: Use the category-specific hooks rather than loading all data when possible
3. **Data Validation**: The system includes built-in validation, but additional validation can be added
4. **Backup**: Regularly export data as JSON for backup purposes
5. **Testing**: Test localStorage functionality across different browsers

## Troubleshooting

### Common Issues

1. **Data not persisting**: Check if localStorage is available and not full
2. **Type errors**: Ensure you're using the correct interfaces and types
3. **Hook errors**: Make sure hooks are used inside React components
4. **Import/Export issues**: Verify JSON format matches the expected structure

### Debug Information

Use the storage info hook to get debug information:
```typescript
const { storageInfo } = useStorageInfo();
console.log('Storage usage:', storageInfo);
```

## Future Enhancements

Potential improvements for the system:

1. **Server Synchronization**: Add API integration for server-side storage
2. **Data Validation**: Enhanced validation rules for paper specifications
3. **Bulk Operations**: Batch import/export with progress indicators
4. **Search Enhancement**: Advanced search with multiple criteria
5. **Audit Trail**: Track changes with timestamps and user information
6. **Data Migration**: Version management for data structure changes

## Support

For questions or issues with the paper database storage system:

1. Check this documentation first
2. Review the example components in `paper-options-example.tsx`
3. Examine the TypeScript interfaces for proper usage
4. Test with the provided initial data to ensure setup is correct

The system is designed to be robust and user-friendly, with comprehensive error handling and fallback mechanisms to ensure a smooth user experience.