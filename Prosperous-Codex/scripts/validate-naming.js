#!/usr/bin/env node

/**
 * Naming Conventions Validation Script
 * 
 * Validates naming conventions across the codebase including:
 * - API field names (camelCase)
 * - Database field names (snake_case)
 * - TypeScript interfaces and types
 * - File and directory names
 */

const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

/**
 * Logger utility
 */
const logger = {
  info: (message) => console.log(`ℹ️  ${message}`),
  success: (message) => console.log(`✅ ${message}`),
  error: (message) => console.error(`❌ ${message}`),
  warn: (message) => console.warn(`⚠️  ${message}`),
};

/**
 * Naming convention patterns
 */
const patterns = {
  camelCase: /^[a-z][a-zA-Z0-9]*$/,
  PascalCase: /^[A-Z][a-zA-Z0-9]*$/,
  snake_case: /^[a-z][a-z0-9_]*$/,
  SCREAMING_SNAKE_CASE: /^[A-Z][A-Z0-9_]*$/,
  kebab_case: /^[a-z][a-z0-9-]*$/,
  fileName: /^[a-z][a-z0-9-]*\.(ts|tsx|js|jsx)$/,
  directoryName: /^[a-z][a-z0-9-]*$/
};

/**
 * Validate TypeScript file naming conventions
 */
function validateFileNames() {
  const errors = [];
  const warnings = [];

  try {
    // Find all TypeScript files
    const files = glob.sync('src/**/*.{ts,tsx}', { 
      cwd: path.join(__dirname, '..'),
      ignore: ['**/*.test.ts', '**/*.test.tsx', '**/node_modules/**']
    });

    for (const file of files) {
      const fileName = path.basename(file);
      const dirName = path.dirname(file).split('/').pop();

      // Validate file names
      if (!patterns.fileName.test(fileName) && !patterns.PascalCase.test(fileName.replace(/\.(ts|tsx)$/, ''))) {
        // Allow PascalCase for component files
        if (!fileName.match(/^[A-Z][a-zA-Z0-9]*\.(tsx?)$/)) {
          errors.push(`File '${file}' does not follow naming convention (should be kebab-case or PascalCase for components)`);
        }
      }

      // Validate directory names
      if (dirName && !patterns.directoryName.test(dirName) && dirName !== 'src') {
        warnings.push(`Directory '${dirName}' should use kebab-case`);
      }
    }
  } catch (error) {
    warnings.push(`Could not validate file names: ${error.message}`);
  }

  return { errors, warnings };
}

/**
 * Validate TypeScript interface and type names
 */
function validateTypeScriptNaming() {
  const errors = [];
  const warnings = [];

  try {
    const files = glob.sync('src/**/*.{ts,tsx}', { 
      cwd: path.join(__dirname, '..'),
      ignore: ['**/*.test.ts', '**/*.test.tsx', '**/node_modules/**']
    });

    for (const file of files) {
      const filePath = path.join(__dirname, '..', file);
      const content = fs.readFileSync(filePath, 'utf8');

      // Check interface names
      const interfaceMatches = content.matchAll(/interface\s+(\w+)/g);
      for (const match of interfaceMatches) {
        const interfaceName = match[1];
        if (!patterns.PascalCase.test(interfaceName)) {
          errors.push(`Interface '${interfaceName}' in ${file} should use PascalCase`);
        }
      }

      // Check type alias names
      const typeMatches = content.matchAll(/type\s+(\w+)/g);
      for (const match of typeMatches) {
        const typeName = match[1];
        if (!patterns.PascalCase.test(typeName)) {
          errors.push(`Type '${typeName}' in ${file} should use PascalCase`);
        }
      }

      // Check class names
      const classMatches = content.matchAll(/class\s+(\w+)/g);
      for (const match of classMatches) {
        const className = match[1];
        if (!patterns.PascalCase.test(className)) {
          errors.push(`Class '${className}' in ${file} should use PascalCase`);
        }
      }

      // Check function names
      const functionMatches = content.matchAll(/function\s+(\w+)/g);
      for (const match of functionMatches) {
        const functionName = match[1];
        if (!patterns.camelCase.test(functionName)) {
          warnings.push(`Function '${functionName}' in ${file} should use camelCase`);
        }
      }

      // Check variable names (simplified check)
      const varMatches = content.matchAll(/(?:const|let|var)\s+(\w+)/g);
      for (const match of varMatches) {
        const varName = match[1];
        if (!patterns.camelCase.test(varName) && !patterns.PascalCase.test(varName) && !patterns.SCREAMING_SNAKE_CASE.test(varName)) {
          // Allow PascalCase for constructors and SCREAMING_SNAKE_CASE for constants
          warnings.push(`Variable '${varName}' in ${file} should use camelCase (or PascalCase for constructors, SCREAMING_SNAKE_CASE for constants)`);
        }
      }
    }
  } catch (error) {
    warnings.push(`Could not validate TypeScript naming: ${error.message}`);
  }

  return { errors, warnings };
}

/**
 * Validate API field naming in route files
 */
function validateApiFieldNaming() {
  const errors = [];
  const warnings = [];

  try {
    const apiFiles = glob.sync('src/app/api/**/*.ts', { 
      cwd: path.join(__dirname, '..'),
      ignore: ['**/node_modules/**']
    });

    for (const file of apiFiles) {
      const filePath = path.join(__dirname, '..', file);
      const content = fs.readFileSync(filePath, 'utf8');

      // Look for object property definitions that might be API fields
      const propertyMatches = content.matchAll(/['"](\w+)['"]:\s*\w+/g);
      for (const match of propertyMatches) {
        const fieldName = match[1];
        
        // Skip common non-field properties
        if (['method', 'status', 'headers', 'body', 'error', 'success', 'message'].includes(fieldName)) {
          continue;
        }

        if (!patterns.camelCase.test(fieldName)) {
          warnings.push(`API field '${fieldName}' in ${file} should use camelCase`);
        }
      }

      // Check for snake_case in API responses (potential issue)
      const snakeCaseMatches = content.matchAll(/['"](\w+_\w+)['"]:/g);
      for (const match of snakeCaseMatches) {
        const fieldName = match[1];
        errors.push(`API field '${fieldName}' in ${file} uses snake_case (should be camelCase)`);
      }
    }
  } catch (error) {
    warnings.push(`Could not validate API field naming: ${error.message}`);
  }

  return { errors, warnings };
}

/**
 * Validate database field naming in schema files
 */
function validateDatabaseFieldNaming() {
  const errors = [];
  const warnings = [];

  try {
    // Check database initialization script
    const initScript = path.join(__dirname, 'init-db.js');
    if (fs.existsSync(initScript)) {
      const content = fs.readFileSync(initScript, 'utf8');

      // Look for CREATE TABLE statements
      const createTableMatches = content.matchAll(/CREATE TABLE\s+(\w+)\s*\((.*?)\)/gs);
      for (const match of createTableMatches) {
        const tableName = match[1];
        const tableContent = match[2];

        // Validate table name
        if (!patterns.snake_case.test(tableName)) {
          errors.push(`Table name '${tableName}' should use snake_case`);
        }

        // Extract column definitions
        const columnMatches = tableContent.matchAll(/(\w+)\s+\w+/g);
        for (const colMatch of columnMatches) {
          const columnName = colMatch[1];
          
          // Skip SQL keywords
          if (['PRIMARY', 'KEY', 'FOREIGN', 'REFERENCES', 'NOT', 'NULL', 'DEFAULT', 'UNIQUE'].includes(columnName.toUpperCase())) {
            continue;
          }

          if (!patterns.snake_case.test(columnName)) {
            errors.push(`Column '${columnName}' in table '${tableName}' should use snake_case`);
          }
        }
      }
    }

    // Check field mapping file for database field consistency
    const mappingFile = path.join(__dirname, '../src/lib/task-master/field-mapping.ts');
    if (fs.existsSync(mappingFile)) {
      const content = fs.readFileSync(mappingFile, 'utf8');
      
      // Extract database field names from mappings
      const mappingMatches = content.matchAll(/\w+:\s*['"](\w+)['"],?/g);
      for (const match of mappingMatches) {
        const dbField = match[1];
        if (!patterns.snake_case.test(dbField)) {
          errors.push(`Database field '${dbField}' in field mappings should use snake_case`);
        }
      }
    }
  } catch (error) {
    warnings.push(`Could not validate database field naming: ${error.message}`);
  }

  return { errors, warnings };
}

/**
 * Validate component naming conventions
 */
function validateComponentNaming() {
  const errors = [];
  const warnings = [];

  try {
    const componentFiles = glob.sync('src/components/**/*.{tsx,jsx}', { 
      cwd: path.join(__dirname, '..'),
      ignore: ['**/node_modules/**']
    });

    for (const file of componentFiles) {
      const fileName = path.basename(file, path.extname(file));
      
      // Component files should use PascalCase
      if (!patterns.PascalCase.test(fileName)) {
        errors.push(`Component file '${file}' should use PascalCase`);
      }

      const filePath = path.join(__dirname, '..', file);
      const content = fs.readFileSync(filePath, 'utf8');

      // Check for component function/class names
      const componentMatches = content.matchAll(/(?:export\s+(?:default\s+)?(?:function|const)\s+(\w+)|export\s+default\s+class\s+(\w+))/g);
      for (const match of componentMatches) {
        const componentName = match[1] || match[2];
        if (componentName && !patterns.PascalCase.test(componentName)) {
          errors.push(`Component '${componentName}' in ${file} should use PascalCase`);
        }
      }
    }
  } catch (error) {
    warnings.push(`Could not validate component naming: ${error.message}`);
  }

  return { errors, warnings };
}

/**
 * Generate validation report
 */
function generateReport(results) {
  const totalErrors = results.reduce((sum, result) => sum + result.errors.length, 0);
  const totalWarnings = results.reduce((sum, result) => sum + result.warnings.length, 0);

  console.log('\n📊 Naming Conventions Validation Report');
  console.log('=======================================\n');

  for (const result of results) {
    if (result.section) {
      console.log(`\n🔍 ${result.section}`);
      console.log('-'.repeat(result.section.length + 4));
    }

    if (result.errors.length > 0) {
      console.log('\n❌ Errors:');
      result.errors.forEach(error => console.log(`   • ${error}`));
    }

    if (result.warnings.length > 0) {
      console.log('\n⚠️  Warnings:');
      result.warnings.forEach(warning => console.log(`   • ${warning}`));
    }

    if (result.errors.length === 0 && result.warnings.length === 0) {
      console.log('   ✅ No issues found');
    }
  }

  console.log('\n📈 Summary');
  console.log('===========');
  console.log(`Total Errors: ${totalErrors}`);
  console.log(`Total Warnings: ${totalWarnings}`);

  if (totalErrors === 0) {
    logger.success('Naming conventions validation passed!');
  } else {
    logger.error(`Naming conventions validation failed with ${totalErrors} errors`);
  }

  return totalErrors === 0;
}

/**
 * Main validation function
 */
function validateNamingConventions() {
  try {
    logger.info('Starting naming conventions validation...');

    // Run validations
    const results = [
      {
        section: 'File and Directory Names',
        ...validateFileNames()
      },
      {
        section: 'TypeScript Naming',
        ...validateTypeScriptNaming()
      },
      {
        section: 'API Field Naming',
        ...validateApiFieldNaming()
      },
      {
        section: 'Database Field Naming',
        ...validateDatabaseFieldNaming()
      },
      {
        section: 'Component Naming',
        ...validateComponentNaming()
      }
    ];

    // Generate report
    const success = generateReport(results);

    if (!success) {
      process.exit(1);
    }

  } catch (error) {
    logger.error(`Naming conventions validation failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  validateNamingConventions();
}

module.exports = { validateNamingConventions };
