import { NextRequest, NextResponse } from 'next/server';
import { SessionService } from '@/lib/database/session-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const sessionService = new SessionService();
    
    // Get user's active sessions
    const sessions = sessionService.getUserSessions(parseInt(user.id));
    
    // Remove sensitive session tokens from response
    const safeSessions = sessions.map(session => ({
      id: session.id,
      createdAt: session.createdAt,
      expiresAt: session.expiresAt
    }));
    
    return NextResponse.json({ sessions: safeSessions });
    
  } catch (error) {
    console.error('Get sessions error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { sessionId } = await request.json();
    
    const sessionService = new SessionService();
    
    if (sessionId === 'all') {
      // Invalidate all sessions for the user
      const success = await sessionService.invalidateAllUserSessions(parseInt(user.id));
      
      if (success) {
        return NextResponse.json({ message: 'All sessions invalidated successfully' });
      } else {
        return NextResponse.json(
          { error: 'Failed to invalidate sessions' },
          { status: 500 }
        );
      }
    } else {
      // Invalidate specific session
      // Note: In a real implementation, you'd want to verify the session belongs to the user
      const success = await sessionService.invalidateSession(sessionId);
      
      if (success) {
        return NextResponse.json({ message: 'Session invalidated successfully' });
      } else {
        return NextResponse.json(
          { error: 'Session not found or already expired' },
          { status: 404 }
        );
      }
    }
    
  } catch (error) {
    console.error('Delete session error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
