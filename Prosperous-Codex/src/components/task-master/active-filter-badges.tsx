"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { FilterState } from './filter-dropdown';

interface ActiveFilterBadgesProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  className?: string;
}

export function ActiveFilterBadges({ filters, onFiltersChange, className }: ActiveFilterBadgesProps) {
  const [visibleBadges, setVisibleBadges] = useState<string[]>([]);
  const activeFilterCount = Object.values(filters).filter(Boolean).length;

  const getActiveFilterBadges = useCallback(() => {
    const badges = [];
    
    if (filters.priority) {
      badges.push({
        key: 'priority',
        label: `Priority: ${filters.priority.charAt(0).toUpperCase() + filters.priority.slice(1)}`,
        onRemove: () => onFiltersChange({ ...filters, priority: undefined }),
        color: filters.priority === 'high' ? 'bg-red-50 text-red-700 border-red-200' :
               filters.priority === 'medium' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
               'bg-green-50 text-green-700 border-green-200'
      });
    }

    if (filters.dueDate) {
      badges.push({
        key: 'dueDate',
        label: 'Due Date',
        onRemove: () => onFiltersChange({ ...filters, dueDate: false }),
        color: 'bg-blue-50 text-blue-700 border-blue-200'
      });
    }

    if (filters.lastUpdated) {
      badges.push({
        key: 'lastUpdated',
        label: 'Recently Updated',
        onRemove: () => onFiltersChange({ ...filters, lastUpdated: false }),
        color: 'bg-purple-50 text-purple-700 border-purple-200'
      });
    }

    if (filters.completion) {
      badges.push({
        key: 'completion',
        label: 'Completion %',
        onRemove: () => onFiltersChange({ ...filters, completion: false }),
        color: 'bg-indigo-50 text-indigo-700 border-indigo-200'
      });
    }
    
    return badges;
  }, [filters, onFiltersChange]);

  // Track which badges should be visible for staggered animations
  useEffect(() => {
    const badges = getActiveFilterBadges();
    const badgeKeys = badges.map(b => b.key);

    // Stagger the appearance of badges
    badgeKeys.forEach((key, index) => {
      setTimeout(() => {
        setVisibleBadges(prev => [...prev.filter(k => k !== key), key]);
      }, index * 100); // 100ms delay between each badge
    });

    // Remove badges that are no longer active
    setVisibleBadges(prev => prev.filter(key => badgeKeys.includes(key)));
  }, [filters, getActiveFilterBadges]);

  if (activeFilterCount === 0) {
    return null;
  }

  return (
    <div className={`flex flex-wrap gap-2 items-center overflow-hidden ${className || ''}`}>
      {getActiveFilterBadges().map((badge, index) => {
        const isVisible = visibleBadges.includes(badge.key);
        return (
          <Badge
            key={badge.key}
            variant="secondary"
            className={`
              ${badge.color || 'bg-[#5E6AD2]/10 text-[#5E6AD2] border-[#5E6AD2]/20'}
              hover:opacity-80
              transition-all duration-300 ease-out
              ${isVisible
                ? 'animate-in fade-in-0 slide-in-from-left-2 scale-in-95'
                : 'animate-out fade-out-0 slide-out-to-left-2 scale-out-95'
              }
              text-xs font-medium px-2 py-1.5 sm:py-1
              shadow-sm hover:shadow-md
              group
              min-h-[32px] sm:min-h-auto
              touch-manipulation
            `}
            style={{
              animationDelay: `${index * 100}ms`,
              animationFillMode: 'both'
            }}
          >
            <span className="truncate max-w-32 sm:max-w-none">{badge.label}</span>
            <button
              onClick={badge.onRemove}
              className="ml-1.5 hover:bg-black/10 rounded-full p-1 sm:p-0.5 transition-all duration-150 group-hover:scale-110 touch-manipulation"
              aria-label={`Remove ${badge.label} filter`}
            >
              <X className="h-3 w-3 sm:h-3 sm:w-3" />
            </button>
          </Badge>
        );
      })}

      {/* Clear all button when multiple filters are active */}
      {activeFilterCount > 1 && (
        <button
          onClick={() => onFiltersChange({})}
          className="text-xs text-muted-foreground hover:text-foreground transition-colors duration-150 px-3 py-2 sm:px-2 sm:py-1 rounded hover:bg-muted/50 touch-manipulation min-h-[32px] sm:min-h-auto"
          aria-label={`Clear all ${activeFilterCount} active filters`}
        >
          Clear all
        </button>
      )}
    </div>
  );
}
