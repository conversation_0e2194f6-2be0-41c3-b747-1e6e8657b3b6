#!/usr/bin/env node

/**
 * Comprehensive API Migration Testing Script
 * Tests all migrated Task Master API routes to ensure they work correctly
 * with the new modular validation middleware system.
 */

const BASE_URL = 'http://localhost:3000';

// Test credentials (using documented test credentials)
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'moderator123'
};

let authToken = null;

/**
 * Make authenticated API request
 */
async function apiRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };

  // For NextAuth.js, we need to include cookies for session-based auth
  const response = await fetch(url, {
    ...options,
    headers,
    credentials: 'include' // Include cookies for session auth
  });

  let data;
  try {
    data = await response.json();
  } catch (error) {
    // Handle non-JSON responses
    data = { error: 'Invalid response format' };
  }

  return { response, data };
}

/**
 * Authenticate using NextAuth.js credentials provider
 */
async function authenticate() {
  console.log('🔐 Authenticating with NextAuth.js...');

  try {
    // First, get the CSRF token
    const csrfResponse = await fetch(`${BASE_URL}/api/auth/csrf`, {
      credentials: 'include'
    });
    const csrfData = await csrfResponse.json();

    if (!csrfData.csrfToken) {
      console.error('❌ Failed to get CSRF token');
      return false;
    }

    // Authenticate using NextAuth.js credentials provider
    const authResponse = await fetch(`${BASE_URL}/api/auth/callback/credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        email: TEST_CREDENTIALS.email,
        password: TEST_CREDENTIALS.password,
        csrfToken: csrfData.csrfToken,
        callbackUrl: `${BASE_URL}/dashboard`,
        json: 'true'
      }),
      credentials: 'include'
    });

    if (authResponse.ok) {
      console.log('✅ Authentication successful');
      return true;
    } else {
      console.error('❌ Authentication failed:', authResponse.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Authentication error:', error);
    return false;
  }
}

/**
 * Test suite for projects API
 */
async function testProjectsAPI() {
  console.log('\n📁 Testing Projects API...');
  
  // Test GET /api/task-master/projects
  console.log('  Testing GET /api/task-master/projects');
  const { response: listResponse, data: listData } = await apiRequest('/api/task-master/projects');
  
  if (listResponse.ok) {
    console.log('  ✅ GET projects successful');
    console.log(`  📊 Found ${listData.projects?.length || 0} projects`);
  } else {
    console.log('  ❌ GET projects failed:', listData);
    return false;
  }

  // Test POST /api/task-master/projects
  console.log('  Testing POST /api/task-master/projects');
  const testProject = {
    title: 'API Migration Test Project',
    description: 'Test project created during API migration validation',
    status: 'todo',
    priority: 'medium'
  };

  const { response: createResponse, data: createData } = await apiRequest('/api/task-master/projects', {
    method: 'POST',
    body: JSON.stringify(testProject)
  });

  if (createResponse.ok && createData.project) {
    console.log('  ✅ POST project successful');
    console.log(`  📝 Created project ID: ${createData.project.id}`);
    return createData.project.id;
  } else {
    console.log('  ❌ POST project failed:', createData);
    return false;
  }
}

/**
 * Test suite for individual project API
 */
async function testIndividualProjectAPI(projectId) {
  console.log('\n📄 Testing Individual Project API...');
  
  // Test GET /api/task-master/projects/[id]
  console.log(`  Testing GET /api/task-master/projects/${projectId}`);
  const { response: getResponse, data: getData } = await apiRequest(`/api/task-master/projects/${projectId}`);
  
  if (getResponse.ok && getData.project) {
    console.log('  ✅ GET individual project successful');
  } else {
    console.log('  ❌ GET individual project failed:', getData);
    return false;
  }

  // Test PUT /api/task-master/projects/[id]
  console.log(`  Testing PUT /api/task-master/projects/${projectId}`);
  const updateData = {
    title: 'Updated API Migration Test Project',
    status: 'inProgress',
    progress: 25
  };

  const { response: updateResponse, data: updateResult } = await apiRequest(`/api/task-master/projects/${projectId}`, {
    method: 'PUT',
    body: JSON.stringify(updateData)
  });

  if (updateResponse.ok) {
    console.log('  ✅ PUT project successful');
  } else {
    console.log('  ❌ PUT project failed:', updateResult);
    return false;
  }

  return true;
}

/**
 * Test suite for tasks API
 */
async function testTasksAPI(projectId) {
  console.log('\n📋 Testing Tasks API...');
  
  // Test GET /api/task-master/tasks
  console.log(`  Testing GET /api/task-master/tasks?project_id=${projectId}`);
  const { response: listResponse, data: listData } = await apiRequest(`/api/task-master/tasks?project_id=${projectId}`);
  
  if (listResponse.ok) {
    console.log('  ✅ GET tasks successful');
    console.log(`  📊 Found ${listData.tasks?.length || 0} tasks`);
  } else {
    console.log('  ❌ GET tasks failed:', listData);
    return false;
  }

  // Test POST /api/task-master/tasks
  console.log('  Testing POST /api/task-master/tasks');
  const testTask = {
    project_id: projectId,
    title: 'API Migration Test Task',
    description: 'Test task created during API migration validation',
    status: 'todo',
    priority: 'high'
  };

  const { response: createResponse, data: createData } = await apiRequest('/api/task-master/tasks', {
    method: 'POST',
    body: JSON.stringify(testTask)
  });

  if (createResponse.ok && createData.task) {
    console.log('  ✅ POST task successful');
    console.log(`  📝 Created task ID: ${createData.task.id}`);
    return createData.task.id;
  } else {
    console.log('  ❌ POST task failed:', createData);
    return false;
  }
}

/**
 * Test suite for individual task API
 */
async function testIndividualTaskAPI(taskId) {
  console.log('\n📝 Testing Individual Task API...');
  
  // Test GET /api/task-master/tasks/[taskId]
  console.log(`  Testing GET /api/task-master/tasks/${taskId}`);
  const { response: getResponse, data: getData } = await apiRequest(`/api/task-master/tasks/${taskId}`);
  
  if (getResponse.ok && getData.task) {
    console.log('  ✅ GET individual task successful');
  } else {
    console.log('  ❌ GET individual task failed:', getData);
    return false;
  }

  // Test PUT /api/task-master/tasks/[taskId]
  console.log(`  Testing PUT /api/task-master/tasks/${taskId}`);
  const updateData = {
    title: 'Updated API Migration Test Task',
    status: 'inProgress',
    progress: 50
  };

  const { response: updateResponse, data: updateResult } = await apiRequest(`/api/task-master/tasks/${taskId}`, {
    method: 'PUT',
    body: JSON.stringify(updateData)
  });

  if (updateResponse.ok) {
    console.log('  ✅ PUT task successful');
  } else {
    console.log('  ❌ PUT task failed:', updateResult);
    return false;
  }

  return true;
}

/**
 * Test validation errors
 */
async function testValidationErrors() {
  console.log('\n🚫 Testing Validation Errors...');
  
  // Test invalid project creation
  console.log('  Testing invalid project data');
  const { response: invalidProjectResponse, data: invalidProjectData } = await apiRequest('/api/task-master/projects', {
    method: 'POST',
    body: JSON.stringify({ status: 'invalid_status' }) // Missing title, invalid status
  });

  if (invalidProjectResponse.status === 400) {
    console.log('  ✅ Invalid project data properly rejected');
  } else {
    console.log('  ❌ Invalid project data should have been rejected');
  }

  // Test invalid task creation
  console.log('  Testing invalid task data');
  const { response: invalidTaskResponse, data: invalidTaskData } = await apiRequest('/api/task-master/tasks', {
    method: 'POST',
    body: JSON.stringify({ project_id: 'invalid' }) // Invalid project_id, missing title
  });

  if (invalidTaskResponse.status === 400) {
    console.log('  ✅ Invalid task data properly rejected');
  } else {
    console.log('  ❌ Invalid task data should have been rejected');
  }
}

/**
 * Cleanup test data
 */
async function cleanup(projectId, taskId) {
  console.log('\n🧹 Cleaning up test data...');
  
  if (taskId) {
    const { response: deleteTaskResponse } = await apiRequest(`/api/task-master/tasks/${taskId}`, {
      method: 'DELETE'
    });
    
    if (deleteTaskResponse.ok) {
      console.log('  ✅ Test task deleted');
    } else {
      console.log('  ⚠️ Failed to delete test task');
    }
  }

  if (projectId) {
    const { response: deleteProjectResponse } = await apiRequest(`/api/task-master/projects/${projectId}`, {
      method: 'DELETE'
    });
    
    if (deleteProjectResponse.ok) {
      console.log('  ✅ Test project deleted');
    } else {
      console.log('  ⚠️ Failed to delete test project');
    }
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting API Migration Validation Tests\n');
  
  try {
    // Authenticate
    const authenticated = await authenticate();
    if (!authenticated) {
      console.log('\n❌ Tests failed: Could not authenticate');
      process.exit(1);
    }

    // Test projects API
    const projectId = await testProjectsAPI();
    if (!projectId) {
      console.log('\n❌ Tests failed: Projects API test failed');
      process.exit(1);
    }

    // Test individual project API
    const projectTestPassed = await testIndividualProjectAPI(projectId);
    if (!projectTestPassed) {
      console.log('\n❌ Tests failed: Individual project API test failed');
      await cleanup(projectId);
      process.exit(1);
    }

    // Test tasks API
    const taskId = await testTasksAPI(projectId);
    if (!taskId) {
      console.log('\n❌ Tests failed: Tasks API test failed');
      await cleanup(projectId);
      process.exit(1);
    }

    // Test individual task API
    const taskTestPassed = await testIndividualTaskAPI(taskId);
    if (!taskTestPassed) {
      console.log('\n❌ Tests failed: Individual task API test failed');
      await cleanup(projectId, taskId);
      process.exit(1);
    }

    // Test validation errors
    await testValidationErrors();

    // Cleanup
    await cleanup(projectId, taskId);

    console.log('\n🎉 All API Migration Tests Passed!');
    console.log('\n✅ Migration Summary:');
    console.log('  • Projects API: ✅ Migrated and working');
    console.log('  • Individual Project API: ✅ Migrated and working');
    console.log('  • Tasks API: ✅ Migrated and working');
    console.log('  • Individual Task API: ✅ Migrated and working');
    console.log('  • Validation Middleware: ✅ Working correctly');
    console.log('  • Error Handling: ✅ Consistent responses');

  } catch (error) {
    console.error('\n💥 Test execution failed:', error);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
