/**
 * Integration Tests for Team Member API
 * 
 * Tests the API endpoints to ensure proper field mapping and team member functionality.
 */

import { NextRequest } from 'next/server';
import { GET, POST } from '@/app/api/task-master/projects/[id]/team/route';

// Mock the database and services
jest.mock('@/lib/database/task-master-service');
jest.mock('@/lib/auth/middleware');

const mockTaskMasterService = {
  getProjectTeamMembers: jest.fn(),
  addTeamMember: jest.fn(),
  getProjectById: jest.fn()
};

const mockRequireAuth = jest.fn();

// Mock implementations
beforeEach(() => {
  jest.clearAllMocks();
  
  // Mock successful authentication
  mockRequireAuth.mockResolvedValue({
    user: { id: '1', role: 'admin', email: '<EMAIL>' }
  });
  
  // Mock project exists
  mockTaskMasterService.getProjectById.mockReturnValue({
    id: 1,
    title: 'Test Project',
    created_by: 1
  });
});

describe('Team Member API Integration', () => {
  describe('GET /api/task-master/projects/[id]/team', () => {
    test('should return team members with camelCase fields', async () => {
      // Mock team members data (as returned from enhanced DataMapper)
      const mockTeamMembers = [
        {
          id: 1,
          projectId: 1,
          userId: 123,
          role: 'member',
          addedAt: '2024-01-01T00:00:00Z',
          addedBy: 1,
          username: 'testuser',
          email: '<EMAIL>',
          name: 'testuser', // CRITICAL: This should be set
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ];

      mockTaskMasterService.getProjectTeamMembers.mockReturnValue(mockTeamMembers);

      const request = new NextRequest('http://localhost/api/task-master/projects/1/team');
      const params = Promise.resolve({ id: '1' });
      
      const response = await GET(request, { params });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.teamMembers).toHaveLength(1);
      
      const member = data.teamMembers[0];
      expect(member.userId).toBe(123);
      expect(member.projectId).toBe(1);
      expect(member.addedAt).toBe('2024-01-01T00:00:00Z');
      expect(member.name).toBe('testuser');
      
      // Ensure no snake_case fields
      expect(member.user_id).toBeUndefined();
      expect(member.project_id).toBeUndefined();
      expect(member.added_at).toBeUndefined();
    });

    test('should handle empty team members list', async () => {
      mockTaskMasterService.getProjectTeamMembers.mockReturnValue([]);

      const request = new NextRequest('http://localhost/api/task-master/projects/1/team');
      const params = Promise.resolve({ id: '1' });
      
      const response = await GET(request, { params });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.teamMembers).toEqual([]);
    });
  });

  describe('POST /api/task-master/projects/[id]/team', () => {
    test('should accept camelCase userEmail field', async () => {
      const mockNewMember = {
        id: 2,
        projectId: 1,
        userId: 456,
        role: 'member',
        addedAt: '2024-01-01T00:00:00Z',
        addedBy: 1,
        username: 'newuser',
        email: '<EMAIL>',
        name: 'newuser',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      };

      mockTaskMasterService.addTeamMember.mockResolvedValue(mockNewMember);

      const requestBody = {
        userEmail: '<EMAIL>',
        role: 'member'
      };

      const request = new NextRequest('http://localhost/api/task-master/projects/1/team', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      });
      const params = Promise.resolve({ id: '1' });
      
      const response = await POST(request, { params });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.teamMember).toBeDefined();
      expect(data.teamMember.name).toBe('newuser');
      
      // Verify the service was called with the correct email
      expect(mockTaskMasterService.addTeamMember).toHaveBeenCalledWith(
        1,
        expect.any(Object),
        '<EMAIL>',
        'member'
      );
    });

    test('should accept legacy snake_case user_email field for backward compatibility', async () => {
      const mockNewMember = {
        id: 2,
        projectId: 1,
        userId: 456,
        role: 'member',
        addedAt: '2024-01-01T00:00:00Z',
        addedBy: 1,
        username: 'legacyuser',
        email: '<EMAIL>',
        name: 'legacyuser',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      };

      mockTaskMasterService.addTeamMember.mockResolvedValue(mockNewMember);

      const requestBody = {
        user_email: '<EMAIL>',
        role: 'member'
      };

      const request = new NextRequest('http://localhost/api/task-master/projects/1/team', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      });
      const params = Promise.resolve({ id: '1' });
      
      const response = await POST(request, { params });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.teamMember).toBeDefined();
      
      // Verify the service was called with the correct email
      expect(mockTaskMasterService.addTeamMember).toHaveBeenCalledWith(
        1,
        expect.any(Object),
        '<EMAIL>',
        'member'
      );
    });

    test('should prioritize camelCase over snake_case when both are provided', async () => {
      const mockNewMember = {
        id: 2,
        projectId: 1,
        userId: 456,
        role: 'member',
        addedAt: '2024-01-01T00:00:00Z',
        addedBy: 1,
        username: 'priorityuser',
        email: '<EMAIL>',
        name: 'priorityuser',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      };

      mockTaskMasterService.addTeamMember.mockResolvedValue(mockNewMember);

      const requestBody = {
        userEmail: '<EMAIL>', // This should be used
        user_email: '<EMAIL>', // This should be ignored
        role: 'member'
      };

      const request = new NextRequest('http://localhost/api/task-master/projects/1/team', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      });
      const params = Promise.resolve({ id: '1' });
      
      const response = await POST(request, { params });

      expect(response.status).toBe(200);
      
      // Verify the camelCase version was used
      expect(mockTaskMasterService.addTeamMember).toHaveBeenCalledWith(
        1,
        expect.any(Object),
        '<EMAIL>',
        'member'
      );
    });

    test('should return error when no email is provided', async () => {
      const requestBody = {
        role: 'member'
        // No email provided
      };

      const request = new NextRequest('http://localhost/api/task-master/projects/1/team', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      });
      const params = Promise.resolve({ id: '1' });
      
      const response = await POST(request, { params });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('User email is required');
    });

    test('should handle service errors gracefully', async () => {
      mockTaskMasterService.addTeamMember.mockResolvedValue(null);

      const requestBody = {
        userEmail: '<EMAIL>',
        role: 'member'
      };

      const request = new NextRequest('http://localhost/api/task-master/projects/1/team', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      });
      const params = Promise.resolve({ id: '1' });
      
      const response = await POST(request, { params });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Failed to add team member');
    });
  });

  describe('Field Mapping Validation', () => {
    test('should ensure all team member fields use camelCase in responses', async () => {
      const mockTeamMembers = [
        {
          id: 1,
          projectId: 1,
          userId: 123,
          role: 'member',
          addedAt: '2024-01-01T00:00:00Z',
          addedBy: 1,
          username: 'testuser',
          email: '<EMAIL>',
          name: 'testuser',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ];

      mockTaskMasterService.getProjectTeamMembers.mockReturnValue(mockTeamMembers);

      const request = new NextRequest('http://localhost/api/task-master/projects/1/team');
      const params = Promise.resolve({ id: '1' });
      
      const response = await GET(request, { params });
      const data = await response.json();

      const member = data.teamMembers[0];
      
      // Check all expected camelCase fields are present
      const expectedCamelCaseFields = [
        'id', 'projectId', 'userId', 'role', 'addedAt', 'addedBy',
        'username', 'email', 'name', 'createdAt', 'updatedAt'
      ];
      
      for (const field of expectedCamelCaseFields) {
        expect(member).toHaveProperty(field);
      }
      
      // Check no snake_case fields are present
      const forbiddenSnakeCaseFields = [
        'project_id', 'user_id', 'added_at', 'added_by', 'created_at', 'updated_at'
      ];
      
      for (const field of forbiddenSnakeCaseFields) {
        expect(member).not.toHaveProperty(field);
      }
    });
  });
});
