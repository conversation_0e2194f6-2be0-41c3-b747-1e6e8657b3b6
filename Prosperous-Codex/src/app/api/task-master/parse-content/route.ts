import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/middleware';
import { aiParsingService } from '@/lib/services/ai-parsing-service';
import { ParseContentRequestSchema } from '@/lib/types/ai-parsing';

export async function POST(request: NextRequest) {
  try {
    // 1. Authenticate user
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    const { user } = authResult;

    // 2. Parse and validate request body
    const body = await request.json();

    // Validate input using Zod schema
    const validationResult = ParseContentRequestSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { inputText } = validationResult.data;

    // 3. Check if AI service is available
    if (!aiParsingService.isAvailable()) {
      return NextResponse.json(
        { error: 'AI parsing service is not available' },
        { status: 503 }
      );
    }

    // 4. Parse the content
    console.log(`Parsing content for user ${user.id}, length: ${inputText.length}`);
    
    const parsedContent = await aiParsingService.parseStructuredContent(inputText);
    
    console.log('Parsing successful:', {
      titleLength: parsedContent.title.length,
      eventLogLength: parsedContent.eventLog.length,
      descriptionLength: parsedContent.description.length,
      tasksCount: parsedContent.tasks.length,
      subtasksCount: parsedContent.tasks.reduce((sum, task) => sum + task.subtasks.length, 0)
    });

    // 5. Return parsed content
    return NextResponse.json({
      success: true,
      data: parsedContent
    });

  } catch (error) {
    console.error('Parse content API error:', error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to parse content',
        success: false 
      },
      { status: 500 }
    );
  }
}
