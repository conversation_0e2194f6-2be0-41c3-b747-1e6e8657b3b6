/**
 * AuthorizationService Unit Tests
 * 
 * Comprehensive unit tests for AuthorizationService
 * focusing on permission checks and access control
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import Database from 'better-sqlite3';
import { AuthorizationService } from '@/lib/task-master/authorization';
import { AuthorizationError } from '@/lib/task-master/errors';

describe('AuthorizationService', () => {
  let db: Database.Database;
  let authService: AuthorizationService;

  beforeEach(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Create test tables
    db.exec(`
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        username TEXT,
        password_hash TEXT NOT NULL,
        role TEXT DEFAULT 'user' CHECK (role IN ('user', 'moderator', 'admin')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        created_by INTEGER NOT NULL,
        assigned_to INTEGER,
        status TEXT DEFAULT 'todo',
        priority TEXT DEFAULT 'medium',
        progress INTEGER DEFAULT 0,
        due_date TEXT,
        visibility TEXT DEFAULT 'public',
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id),
        FOREIGN KEY (assigned_to) REFERENCES users(id)
      );

      CREATE TABLE tasks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        project_id INTEGER NOT NULL,
        created_by INTEGER NOT NULL,
        assigned_to INTEGER,
        status TEXT DEFAULT 'todo',
        priority TEXT DEFAULT 'medium',
        progress INTEGER DEFAULT 0,
        due_date TEXT,
        parent_task_id INTEGER,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id),
        FOREIGN KEY (created_by) REFERENCES users(id),
        FOREIGN KEY (assigned_to) REFERENCES users(id),
        FOREIGN KEY (parent_task_id) REFERENCES tasks(id)
      );

      CREATE TABLE comments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content TEXT NOT NULL,
        task_id INTEGER NOT NULL,
        created_by INTEGER NOT NULL,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT,
        FOREIGN KEY (task_id) REFERENCES tasks(id),
        FOREIGN KEY (created_by) REFERENCES users(id)
      );

      CREATE TABLE project_team_members (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        role TEXT NOT NULL DEFAULT 'member',
        joined_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id),
        FOREIGN KEY (user_id) REFERENCES users(id),
        UNIQUE(project_id, user_id)
      );
    `);

    authService = new AuthorizationService(db);

    // Create test data
    setupTestData();
  });

  afterEach(() => {
    if (db) {
      db.close();
    }
  });

  function setupTestData() {
    // Create test users
    db.prepare(`
      INSERT INTO users (id, email, username, password_hash, role) VALUES
      (1, '<EMAIL>', 'user1', 'hash1', 'user'),
      (2, '<EMAIL>', 'user2', 'hash2', 'user'),
      (3, '<EMAIL>', 'user3', 'hash3', 'user')
    `).run();

    // Create test projects
    db.prepare(`
      INSERT INTO projects (id, title, created_by, assigned_to) VALUES
      (1, 'Project 1', 1, 2),
      (2, 'Project 2', 2, NULL),
      (3, 'Project 3', 3, 3)
    `).run();

    // Create test tasks
    db.prepare(`
      INSERT INTO tasks (id, title, project_id, created_by, assigned_to) VALUES
      (1, 'Task 1', 1, 1, 2),
      (2, 'Task 2', 1, 2, 1),
      (3, 'Task 3', 3, 3, 3)
    `).run();

    // Create test comments
    db.prepare(`
      INSERT INTO comments (id, content, task_id, created_by) VALUES
      (1, 'Comment 1', 1, 1),
      (2, 'Comment 2', 1, 2),
      (3, 'Comment 3', 3, 3)
    `).run();

    // Create test team members
    db.prepare(`
      INSERT INTO project_team_members (project_id, user_id, role) VALUES
      (1, 1, 'owner'),
      (1, 2, 'member'),
      (2, 2, 'owner'),
      (2, 1, 'member'),
      (3, 3, 'owner')
    `).run();
  }

  describe('Project Authorization', () => {
    describe('canAccessProject', () => {
      it('should allow project owner to access', async () => {
        const canAccess = await authService.canAccessProject(1, 1);
        expect(canAccess).toBe(true);
      });

      it('should allow project assignee to access', async () => {
        const canAccess = await authService.canAccessProject(1, 2);
        expect(canAccess).toBe(true);
      });

      it('should allow team member to access', async () => {
        const canAccess = await authService.canAccessProject(2, 1);
        expect(canAccess).toBe(true);
      });

      it('should deny access to non-member', async () => {
        const canAccess = await authService.canAccessProject(3, 1);
        expect(canAccess).toBe(false);
      });

      it('should deny access to non-existent project', async () => {
        const canAccess = await authService.canAccessProject(999, 1);
        expect(canAccess).toBe(false);
      });
    });

    describe('canModifyProject', () => {
      it('should allow project owner to modify', async () => {
        const canModify = await authService.canModifyProject(1, 1);
        expect(canModify).toBe(true);
      });

      it('should deny modification to non-owner team member', async () => {
        const canModify = await authService.canModifyProject(1, 2);
        expect(canModify).toBe(false);
      });

      it('should deny modification to non-member', async () => {
        const canModify = await authService.canModifyProject(3, 1);
        expect(canModify).toBe(false);
      });
    });

    describe('canDeleteProject', () => {
      it('should allow project creator to delete', async () => {
        const canDelete = await authService.canDeleteProject(1, 1);
        expect(canDelete).toBe(true);
      });

      it('should deny deletion to non-creator', async () => {
        const canDelete = await authService.canDeleteProject(1, 2);
        expect(canDelete).toBe(false);
      });

      it('should deny deletion to non-member', async () => {
        const canDelete = await authService.canDeleteProject(3, 1);
        expect(canDelete).toBe(false);
      });
    });

    describe('requireProjectAccess', () => {
      it('should pass for authorized user', async () => {
        await expect(authService.requireProjectAccess(1, 1))
          .resolves.not.toThrow();
      });

      it('should throw AuthorizationError for unauthorized user', async () => {
        await expect(authService.requireProjectAccess(3, 1))
          .rejects.toThrow(AuthorizationError);
      });

      it('should include project ID in error message', async () => {
        try {
          await authService.requireProjectAccess(3, 1);
        } catch (error) {
          expect(error).toBeInstanceOf(AuthorizationError);
          expect((error as AuthorizationError).message).toContain('project 3');
        }
      });
    });
  });

  describe('Task Authorization', () => {
    describe('canAccessTask', () => {
      it('should allow task creator to access', async () => {
        const canAccess = await authService.canAccessTask(1, 1);
        expect(canAccess).toBe(true);
      });

      it('should allow task assignee to access', async () => {
        const canAccess = await authService.canAccessTask(1, 2);
        expect(canAccess).toBe(true);
      });

      it('should allow project team member to access task', async () => {
        const canAccess = await authService.canAccessTask(2, 1);
        expect(canAccess).toBe(true);
      });

      it('should deny access to non-member', async () => {
        const canAccess = await authService.canAccessTask(3, 1);
        expect(canAccess).toBe(false);
      });
    });

    describe('canModifyTask', () => {
      it('should allow task creator to modify', async () => {
        const canModify = await authService.canModifyTask(1, 1);
        expect(canModify).toBe(true);
      });

      it('should allow task assignee to modify', async () => {
        const canModify = await authService.canModifyTask(1, 2);
        expect(canModify).toBe(true);
      });

      it('should allow project owner to modify any task', async () => {
        const canModify = await authService.canModifyTask(2, 1);
        expect(canModify).toBe(true);
      });

      it('should deny modification to non-authorized user', async () => {
        const canModify = await authService.canModifyTask(3, 1);
        expect(canModify).toBe(false);
      });
    });

    describe('canDeleteTask', () => {
      it('should allow task creator to delete', async () => {
        const canDelete = await authService.canDeleteTask(1, 1);
        expect(canDelete).toBe(true);
      });

      it('should allow project owner to delete any task', async () => {
        const canDelete = await authService.canDeleteTask(2, 1);
        expect(canDelete).toBe(true);
      });

      it('should deny deletion to task assignee (not creator)', async () => {
        const canDelete = await authService.canDeleteTask(1, 2);
        expect(canDelete).toBe(false);
      });

      it('should deny deletion to non-authorized user', async () => {
        const canDelete = await authService.canDeleteTask(3, 1);
        expect(canDelete).toBe(false);
      });
    });
  });

  describe('Comment Authorization', () => {
    describe('canAccessComment', () => {
      it('should allow comment creator to access', async () => {
        const canAccess = await authService.canAccessComment(1, 1);
        expect(canAccess).toBe(true);
      });

      it('should allow project team member to access comment', async () => {
        const canAccess = await authService.canAccessComment(1, 2);
        expect(canAccess).toBe(true);
      });

      it('should deny access to non-member', async () => {
        const canAccess = await authService.canAccessComment(3, 1);
        expect(canAccess).toBe(false);
      });
    });

    describe('canModifyComment', () => {
      it('should allow comment creator to modify', async () => {
        const canModify = await authService.canModifyComment(1, 1);
        expect(canModify).toBe(true);
      });

      it('should deny modification to non-creator', async () => {
        const canModify = await authService.canModifyComment(1, 2);
        expect(canModify).toBe(false);
      });
    });

    describe('canDeleteComment', () => {
      it('should allow comment creator to delete', async () => {
        const canDelete = await authService.canDeleteComment(1, 1);
        expect(canDelete).toBe(true);
      });

      it('should allow project owner to delete any comment', async () => {
        const canDelete = await authService.canDeleteComment(2, 1);
        expect(canDelete).toBe(true);
      });

      it('should deny deletion to non-authorized user', async () => {
        const canDelete = await authService.canDeleteComment(1, 2);
        expect(canDelete).toBe(false);
      });
    });
  });

  describe('Team Member Authorization', () => {
    describe('canManageTeam', () => {
      it('should allow project owner to manage team', async () => {
        const canManage = await authService.canManageTeam(1, 1);
        expect(canManage).toBe(true);
      });

      it('should deny team management to non-owner', async () => {
        const canManage = await authService.canManageTeam(1, 2);
        expect(canManage).toBe(false);
      });

      it('should deny team management to non-member', async () => {
        const canManage = await authService.canManageTeam(3, 1);
        expect(canManage).toBe(false);
      });
    });

    describe('getUserProjectRole', () => {
      it('should return owner role for project creator', async () => {
        const role = await authService.getUserProjectRole(1, 1);
        expect(role).toBe('owner');
      });

      it('should return member role for team member', async () => {
        const role = await authService.getUserProjectRole(1, 2);
        expect(role).toBe('member');
      });

      it('should return null for non-member', async () => {
        const role = await authService.getUserProjectRole(3, 1);
        expect(role).toBeNull();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Close database to simulate error
      db.close();

      await expect(authService.canAccessProject(1, 1))
        .rejects.toThrow();
    });

    it('should provide detailed error messages', async () => {
      try {
        await authService.requireProjectAccess(999, 1);
      } catch (error) {
        expect(error).toBeInstanceOf(AuthorizationError);
        expect((error as AuthorizationError).message).toContain('Access denied');
        expect((error as AuthorizationError).context).toHaveProperty('projectId', 999);
        expect((error as AuthorizationError).context).toHaveProperty('userId', 1);
      }
    });
  });

  describe('Performance', () => {
    it('should handle multiple authorization checks efficiently', async () => {
      const startTime = Date.now();
      
      // Perform multiple authorization checks
      const promises = [];
      for (let i = 0; i < 100; i++) {
        promises.push(authService.canAccessProject(1, 1));
      }
      
      await Promise.all(promises);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });
  });

  describe('Edge Cases', () => {
    it('should handle null/undefined user IDs', async () => {
      await expect(authService.canAccessProject(1, null as any))
        .resolves.toBe(false);
      
      await expect(authService.canAccessProject(1, undefined as any))
        .resolves.toBe(false);
    });

    it('should handle null/undefined resource IDs', async () => {
      await expect(authService.canAccessProject(null as any, 1))
        .resolves.toBe(false);
      
      await expect(authService.canAccessProject(undefined as any, 1))
        .resolves.toBe(false);
    });

    it('should handle negative IDs', async () => {
      await expect(authService.canAccessProject(-1, 1))
        .resolves.toBe(false);
      
      await expect(authService.canAccessProject(1, -1))
        .resolves.toBe(false);
    });
  });
});
