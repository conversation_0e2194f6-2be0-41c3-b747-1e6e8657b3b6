"use client";

import { useSession, signOut } from 'next-auth/react';
import { useSidebar } from '@/contexts/sidebar-context';
import { useSelectedComponents } from '@/contexts/selected-components-context';
import { Button } from '@/components/ui/button';
import { ThemeSwitcher } from '@/components/theme-switcher';
import { LanguageSwitcher } from '@/components/language-switcher';
import { LogOut, Menu, ShoppingCart, Calculator } from 'lucide-react';
import SelectedComponentDrawer from '@/components/SelectedComponent';
import { Link } from '@/i18n/navigation';
import { usePathname } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';
import SimpleCalculator from '@/components/simple-calculator';

export function ProtectedHeader() {
  const { data: session } = useSession();
  const currentUser = session?.user;
  const { toggle: toggleSidebar, toggleCollapse, isCollapsed, isMobile } = useSidebar();
  const { selectedComponents, isDrawerOpen, setIsDrawerOpen, removeComponent } = useSelectedComponents();
  const t = useTranslations('header');
  const [isCalculatorOpen, setIsCalculatorOpen] = useState(false);
  const pathname = usePathname();

  return (
    <header className="sticky top-0 left-0 right-0 z-[100] bg-white dark:bg-neutral-800 shadow-md w-full py-2">
      <div className="w-full px-4 sm:px-6 lg:px-8 flex items-center justify-between">
        {/* Left side with hamburger and brand */}
        <div className="flex items-center space-x-4">
          {/* Hamburger button - hide when sidebar is expanded on desktop */}
          {(isCollapsed || isMobile) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (isMobile) {
                  toggleSidebar();
                } else {
                  toggleCollapse();
                }
              }}
              className="p-2"
            >
              <Menu className="w-5 h-5" />
            </Button>
          )}

          {/* Brand - always visible */}
          <Link href="/dashboard" className="flex items-center gap-2 text-xl font-semibold tracking-tight brand-title">
            <Image
              src="/ppcl-p-only-logo.png"
              alt="PPCL Logo"
              width={24}
              height={24}
              className="w-6 h-6"
            />
            <span className="hidden lg:block whitespace-nowrap">Prosperous Codex</span>
          </Link>
        </div>

        {/* Controls and User Info (Right) */}
        <div className="flex items-center space-x-4">
          <div className="hidden sm:block">
            <LanguageSwitcher />
          </div>
          <div className="hidden md:block">
            <ThemeSwitcher />
          </div>

          {/* Calculator Button - Always visible next to shopping cart */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCalculatorOpen(true)}
            className="h-9 w-9 p-0 hover:bg-[#5E6AD2]/10 dark:hover:bg-[#6E56CF]/10 transition-colors"
            title={t('calculator')}
          >
            <Calculator className="h-4 w-4 text-[#5E6AD2] dark:text-[#6E56CF]" />
          </Button>

          {/* Shopping Cart Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsDrawerOpen(true)}
            className="relative h-9 w-9 p-0 hover:bg-[#5E6AD2]/10 dark:hover:bg-[#6E56CF]/10 transition-colors"
            title={t('viewCart')}
          >
            <ShoppingCart className="h-4 w-4 text-[#5E6AD2] dark:text-[#6E56CF]" />
            {selectedComponents.length > 0 && (
              <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-[#5E6AD2] dark:bg-[#6E56CF] text-white text-xs font-medium flex items-center justify-center">
                {selectedComponents.length}
              </span>
            )}
          </Button>

          {/* Welcome message - only show on dashboard */}
          {currentUser && pathname === '/dashboard' && (
            <span className="text-sm text-muted-foreground hidden xl:block">
              Welcome, {currentUser.name || currentUser.email}!
            </span>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={() => signOut({ callbackUrl: "/auth/login" })}
            className="text-accent-foreground hover:bg-accent/20"
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span className="hidden sm:inline">{t('logout')}</span>
          </Button>
        </div>
      </div>

      {/* Simple Calculator Modal */}
      <SimpleCalculator 
        open={isCalculatorOpen} 
        onOpenChange={setIsCalculatorOpen} 
      />
    </header>
  );
}
