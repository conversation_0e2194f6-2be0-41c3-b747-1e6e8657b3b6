"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type UnitType = 'mm' | 'in';

interface UnitContextType {
  unit: UnitType;
  setUnit: (unit: UnitType) => void;
  toggleUnit: () => void;
  convertToMm: (value: number, fromUnit: UnitType) => number;
  convertFromMm: (value: number, toUnit: UnitType) => number;
  formatValue: (value: number, unit: UnitType) => string;
  // Helper functions for simple conversion
  convertDisplayValueToMm: (displayValue: string, currentUnit: UnitType) => string;
  convertMmToDisplayValue: (mmValue: string, targetUnit: UnitType) => string;
}

const UnitContext = createContext<UnitContextType | undefined>(undefined);

interface UnitProviderProps {
  children: ReactNode;
}

export function UnitProvider({ children }: UnitProviderProps) {
  const [unit, setUnitState] = useState<UnitType>('mm');

  // Load unit preference from localStorage on mount
  useEffect(() => {
    const savedUnit = localStorage.getItem('paperCostEstimator_unit') as UnitType;
    if (savedUnit === 'mm' || savedUnit === 'in') {
      setUnitState(savedUnit);
    }
  }, []);

  // Save unit preference to localStorage when changed
  const setUnit = (newUnit: UnitType) => {
    setUnitState(newUnit);
    localStorage.setItem('paperCostEstimator_unit', newUnit);
  };

  const toggleUnit = () => {
    setUnit(unit === 'mm' ? 'in' : 'mm');
  };

  // Convert any value to mm (for backend calculations)
  const convertToMm = (value: number, fromUnit: UnitType): number => {
    if (fromUnit === 'mm') return value;
    return value * 25.4; // inches to mm
  };

  // Convert mm value to specified unit (for display)
  const convertFromMm = (value: number, toUnit: UnitType): number => {
    if (toUnit === 'mm') return value;
    return value / 25.4; // mm to inches
  };

  // Format value for display with appropriate precision
  const formatValue = (value: number, unit: UnitType): string => {
    if (unit === 'mm') {
      return value.toFixed(1);
    } else {
      return value.toFixed(2);
    }
  };

  // Helper function to convert display value to mm (for calculation)
  const convertDisplayValueToMm = (displayValue: string, currentUnit: UnitType): string => {
    if (!displayValue || displayValue === '') return '';
    const numValue = parseFloat(displayValue);
    if (isNaN(numValue)) return '';

    if (currentUnit === 'mm') {
      return displayValue;
    } else {
      // Convert inches to mm
      const mmValue = numValue * 25.4;
      return mmValue.toFixed(1);
    }
  };

  // Helper function to convert mm value to display unit
  const convertMmToDisplayValue = (mmValue: string, targetUnit: UnitType): string => {
    if (!mmValue || mmValue === '') return '';
    const numValue = parseFloat(mmValue);
    if (isNaN(numValue)) return '';

    if (targetUnit === 'mm') {
      return mmValue;
    } else {
      // Convert mm to inches
      const inchValue = numValue / 25.4;
      return inchValue.toFixed(2);
    }
  };

  const contextValue: UnitContextType = {
    unit,
    setUnit,
    toggleUnit,
    convertToMm,
    convertFromMm,
    formatValue,
    convertDisplayValueToMm,
    convertMmToDisplayValue
  };

  return (
    <UnitContext.Provider value={contextValue}>
      {children}
    </UnitContext.Provider>
  );
}

export function useUnit() {
  const context = useContext(UnitContext);
  if (context === undefined) {
    throw new Error('useUnit must be used within a UnitProvider');
  }
  return context;
}
