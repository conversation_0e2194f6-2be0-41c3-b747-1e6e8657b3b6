#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to add multiple users to the Prosperous Codex system
 * Usage: node scripts/add-users.js
 */

const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');
const bcrypt = require('bcryptjs');

// Users to add
const usersToAdd = [
  {
    email: '<EMAIL>',
    username: '<PERSON>',
    password: 'candy123', // Default password - users should change this
    role: 'user'
  },
  {
    email: '<EMAIL>',
    username: '<PERSON> Chan',
    password: 'rainbow123', // Default password - users should change this
    role: 'user'
  },
  {
    email: '<EMAIL>',
    username: '<PERSON>',
    password: 'elvis123', // Default password - users should change this
    role: 'user'
  },
  {
    email: '<EMAIL>',
    username: '<PERSON> Chiu',
    password: 'florence123', // Default password - users should change this
    role: 'user'
  },
  {
    email: '<PERSON><PERSON>@prosperous-printing.com',
    username: '<PERSON>',
    password: 'harry123', // Default password - users should change this
    role: 'user'
  }
];

async function addUsers() {
  try {
    console.log('🚀 Starting user creation process...');
    console.log('');

    // Connect to database
    const dbPath = path.join(__dirname, '../data/prosperous-codex.db');
    if (!fs.existsSync(dbPath)) {
      console.error('❌ Database not found. Please run: npm run init-db');
      process.exit(1);
    }

    const db = new Database(dbPath);
    const results = [];

    // Prepare statements
    const getUserByEmailStmt = db.prepare('SELECT * FROM users WHERE email = ?');
    const insertUserStmt = db.prepare(`
      INSERT INTO users (email, username, password_hash, role, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `);

    for (const userData of usersToAdd) {
      try {
        console.log(`👤 Creating user: ${userData.email} (${userData.username})`);

        // Check if user already exists
        const existingUser = getUserByEmailStmt.get(userData.email);
        if (existingUser) {
          console.log(`   ⚠️  User ${userData.email} already exists - skipping`);
          results.push({
            email: userData.email,
            status: 'skipped',
            reason: 'User already exists'
          });
          continue;
        }

        // Hash password
        const passwordHash = await bcrypt.hash(userData.password, 12);

        // Create the user
        const result = insertUserStmt.run(
          userData.email,
          userData.username,
          passwordHash,
          userData.role
        );

        console.log(`   ✅ Successfully created user: ${userData.email}`);
        results.push({
          email: userData.email,
          status: 'created',
          id: result.lastInsertRowid
        });

      } catch (error) {
        console.log(`   ❌ Failed to create user ${userData.email}: ${error.message}`);
        results.push({
          email: userData.email,
          status: 'failed',
          error: error.message
        });
      }
    }

    db.close();
    
    console.log('');
    console.log('📊 Summary:');
    console.log('='.repeat(50));
    
    const created = results.filter(r => r.status === 'created');
    const skipped = results.filter(r => r.status === 'skipped');
    const failed = results.filter(r => r.status === 'failed');
    
    console.log(`✅ Created: ${created.length} users`);
    console.log(`⚠️  Skipped: ${skipped.length} users`);
    console.log(`❌ Failed: ${failed.length} users`);
    
    if (created.length > 0) {
      console.log('');
      console.log('🔑 Default Passwords (users should change these):');
      console.log('-'.repeat(50));
      usersToAdd.forEach(user => {
        const result = results.find(r => r.email === user.email);
        if (result && result.status === 'created') {
          console.log(`${user.email} / ${user.password}`);
        }
      });
    }
    
    if (failed.length > 0) {
      console.log('');
      console.log('❌ Failed Users:');
      console.log('-'.repeat(50));
      failed.forEach(result => {
        console.log(`${result.email}: ${result.error}`);
      });
    }
    
    console.log('');
    console.log('✅ User creation process completed!');
    
  } catch (error) {
    console.error('❌ Error during user creation:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  addUsers();
}

module.exports = { addUsers };
