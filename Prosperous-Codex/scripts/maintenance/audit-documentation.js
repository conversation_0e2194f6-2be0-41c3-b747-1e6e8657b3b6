#!/usr/bin/env node

/**
 * Documentation Audit Tool
 * 
 * Audits project documentation for field naming consistency and accuracy.
 * Identifies outdated documentation that references old field names or patterns.
 */

const fs = require('fs');
const path = require('path');

class DocumentationAuditor {
  constructor() {
    this.findings = [];
    this.config = {
      // Documentation directories to audit
      docDirectories: [
        'docs',
        'README.md',
        'CONTRIBUTING.md',
        'API.md',
        'CHANGELOG.md',
        'src/**/*.md',
        'scripts/**/*.md'
      ],
      
      // Code files with significant documentation
      codeWithDocs: [
        'src/lib/task-master/field-mapping.ts',
        'src/lib/task-master/types.ts',
        'src/lib/types/*.ts',
        'src/app/api/**/*.ts'
      ],
      
      // Patterns to check for outdated references
      outdatedPatterns: [
        // Old field names that should be updated
        { pattern: /user_id/g, suggestion: 'userId', context: 'API/Frontend' },
        { pattern: /project_id/g, suggestion: 'projectId', context: 'API/Frontend' },
        { pattern: /team_members/g, suggestion: 'teamMembers', context: 'API/Frontend' },
        { pattern: /created_at/g, suggestion: 'createdAt', context: 'API/Frontend' },
        { pattern: /updated_at/g, suggestion: 'updatedAt', context: 'API/Frontend' },
        { pattern: /added_at/g, suggestion: 'addedAt', context: 'API/Frontend' },
        { pattern: /due_date/g, suggestion: 'dueDate', context: 'API/Frontend' },
        { pattern: /assigned_to/g, suggestion: 'assignedTo', context: 'API/Frontend' },
        { pattern: /created_by/g, suggestion: 'createdBy', context: 'API/Frontend' },
        { pattern: /full_description/g, suggestion: 'fullDescription', context: 'API/Frontend' },
        { pattern: /event_log/g, suggestion: 'eventLog', context: 'API/Frontend' },
        { pattern: /user_email/g, suggestion: 'userEmail', context: 'API/Frontend' },
        { pattern: /parent_task_id/g, suggestion: 'parentTaskId', context: 'API/Frontend' },
        
        // Outdated technology references
        { pattern: /custom SQLite.*authentication/gi, suggestion: 'NextAuth.js authentication', context: 'Architecture' },
        { pattern: /manual initialization.*database/gi, suggestion: 'automatic database initialization', context: 'Setup' },
        { pattern: /field migration checklist/gi, suggestion: 'automated field mapping system', context: 'Development' },
        
        // Outdated component references
        { pattern: /Paper Cost Estimator/g, suggestion: 'Prosperous Codex', context: 'Branding' },
        { pattern: /separate page navigation/gi, suggestion: 'in-page transitions', context: 'UI/UX' },
        { pattern: /blue.*primary.*buttons/gi, suggestion: 'purple color scheme', context: 'Design' },
        { pattern: /toggle switches.*multi-option/gi, suggestion: 'dropdown/select menus', context: 'UI Components' }
      ],
      
      // Required documentation sections
      requiredSections: [
        'Field Naming Conventions',
        'API Documentation',
        'Database Schema',
        'Component Usage',
        'Testing Guidelines',
        'Migration Guide'
      ]
    };
  }

  /**
   * Run comprehensive documentation audit
   */
  async audit() {
    console.log('📚 Starting Documentation Audit...\n');
    
    // Audit documentation files
    await this.auditDocumentationFiles();
    
    // Audit code documentation
    await this.auditCodeDocumentation();
    
    // Check for missing documentation
    await this.checkMissingDocumentation();
    
    // Generate audit report
    this.generateAuditReport();
    
    return this.findings.length === 0;
  }

  /**
   * Audit standalone documentation files
   */
  async auditDocumentationFiles() {
    console.log('📄 Auditing Documentation Files...');
    
    for (const docPath of this.config.docDirectories) {
      await this.auditPath(docPath, 'documentation');
    }
  }

  /**
   * Audit documentation in code files
   */
  async auditCodeDocumentation() {
    console.log('💻 Auditing Code Documentation...');
    
    for (const codePath of this.config.codeWithDocs) {
      await this.auditPath(codePath, 'code');
    }
  }

  /**
   * Audit specific path for documentation issues
   */
  async auditPath(pathPattern, type) {
    const files = this.expandGlob(pathPattern);
    
    for (const file of files) {
      if (fs.existsSync(file) && fs.statSync(file).isFile()) {
        await this.auditFile(file, type);
      }
    }
  }

  /**
   * Simple glob expansion (basic implementation)
   */
  expandGlob(pattern) {
    if (pattern.includes('*')) {
      // Basic glob handling - in production, use a proper glob library
      const baseDir = pattern.split('*')[0];
      if (fs.existsSync(baseDir)) {
        return this.getFilesRecursively(baseDir).filter(file => 
          file.endsWith('.md') || file.endsWith('.ts') || file.endsWith('.tsx')
        );
      }
      return [];
    }
    return [pattern];
  }

  /**
   * Get files recursively from directory
   */
  getFilesRecursively(dir) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files.push(...this.getFilesRecursively(fullPath));
        } else if (stat.isFile()) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Directory doesn't exist or can't be read
    }
    
    return files;
  }

  /**
   * Audit individual file
   */
  async auditFile(filePath, type) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      lines.forEach((line, lineNumber) => {
        this.auditLine(line, lineNumber + 1, filePath, type);
      });
      
    } catch (error) {
      this.addFinding({
        type: 'ERROR',
        severity: 'HIGH',
        file: filePath,
        message: `Failed to read file: ${error.message}`,
        category: 'File Access'
      });
    }
  }

  /**
   * Audit individual line for documentation issues
   */
  auditLine(line, lineNumber, filePath, type) {
    for (const pattern of this.config.outdatedPatterns) {
      const matches = line.match(pattern.pattern);
      
      if (matches) {
        this.addFinding({
          type: 'OUTDATED_REFERENCE',
          severity: 'MEDIUM',
          file: filePath,
          line: lineNumber,
          content: line.trim(),
          pattern: pattern.pattern.source,
          suggestion: pattern.suggestion,
          context: pattern.context,
          category: 'Outdated Documentation'
        });
      }
    }
  }

  /**
   * Check for missing required documentation sections
   */
  async checkMissingDocumentation() {
    console.log('🔍 Checking for Missing Documentation...');
    
    const mainReadme = path.join(process.cwd(), 'README.md');
    const docsDir = path.join(process.cwd(), 'docs');
    
    // Check if main README exists
    if (!fs.existsSync(mainReadme)) {
      this.addFinding({
        type: 'MISSING_DOCUMENTATION',
        severity: 'HIGH',
        file: 'README.md',
        message: 'Main README.md file is missing',
        category: 'Missing Files'
      });
    }
    
    // Check for required documentation sections
    for (const section of this.config.requiredSections) {
      const hasSection = await this.checkForDocumentationSection(section);
      
      if (!hasSection) {
        this.addFinding({
          type: 'MISSING_SECTION',
          severity: 'MEDIUM',
          section: section,
          message: `Missing documentation section: ${section}`,
          category: 'Missing Content'
        });
      }
    }
  }

  /**
   * Check if documentation section exists
   */
  async checkForDocumentationSection(sectionName) {
    const searchPaths = [
      'README.md',
      'docs/',
      'src/lib/task-master/',
      'API.md'
    ];
    
    for (const searchPath of searchPaths) {
      const files = this.expandGlob(searchPath + '**/*.md');
      
      for (const file of files) {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8');
          
          if (content.toLowerCase().includes(sectionName.toLowerCase())) {
            return true;
          }
        }
      }
    }
    
    return false;
  }

  /**
   * Add finding to audit results
   */
  addFinding(finding) {
    this.findings.push({
      ...finding,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Generate comprehensive audit report
   */
  generateAuditReport() {
    console.log('\n📋 Documentation Audit Report');
    console.log('==============================');
    
    const summary = this.generateSummary();
    
    console.log(`📊 Summary:`);
    console.log(`   Total Issues: ${this.findings.length}`);
    console.log(`   High Severity: ${summary.high}`);
    console.log(`   Medium Severity: ${summary.medium}`);
    console.log(`   Low Severity: ${summary.low}`);
    
    if (this.findings.length === 0) {
      console.log('\n✅ No documentation issues found! All documentation is up-to-date.');
      return;
    }
    
    // Group findings by category
    const categories = this.groupFindingsByCategory();
    
    for (const [category, findings] of Object.entries(categories)) {
      console.log(`\n📂 ${category} (${findings.length} issues):`);
      
      findings.slice(0, 5).forEach(finding => {
        console.log(`  ${this.getSeverityIcon(finding.severity)} ${finding.message || finding.type}`);
        
        if (finding.file) {
          console.log(`     File: ${path.relative(process.cwd(), finding.file)}${finding.line ? `:${finding.line}` : ''}`);
        }
        
        if (finding.suggestion) {
          console.log(`     Suggestion: ${finding.suggestion}`);
        }
        
        console.log('');
      });
      
      if (findings.length > 5) {
        console.log(`     ... and ${findings.length - 5} more issues`);
      }
    }
    
    console.log('\n🛠️  Recommended Actions:');
    console.log('1. Update field name references in documentation');
    console.log('2. Add missing documentation sections');
    console.log('3. Review and update outdated technology references');
    console.log('4. Ensure API documentation reflects current field naming');
    
    // Save detailed report
    this.saveDetailedReport();
  }

  /**
   * Generate summary statistics
   */
  generateSummary() {
    return {
      high: this.findings.filter(f => f.severity === 'HIGH').length,
      medium: this.findings.filter(f => f.severity === 'MEDIUM').length,
      low: this.findings.filter(f => f.severity === 'LOW').length
    };
  }

  /**
   * Group findings by category
   */
  groupFindingsByCategory() {
    const grouped = {};
    
    this.findings.forEach(finding => {
      const category = finding.category || 'Other';
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(finding);
    });
    
    return grouped;
  }

  /**
   * Get icon for severity level
   */
  getSeverityIcon(severity) {
    switch (severity) {
      case 'HIGH': return '🚨';
      case 'MEDIUM': return '⚠️';
      case 'LOW': return 'ℹ️';
      default: return '📝';
    }
  }

  /**
   * Save detailed audit report
   */
  saveDetailedReport() {
    const reportPath = path.join(process.cwd(), 'documentation-audit-report.json');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: this.generateSummary(),
      findings: this.findings,
      recommendations: [
        'Update field name references to use camelCase in API/frontend documentation',
        'Add comprehensive field naming convention documentation',
        'Create migration guide for field naming changes',
        'Update component usage examples with current field names',
        'Review and update all technology stack references'
      ]
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 Detailed audit report saved to: ${reportPath}`);
  }
}

// Run audit if called directly
if (require.main === module) {
  const auditor = new DocumentationAuditor();
  
  auditor.audit().then(success => {
    if (success) {
      console.log('\n🎉 Documentation audit passed!');
      process.exit(0);
    } else {
      console.log('\n📝 Documentation audit completed with findings. Please review the issues above.');
      process.exit(1);
    }
  }).catch(error => {
    console.error('❌ Documentation audit failed:', error);
    process.exit(1);
  });
}

module.exports = DocumentationAuditor;
