{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/lib/task-master": ["./src/lib/task-master"], "@/lib/task-master/*": ["./src/lib/task-master/*"], "@/task-master/services": ["./src/lib/task-master/services"], "@/task-master/services/*": ["./src/lib/task-master/services/*"], "@/task-master/schemas": ["./src/lib/task-master/schemas"], "@/task-master/schemas/*": ["./src/lib/task-master/schemas/*"], "@/task-master/types": ["./src/lib/task-master/types"], "@/task-master/types/*": ["./src/lib/task-master/types/*"], "@/task-master/utils": ["./src/lib/task-master/utils"], "@/task-master/utils/*": ["./src/lib/task-master/utils/*"], "@/components": ["./src/components"], "@/components/*": ["./src/components/*"], "@/hooks": ["./src/hooks"], "@/hooks/*": ["./src/hooks/*"], "@/api": ["./src/app/api"], "@/api/*": ["./src/app/api/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}