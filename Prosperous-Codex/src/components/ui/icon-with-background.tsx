"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const iconWithBackgroundVariants = cva(
  "flex items-center justify-center rounded-full",
  {
    variants: {
      variant: {
        brand: "bg-[#5E6AD2]/10 text-[#5E6AD2] dark:bg-[#6E56CF]/20 dark:text-[#9E8CFC]",
        neutral: "bg-gray-100 text-gray-600 dark:bg-neutral-800 dark:text-neutral-400",
        warning: "bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400",
        success: "bg-emerald-100 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400",
        error: "bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400",
      },
      size: {
        "x-small": "h-5 w-5 text-xs",
        small: "h-6 w-6 text-sm",
        medium: "h-8 w-8 text-base",
        large: "h-12 w-12 text-lg",
        "x-large": "h-16 w-16 text-xl",
      },
      square: {
        true: "rounded-md",
        false: "rounded-full",
      },
    },
    defaultVariants: {
      variant: "brand",
      size: "small",
      square: false,
    },
  }
)

export interface IconWithBackgroundProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof iconWithBackgroundVariants> {
  icon?: React.ReactNode
}

const IconWithBackground = React.forwardRef<
  HTMLDivElement,
  IconWithBackgroundProps
>(({ className, variant, size, square, icon, ...props }, ref) => {
  return (
    <div
      className={cn(iconWithBackgroundVariants({ variant, size, square, className }))}
      ref={ref}
      {...props}
    >
      {icon}
    </div>
  )
})
IconWithBackground.displayName = "IconWithBackground"

export { IconWithBackground, iconWithBackgroundVariants }
