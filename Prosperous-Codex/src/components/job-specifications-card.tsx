"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface JobSpecificationsProps {
  className?: string;
  onDataChange?: (data: JobSpecificationData) => void;
}

export interface JobSpecificationData {
  trimH: number;
  trimW: number;
  totalPages: number;
  quantity: number;
  bindingMethod: string;
  spoilagePct: number;
}

export default function JobSpecificationsCard({ className = "", onDataChange }: JobSpecificationsProps) {
  const [pageHeightMm, setPageHeightMm] = useState("297");
  const [pageHeightIn, setPageHeightIn] = useState("");
  const [pageWidthMm, setPageWidthMm] = useState("210");
  const [pageWidthIn, setPageWidthIn] = useState("");
  const [totalPages, setTotalPages] = useState("320");
  const [quantity, setQuantity] = useState("1000");
  const [bindingMethod, setBindingMethod] = useState("Saddle Stitch");
  const [spoilage, setSpoilage] = useState("5");
  
  // Track which field was last edited to prevent infinite loops
  const [lastEditedHeight, setLastEditedHeight] = useState<'mm' | 'in' | null>(null);
  const [lastEditedWidth, setLastEditedWidth] = useState<'mm' | 'in' | null>(null);

  // Notify parent component when data changes
  useEffect(() => {
    if (onDataChange) {
      const data: JobSpecificationData = {
        trimH: parseFloat(pageHeightMm) || 0,
        trimW: parseFloat(pageWidthMm) || 0,
        totalPages: parseInt(totalPages) || 0,
        quantity: parseInt(quantity) || 0,
        bindingMethod,
        spoilagePct: (parseFloat(spoilage) || 0) / 100
      };
      onDataChange(data);
    }
  }, [pageHeightMm, pageWidthMm, totalPages, quantity, bindingMethod, spoilage, onDataChange]);

  // Convert mm to inches
  const mmToInches = (mm: string) => {
    const mmValue = parseFloat(mm);
    if (isNaN(mmValue)) return "";
    return (mmValue / 25.4).toFixed(2);
  };

  // Convert inches to mm
  const inchesToMm = (inches: string) => {
    const inchValue = parseFloat(inches);
    if (isNaN(inchValue)) return "";
    return (inchValue * 25.4).toFixed(0);
  };

  // Input validation function
  const validateNumberInput = (value: string) => {
    // Allow empty string, numbers, decimal points, and negative signs
    return value === '' || /^-?\d*\.?\d*$/.test(value);
  };

  const handlePageHeightMmChange = (value: string) => {
    if (!validateNumberInput(value)) return;
    setPageHeightMm(value);
    setLastEditedHeight('mm');
    if (value && !isNaN(Number(value)) && Number(value) > 0) {
      setPageHeightIn(mmToInches(value));
    } else if (value === '') {
      setPageHeightIn('');
    }
  };

  const handlePageHeightInChange = (value: string) => {
    if (!validateNumberInput(value)) return;
    setPageHeightIn(value);
    setLastEditedHeight('in');
    if (value && !isNaN(Number(value)) && Number(value) > 0) {
      setPageHeightMm(inchesToMm(value));
    } else if (value === '') {
      setPageHeightMm('');
    }
  };

  const handlePageWidthMmChange = (value: string) => {
    if (!validateNumberInput(value)) return;
    setPageWidthMm(value);
    setLastEditedWidth('mm');
    if (value && !isNaN(Number(value)) && Number(value) > 0) {
      setPageWidthIn(mmToInches(value));
    } else if (value === '') {
      setPageWidthIn('');
    }
  };

  const handlePageWidthInChange = (value: string) => {
    if (!validateNumberInput(value)) return;
    setPageWidthIn(value);
    setLastEditedWidth('in');
    if (value && !isNaN(Number(value)) && Number(value) > 0) {
      setPageWidthMm(inchesToMm(value));
    } else if (value === '') {
      setPageWidthMm('');
    }
  };

  return (
    <Card className={className} data-glow>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <span className="w-2 h-2 bg-[#5E6AD2] dark:bg-[#6E56CF] rounded-full mr-2"></span>
          Job Specifications
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Page Height Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="page-height-mm" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              Page Height
            </Label>
            <span className="text-xs text-neutral-500 dark:text-neutral-400 font-medium">
              mm
            </span>
            </div>
            <Input
              id="page-height-mm"
              type="number"
              placeholder="e.g., 225"
              value={pageHeightMm}
              onChange={(e) => handlePageHeightMmChange(e.target.value)}
              className="h-9 text-sm"
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="page-height-in" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              Page Height
            </Label>
            <span className="text-xs text-neutral-500 dark:text-neutral-400 font-medium">
              in
            </span>
            </div>
            <Input
              id="page-height-in"
              type="number"
              step="0.01"
              placeholder="e.g., 8.86"
              value={pageHeightIn}
              onChange={(e) => handlePageHeightInChange(e.target.value)}
              className="h-9 text-sm"
            />
          </div>
        </div>

        {/* Page Width Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="page-width-mm" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              Page Width
            </Label>
            <span className="text-xs text-neutral-500 dark:text-neutral-400 font-medium">
              mm
            </span>
            </div>
            <Input
              id="page-width-mm"
              type="number"
              placeholder="150"
              value={pageWidthMm}
              onChange={(e) => handlePageWidthMmChange(e.target.value)}
              className="h-9 text-sm"
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="page-width-in" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              Page Width
            </Label>
            <span className="text-xs text-neutral-500 dark:text-neutral-400 font-medium">
              in
            </span>
            </div>
            <Input
              id="page-width-in"
              type="number"
              step="0.01"
              placeholder="e.g., 5.91"
              value={pageWidthIn}
              onChange={(e) => handlePageWidthInChange(e.target.value)}
              className="h-9 text-sm"
            />
          </div>
        </div>

        {/* Total Pages and Quantity Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="total-pages" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Total Pages
          </Label>
            <Input
              id="total-pages"
              type="number"
              placeholder="320"
              value={totalPages}
              onChange={(e) => setTotalPages(e.target.value)}
              className="mt-1 h-9 text-sm"
            />
          </div>
          <div>
            <Label htmlFor="quantity" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Quantity
          </Label>
            <Input
              id="quantity"
              type="number"
              placeholder="1000"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              className="mt-1 h-9 text-sm"
            />
          </div>
        </div>

        {/* Binding Method and Spoilage Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="binding-method" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Binding Method
          </Label>
            <Select value={bindingMethod} onValueChange={setBindingMethod}>
              <SelectTrigger className="mt-1 h-9 text-sm" data-glow>
                <SelectValue placeholder="Select binding" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Saddle Stitch">Saddle Stitch</SelectItem>
                <SelectItem value="Perfect Bound">Perfect Bound</SelectItem>
                <SelectItem value="Spiral">Spiral</SelectItem>
                <SelectItem value="Wire-O">Wire-O</SelectItem>
                <SelectItem value="Case Bound">Case Bound</SelectItem>
                <SelectItem value="Coil Bound">Coil Bound</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="spoilage" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Spoilage (%)
          </Label>
            <Input
              id="spoilage"
              type="number"
              placeholder="5"
              value={spoilage}
              onChange={(e) => setSpoilage(e.target.value)}
              className="mt-1 h-9 text-sm"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}