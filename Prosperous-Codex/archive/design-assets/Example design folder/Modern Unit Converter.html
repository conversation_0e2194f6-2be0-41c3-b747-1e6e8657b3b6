<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate-key="pageTitle">Unit Converter</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Color Variables - Matching Paper Cost Estimator */
            --primary: #4f46e5; /* Indigo-600 */
            --primary-light: #818cf8; /* Indigo-400 */
            --primary-dark: #4338ca; /* Indigo-700 */
            --secondary: #0ea5e9; /* Sky-500 */
            --accent: #f59e0b; /* Amber-500 */
            --success: #10b981; /* Emerald-500 */
            --danger: #ef4444; /* Red-500 */
            --warning: #f59e0b; /* Amber-500 */
            --info: #3b82f6; /* Blue-500 */

            --neutral-50: #f9fafb;
            --neutral-100: #f3f4f6;
            --neutral-200: #e5e7eb;
            --neutral-300: #d1d5db;
            --neutral-400: #9ca3af;
            --neutral-500: #6b7280;
            --neutral-600: #4b5563;
            --neutral-700: #1e2a3a;
            --neutral-800: #16202d;
            --neutral-900: #0e141f;

            --bg-light: #ffffff;
            --bg-dark: #0e141f;

            --transition-theme: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease; /* Made snappier */
            --transition-transform: transform 0.3s ease;
            
            /* Glow effect variables */
            --x: calc(50vw * 1);
            --y: calc(50vh * 1);
            --spotlight: 50vmin;
            --base: 210;
            --saturation: 100;
            --lightness: 50;
            --bg-spot-opacity: 0.1;
            --border-spot-opacity: 0.35;
            --border-light-opacity: 0.4;
            --border: 1;
            --radius: 8;
            --card-size: 300px;
            --backup-border: rgba(75, 85, 99, 0.3);
            --surface-hover: rgba(17, 26, 38, 0.9);
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            background-color: var(--neutral-50);
            color: var(--neutral-800);
            transition: var(--transition-theme);
        }

        body.dark-mode {
            background-color: var(--bg-dark);
            color: var(--neutral-100);
        }

        /* Card Styles with Glow Effects */
        .converter-card {
            background-color: var(--bg-light);
            border-radius: 0.5rem;
            box-shadow: var(--shadow-md);
            transition: var(--transition-theme);
        }

        body.dark-mode .converter-card {
            background-color: rgba(21, 25, 41, 0.9);
            border: 1px solid var(--neutral-700);
        }
        
        /* Light mode simple card style */
        body:not(.dark-mode) [data-glow] {
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        }

        /* Glow Effect System - Only active in dark mode with GPU acceleration */
        body.dark-mode [data-glow] {
            --border-size: calc(var(--border, 1) * 1px);
            --spotlight-size: calc(var(--card-size) * 1.2);
            --hue: calc(var(--base) + (var(--xp, 0) * var(--spread, 0)));
            background-image: radial-gradient(
                var(--spotlight-size) var(--spotlight-size) at
                var(--x)
                var(--y),
                hsl(var(--hue, 210) calc(var(--saturation, 100) * 1%) calc(var(--lightness, 70) * 1%) / var(--bg-spot-opacity, 0.1)), transparent
            );
            border: var(--border-size) solid var(--backup-border);
            position: relative;
            touch-action: none;
            transition: all 0.3s cubic-bezier(.4,0,.2,1);
            /* GPU acceleration for better performance */
            will-change: transform, opacity;
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000px;
        }

        body.dark-mode [data-glow]::before,
        body.dark-mode [data-glow]::after {
            pointer-events: none;
            content: "";
            position: absolute;
            inset: calc(var(--border-size) * -1);
            border: var(--border-size) solid transparent;
            border-radius: calc(var(--radius) * 1px);
            background-repeat: no-repeat;
            mask:
                linear-gradient(transparent, transparent),
                linear-gradient(white, white);
            mask-clip: padding-box, border-box;
            mask-composite: intersect;
            transition: opacity 0.3s ease;
            /* GPU acceleration for pseudo-elements */
            will-change: opacity, transform;
            transform: translateZ(0);
            backface-visibility: hidden;
        }

        body.dark-mode [data-glow]::before {
            background-image: radial-gradient(
                calc(var(--spotlight-size) * 0.75) calc(var(--spotlight-size) * 0.75) at
                var(--x)
                var(--y),
                hsl(var(--hue, 210) calc(var(--saturation, 100) * 1%) calc(var(--lightness, 50) * 1%) / var(--border-spot-opacity, 0.35)), transparent 100%
            );
            filter: brightness(1.5);
        }
        
        body.dark-mode [data-glow]::after {
            background-image: radial-gradient(
                calc(var(--spotlight-size) * 0.4) calc(var(--spotlight-size) * 0.4) at
                var(--x)
                var(--y),
                hsl(0 100% 100% / var(--border-light-opacity, 0.4)), transparent 100%
            );
        }

        /* Enhanced hover effect - Light mode (simplified) */
        body:not(.dark-mode) [data-glow]:hover {
            border-color: rgba(94, 106, 210, 0.3);
            box-shadow: 0 4px 12px rgba(0,0,0,0.06);
        }
        
        /* Enhanced hover effect - Dark mode with GPU optimization */
        body.dark-mode [data-glow]:hover {
            background-color: var(--surface-hover);
            transform: translateY(-4px) translateZ(0);
            box-shadow: 
                0 20px 35px rgba(0, 0, 0, 0.3),
                0 8px 40px 0 rgba(85, 110, 255, 0.25), 
                0 0 0 1px rgba(94, 106, 210, 0.6);
            border-color: rgba(94, 106, 210, 0.7);
            z-index: 10;
            /* Optimize hover performance */
            will-change: transform, box-shadow;
        }
        
        body.dark-mode [data-glow]:hover::before {
            opacity: 1.6;
        }
        
        body.dark-mode [data-glow]:hover::after {
            opacity: 1;
        }
        
        /* Card-specific glow colors */
        .converter-card:nth-child(1)[data-glow] {
            --base: 210;
            --spread: 125;
            --card-size: 300px;
        }
        
        .converter-card:nth-child(2)[data-glow] {
            --base: 260;
            --spread: 125;
            --card-size: 300px;
        }
        
        .converter-card:nth-child(3)[data-glow] {
            --base: 310;
            --spread: 125;
            --card-size: 300px;
        }
        
        .converter-card:nth-child(4)[data-glow] {
            --base: 360;
            --spread: 125;
            --card-size: 300px;
        }

        /* Modern Tab Styles with Glassmorphism */
        .tabs-container {
            position: relative;
        }
        
        /* Brand Title */
        .brand-title {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
        
        body.dark-mode .brand-title {
            background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
        
        .tabs-wrapper {
            display: flex;
            position: relative;
            background: rgba(243, 244, 246, 0.8);
            border-radius: 12px;
            padding: 4px;
            backdrop-filter: blur(8px);
            border: 1px solid rgba(229, 231, 235, 0.6);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        body.dark-mode .tabs-wrapper {
            background: rgba(31, 41, 55, 0.8);
            border: 1px solid rgba(75, 85, 99, 0.6);
        }
        
        .tab-item {
            position: relative;
            z-index: 2;
            padding: 10px 20px;
            border: none;
            background: transparent;
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
            outline: none;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .tab-item:hover:not(.active) {
            color: #374151;
            background: rgba(156, 163, 175, 0.1);
        }
        
        .tab-item.active {
            color: #5E6AD2;
            font-weight: 600;
        }
        
        body.dark-mode .tab-item {
            color: #9ca3af;
        }
        
        body.dark-mode .tab-item:hover:not(.active) {
            color: #d1d5db;
            background: rgba(156, 163, 175, 0.1);
        }
        
        body.dark-mode .tab-item.active {
            color: #8b93f1;
        }
        
        .tab-indicator {
            position: absolute;
            top: 4px;
            left: 4px;
            height: calc(100% - 8px);
            background: white;
            border-radius: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(229, 231, 235, 0.8);
        }
        
        body.dark-mode .tab-indicator {
            background: #374151;
            border: 1px solid rgba(75, 85, 99, 0.8);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* Form Controls - Matching Paper Cost Estimator */
        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--neutral-600);
            margin-bottom: 0.375rem;
            transition: var(--transition-theme);
        }

        body.dark-mode .form-label {
            color: var(--neutral-300);
        }

        .input-field {
            width: 100%;
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border: 1px solid var(--neutral-300);
            border-radius: 0.375rem;
            background-color: var(--bg-light);
            color: var(--neutral-800);
            transition: var(--transition-theme);
        }

        .select-field {
            width: 100%;
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border: 1px solid var(--neutral-300);
            border-radius: 0.375rem;
            background-color: var(--bg-light);
            color: var(--neutral-800);
            transition: var(--transition-theme);
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            padding-right: 2.5rem;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z' clip-rule='evenodd' /%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 1rem 1rem;
        }

        .input-field:focus, .select-field:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
        }

        body.dark-mode .input-field, body.dark-mode .select-field {
            background-color: var(--neutral-800);
            border-color: var(--neutral-600);
            color: var(--neutral-100);
        }

        body.dark-mode .select-field {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%239ca3af'%3E%3Cpath fill-rule='evenodd' d='M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z' clip-rule='evenodd' /%3E%3C/svg%3E");
        }

        body.dark-mode .input-field:focus, body.dark-mode .select-field:focus {
            border-color: var(--primary-light);
            box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.2);
        }

        /* Output Items */
        .output-item {
            background-color: var(--neutral-100);
            border-radius: 0.375rem;
            border: 1px solid var(--neutral-200);
            transition: var(--transition-theme), var(--transition-transform);
        }

        .output-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        body.dark-mode .output-item {
            background-color: var(--neutral-700);
            border-color: var(--neutral-600);
        }

        .copy-button {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .output-item:hover .copy-button {
            opacity: 1;
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 0.375rem;
            transition: var(--transition-theme);
            cursor: pointer;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
            border: 1px solid var(--primary-dark);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
        }

        body.dark-mode .btn-primary {
            background-color: var(--primary-light);
            border-color: var(--primary);
        }

        body.dark-mode .btn-primary:hover {
            background-color: var(--primary);
        }

        .gemini-button {
            background-color: #fbbf24; /* Amber-400 */
            color: #78350f; /* Amber-900 */
            font-weight: 600;
            padding: 10px 18px;
            border-radius: 8px;
            transition: background-color 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-block;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .gemini-button:hover {
            background-color: #f59e0b; /* Amber-500 */
        }

        .gemini-response-area {
            margin-top: 16px;
            padding: 12px;
            background-color: #eef2ff; /* Indigo-50 */
            border-radius: 8px;
            border: 1px solid #c7d2fe; /* Indigo-200 */
            color: #3730a3; /* Indigo-800 */
            min-height: 50px;
            font-style: italic;
            transition: var(--transition-theme);
        }

        body.dark-mode .gemini-response-area {
            background-color: var(--neutral-700);
            border-color: var(--neutral-600);
            color: var(--neutral-300);
        }

        /* Common Switch Base */
        .lang-switch, .theme-switch {
            position: relative;
            display: inline-block;
            cursor: pointer;
            transition: var(--transition-theme), transform 0.3s ease;
            overflow: hidden;
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            outline: none;
        }

        .lang-switch:before, .theme-switch:before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .lang-switch:hover:before, .theme-switch:hover:before {
            opacity: 1;
        }

        /* Language Switcher */
        .lang-switch {
            background: #ef4444; /* Red-500 */
            padding: 0.25rem;
            border-radius: 9999px;
            width: 80px;
            height: 36px;
            box-shadow: 0 4px 10px rgba(79, 70, 229, 0.6), inset 0 2px 3px rgba(255, 255, 255, 0.2);
        }

        .lang-switch.active {
            background: #4f46e5; /* Indigo-600 */
            box-shadow: 0 4px 10px rgba(239, 68, 68, 0.6), inset 0 2px 3px rgba(255, 255, 255, 0.2);
        }

        .lang-switch:hover {
            transform: translateY(-1px);
        }

        .lang-switch-label {
            font-size: 0.75rem;
            font-weight: 600;
            z-index: 1;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            transition: var(--transition-theme);
            color: rgba(255, 255, 255, 0.7);
            letter-spacing: 0.5px;
        }

        .lang-switch-label.en {
            left: 15px;
        }

        .lang-switch-label.zh {
            right: 15px;
        }

        .lang-switch-toggle {
            width: 30px;
            height: 30px;
            background: #ffffff;
            border-radius: 9999px;
            position: absolute;
            top: 3px;
            left: 3px;
            transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
            z-index: 2;
        }

        .lang-switch-toggle:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 16px;
            height: 16px;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            border-radius: 50%;
            opacity: 0.15;
            transition: opacity 0.3s ease;
        }

        .lang-switch.active .lang-switch-toggle {
            transform: translateX(44px);
        }

        .lang-switch.active .lang-switch-label.en {
            color: rgba(255, 255, 255, 0.9);
        }

        .lang-switch.active .lang-switch-label.zh {
            color: #ffffff;
            font-weight: 800;
        }

        .lang-switch:not(.active) .lang-switch-label.en {
            color: #ffffff;
            font-weight: 800;
        }

        .lang-switch:not(.active):active .lang-switch-toggle {
            transform: scale(0.95);
        }

        .lang-switch.active:active .lang-switch-toggle {
            transform: translateX(44px) scale(0.95);
        }

        .lang-switch:active .lang-switch-toggle:after {
            opacity: 0.3;
        }

        /* Theme Switcher */
        .theme-switch {
            background: linear-gradient(135deg, #f59e0b, #fbbf24); /* Amber gradient */
            padding: 0.25rem;
            border-radius: 9999px;
            width: 80px;
            height: 36px;
            box-shadow: 0 4px 10px rgba(245, 158, 11, 0.3), inset 0 2px 3px rgba(255, 255, 255, 0.2);
        }

        .theme-switch:hover {
            box-shadow: 0 6px 12px rgba(245, 158, 11, 0.4), inset 0 2px 3px rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .theme-switch.dark {
            background: linear-gradient(135deg, #1f2937, #374151); /* Dark gradient */
        }

        .theme-icon {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1;
            transition: var(--transition-theme);
            color: rgba(255, 255, 255, 0.9);
            opacity: 1 !important;
        }

        .theme-icon.sun {
            left: 12px;
        }

        .theme-icon.moon {
            right: 12px;
        }

        .theme-switch-toggle {
            width: 30px;
            height: 30px;
            background: #ffffff;
            border-radius: 9999px;
            position: absolute;
            top: 3px;
            left: 3px;
            transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
            z-index: 2;
        }

        .theme-switch.dark .theme-switch-toggle {
            transform: translateX(44px);
        }

        /* Prevent text selection on UI elements */
        .no-select {
            user-select: none;
        }

        /* Allow text selection in input fields and results */
        .allow-select {
            user-select: text;
        }

        /* Animation for tab transitions */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Toast notification */
        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 20px;
            background-color: var(--primary);
            color: white;
            border-radius: 8px;
            box-shadow: var(--shadow-md);
            z-index: 1000;
            transform: translateY(100px);
            opacity: 0;
            transition: transform 0.3s ease, opacity 0.3s ease;
        }

        body.dark-mode .toast {
            background-color: var(--primary-light);
        }

        .toast.show {
            transform: translateY(0);
            opacity: 1;
        }
    </style>
</head>
<body>
    <header class="sticky top-0 left-0 right-0 z-[1000] bg-white shadow-md w-full py-2 dark:bg-neutral-800 dark:border-b dark:border-neutral-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center justify-between">
            <h1 class="text-xl font-semibold tracking-tight brand-title">Prosperous Codex</h1>
            <!-- Tabs Group (Left) -->
            <div class="tabs-container">
                <div class="tabs-wrapper">
                    <button id="tabLength" data-translate-key="tabLength" class="tab-item active" data-tab="length">
                        <span>Length</span>
                    </button>
                    <button id="tabWeight" data-translate-key="tabWeight" class="tab-item" data-tab="weight">
                        <span>Weight</span>
                    </button>
                    <button id="tabArea" data-translate-key="tabArea" class="tab-item" data-tab="area">
                        <span>Area</span>
                    </button>
                    <button id="tabVolume" data-translate-key="tabVolume" class="tab-item" data-tab="volume">
                        <span>Volume</span>
                    </button>
                    <button id="tabTemperature" data-translate-key="tabTemperature" class="tab-item" data-tab="temperature">
                        <span>Temperature</span>
                    </button>
                    <div class="tab-indicator"></div>
                </div>
            </div>

            <!-- Controls Group (Right) -->
            <div class="flex items-center space-x-4">
                <!-- Language Switcher -->
                <div id="language-switcher" class="lang-switch" title="Switch Language">
                    <span class="lang-switch-label en">EN</span>
                    <div class="lang-switch-toggle"></div>
                    <span class="lang-switch-label zh">中</span>
                </div>

                <!-- Theme Switcher -->
                <div id="theme-switcher" class="theme-switch" title="Switch Theme">
                    <svg class="theme-icon sun" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z"/></svg>
                    <div class="theme-switch-toggle"></div>
                    <svg class="theme-icon moon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path fill-rule="evenodd" d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z" clip-rule="evenodd"/></svg>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Page Title -->
        <div class="mb-6">
            <h1 data-translate-key="pageTitle" class="text-2xl font-bold no-select">Unit Converter</h1>
            <p data-translate-key="pageSubtitle" class="text-sm text-neutral-600 dark:text-neutral-400 mt-1 no-select">Convert between different units of measurement with precision.</p>
        </div>

        <!-- Converter Container -->
        <div id="convertersContainer">
            <!-- Length Converter -->
            <div id="lengthConverter" class="converter-card p-6 tab-content active" data-glow>
                <h2 data-translate-key="lengthConverterTitle" class="text-xl font-semibold mb-6 no-select">Length Converter</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="input-group">
                        <label for="lengthInput" data-translate-key="enterValueLabel" class="form-label">Enter Value:</label>
                        <div class="relative">
                            <input type="text" id="lengthInput" placeholder="0.00" class="input-field allow-select">
                            <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300" id="clearLengthInput">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="15" y1="9" x2="9" y2="15"></line>
                                    <line x1="9" y1="9" x2="15" y2="15"></line>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="input-group">
                        <label for="lengthFromUnit" data-translate-key="fromUnitLabel" class="form-label">From Unit:</label>
                        <select id="lengthFromUnit" class="select-field">
                            <!-- Options populated by JavaScript -->
                        </select>
                    </div>
                </div>

                <h3 data-translate-key="resultsTitle" class="text-lg font-semibold mb-4 no-select">Results:</h3>
                <div id="lengthOutput" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Dynamic output items will be injected here by JavaScript -->
                </div>

                <div class="mt-6 text-center">
                    <button id="geminiLengthButton" data-translate-key="geminiButtonText" class="gemini-button">Get Contextual Comparison</button>
                    <div id="geminiLengthResponse" class="gemini-response-area mt-4 p-4 rounded-lg text-sm allow-select"></div>
                </div>
            </div>

            <!-- Other converters will be added with JavaScript -->
        </div>
    </div>

    <!-- Toast Notification -->
    <div id="toast" class="toast">
        <span id="toastMessage"></span>
    </div>

    <script>
        // Global variables
        let currentLanguage = 'en'; // Default language
        let activeConverterCategory = 'length'; // Default active tab
        let conversionHistory = JSON.parse(localStorage.getItem('conversionHistory')) || [];

        // Translation data
        const translations = {
            'en': {
                'pageTitle': 'Unit Converter',
                'tabLength': 'Length',
                'tabWeight': 'Weight',
                'tabArea': 'Area',
                'tabVolume': 'Volume',
                'tabTemperature': 'Temperature',
                'lengthConverterTitle': 'Length Converter',
                'weightConverterTitle': 'Weight Converter',
                'areaConverterTitle': 'Area Converter',
                'volumeConverterTitle': 'Volume Converter',
                'temperatureConverterTitle': 'Temperature Converter',
                'enterValueLabel': 'Enter Value:',
                'fromUnitLabel': 'From Unit:',
                'resultsTitle': 'Results:',
                'geminiButtonText': 'Get Contextual Comparison',
                'geminiLoading': 'Thinking of a comparison...',
                'geminiError': 'Sorry, I couldn\'t think of a comparison right now. Please try again.',
                'copiedToClipboard': 'Copied to clipboard!',
                'invalidInput': 'Please enter a valid number',
                // Length units
                'mm': 'Millimeters', 'cm': 'Centimeters', 'm': 'Meters', 'km': 'Kilometers',
                'in': 'Inches', 'ft': 'Feet', 'yd': 'Yards', 'mi': 'Miles',
                // Weight units
                'mg': 'Milligrams', 'g': 'Grams', 'kg': 'Kilograms', 'tonne': 'Metric Tons',
                'oz': 'Ounces', 'lb': 'Pounds', 'st': 'Stone',
                // Area units
                'mm2': 'Square mm', 'cm2': 'Square cm', 'm2': 'Square m', 'ha': 'Hectares',
                'km2': 'Square km', 'in2': 'Square inches', 'ft2': 'Square feet',
                'yd2': 'Square yards', 'acre': 'Acres',
                // Volume units
                'ml': 'Milliliters', 'l': 'Liters', 'm3': 'Cubic meters',
                'gal': 'Gallons (US)', 'pt': 'Pints (US)', 'qt': 'Quarts (US)',
                'fl_oz': 'Fluid Ounces (US)', 'cup': 'Cups (US)',
                // Temperature units
                'c': 'Celsius', 'f': 'Fahrenheit', 'k': 'Kelvin'
            },
            'zh': {
                'pageTitle': '度量衡單位換算器',
                'tabLength': '長度',
                'tabWeight': '重量',
                'tabArea': '面積',
                'tabVolume': '體積',
                'tabTemperature': '溫度',
                'lengthConverterTitle': '長度換算',
                'weightConverterTitle': '重量換算',
                'areaConverterTitle': '面積換算',
                'volumeConverterTitle': '體積換算',
                'temperatureConverterTitle': '溫度換算',
                'enterValueLabel': '輸入數值:',
                'fromUnitLabel': '選擇單位:',
                'resultsTitle': '換算結果:',
                'geminiButtonText': '獲取比較場景',
                'geminiLoading': '正在思考比較中...',
                'geminiError': '抱歉，暫時無法提供比較。請稍後再試。',
                'copiedToClipboard': '已複製到剪貼板！',
                'invalidInput': '請輸入有效數字',
                // Length units
                'mm': '毫米', 'cm': '厘米', 'm': '米', 'km': '公里',
                'in': '英寸', 'ft': '英尺', 'yd': '碼', 'mi': '英里',
                // Weight units
                'mg': '毫克', 'g': '克', 'kg': '公斤', 'tonne': '公噸',
                'oz': '盎司', 'lb': '磅', 'st': '英石',
                // Area units
                'mm2': '平方毫米', 'cm2': '平方厘米', 'm2': '平方米', 'ha': '公頃',
                'km2': '平方公里', 'in2': '平方英寸', 'ft2': '平方英尺',
                'yd2': '平方碼', 'acre': '英畝',
                // Volume units
                'ml': '毫升', 'l': '升', 'm3': '立方米',
                'gal': '加侖 (美制)', 'pt': '品脫 (美制)', 'qt': '夸脫 (美制)',
                'fl_oz': '液量盎司 (美制)', 'cup': '杯 (美制)',
                // Temperature units
                'c': '攝氏度', 'f': '華氏度', 'k': '開爾文'
            }
        };

        // Unit configuration
        const BASE_UNITS = {
            length: 'm',     // Meters
            weight: 'kg',    // Kilograms
            area: 'm2',      // Square Meters
            volume: 'l',     // Liters
            temperature: 'c'  // Celsius (special case)
        };

        const FACTORS_TO_BASE = {
            length: { 'mm': 0.001, 'cm': 0.01, 'm': 1, 'km': 1000, 'in': 0.0254, 'ft': 0.3048, 'yd': 0.9144, 'mi': 1609.34 },
            weight: { 'mg': 0.000001, 'g': 0.001, 'kg': 1, 'tonne': 1000, 'oz': 0.0283495, 'lb': 0.453592, 'st': 6.35029 },
            area: { 'mm2': 0.000001, 'cm2': 0.0001, 'm2': 1, 'ha': 10000, 'km2': 1000000, 'in2': 0.00064516, 'ft2': 0.092903, 'yd2': 0.836127, 'acre': 4046.86 },
            volume: { 'ml': 0.001, 'l': 1, 'm3': 1000, 'gal': 3.78541, 'pt': 0.473176, 'qt': 0.946353, 'fl_oz': 0.0295735, 'cup': 0.24 }
            // Temperature is handled separately
        };

        const DISPLAY_UNITS = {
            length: [
                { id: 'mm', nameKey: 'mm', symbol: 'mm' },
                { id: 'cm', nameKey: 'cm', symbol: 'cm' },
                { id: 'm', nameKey: 'm', symbol: 'm' },
                { id: 'km', nameKey: 'km', symbol: 'km' },
                { id: 'in', nameKey: 'in', symbol: 'in' },
                { id: 'ft', nameKey: 'ft', symbol: 'ft' },
                { id: 'yd', nameKey: 'yd', symbol: 'yd' },
                { id: 'mi', nameKey: 'mi', symbol: 'mi' }
            ],
            weight: [
                { id: 'mg', nameKey: 'mg', symbol: 'mg' },
                { id: 'g', nameKey: 'g', symbol: 'g' },
                { id: 'kg', nameKey: 'kg', symbol: 'kg' },
                { id: 'tonne', nameKey: 'tonne', symbol: 't' },
                { id: 'oz', nameKey: 'oz', symbol: 'oz' },
                { id: 'lb', nameKey: 'lb', symbol: 'lb' },
                { id: 'st', nameKey: 'st', symbol: 'st' }
            ],
            area: [
                { id: 'mm2', nameKey: 'mm2', symbol: 'mm²' },
                { id: 'cm2', nameKey: 'cm2', symbol: 'cm²' },
                { id: 'm2', nameKey: 'm2', symbol: 'm²' },
                { id: 'ha', nameKey: 'ha', symbol: 'ha' },
                { id: 'km2', nameKey: 'km2', symbol: 'km²' },
                { id: 'in2', nameKey: 'in2', symbol: 'in²' },
                { id: 'ft2', nameKey: 'ft2', symbol: 'ft²' },
                { id: 'yd2', nameKey: 'yd2', symbol: 'yd²' },
                { id: 'acre', nameKey: 'acre', symbol: 'acre' }
            ],
            volume: [
                { id: 'ml', nameKey: 'ml', symbol: 'ml' },
                { id: 'l', nameKey: 'l', symbol: 'l' },
                { id: 'm3', nameKey: 'm3', symbol: 'm³' },
                { id: 'gal', nameKey: 'gal', symbol: 'gal' },
                { id: 'pt', nameKey: 'pt', symbol: 'pt' },
                { id: 'qt', nameKey: 'qt', symbol: 'qt' },
                { id: 'fl_oz', nameKey: 'fl_oz', symbol: 'fl oz' },
                { id: 'cup', nameKey: 'cup', symbol: 'cup' }
            ],
            temperature: [
                { id: 'c', nameKey: 'c', symbol: '°C' },
                { id: 'f', nameKey: 'f', symbol: '°F' },
                { id: 'k', nameKey: 'k', symbol: 'K' }
            ]
        };

        // Helper Functions
        function formatNumber(num) {
            if (num === 0) return '0.00';
            if (isNaN(num)) return '0.00';

            // Handle very small numbers
            if (Math.abs(num) < 0.00001 && Math.abs(num) > 0) {
                return num.toExponential(4);
            }

            // Handle very large or very small numbers
            if (Math.abs(num) >= 1000000 || (Math.abs(num) > 0 && Math.abs(num) < 0.001)) {
                let fixed = 6;
                if (Math.abs(num) >= 1) fixed = 4;
                if (Math.abs(num) >= 100) fixed = 2;
                if (Math.abs(num) >= 10000) fixed = 0;
                if (Math.abs(num) < 0.000001) fixed = 8;

                let stringNum = num.toFixed(fixed);
                if (stringNum.includes('.')) {
                    stringNum = stringNum.replace(/0+$/, '');
                    if (stringNum.endsWith('.')) stringNum = stringNum.slice(0, -1);
                }

                const numericVal = parseFloat(stringNum);
                if (!Number.isInteger(numericVal) && (!stringNum.includes('.') || (stringNum.split('.')[1] || "").length < 2)) {
                    return numericVal.toFixed(2);
                }
                return stringNum;
            }

            // Normal numbers
            return num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 6 });
        }

        function showToast(message, duration = 2000) {
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toastMessage');

            toastMessage.textContent = message;
            toast.classList.add('show');

            setTimeout(() => {
                toast.classList.remove('show');
            }, duration);
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showToast(translations[currentLanguage]['copiedToClipboard'] || 'Copied to clipboard!');
            }).catch(err => {
                console.error('Could not copy text: ', err);
            });
        }

        // Initialize the app when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Theme toggle functionality
            const themeSwitcher = document.getElementById('theme-switcher');

            function switchTheme(theme) {
                if (theme === 'dark') {
                    document.body.classList.add('dark-mode');
                    themeSwitcher.classList.add('dark');
                } else {
                    document.body.classList.remove('dark-mode');
                    themeSwitcher.classList.remove('dark');
                }
                localStorage.setItem('preferredTheme', theme);
            }

            // Check for saved theme preference or default to dark mode
            let preferredTheme = localStorage.getItem('preferredTheme') || 'dark';
            switchTheme(preferredTheme);

            themeSwitcher.addEventListener('click', () => {
                preferredTheme = document.body.classList.contains('dark-mode') ? 'light' : 'dark';
                switchTheme(preferredTheme);
            });

            // Language switcher functionality
            const languageSwitcher = document.getElementById('language-switcher');

            function switchLanguage(lang) {
                if (!translations[lang]) return;
                currentLanguage = lang;
                document.documentElement.lang = lang;

                if (lang === 'zh') {
                    languageSwitcher.classList.add('active');
                } else {
                    languageSwitcher.classList.remove('active');
                }

                updateLanguageDisplay();
                
                // Update tab indicator position after language change
                // Use setTimeout to allow the DOM to update with new text
                setTimeout(updateTabIndicator, 300);
            }

            // Initialize language
            if (localStorage.getItem('language')) {
                switchLanguage(localStorage.getItem('language'));
            }

            languageSwitcher.addEventListener('click', () => {
                const newLang = currentLanguage === 'en' ? 'zh' : 'en';
                switchLanguage(newLang);
                localStorage.setItem('language', newLang);
            });

            // Add subtitle translation
            translations.en['pageSubtitle'] = 'Convert between different units of measurement with precision.';
            translations.zh['pageSubtitle'] = '在不同測量單位之間轉換。';

            // Create missing converter cards
            createConverterCards();

            // Set up tab switching
            setupTabSwitching();

            // Set up language switching
            setupLanguageSwitching();

            // Initialize the active tab
            switchTab(activeConverterCategory);

            // Set up clear buttons for inputs
            setupClearButtons();
        });

        // Create converter cards for all categories
        function createConverterCards() {
            const convertersContainer = document.getElementById('convertersContainer');
            const categories = ['weight', 'area', 'volume', 'temperature'];

            categories.forEach(category => {
                // Skip if the card already exists
                if (document.getElementById(`${category}Converter`)) return;

                const card = document.createElement('div');
                card.id = `${category}Converter`;
                card.className = 'converter-card p-6 tab-content';
                card.setAttribute('data-glow', '');
                // Don't set display:none, we'll use the active class instead

                const titleKey = `${category}ConverterTitle`;

                card.innerHTML = `
                    <h2 data-translate-key="${titleKey}" class="text-xl font-semibold mb-6 no-select">${translations[currentLanguage][titleKey] || titleKey}</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="input-group">
                            <label for="${category}Input" data-translate-key="enterValueLabel" class="form-label">${translations[currentLanguage]['enterValueLabel'] || 'Enter Value:'}</label>
                            <div class="relative">
                                <input type="text" id="${category}Input" placeholder="0.00" class="input-field allow-select">
                                <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300" id="clear${category.charAt(0).toUpperCase() + category.slice(1)}Input">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="15" y1="9" x2="9" y2="15"></line>
                                        <line x1="9" y1="9" x2="15" y2="15"></line>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="input-group">
                            <label for="${category}FromUnit" data-translate-key="fromUnitLabel" class="form-label">${translations[currentLanguage]['fromUnitLabel'] || 'From Unit:'}</label>
                            <select id="${category}FromUnit" class="select-field">
                                <!-- Options populated by JavaScript -->
                            </select>
                        </div>
                    </div>

                    <h3 data-translate-key="resultsTitle" class="text-lg font-semibold mb-4 no-select">${translations[currentLanguage]['resultsTitle'] || 'Results:'}</h3>
                    <div id="${category}Output" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- Dynamic output items will be injected here by JavaScript -->
                    </div>

                    <div class="mt-6 text-center">
                        <button id="gemini${category.charAt(0).toUpperCase() + category.slice(1)}Button" data-translate-key="geminiButtonText" class="gemini-button">Get Contextual Comparison</button>
                        <div id="gemini${category.charAt(0).toUpperCase() + category.slice(1)}Response" class="gemini-response-area mt-4 p-4 rounded-lg text-sm allow-select"></div>
                    </div>
                `;

                convertersContainer.appendChild(card);
            });
        }

        // Set up tab switching
        function setupTabSwitching() {
            const tabButtons = {
                length: document.getElementById('tabLength'),
                weight: document.getElementById('tabWeight'),
                area: document.getElementById('tabArea'),
                volume: document.getElementById('tabVolume'),
                temperature: document.getElementById('tabTemperature')
            };

            Object.keys(tabButtons).forEach(category => {
                tabButtons[category].addEventListener('click', () => switchTab(category));
            });
            
            // Set initial indicator position
            setTimeout(updateTabIndicator, 100);
            
            // Update indicator position on window resize
            window.addEventListener('resize', updateTabIndicator);
            
            // Update indicator position when fonts load
            document.fonts.ready.then(updateTabIndicator);
        }

        // Update tab indicator position
        function updateTabIndicator() {
            const activeTab = document.querySelector('.tab-item.active');
            const tabIndicator = document.querySelector('.tab-indicator');
            if (activeTab && tabIndicator) {
                const tabsWrapper = activeTab.parentElement;
                const activeTabRect = activeTab.getBoundingClientRect();
                const wrapperRect = tabsWrapper.getBoundingClientRect();
                const offsetLeft = activeTabRect.left - wrapperRect.left - 4; // Account for wrapper padding
                const width = activeTabRect.width;
                
                tabIndicator.style.width = `${width}px`;
                tabIndicator.style.transform = `translateX(${offsetLeft}px)`;
            }
        }

        // Switch between tabs
        function switchTab(newActiveCategory) {
            activeConverterCategory = newActiveCategory;

            const tabButtons = document.querySelectorAll('.tab-item');
            const converterCards = document.querySelectorAll('.tab-content');

            // Update tab buttons
            tabButtons.forEach(button => {
                if (button.id === `tab${newActiveCategory.charAt(0).toUpperCase() + newActiveCategory.slice(1)}`) {
                    button.classList.add('active');
                } else {
                    button.classList.remove('active');
                }
            });

            // Update converter cards
            converterCards.forEach(card => {
                if (card.id === `${newActiveCategory}Converter`) {
                    card.classList.add('active');
                } else {
                    card.classList.remove('active');
                }
            });

            // Update tab indicator
            updateTabIndicator();

            // Update UI for the active tab
            updateLanguageDisplay();

            // Set up event listeners for the active tab
            setupEventListeners(newActiveCategory);
        }

        // Set up language switching
        function setupLanguageSwitching() {
            const languageSwitcher = document.getElementById('language-switcher');

            languageSwitcher.addEventListener('change', (event) => {
                currentLanguage = event.target.value;
                localStorage.setItem('language', currentLanguage);
                updateLanguageDisplay();
            });
        }

        // Update UI text based on selected language
        function updateLanguageDisplay() {
            // Update static UI text
            document.querySelectorAll('[data-translate-key]').forEach(element => {
                const key = element.getAttribute('data-translate-key');
                if (translations[currentLanguage] && translations[currentLanguage][key]) {
                    element.textContent = translations[currentLanguage][key];
                }
            });

            // Update dynamic elements
            populateSelectWithOptions(activeConverterCategory, currentLanguage);
            populateOutputFields(activeConverterCategory, currentLanguage);

            // Convert with current values
            convert(activeConverterCategory);

            // Clear Gemini response
            const geminiResponseArea = document.getElementById(`gemini${activeConverterCategory.charAt(0).toUpperCase() + activeConverterCategory.slice(1)}Response`);
            if (geminiResponseArea) geminiResponseArea.textContent = '';
        }

        // Populate select dropdowns with unit options
        function populateSelectWithOptions(category, lang) {
            const selectElement = document.getElementById(`${category}FromUnit`);
            if (!selectElement) return;

            const currentSelectedValue = selectElement.value;
            selectElement.innerHTML = '';

            DISPLAY_UNITS[category].forEach(unit => {
                const option = document.createElement('option');
                option.value = unit.id;
                option.textContent = translations[lang][unit.nameKey] || unit.nameKey;
                selectElement.appendChild(option);
            });

            // Restore selected value or set default
            if (Array.from(selectElement.options).some(opt => opt.value === currentSelectedValue)) {
                selectElement.value = currentSelectedValue;
            } else if (selectElement.options.length > 0) {
                if (category === 'length') selectElement.value = 'cm';
                else if (category === 'weight') selectElement.value = 'g';
                else if (category === 'area') selectElement.value = 'cm2';
                else if (category === 'volume') selectElement.value = 'l';
                else if (category === 'temperature') selectElement.value = 'c';
                else selectElement.value = selectElement.options[0].value;
            }
        }

        // Create output fields for conversion results
        function populateOutputFields(category, lang) {
            const outputContainer = document.getElementById(`${category}Output`);
            if (!outputContainer) return;

            outputContainer.innerHTML = '';

            DISPLAY_UNITS[category].forEach(unit => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'output-item p-4 relative';

                const translatedName = translations[lang][unit.nameKey] || unit.nameKey;
                const unitSymbol = unit.id === 'in' ? 'in' : unit.symbol; // Use 'in' for inches as per preference

                itemDiv.innerHTML = `
                    <div class="unit-name text-sm font-medium text-neutral-600 dark:text-neutral-400 no-select">${translatedName}</div>
                    <div class="flex items-baseline mt-1">
                        <div class="unit-value text-xl font-bold allow-select" id="${category}Output${unit.id.charAt(0).toUpperCase() + unit.id.slice(1)}">0.00</div>
                        <div class="unit-symbol ml-1 text-lg font-medium text-neutral-500 dark:text-neutral-400">${unitSymbol}</div>
                    </div>
                    <button class="copy-button absolute top-2 right-2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300" data-value="0.00" data-unit="${unitSymbol}" title="Copy to clipboard">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                        </svg>
                    </button>
                `;

                outputContainer.appendChild(itemDiv);

                // Add copy to clipboard functionality
                const copyButton = itemDiv.querySelector('.copy-button');
                copyButton.addEventListener('click', () => {
                    const valueElement = itemDiv.querySelector('.unit-value');
                    const unitElement = itemDiv.querySelector('.unit-symbol');
                    const textToCopy = `${valueElement.textContent} ${unitElement.textContent}`;
                    copyToClipboard(textToCopy);
                });
            });
        }

        // Set up clear buttons for input fields
        function setupClearButtons() {
            const categories = ['length', 'weight', 'area', 'volume', 'temperature'];

            categories.forEach(category => {
                const clearButton = document.getElementById(`clear${category.charAt(0).toUpperCase() + category.slice(1)}Input`);
                const inputField = document.getElementById(`${category}Input`);

                if (clearButton && inputField) {
                    clearButton.addEventListener('click', () => {
                        inputField.value = '';
                        convert(category);
                    });
                }
            });
        }

        // Set up event listeners for a specific category
        function setupEventListeners(category) {
            const inputElement = document.getElementById(`${category}Input`);
            const fromUnitElement = document.getElementById(`${category}FromUnit`);
            const geminiButton = document.getElementById(`gemini${category.charAt(0).toUpperCase() + category.slice(1)}Button`);

            if (inputElement) {
                // Remove existing listeners to prevent duplicates
                const newInputElement = inputElement.cloneNode(true);
                inputElement.parentNode.replaceChild(newInputElement, inputElement);

                // Add new listener
                newInputElement.addEventListener('input', () => convert(category));
            }

            if (fromUnitElement) {
                // Remove existing listeners to prevent duplicates
                const newFromUnitElement = fromUnitElement.cloneNode(true);
                fromUnitElement.parentNode.replaceChild(newFromUnitElement, fromUnitElement);

                // Add new listener
                newFromUnitElement.addEventListener('change', () => convert(category));
            }

            if (geminiButton) {
                // Remove existing listeners to prevent duplicates
                const newGeminiButton = geminiButton.cloneNode(true);
                geminiButton.parentNode.replaceChild(newGeminiButton, geminiButton);

                // Add new listener
                newGeminiButton.addEventListener('click', () => callGeminiAPI(category));
            }
        }

        // Core conversion logic
        function convert(category) {
            const inputValue = parseFloat(document.getElementById(`${category}Input`).value);
            const fromUnitElement = document.getElementById(`${category}FromUnit`);

            if (!fromUnitElement || !fromUnitElement.value) {
                return;
            }

            const fromUnit = fromUnitElement.value;

            // Clear Gemini response when input changes
            const geminiResponseArea = document.getElementById(`gemini${category.charAt(0).toUpperCase() + category.slice(1)}Response`);
            if (geminiResponseArea) geminiResponseArea.textContent = '';

            // Handle invalid input
            if (isNaN(inputValue)) {
                DISPLAY_UNITS[category].forEach(unit => {
                    const outputElem = document.getElementById(`${category}Output${unit.id.charAt(0).toUpperCase() + unit.id.slice(1)}`);
                    if (outputElem) outputElem.textContent = '0.00';
                });
                return;
            }

            // Special case for temperature
            if (category === 'temperature') {
                convertTemperature(inputValue, fromUnit);
                return;
            }

            // Standard conversion for other categories
            const valueInBaseUnit = inputValue * FACTORS_TO_BASE[category][fromUnit];

            DISPLAY_UNITS[category].forEach(unit => {
                const outputElement = document.getElementById(`${category}Output${unit.id.charAt(0).toUpperCase() + unit.id.slice(1)}`);
                if (outputElement) {
                    const factorFromBase = 1 / FACTORS_TO_BASE[category][unit.id];
                    const convertedValue = valueInBaseUnit * factorFromBase;
                    outputElement.textContent = formatNumber(convertedValue);

                    // Update copy button data
                    const parentItem = outputElement.closest('.output-item');
                    if (parentItem) {
                        const copyButton = parentItem.querySelector('.copy-button');
                        if (copyButton) {
                            copyButton.setAttribute('data-value', formatNumber(convertedValue));
                        }
                    }
                }
            });

            // Save to conversion history
            saveToHistory(category, inputValue, fromUnit);
        }

        // Special case for temperature conversion
        function convertTemperature(inputValue, fromUnit) {
            let celsiusValue;

            // Convert to Celsius first (our base unit for temperature)
            switch (fromUnit) {
                case 'c':
                    celsiusValue = inputValue;
                    break;
                case 'f':
                    celsiusValue = (inputValue - 32) * 5/9;
                    break;
                case 'k':
                    celsiusValue = inputValue - 273.15;
                    break;
            }

            // Convert from Celsius to all units
            DISPLAY_UNITS.temperature.forEach(unit => {
                const outputElement = document.getElementById(`temperatureOutput${unit.id.charAt(0).toUpperCase() + unit.id.slice(1)}`);
                if (outputElement) {
                    let convertedValue;

                    switch (unit.id) {
                        case 'c':
                            convertedValue = celsiusValue;
                            break;
                        case 'f':
                            convertedValue = celsiusValue * 9/5 + 32;
                            break;
                        case 'k':
                            convertedValue = celsiusValue + 273.15;
                            break;
                    }

                    outputElement.textContent = formatNumber(convertedValue);

                    // Update copy button data
                    const parentItem = outputElement.closest('.output-item');
                    if (parentItem) {
                        const copyButton = parentItem.querySelector('.copy-button');
                        if (copyButton) {
                            copyButton.setAttribute('data-value', formatNumber(convertedValue));
                        }
                    }
                }
            });
        }

        // Save conversion to history
        function saveToHistory(category, value, fromUnit) {
            if (isNaN(value) || value === 0) return;

            const historyItem = {
                timestamp: new Date().toISOString(),
                category: category,
                value: value,
                unit: fromUnit
            };

            conversionHistory.unshift(historyItem);

            // Limit history to 20 items
            if (conversionHistory.length > 20) {
                conversionHistory.pop();
            }

            localStorage.setItem('conversionHistory', JSON.stringify(conversionHistory));
        }

        // Gemini API Integration
        async function callGeminiAPI(category) {
            const inputValue = parseFloat(document.getElementById(`${category}Input`).value);
            const fromUnitId = document.getElementById(`${category}FromUnit`).value;
            const fromUnitText = translations[currentLanguage][fromUnitId] || fromUnitId;

            const responseArea = document.getElementById(`gemini${category.charAt(0).toUpperCase() + category.slice(1)}Response`);

            if (isNaN(inputValue) || inputValue === 0) {
                responseArea.textContent = translations[currentLanguage]['geminiError'] || 'Please enter a valid value for comparison.';
                return;
            }

            responseArea.textContent = translations[currentLanguage]['geminiLoading'] || 'Loading...';

            // Get a couple of converted values for more context
            let convertedValuesText = "";
            let count = 0;

            if (category === 'temperature') {
                // Special handling for temperature
                let celsiusValue;
                switch (fromUnitId) {
                    case 'c': celsiusValue = inputValue; break;
                    case 'f': celsiusValue = (inputValue - 32) * 5/9; break;
                    case 'k': celsiusValue = inputValue - 273.15; break;
                }

                for (const unit of DISPLAY_UNITS[category]) {
                    if (unit.id !== fromUnitId && count < 2) {
                        let convertedValue;
                        switch (unit.id) {
                            case 'c': convertedValue = celsiusValue; break;
                            case 'f': convertedValue = celsiusValue * 9/5 + 32; break;
                            case 'k': convertedValue = celsiusValue + 273.15; break;
                        }
                        const unitName = translations[currentLanguage][unit.nameKey] || unit.nameKey;
                        convertedValuesText += `${formatNumber(convertedValue)} ${unitName}; `;
                        count++;
                    }
                }
            } else {
                // Standard handling for other categories
                const baseUnitForCategory = BASE_UNITS[category];
                const valueInBase = inputValue * FACTORS_TO_BASE[category][fromUnitId];

                for (const unit of DISPLAY_UNITS[category]) {
                    if (unit.id !== fromUnitId && count < 2) {
                        const convertedValue = valueInBase * (1 / FACTORS_TO_BASE[category][unit.id]);
                        const unitName = translations[currentLanguage][unit.nameKey] || unit.nameKey;
                        convertedValuesText += `${formatNumber(convertedValue)} ${unitName}; `;
                        count++;
                    }
                }
            }

            let prompt = "";
            if (currentLanguage === 'zh') {
                prompt = `你是一个乐于助人的助手，为测量单位提供有趣的真实世界比较。
用户刚刚转换了一个数量。
原始值：${inputValue}
原始单位：${fromUnitText}
一些转换后的值是：${convertedValuesText.trim()}

请提供一个关于这个数量（${inputValue} ${fromUnitText}）的简洁、引人入胜的真实世界比较或一个有趣的事实。
让比较易于大众理解。请用中文回答。回答请保持在1-2个短句。`;
            } else { // English
                prompt = `You are a helpful assistant that provides interesting real-world comparisons for measurements.
User has just converted a quantity.
Original value: ${inputValue}
Original unit: ${fromUnitText}
Some converted values are: ${convertedValuesText.trim()}

Please provide a concise and engaging real-world comparison or an interesting fact about this quantity (${inputValue} ${fromUnitText}).
Make the comparison easy to understand for a general audience. Respond in English. Keep the response to 1-2 short sentences.`;
            }

            const apiKey = "AIzaSyAfTGMc0Qgi8zVya0-jesmC77GUD60xFvQ"; // Gemini 2.0 Flash API key
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

            let chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
            const payload = { contents: chatHistory };

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    console.error("API Error Response:", response);
                    throw new Error(`API request failed with status ${response.status}`);
                }

                const result = await response.json();

                if (result.candidates && result.candidates.length > 0 &&
                    result.candidates[0].content && result.candidates[0].content.parts &&
                    result.candidates[0].content.parts.length > 0) {
                    const text = result.candidates[0].content.parts[0].text;
                    responseArea.textContent = text;
                } else {
                    console.error("Unexpected API response structure:", result);
                    responseArea.textContent = translations[currentLanguage]['geminiError'];
                }
            } catch (error) {
                console.error('Error calling Gemini API:', error);
                responseArea.textContent = translations[currentLanguage]['geminiError'];
            }
        }
        
        // Optimized pointer tracking for glow effect with individual card positioning
        let rafId = null;
        let lastX = 0;
        let lastY = 0;
        
        const syncPointer = (event) => {
            const { clientX: x, clientY: y } = event;
            
            // Skip if position hasn't changed significantly (reduce unnecessary updates)
            if (Math.abs(x - lastX) < 2 && Math.abs(y - lastY) < 2) return;
            
            lastX = x;
            lastY = y;
            
            // Cancel previous frame if still pending
            if (rafId) cancelAnimationFrame(rafId);
            
            // Use requestAnimationFrame for smooth 60fps updates
            rafId = requestAnimationFrame(() => {
                // Update each glow card individually with relative positioning
                const glowCards = document.querySelectorAll('[data-glow]');
                
                glowCards.forEach(card => {
                    const rect = card.getBoundingClientRect();
                    const relativeX = x - rect.left;
                    const relativeY = y - rect.top;
                    
                    // Set relative position for this specific card
                    card.style.setProperty('--x', `${relativeX}px`);
                    card.style.setProperty('--y', `${relativeY}px`);
                });
                
                rafId = null;
            });
        };
        
        // Use passive listener for better scroll performance
        document.body.addEventListener('pointermove', syncPointer, { passive: true });
    </script>
</body>
</html>
