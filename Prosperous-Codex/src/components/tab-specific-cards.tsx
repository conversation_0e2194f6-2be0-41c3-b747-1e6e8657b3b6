"use client";

import { forwardRef } from "react";
import {
  TabId,
  InnerTextJobSpecificationData,
  InnerTextProductionParameterData,
  CoverJobSpecificationData,
  CoverProductionParameterData,
  EndpaperJobSpecificationData,
  EndpaperProductionParameterData
} from "@/lib/types/ui-components";

// Import tab-specific components
import InnerTextJobSpecificationsCard, { InnerTextJobSpecificationsRef } from "@/components/inner-text/inner-text-job-specifications-card";
import InnerTextProductionParametersCard from "@/components/inner-text/inner-text-production-parameters-card";
import CoverJobSpecificationsCard, { CoverJobSpecificationsRef } from "@/components/cover/cover-job-specifications-card";
import CoverProductionParametersCard from "@/components/cover/cover-production-parameters-card";
import EndpaperJobSpecificationsCard, { EndpaperJobSpecificationsRef } from "@/components/endpaper/endpaper-job-specifications-card";
import EndpaperProductionParametersCard from "@/components/endpaper/endpaper-production-parameters-card";

// Union type for job specification refs
export type JobSpecificationRef = InnerTextJobSpecificationsRef | CoverJobSpecificationsRef | EndpaperJobSpecificationsRef;

// Props for the tab-specific card selector
interface TabSpecificJobSpecificationsProps {
  activeTab: TabId;
  className?: string;
  onInnerTextDataChange?: (data: InnerTextJobSpecificationData) => void;
  onCoverDataChange?: (data: CoverJobSpecificationData) => void;
  onEndpaperDataChange?: (data: EndpaperJobSpecificationData) => void;
  initialInnerTextData?: Partial<InnerTextJobSpecificationData>;
  initialCoverData?: Partial<CoverJobSpecificationData>;
  initialEndpaperData?: Partial<EndpaperJobSpecificationData>;
  // Refs for accessing current mm values
  innerTextRef?: React.RefObject<InnerTextJobSpecificationsRef | null>;
  coverRef?: React.RefObject<CoverJobSpecificationsRef | null>;
  endpaperRef?: React.RefObject<EndpaperJobSpecificationsRef | null>;
}

interface TabSpecificProductionParametersProps {
  activeTab: TabId;
  className?: string;
  onInnerTextDataChange?: (data: InnerTextProductionParameterData) => void;
  onCoverDataChange?: (data: CoverProductionParameterData) => void;
  onEndpaperDataChange?: (data: EndpaperProductionParameterData) => void;
  initialInnerTextData?: Partial<InnerTextProductionParameterData>;
  initialCoverData?: Partial<CoverProductionParameterData>;
  initialEndpaperData?: Partial<EndpaperProductionParameterData>;
}

// Job Specifications Card Selector
export function TabSpecificJobSpecificationsCard({
  activeTab,
  className,
  onInnerTextDataChange,
  onCoverDataChange,
  onEndpaperDataChange,
  initialInnerTextData,
  initialCoverData,
  initialEndpaperData,
  innerTextRef,
  coverRef,
  endpaperRef
}: TabSpecificJobSpecificationsProps) {
  switch (activeTab) {
    case 'innerText':
      return (
        <InnerTextJobSpecificationsCard
          ref={innerTextRef}
          className={className}
          onDataChange={onInnerTextDataChange}
          initialData={initialInnerTextData}
        />
      );
    case 'cover':
      return (
        <CoverJobSpecificationsCard
          ref={coverRef}
          className={className}
          onDataChange={onCoverDataChange}
          initialData={initialCoverData}
        />
      );
    case 'endpapers':
      return (
        <EndpaperJobSpecificationsCard
          ref={endpaperRef}
          className={className}
          onDataChange={onEndpaperDataChange}
          initialData={initialEndpaperData}
        />
      );
    default:
      return null;
  }
}

// Production Parameters Card Selector
export function TabSpecificProductionParametersCard({
  activeTab,
  className,
  onInnerTextDataChange,
  onCoverDataChange,
  onEndpaperDataChange,
  initialInnerTextData,
  initialCoverData,
  initialEndpaperData
}: TabSpecificProductionParametersProps) {
  switch (activeTab) {
    case 'innerText':
      return (
        <InnerTextProductionParametersCard
          className={className}
          onDataChange={onInnerTextDataChange}
          initialData={initialInnerTextData}
        />
      );
    case 'cover':
      return (
        <CoverProductionParametersCard
          className={className}
          onDataChange={onCoverDataChange}
          initialData={initialCoverData}
        />
      );
    case 'endpapers':
      return (
        <EndpaperProductionParametersCard
          className={className}
          onDataChange={onEndpaperDataChange}
          initialData={initialEndpaperData}
        />
      );
    default:
      return null;
  }
}

// Helper function to convert tab-specific data to calculation input format
export function convertToCalculationInputs(
  activeTab: TabId,
  innerTextJobData?: InnerTextJobSpecificationData,
  innerTextProdData?: InnerTextProductionParameterData,
  coverJobData?: CoverJobSpecificationData,
  coverProdData?: CoverProductionParameterData,
  endpaperJobData?: EndpaperJobSpecificationData,
  endpaperProdData?: EndpaperProductionParameterData
) {
  switch (activeTab) {
    case 'innerText':
      if (!innerTextJobData || !innerTextProdData) return null;
      return {
        trimH: innerTextJobData.trimH,
        trimW: innerTextJobData.trimW,
        quantity: innerTextJobData.quantity,
        bleed: innerTextProdData.bleed,
        gripper: innerTextProdData.gripper,
        colorBar: innerTextProdData.colorBar,
        spoilagePct: innerTextJobData.spoilagePct,
        alignmentMode: innerTextProdData.alignmentMode,
        totalPages: innerTextJobData.totalPages,
        lip: innerTextProdData.lip,
        bindingMethod: innerTextJobData.bindingMethod,
        isDoubleLipActive: innerTextProdData.isDoubleLipActive
      };
    
    case 'cover':
      if (!coverJobData || !coverProdData) return null;
      return {
        trimH: coverJobData.trimH,
        trimW: coverJobData.trimW,
        quantity: coverJobData.quantity,
        bleed: coverProdData.bleed,
        gripper: coverProdData.gripper,
        colorBar: coverProdData.colorBar,
        spoilagePct: coverJobData.spoilagePct,
        alignmentMode: coverProdData.alignmentMode,
        spineThickness: coverJobData.spineThickness,
        coverType: coverJobData.coverType,
        turnInAllowance: coverProdData.turnInAllowance || 18,
        flapWidth: coverJobData.flapWidth || (coverJobData.trimW / 2)
      };
    
    case 'endpapers':
      if (!endpaperJobData || !endpaperProdData) return null;
      return {
        trimH: endpaperJobData.trimH,
        trimW: endpaperJobData.trimW,
        quantity: endpaperJobData.quantity,
        bleed: endpaperProdData.bleed,
        gripper: endpaperProdData.gripper,
        colorBar: endpaperProdData.colorBar,
        spoilagePct: endpaperJobData.spoilagePct,
        alignmentMode: endpaperProdData.alignmentMode,
        endpaperType: endpaperJobData.endpaperType
      };
    
    default:
      return null;
  }
}
