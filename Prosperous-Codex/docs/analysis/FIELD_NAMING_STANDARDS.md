# Field Naming Standards for Task Master

## Overview

This document establishes comprehensive naming conventions and standards for field names across the Task Master application to ensure consistency between database schema, API interfaces, and frontend components.

## Core Principles

### 1. Consistency Across Layers
- **Database Layer**: Use `snake_case` for all column names
- **API Layer**: Use `camelCase` for all field names in requests/responses
- **Frontend Layer**: Use `camelCase` for all component props and state variables

### 2. Bidirectional Mapping
- Every API field must have a corresponding database column
- All mappings must be bidirectional and reversible
- Field mappings must be explicitly defined in the field mapping utility

### 3. Semantic Clarity
- Field names should clearly indicate their purpose and data type
- Use descriptive names that are self-documenting
- Avoid abbreviations unless they are widely understood

## Naming Conventions

### Database Schema (snake_case)

#### Standard Patterns
```sql
-- Primary keys
id INTEGER PRIMARY KEY

-- Foreign keys
project_id INTEGER
parent_task_id INTEGER
created_by INTEGER
assigned_to INTEGER

-- Timestamps
created_at TEXT
updated_at TEXT
completed_date TEXT
due_date TEXT
uploaded_at TEXT
joined_at TEXT
performed_at TEXT

-- Content fields
full_description TEXT
event_log TEXT
file_name TEXT
file_size INTEGER
mime_type TEXT
file_path TEXT

-- Metadata fields
activity_type TEXT
entity_type TEXT
entity_id INTEGER
old_value TEXT
new_value TEXT
```

#### Naming Rules
1. Use lowercase letters only
2. Separate words with underscores
3. Use descriptive, full words (avoid abbreviations)
4. Foreign keys should end with `_id`
5. Timestamp fields should end with `_at` or `_date`
6. Boolean fields should start with `is_` or `has_`

### API Interface (camelCase)

#### Standard Patterns
```typescript
// Primary identifiers
id: number

// Foreign keys
projectId: number
parentTaskId: number
createdBy: number
assignedTo: number

// Timestamps
createdAt: string
updatedAt: string
completedDate: string
dueDate: string
uploadedAt: string
joinedAt: string
performedAt: string

// Content fields
fullDescription: string
eventLog: string
fileName: string
fileSize: number
mimeType: string

// Metadata fields
activityType: string
entityType: string
entityId: number
oldValue: string
newValue: string
```

#### Naming Rules
1. Use camelCase for all field names
2. Start with lowercase letter
3. Use descriptive, full words
4. Foreign keys should end with `Id`
5. Boolean fields should start with `is` or `has`
6. Arrays should use plural nouns

### Frontend Components (camelCase)

#### Props and State
```typescript
// Component props
interface ProjectCardProps {
  projectId: number;
  projectTitle: string;
  fullDescription?: string;
  dueDate?: string;
  assignedTo?: number;
  onProjectSelect: (projectId: number) => void;
}

// Component state
const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
const [isLoadingProjects, setIsLoadingProjects] = useState(false);
const [projectFormData, setProjectFormData] = useState<ProjectFormData>({});
```

## Field Mapping Registry

### Core Entity Mappings

#### Project Fields
| API Field (camelCase) | Database Column (snake_case) | Type | Description |
|----------------------|------------------------------|------|-------------|
| `id` | `id` | number | Primary key |
| `title` | `title` | string | Project title |
| `description` | `description` | string | Short description |
| `fullDescription` | `full_description` | string | Detailed description |
| `eventLog` | `event_log` | string | Event log content |
| `status` | `status` | string | Project status |
| `priority` | `priority` | string | Priority level |
| `progress` | `progress` | number | Completion percentage |
| `dueDate` | `due_date` | string | Due date (ISO string) |
| `completedDate` | `completed_date` | string | Completion date |
| `createdBy` | `created_by` | number | Creator user ID |
| `assignedTo` | `assigned_to` | number | Assignee user ID |
| `createdAt` | `created_at` | string | Creation timestamp |
| `updatedAt` | `updated_at` | string | Last update timestamp |

#### Task Fields
| API Field (camelCase) | Database Column (snake_case) | Type | Description |
|----------------------|------------------------------|------|-------------|
| `id` | `id` | number | Primary key |
| `title` | `title` | string | Task title |
| `description` | `description` | string | Task description |
| `status` | `status` | string | Task status |
| `priority` | `priority` | string | Priority level |
| `progress` | `progress` | number | Completion percentage |
| `dueDate` | `due_date` | string | Due date (ISO string) |
| `completedDate` | `completed_date` | string | Completion date |
| `projectId` | `project_id` | number | Parent project ID |
| `parentTaskId` | `parent_task_id` | number | Parent task ID |
| `createdBy` | `created_by` | number | Creator user ID |
| `assignedTo` | `assigned_to` | number | Assignee user ID |
| `createdAt` | `created_at` | string | Creation timestamp |
| `updatedAt` | `updated_at` | string | Last update timestamp |

#### Comment Fields
| API Field (camelCase) | Database Column (snake_case) | Type | Description |
|----------------------|------------------------------|------|-------------|
| `id` | `id` | number | Primary key |
| `content` | `content` | string | Comment content |
| `taskId` | `task_id` | number | Parent task ID |
| `parentCommentId` | `parent_comment_id` | number | Parent comment ID |
| `createdBy` | `created_by` | number | Creator user ID |
| `createdAt` | `created_at` | string | Creation timestamp |
| `updatedAt` | `updated_at` | string | Last update timestamp |

#### File Fields
| API Field (camelCase) | Database Column (snake_case) | Type | Description |
|----------------------|------------------------------|------|-------------|
| `id` | `id` | number | Primary key |
| `fileName` | `file_name` | string | Original file name |
| `fileSize` | `file_size` | number | File size in bytes |
| `mimeType` | `mime_type` | string | MIME type |
| `projectId` | `project_id` | number | Parent project ID |
| `uploadedBy` | `uploaded_by` | number | Uploader user ID |
| `uploadedAt` | `uploaded_at` | string | Upload timestamp |

#### Team Member Fields
| API Field (camelCase) | Database Column (snake_case) | Type | Description |
|----------------------|------------------------------|------|-------------|
| `id` | `id` | number | Primary key |
| `projectId` | `project_id` | number | Project ID |
| `userId` | `user_id` | number | User ID |
| `role` | `role` | string | Team member role |
| `joinedAt` | `joined_at` | string | Join timestamp |

#### Activity Log Fields
| API Field (camelCase) | Database Column (snake_case) | Type | Description |
|----------------------|------------------------------|------|-------------|
| `id` | `id` | number | Primary key |
| `activityType` | `activity_type` | string | Type of activity |
| `entityType` | `entity_type` | string | Entity type affected |
| `entityId` | `entity_id` | number | Entity ID affected |
| `oldValue` | `old_value` | string | Previous value |
| `newValue` | `new_value` | string | New value |
| `projectId` | `project_id` | number | Related project ID |
| `performedBy` | `performed_by` | number | User who performed action |
| `performedAt` | `performed_at` | string | Action timestamp |

## Validation Rules

### Automatic Validation
1. **Field Name Pattern Validation**
   - API fields must match `/^[a-z][a-zA-Z0-9]*$/`
   - Database columns must match `/^[a-z][a-z0-9_]*$/`

2. **Mapping Consistency**
   - Every camelCase API field with capital letters must have a snake_case mapping
   - Every snake_case database column with underscores must have a camelCase mapping

3. **Type Consistency**
   - Mapped fields must have compatible types
   - Timestamps must be strings in ISO format
   - IDs must be numbers

### Manual Review Required
1. **Semantic Consistency**
   - Field names should accurately represent their content
   - Related fields should use consistent naming patterns

2. **Documentation Updates**
   - New fields must be documented in this standards document
   - API documentation must be updated for new endpoints

## Implementation Guidelines

### Adding New Fields

1. **Define Database Column**
   ```sql
   ALTER TABLE table_name ADD COLUMN new_field_name TYPE;
   ```

2. **Add Field Mapping**
   ```typescript
   // In field-mapping.ts
   export const FIELD_MAPPINGS = {
     // ... existing mappings
     newFieldName: 'new_field_name',
   } as const;
   ```

3. **Update Type Definitions**
   ```typescript
   // In validation-types.ts
   export namespace DbTypes {
     export interface EntityDbFields {
       // ... existing fields
       new_field_name: Type;
     }
   }

   export namespace ApiTypes {
     export interface EntityApiFields {
       // ... existing fields
       newFieldName: Type;
     }
   }
   ```

4. **Update Validation Schemas**
   ```typescript
   // In validation-types.ts
   export const ValidationSchemas = {
     entityDbFields: z.object({
       // ... existing fields
       new_field_name: z.type(),
     }),
     entityApiFields: z.object({
       // ... existing fields
       newFieldName: z.type(),
     }),
   };
   ```

5. **Add API Contract Tests**
   ```typescript
   // In api-validator.ts - update endpoint configurations
   { 
     path: '/api/endpoint', 
     method: 'POST', 
     requestFields: [..., 'newFieldName'], 
     responseFields: [..., 'newFieldName'], 
     entity: 'entity' 
   }
   ```

### Validation Checklist

Before deploying changes with new fields:

- [ ] Database schema updated with snake_case column
- [ ] Field mapping added to FIELD_MAPPINGS
- [ ] Type definitions updated for both API and DB
- [ ] Validation schemas updated
- [ ] API contract tests updated
- [ ] Documentation updated
- [ ] Integration tests pass
- [ ] Field name validation tests pass

## Tools and Automation

### Validation Tools
- **Schema Validator**: Validates database schema consistency
- **Field Mapper**: Handles bidirectional field mapping
- **Type Safety Enforcer**: Runtime type validation
- **API Validator**: Contract validation for endpoints

### IDE Integration
- ESLint rules for field naming conventions
- TypeScript strict mode for compile-time validation
- Custom type guards for runtime validation

### CI/CD Integration
- Automated field name validation in build pipeline
- API contract tests in test suite
- Schema migration validation

## Common Patterns and Examples

### Timestamp Fields
```typescript
// API
createdAt: "2024-01-01T00:00:00Z"
updatedAt: "2024-01-01T00:00:00Z"
dueDate: "2024-12-31T23:59:59Z"

// Database
created_at: "2024-01-01T00:00:00Z"
updated_at: "2024-01-01T00:00:00Z"
due_date: "2024-12-31T23:59:59Z"
```

### Foreign Key Relationships
```typescript
// API
projectId: 123
parentTaskId: 456
assignedTo: 789

// Database
project_id: 123
parent_task_id: 456
assigned_to: 789
```

### Content Fields
```typescript
// API
fullDescription: "Detailed project description"
eventLog: "Project event log content"
fileName: "document.pdf"

// Database
full_description: "Detailed project description"
event_log: "Project event log content"
file_name: "document.pdf"
```

## Maintenance and Updates

This document should be updated whenever:
- New entities are added to the system
- New fields are added to existing entities
- Field naming conventions are modified
- New validation rules are implemented

Last Updated: June 21, 2025
Version: 1.0
