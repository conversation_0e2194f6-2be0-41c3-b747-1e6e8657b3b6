// API Route for Paper Cost Calculations
// Handles server-side calculation requests securely

import { NextRequest, NextResponse } from 'next/server';
import { calculatePaperCosts } from '@/lib/calculations/calculator';
import { CalculationRequest, CalculationResponse } from '@/lib/types/calculation';

export async function POST(request: NextRequest) {
  try {
    const body: CalculationRequest = await request.json();

    // Validate request structure
    if (!body.tabId || !body.jobInputs || !body.paperOptions) {
      return NextResponse.json(
        { error: 'Invalid request structure' },
        { status: 400 }
      );
    }

    // Validate tabId
    if (!['innerText', 'cover', 'endpapers'].includes(body.tabId)) {
      return NextResponse.json(
        { error: 'Invalid tab ID' },
        { status: 400 }
      );
    }

    // Validate paper options array
    if (!Array.isArray(body.paperOptions) || body.paperOptions.length === 0) {
      return NextResponse.json(
        { error: 'No paper options provided' },
        { status: 400 }
      );
    }

    // Perform calculations
    try {
      const result: CalculationResponse = await calculatePaperCosts(body);
      return NextResponse.json(result);
    } catch (calcError) {
      console.error('Calculation function error:', calcError);
      return NextResponse.json({
        results: [],
        benchmarkValue: 0,
        errors: ['Calculation failed: ' + (calcError instanceof Error ? calcError.message : 'Unknown error')]
      });
    }
    
  } catch (error) {
    console.error('API calculation error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error during calculation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
