{"metadata": {"backupType": "global-comprehensive", "createdAt": "2025-06-30T18:16:43.582Z", "version": "1.0.0", "description": "Comprehensive global backup including all application data and modules"}, "modules": {"userManagement": {"description": "User accounts, authentication, and session data", "tables": ["users", "user_sessions", "user_settings"], "recordCounts": {"users": 2, "sessions": 0}}, "taskMaster": {"description": "Project management, tasks, comments, and activity tracking", "tables": ["projects", "project_comments", "activity_log", "project_team_members", "project_files"], "recordCounts": {"projects": 0, "comments": 0, "activities": 0}}, "paperCostEstimator": {"description": "Paper cost calculations, saved estimates, and calculation history", "tables": ["saved_calculations", "calculation_history"], "recordCounts": {"savedCalculations": 0, "calculationHistory": 0}}, "systemData": {"description": "Access requests and system configuration", "tables": ["access_requests"], "recordCounts": {}}}, "totalRecords": 2, "backupContents": ["prosperous-codex.db - Complete SQLite database file", "backup-manifest.json - This metadata file", "modules/ - Module-specific data exports", "modules/task-master-data.json - Task Master data export", "modules/paper-calculator-data.json - Paper Cost Estimator data export", "modules/user-data.json - User and session data export"]}