"use client";

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Filter, X, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { ActiveFilterBadges } from './active-filter-badges';

export interface FilterState {
  priority?: 'high' | 'medium' | 'low';
  dueDate?: boolean;
  lastUpdated?: boolean;
  completion?: boolean;
}

interface FilterDropdownProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  className?: string;
  isLoading?: boolean;
}

export function FilterDropdown({ filters, onFiltersChange, className, isLoading = false }: FilterDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Count active filters
  const activeFilterCount = Object.values(filters).filter(Boolean).length;

  const handlePriorityChange = useCallback((value: string) => {
    onFiltersChange({
      ...filters,
      priority: value as 'high' | 'medium' | 'low'
    });
  }, [filters, onFiltersChange]);

  const handleToggleFilter = useCallback((filterKey: keyof FilterState) => {
    if (filterKey === 'priority') return; // Priority is handled separately

    onFiltersChange({
      ...filters,
      [filterKey]: !filters[filterKey]
    });
  }, [filters, onFiltersChange]);

  const handleClearAll = useCallback(() => {
    onFiltersChange({});
  }, [onFiltersChange]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          event.preventDefault();
          setIsOpen(false);
          triggerRef.current?.focus();
          break;
        case 'ArrowDown':
          event.preventDefault();
          setFocusedIndex(prev => {
            const maxIndex = 6; // 3 priority options + 3 toggle options
            return prev < maxIndex ? prev + 1 : 0;
          });
          break;
        case 'ArrowUp':
          event.preventDefault();
          setFocusedIndex(prev => {
            const maxIndex = 6;
            return prev > 0 ? prev - 1 : maxIndex;
          });
          break;
        case 'Enter':
        case ' ':
          event.preventDefault();
          // Handle selection based on focused index
          handleFocusedSelection();
          break;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, focusedIndex, handleFocusedSelection]);

  const handleFocusedSelection = useCallback(() => {
    switch (focusedIndex) {
      case 0:
        handlePriorityChange('high');
        break;
      case 1:
        handlePriorityChange('medium');
        break;
      case 2:
        handlePriorityChange('low');
        break;
      case 3:
        handleToggleFilter('dueDate');
        break;
      case 4:
        handleToggleFilter('lastUpdated');
        break;
      case 5:
        handleToggleFilter('completion');
        break;
      case 6:
        handleClearAll();
        break;
    }
  }, [focusedIndex, handlePriorityChange, handleToggleFilter, handleClearAll]);

  // Reset focus when dropdown closes
  useEffect(() => {
    if (!isOpen) {
      setFocusedIndex(-1);
    }
  }, [isOpen]);



  return (
    <div className={`space-y-3 ${className || ''}`}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            ref={triggerRef}
            variant="neutral-secondary"
            className="relative touch-manipulation min-h-[44px] sm:min-h-auto"
            disabled={isLoading}
            aria-expanded={isOpen}
            aria-haspopup="menu"
            aria-label={`Filter tasks${activeFilterCount > 0 ? ` (${activeFilterCount} active)` : ''}`}
          >
            <Filter className="h-4 w-4" />
            Filter
            {activeFilterCount > 0 && (
              <Badge 
                variant="secondary" 
                className="ml-2 h-5 min-w-5 bg-[#5E6AD2] text-white text-xs px-1.5"
              >
                {activeFilterCount}
              </Badge>
            )}
            <ChevronDown className="h-3 w-3 ml-1" />
          </Button>
        </PopoverTrigger>
        
        <PopoverContent
          ref={contentRef}
          className="w-80 sm:w-80 w-[calc(100vw-2rem)] max-w-sm p-0 z-[var(--z-dropdown)] animate-in fade-in-0 zoom-in-95 duration-200"
          align="end"
          sideOffset={8}
          role="menu"
          aria-label="Filter options"
        >
          <div className="p-4 space-y-4">
            {/* Header */}
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-sm">Filter Tasks</h3>
              {activeFilterCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearAll}
                  className={`h-8 px-3 text-xs text-muted-foreground hover:text-foreground touch-manipulation ${focusedIndex === 6 ? 'bg-muted' : ''}`}
                  aria-label={`Clear all ${activeFilterCount} active filters`}
                >
                  Clear All
                </Button>
              )}
            </div>

            {/* Priority Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Priority</Label>
              <RadioGroup
                value={filters.priority || ''}
                onValueChange={handlePriorityChange}
                className="space-y-2"
                aria-label="Filter by priority level"
              >
                <div className={`flex items-center space-x-2 p-2 rounded hover:bg-muted/50 transition-colors ${focusedIndex === 0 ? 'bg-muted' : ''}`}>
                  <RadioGroupItem
                    value="high"
                    id="priority-high"
                    className="touch-manipulation"
                    aria-describedby="priority-high-desc"
                  />
                  <Label htmlFor="priority-high" className="text-sm cursor-pointer flex-1">High Priority</Label>
                  <span id="priority-high-desc" className="sr-only">Filter tasks by high priority</span>
                </div>
                <div className={`flex items-center space-x-2 p-2 rounded hover:bg-muted/50 transition-colors ${focusedIndex === 1 ? 'bg-muted' : ''}`}>
                  <RadioGroupItem
                    value="medium"
                    id="priority-medium"
                    className="touch-manipulation"
                    aria-describedby="priority-medium-desc"
                  />
                  <Label htmlFor="priority-medium" className="text-sm cursor-pointer flex-1">Medium Priority</Label>
                  <span id="priority-medium-desc" className="sr-only">Filter tasks by medium priority</span>
                </div>
                <div className={`flex items-center space-x-2 p-2 rounded hover:bg-muted/50 transition-colors ${focusedIndex === 2 ? 'bg-muted' : ''}`}>
                  <RadioGroupItem
                    value="low"
                    id="priority-low"
                    className="touch-manipulation"
                    aria-describedby="priority-low-desc"
                  />
                  <Label htmlFor="priority-low" className="text-sm cursor-pointer flex-1">Low Priority</Label>
                  <span id="priority-low-desc" className="sr-only">Filter tasks by low priority</span>
                </div>
              </RadioGroup>
            </div>

            {/* Other Filters */}
            <div className="space-y-3">
              <div className={`flex items-center space-x-2 p-2 rounded hover:bg-muted/50 transition-colors ${focusedIndex === 3 ? 'bg-muted' : ''}`}>
                <Checkbox
                  id="due-date"
                  checked={filters.dueDate || false}
                  onCheckedChange={() => handleToggleFilter('dueDate')}
                  className="touch-manipulation"
                  aria-describedby="due-date-desc"
                />
                <Label htmlFor="due-date" className="text-sm cursor-pointer flex-1">Sort by Due Date</Label>
                <span id="due-date-desc" className="sr-only">Sort tasks by their due dates, with closer dates first</span>
              </div>

              <div className={`flex items-center space-x-2 p-2 rounded hover:bg-muted/50 transition-colors ${focusedIndex === 4 ? 'bg-muted' : ''}`}>
                <Checkbox
                  id="last-updated"
                  checked={filters.lastUpdated || false}
                  onCheckedChange={() => handleToggleFilter('lastUpdated')}
                  className="touch-manipulation"
                  aria-describedby="last-updated-desc"
                />
                <Label htmlFor="last-updated" className="text-sm cursor-pointer flex-1">Recently Updated</Label>
                <span id="last-updated-desc" className="sr-only">Sort tasks by most recent updates first</span>
              </div>

              <div className={`flex items-center space-x-2 p-2 rounded hover:bg-muted/50 transition-colors ${focusedIndex === 5 ? 'bg-muted' : ''}`}>
                <Checkbox
                  id="completion"
                  checked={filters.completion || false}
                  onCheckedChange={() => handleToggleFilter('completion')}
                  className="touch-manipulation"
                  aria-describedby="completion-desc"
                />
                <Label htmlFor="completion" className="text-sm cursor-pointer flex-1">Sort by Completion %</Label>
                <span id="completion-desc" className="sr-only">Sort tasks by completion percentage, with lower percentages first</span>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Active Filter Badges */}
      <ActiveFilterBadges
        filters={filters}
        onFiltersChange={onFiltersChange}
      />
    </div>
  );
}
