"use client";

import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { TaskMaster } from '@/lib/types/task-master';
import { TaskMasterCard } from './task-master-card';

// Enhanced animation data interface supporting both legacy and new formats
interface EnhancedAnimationData {
  cardId: string;
  cardData: TaskMaster;
  sourcePosition: DOMRect;
  destinationPosition: DOMRect | { x: number; y: number }; // Support both formats
  fromColumnId: string;
  toColumnId: string;
}

// Legacy animation data interface for backward compatibility
interface LegacyAnimationData {
  cardId: string;
  cardData: TaskMaster;
  sourcePosition: DOMRect;
  destinationPosition: { x: number; y: number };
  fromColumnId: string;
  toColumnId: string;
}

interface CardAnimationOverlayProps {
  animation: EnhancedAnimationData | LegacyAnimationData | null;
  duration: number;
  easing: string;
}

export function CardAnimationOverlay({ animation, duration, easing }: CardAnimationOverlayProps) {
  const [mounted, setMounted] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (animation) {
      // Start animation on next frame to ensure DOM is ready
      requestAnimationFrame(() => {
        setIsAnimating(true);
      });
    } else {
      setIsAnimating(false);
    }
  }, [animation]);

  if (!mounted || !animation) {
    return null;
  }

  const { cardData, sourcePosition, destinationPosition, fromColumnId } = animation;

  // Helper function to check if destination is DOMRect or legacy format
  const isDOMRect = (dest: DOMRect | { x: number; y: number }): dest is DOMRect => {
    return 'left' in dest && 'top' in dest;
  };

  // Calculate transform values with support for both formats
  const translateX = isAnimating
    ? (isDOMRect(destinationPosition)
        ? destinationPosition.left - sourcePosition.left
        : destinationPosition.x - sourcePosition.left)
    : 0;
  const translateY = isAnimating
    ? (isDOMRect(destinationPosition)
        ? destinationPosition.top - sourcePosition.top
        : destinationPosition.y - sourcePosition.top)
    : 0;

  // Determine variant based on source column
  const getVariant = (columnId: string): 'neutral' | 'warning' | 'success' | 'brand' => {
    switch (columnId) {
      case 'todo': return 'neutral';
      case 'inProgress': return 'warning';
      case 'completed': return 'success';
      default: return 'brand';
    }
  };

  const overlayContent = (
    <div
      className="fixed inset-0 pointer-events-none z-[500]"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 500,
        pointerEvents: 'none'
      }}
    >
      <div
        className="absolute"
        style={{
          left: sourcePosition.left,
          top: sourcePosition.top,
          width: sourcePosition.width,
          height: sourcePosition.height,
          transform: `translate3d(${translateX}px, ${translateY}px, 0)`,
          transition: isAnimating ? `transform ${duration}ms ${easing}` : 'none',
          willChange: 'transform'
        }}
      >
        <TaskMasterCard
          task={cardData}
          variant={getVariant(fromColumnId)}
          columnId={fromColumnId}
          // Disable interactions during animation
          onTaskClick={undefined}
          onDragStart={undefined}
          onDragEnd={undefined}
        />
      </div>
    </div>
  );

  return createPortal(overlayContent, document.body);
}
