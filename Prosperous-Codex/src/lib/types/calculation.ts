// Calculation Types and Interfaces
// This file defines all the types used in the paper cost calculation system

export interface JobInputs {
  // Common inputs for all components
  trimH: number;
  trimW: number;
  quantity: number;
  bleed: number;
  gripper: number;
  colorBar: number;
  spoilagePct: number;
  alignmentMode: 'Aligned' | 'Misaligned';
}

export interface InnerTextJobInputs extends JobInputs {
  totalPages: number;
  lip: number;
  bindingMethod: string;
  isDoubleLipActive: boolean;
}

export interface CoverJobInputs extends JobInputs {
  spineThickness: number;
  coverType: string;
  turnInAllowance: number;
  flapWidth: number;
}

export interface EndpaperJobInputs extends JobInputs {
  endpaperType: string;
}

export interface BleedDimensions {
  layoutPageH: number;
  layoutPageW: number;
}

export interface PaperCandidate {
  id: string;
  name: string;
  source: 'Pre-Cut' | 'Roll';
  sheetH: number | null;
  sheetW: number;
  grainDirection: 'Height' | 'Width';
  caliperMicrons: number;
  costReam: number | null;
  gsm: number;
  costTonne: number | null;
  category?: 'Inner Text' | 'Cover' | 'Endpapers';

  // Calculated properties
  pressH?: number;
  pressW?: number;
  usableH?: number;
  usableW?: number;
  maxItemsPerSide?: number;
  grainAlignmentStatus?: 'Aligned' | 'Misaligned';
  paperWasRotated?: boolean;
  rollDimensionOptimizedForFitting?: boolean;
  inputNoteKey?: string;
  rollOptimizationNoteKey?: string;
  layoutDescKey?: string;
  layoutOrientation?: string;
  winningLayoutDownPages?: number;
  winningLayoutAcrossPages?: number;
  occupiedHeight?: number;
  occupiedWidth?: number;
  layoutDown?: number;
  layoutAcross?: number;

  // Bleed dimensions (untrimmed page size)
  layoutPageH?: number;
  layoutPageW?: number;

  // Final metrics
  pagesPerSheetOutput?: number;
  totalSheetsNeeded?: number;
  costPerSheet?: number;
  totalCost?: number;
  costPerBook?: number;
  bookBlockThickness_mm?: number;
  wastePercent?: number;
  utilisationRate?: number;
  isOptimalCandidate?: boolean;

  // Error handling
  error?: boolean;
  errorMessageKey?: string;
}

export interface LayoutFitResult {
  layoutDown: number;
  layoutAcross: number;
  fit: number;
  occupiedHeight: number;
  occupiedWidth: number;
}

export interface CalculationRequest {
  tabId: 'innerText' | 'cover' | 'endpapers';
  jobInputs: JobInputs | InnerTextJobInputs | CoverJobInputs | EndpaperJobInputs;
  paperOptions: any[]; // Can be PaperOption[] from UI or PaperCandidate[] internally
}

export interface CalculationResponse {
  results: PaperCandidate[];
  benchmarkValue: number;
  errors?: string[];
}

// Press constraints
export const PRESS_CONSTRAINTS = {
  MAX_PRESS_H: 720,
  MAX_PRESS_W: 1020,
  MAX_PAGES_PER_SIDE: 16, // Folding limit for inner text
} as const;

// Binding methods that require four-side bleed (matches reference v23.html)
export const FOUR_SIDE_BLEED_METHODS = [
  'singlePage',
  'wireO'
] as const;

// Error message keys
export const ERROR_KEYS = {
  INVALID_TRIM_DIMENSIONS: 'errorTrimDimensionsInvalid',
  INVALID_TOTAL_PAGES: 'errorTotalPagesInvalid',
  NEGATIVE_PROD_PARAMS: 'alertNegativeProdParams',
  INVALID_PAPER_DIMENSIONS: 'errorPaperDimensionsInvalid',
  INVALID_COST_DATA: 'errorInvalidCostData',
  USABLE_AREA_NEGATIVE: 'errorUsableAreaNegative',
  COST_SHEET_NEGATIVE: 'errorCostSheetNegative',
  INVALID_ROLL_COST: 'errorInvalidRollCost',
  MISSING_COST_REAM: 'errorMissingCostReam',
  COST_CALC_ERROR: 'errorCostCalc',
  PAGE_DIMS_INVALID: 'errorPageDimsInvalid',
  COVER_SPREAD_SIZE: 'errorCoverSpreadSize',
  ENDPAPER_SPREAD_SIZE: 'errorEndpaperSpreadSize',
  CANNOT_FIT: 'errorCannotFit'
} as const;
