"use client";

import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import { Sun, Moon, Monitor } from "lucide-react";

export function ThemeSwitcher() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="theme-switch" suppressHydrationWarning>
        <div className="theme-switch-toggle"></div>
      </div>
    );
  }

  const getThemeClass = () => {
    if (theme === "dark") return "dark";
    if (theme === "system") return "system";
    return "light";
  };

  return (
    <div className={`theme-switch ${getThemeClass()}`} suppressHydrationWarning>
      <button
        className="theme-icon sun-icon"
        onClick={() => setTheme("light")}
        title="Light mode"
        aria-label="Switch to light mode"
      >
        <Sun size={14} />
      </button>
      <button
        className="theme-icon system-icon"
        onClick={() => setTheme("system")}
        title="System mode"
        aria-label="Switch to system mode"
      >
        <Monitor size={14} />
      </button>
      <button
        className="theme-icon moon-icon"
        onClick={() => setTheme("dark")}
        title="Dark mode"
        aria-label="Switch to dark mode"
      >
        <Moon size={14} />
      </button>
      <div className="theme-switch-toggle"></div>
    </div>
  );
}