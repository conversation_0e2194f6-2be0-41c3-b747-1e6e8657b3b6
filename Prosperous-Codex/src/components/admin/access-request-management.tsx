"use client";

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { UserPlus, Check, X, Clock, User } from 'lucide-react';

interface AccessRequest {
  id: number;
  email: string;
  name?: string;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  requested_at: string;
  processed_at?: string;
  processed_by_username?: string;
}

export function AccessRequestManagement() {
  const { toast } = useToast();
  const [requests, setRequests] = useState<AccessRequest[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<AccessRequest | null>(null);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [password, setPassword] = useState('');

  const fetchAccessRequests = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/access-requests');
      const data = await response.json();

      if (response.ok) {
        setRequests(data.requests);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch access requests",
          variant: "destructive"
        });
      }
    } catch (_error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const handleApproveRequest = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedRequest) return;
    
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/admin/access-requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          requestId: selectedRequest.id,
          action: 'approve',
          password
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast({
          title: "Success",
          description: "Access request approved and user created successfully"
        });
        setIsApproveDialogOpen(false);
        setSelectedRequest(null);
        setPassword('');
        fetchAccessRequests();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to approve request",
          variant: "destructive"
        });
      }
    } catch (_error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejectRequest = async (request: AccessRequest) => {
    if (!confirm(`Are you sure you want to reject the access request from ${request.email}?`)) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/admin/access-requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          requestId: request.id,
          action: 'reject'
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast({
          title: "Success",
          description: "Access request rejected"
        });
        fetchAccessRequests();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to reject request",
          variant: "destructive"
        });
      }
    } catch (_error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const openApproveDialog = (request: AccessRequest) => {
    setSelectedRequest(request);
    setIsApproveDialogOpen(true);
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved': return 'success';
      case 'rejected': return 'destructive';
      default: return 'secondary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <Check className="h-3 w-3" />;
      case 'rejected': return <X className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  useEffect(() => {
    fetchAccessRequests();
  }, [fetchAccessRequests]);

  const pendingRequests = requests.filter(r => r.status === 'pending');
  const processedRequests = requests.filter(r => r.status !== 'pending');

  return (
    <div className="space-y-6">
      {/* Pending Requests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Pending Access Requests
            {pendingRequests.length > 0 && (
              <Badge variant="secondary">{pendingRequests.length}</Badge>
            )}
          </CardTitle>
          <CardDescription>
            Review and approve or reject new user access requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading && requests.length === 0 ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#5E6AD2] mx-auto"></div>
              <p className="text-sm text-muted-foreground mt-2">Loading requests...</p>
            </div>
          ) : pendingRequests.length === 0 ? (
            <div className="text-center py-8">
              <UserPlus className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No pending access requests</p>
            </div>
          ) : (
            <div className="space-y-4">
              {pendingRequests.map((request) => (
                <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center justify-center w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full">
                      <User className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{request.email}</span>
                        {request.name && (
                          <span className="text-sm text-muted-foreground">({request.name})</span>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Requested: {new Date(request.requested_at).toLocaleDateString()}
                      </div>
                      {request.reason && (
                        <div className="text-sm text-muted-foreground mt-1">
                          Reason: {request.reason}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openApproveDialog(request)}
                      className="text-green-600 hover:text-green-700"
                    >
                      <Check className="h-4 w-4 mr-1" />
                      Approve
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRejectRequest(request)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4 mr-1" />
                      Reject
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Processed Requests */}
      <Card>
        <CardHeader>
          <CardTitle>Request History</CardTitle>
          <CardDescription>
            Previously processed access requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          {processedRequests.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No processed requests</p>
            </div>
          ) : (
            <div className="space-y-4">
              {processedRequests.map((request) => (
                <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center justify-center w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full">
                      <User className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{request.email}</span>
                        {request.name && (
                          <span className="text-sm text-muted-foreground">({request.name})</span>
                        )}
                        <Badge variant={getStatusBadgeVariant(request.status)} className="flex items-center gap-1">
                          {getStatusIcon(request.status)}
                          {request.status}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Requested: {new Date(request.requested_at).toLocaleDateString()}
                        {request.processed_at && (
                          <> • Processed: {new Date(request.processed_at).toLocaleDateString()}</>
                        )}
                        {request.processed_by_username && (
                          <> • By: {request.processed_by_username}</>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Approve Request Dialog */}
      <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Access Request</DialogTitle>
            <DialogDescription>
              Create a new user account for {selectedRequest?.email}. Please set a temporary password.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleApproveRequest} className="space-y-4">
            <div className="space-y-2">
              <Label>Email</Label>
              <Input value={selectedRequest?.email || ''} disabled className="bg-gray-50 dark:bg-gray-800" />
            </div>
            <div className="space-y-2">
              <Label>Username</Label>
              <Input value={selectedRequest?.name || selectedRequest?.email?.split('@')[0] || ''} disabled className="bg-gray-50 dark:bg-gray-800" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Temporary Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                minLength={6}
                placeholder="Enter a temporary password for the user"
              />
              <p className="text-xs text-muted-foreground">
                The user should change this password after their first login.
              </p>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsApproveDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Creating User...' : 'Approve & Create User'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
