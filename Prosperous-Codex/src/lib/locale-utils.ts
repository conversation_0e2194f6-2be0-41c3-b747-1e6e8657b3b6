import { routing } from '@/i18n/routing';

export type Locale = (typeof routing.locales)[number];

export const localeNames: Record<Locale, string> = {
  en: 'English',
  'zh-cn': '简体中文',
  'zh-tw': '繁體中文'
};

export const localeFlags: Record<Locale, string> = {
  en: '🇺🇸',
  'zh-cn': '🇨🇳',
  'zh-tw': '🇹🇼'
};

export function getLocaleDisplayName(locale: Locale): string {
  return localeNames[locale] || locale;
}

export function getLocaleFlag(locale: Locale): string {
  return localeFlags[locale] || '🌐';
}

export function isValidLocale(locale: string): locale is Locale {
  return routing.locales.includes(locale as Locale);
}

export function getDefaultLocale(): Locale {
  return routing.defaultLocale as Locale;
}

export function saveUserLanguagePreference(locale: Locale): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('user-language-preference', locale);
  }
}

export function getUserLanguagePreference(): Locale | null {
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem('user-language-preference');
    return saved && isValidLocale(saved) ? saved : null;
  }
  return null;
}

export async function updateUserLanguageInDatabase(locale: Locale): Promise<boolean> {
  try {
    const response = await fetch('/api/user/language', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ language: locale })
    });
    return response.ok;
  } catch (error) {
    console.error('Failed to update user language preference:', error);
    return false;
  }
}
