"use client";

import React from 'react';
import { Search, Filter, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { FilterState } from './filter-dropdown';

interface EmptyFilterResultsProps {
  filters: FilterState;
  onClearFilters: () => void;
  onRefresh: () => void;
  isLoading?: boolean;
}

export function EmptyFilterResults({ 
  filters, 
  onClearFilters, 
  onRefresh, 
  isLoading = false 
}: EmptyFilterResultsProps) {
  const activeFilterCount = Object.values(filters).filter(Boolean).length;
  
  const getFilterDescription = () => {
    const descriptions = [];
    
    if (filters.priority) {
      descriptions.push(`${filters.priority} priority`);
    }
    
    if (filters.dueDate) {
      descriptions.push('due date sorting');
    }
    
    if (filters.lastUpdated) {
      descriptions.push('recent updates');
    }
    
    if (filters.completion) {
      descriptions.push('completion percentage');
    }
    
    if (descriptions.length === 0) {
      return 'your current filters';
    }
    
    if (descriptions.length === 1) {
      return descriptions[0];
    }
    
    if (descriptions.length === 2) {
      return descriptions.join(' and ');
    }
    
    return descriptions.slice(0, -1).join(', ') + ', and ' + descriptions[descriptions.length - 1];
  };

  return (
    <div className="flex items-center justify-center min-h-[400px] p-8">
      <Card className="w-full max-w-md">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            {/* Icon */}
            <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center">
              <Search className="h-8 w-8 text-muted-foreground" />
            </div>
            
            {/* Title */}
            <h3 className="text-lg font-semibold text-foreground">
              No Tasks Found
            </h3>
            
            {/* Description */}
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                No tasks match {getFilterDescription()}.
              </p>
              
              {activeFilterCount > 0 && (
                <p className="text-xs text-muted-foreground">
                  Try adjusting your filters or clearing them to see all tasks.
                </p>
              )}
            </div>
            
            {/* Active Filters Summary */}
            {activeFilterCount > 0 && (
              <div className="bg-muted/50 rounded-lg p-3 space-y-2">
                <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                  <Filter className="h-3 w-3" />
                  <span>{activeFilterCount} active filter{activeFilterCount > 1 ? 's' : ''}</span>
                </div>
                
                <div className="flex flex-wrap gap-1 justify-center">
                  {filters.priority && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-background text-foreground border">
                      Priority: {filters.priority}
                    </span>
                  )}
                  {filters.dueDate && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-background text-foreground border">
                      Due Date
                    </span>
                  )}
                  {filters.lastUpdated && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-background text-foreground border">
                      Recent Updates
                    </span>
                  )}
                  {filters.completion && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-background text-foreground border">
                      Completion %
                    </span>
                  )}
                </div>
              </div>
            )}
            
            {/* Actions */}
            <div className="flex flex-col sm:flex-row gap-2 pt-2">
              {activeFilterCount > 0 && (
                <Button
                  variant="outline"
                  onClick={onClearFilters}
                  className="flex-1"
                  disabled={isLoading}
                >
                  Clear Filters
                </Button>
              )}
              
              <Button
                variant="default"
                onClick={onRefresh}
                className="flex-1"
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
            
            {/* Help Text */}
            <p className="text-xs text-muted-foreground">
              If you believe this is an error, try refreshing the page or contact support.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
