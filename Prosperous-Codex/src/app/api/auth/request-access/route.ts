import { NextRequest } from 'next/server'
import { AccessRequestSchema } from '@/lib/schemas/auth'
import { UserService } from '@/lib/database/user-service'
import { getDatabase } from '@/lib/database/database'

const userService = new UserService()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the request data
    const validatedFields = AccessRequestSchema.safeParse(body)
    
    if (!validatedFields.success) {
      return Response.json(
        { success: false, error: 'Invalid form data' },
        { status: 400 }
      )
    }

    const { email, name, reason } = validatedFields.data

    // Check if user already exists
    const existingUser = await userService.getUserByEmail(email)
    if (existingUser) {
      return Response.json(
        { success: false, error: 'User already exists' },
        { status: 400 }
      )
    }

    // Check if access request already exists
    const db = getDatabase()
    const existingRequestStmt = db.prepare('SELECT id FROM access_requests WHERE email = ?')
    const existingRequest = existingRequestStmt.get(email)

    if (existingRequest) {
      return Response.json(
        { success: false, error: 'Access request already submitted for this email' },
        { status: 400 }
      )
    }

    // Store the access request in database
    const insertStmt = db.prepare(`
      INSERT INTO access_requests (email, name, reason, status, requested_at)
      VALUES (?, ?, ?, 'pending', CURRENT_TIMESTAMP)
    `)

    try {
      insertStmt.run(email, name || null, reason || null)

      return Response.json(
        { success: true, message: 'Access request submitted successfully' },
        { status: 200 }
      )
    } catch (error) {
      console.error('Database error:', error)
      return Response.json(
        { success: false, error: 'Failed to store access request' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Access request error:', error)
    return Response.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
