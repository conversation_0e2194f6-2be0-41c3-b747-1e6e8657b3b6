/**
 * Security audit utilities for Task Master API endpoints
 * Provides tools to analyze and improve API security
 */

import { readdir, readFile } from 'fs/promises';
import { join } from 'path';

/**
 * Security check result
 */
export interface SecurityCheck {
  endpoint: string;
  method: string;
  issues: SecurityIssue[];
  score: number; // 0-100
  recommendations: string[];
}

/**
 * Security issue types
 */
export interface SecurityIssue {
  type: 'authentication' | 'authorization' | 'validation' | 'sanitization' | 'error-handling' | 'rate-limiting';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  line?: number;
  code?: string;
}

/**
 * Security audit configuration
 */
export interface SecurityAuditConfig {
  checkAuthentication: boolean;
  checkAuthorization: boolean;
  checkInputValidation: boolean;
  checkInputSanitization: boolean;
  checkErrorHandling: boolean;
  checkRateLimiting: boolean;
  checkSecurityHeaders: boolean;
}

/**
 * Default security audit configuration
 */
export const DEFAULT_AUDIT_CONFIG: SecurityAuditConfig = {
  checkAuthentication: true,
  checkAuthorization: true,
  checkInputValidation: true,
  checkInputSanitization: true,
  checkErrorHandling: true,
  checkRateLimiting: true,
  checkSecurityHeaders: true,
};

/**
 * Security patterns to look for
 */
const SECURITY_PATTERNS = {
  authentication: [
    /requireAuth\(/,
    /requireRole\(/,
    /authenticateRequest\(/,
  ],
  authorization: [
    /hasAccess/,
    /AuthorizationService/,
    /assertCan\(/,
    /checkPermission/,
  ],
  validation: [
    /parseInt\(/,
    /isNaN\(/,
    /\.includes\(/,
    /validate/i,
    /schema/i,
  ],
  sanitization: [
    /sanitize/i,
    /DOMPurify/,
    /InputSanitizer/,
    /escape/i,
  ],
  errorHandling: [
    /try\s*{/,
    /catch\s*\(/,
    /throw\s+/,
    /NextResponse\.json/,
  ],
  rateLimiting: [
    /rateLimit/i,
    /throttle/i,
    /limit/i,
  ],
  securityHeaders: [
    /X-Content-Type-Options/,
    /X-Frame-Options/,
    /X-XSS-Protection/,
    /Content-Security-Policy/,
  ],
};

/**
 * Analyze a single API route file for security issues
 */
export async function analyzeRouteFile(filePath: string, config: SecurityAuditConfig = DEFAULT_AUDIT_CONFIG): Promise<SecurityCheck> {
  const content = await readFile(filePath, 'utf-8');
  const issues: SecurityIssue[] = [];
  const recommendations: string[] = [];

  // Extract endpoint info from file path
  const pathParts = filePath.split('/');
  const endpoint = pathParts.slice(pathParts.indexOf('api')).join('/').replace(/route\.ts$/, '');
  
  // Detect HTTP methods
  const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'].filter(method => 
    content.includes(`export async function ${method}(`)
  );

  let score = 100;

  for (const method of methods) {
    const methodContent = extractMethodContent(content, method);
    
    // Check authentication
    if (config.checkAuthentication) {
      const hasAuth = SECURITY_PATTERNS.authentication.some(pattern => pattern.test(methodContent));
      if (!hasAuth && !isPublicEndpoint(endpoint)) {
        issues.push({
          type: 'authentication',
          severity: 'critical',
          description: `${method} ${endpoint} lacks authentication`,
        });
        recommendations.push(`Add requireAuth() or requireRole() to ${method} ${endpoint}`);
        score -= 25;
      }
    }

    // Check authorization
    if (config.checkAuthorization) {
      const hasAuth = SECURITY_PATTERNS.authentication.some(pattern => pattern.test(methodContent));
      const hasAuthz = SECURITY_PATTERNS.authorization.some(pattern => pattern.test(methodContent));
      
      if (hasAuth && !hasAuthz && !isPublicEndpoint(endpoint)) {
        issues.push({
          type: 'authorization',
          severity: 'high',
          description: `${method} ${endpoint} has authentication but lacks proper authorization checks`,
        });
        recommendations.push(`Add authorization checks to ${method} ${endpoint}`);
        score -= 15;
      }
    }

    // Check input validation
    if (config.checkInputValidation) {
      const hasValidation = SECURITY_PATTERNS.validation.some(pattern => pattern.test(methodContent));
      const hasUserInput = methodContent.includes('request.json()') || methodContent.includes('searchParams');
      
      if (hasUserInput && !hasValidation) {
        issues.push({
          type: 'validation',
          severity: 'high',
          description: `${method} ${endpoint} accepts user input but lacks validation`,
        });
        recommendations.push(`Add input validation to ${method} ${endpoint}`);
        score -= 15;
      }
    }

    // Check input sanitization
    if (config.checkInputSanitization) {
      const hasSanitization = SECURITY_PATTERNS.sanitization.some(pattern => pattern.test(methodContent));
      const hasUserInput = methodContent.includes('request.json()') || methodContent.includes('searchParams');
      
      if (hasUserInput && !hasSanitization) {
        issues.push({
          type: 'sanitization',
          severity: 'medium',
          description: `${method} ${endpoint} accepts user input but lacks sanitization`,
        });
        recommendations.push(`Add input sanitization to ${method} ${endpoint}`);
        score -= 10;
      }
    }

    // Check error handling
    if (config.checkErrorHandling) {
      const hasErrorHandling = methodContent.includes('try') && methodContent.includes('catch');
      
      if (!hasErrorHandling) {
        issues.push({
          type: 'error-handling',
          severity: 'medium',
          description: `${method} ${endpoint} lacks proper error handling`,
        });
        recommendations.push(`Add try-catch error handling to ${method} ${endpoint}`);
        score -= 10;
      }
    }

    // Check rate limiting
    if (config.checkRateLimiting) {
      const hasRateLimit = SECURITY_PATTERNS.rateLimiting.some(pattern => pattern.test(methodContent));
      
      if (!hasRateLimit && isWriteOperation(method)) {
        issues.push({
          type: 'rate-limiting',
          severity: 'low',
          description: `${method} ${endpoint} lacks rate limiting`,
        });
        recommendations.push(`Consider adding rate limiting to ${method} ${endpoint}`);
        score -= 5;
      }
    }

    // Check security headers
    if (config.checkSecurityHeaders) {
      const hasSecurityHeaders = SECURITY_PATTERNS.securityHeaders.some(pattern => pattern.test(methodContent));
      
      if (!hasSecurityHeaders) {
        issues.push({
          type: 'error-handling',
          severity: 'low',
          description: `${method} ${endpoint} doesn't set security headers`,
        });
        recommendations.push(`Add security headers to ${method} ${endpoint} responses`);
        score -= 5;
      }
    }
  }

  return {
    endpoint,
    method: methods.join(', '),
    issues,
    score: Math.max(0, score),
    recommendations,
  };
}

/**
 * Extract method content from file
 */
function extractMethodContent(content: string, method: string): string {
  const methodStart = content.indexOf(`export async function ${method}(`);
  if (methodStart === -1) return '';

  let braceCount = 0;
  let inMethod = false;
  let methodContent = '';
  
  for (let i = methodStart; i < content.length; i++) {
    const char = content[i];
    
    if (char === '{') {
      braceCount++;
      inMethod = true;
    } else if (char === '}') {
      braceCount--;
    }
    
    if (inMethod) {
      methodContent += char;
    }
    
    if (inMethod && braceCount === 0) {
      break;
    }
  }
  
  return methodContent;
}

/**
 * Check if endpoint is public (doesn't require authentication)
 */
function isPublicEndpoint(endpoint: string): boolean {
  const publicEndpoints = [
    '/api/health',
    '/api/status',
    '/api/auth',
  ];
  
  return publicEndpoints.some(publicEndpoint => endpoint.includes(publicEndpoint));
}

/**
 * Check if HTTP method is a write operation
 */
function isWriteOperation(method: string): boolean {
  return ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method);
}

/**
 * Audit all Task Master API routes
 */
export async function auditTaskMasterRoutes(config: SecurityAuditConfig = DEFAULT_AUDIT_CONFIG): Promise<SecurityCheck[]> {
  const apiDir = join(process.cwd(), 'src/app/api/task-master');
  const results: SecurityCheck[] = [];
  
  async function scanDirectory(dir: string): Promise<void> {
    try {
      const entries = await readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = join(dir, entry.name);
        
        if (entry.isDirectory()) {
          await scanDirectory(fullPath);
        } else if (entry.name === 'route.ts') {
          const result = await analyzeRouteFile(fullPath, config);
          results.push(result);
        }
      }
    } catch (error) {
      console.warn(`Failed to scan directory ${dir}:`, error);
    }
  }
  
  await scanDirectory(apiDir);
  return results;
}

/**
 * Generate security audit report
 */
export function generateSecurityReport(checks: SecurityCheck[]): string {
  const totalEndpoints = checks.length;
  const averageScore = checks.reduce((sum, check) => sum + check.score, 0) / totalEndpoints;
  const criticalIssues = checks.flatMap(check => check.issues).filter(issue => issue.severity === 'critical');
  const highIssues = checks.flatMap(check => check.issues).filter(issue => issue.severity === 'high');
  
  let report = `# Task Master API Security Audit Report\n\n`;
  report += `**Generated:** ${new Date().toISOString()}\n`;
  report += `**Total Endpoints:** ${totalEndpoints}\n`;
  report += `**Average Security Score:** ${averageScore.toFixed(1)}/100\n`;
  report += `**Critical Issues:** ${criticalIssues.length}\n`;
  report += `**High Priority Issues:** ${highIssues.length}\n\n`;

  // Summary by endpoint
  report += `## Endpoint Security Scores\n\n`;
  checks.sort((a, b) => a.score - b.score).forEach(check => {
    const status = check.score >= 80 ? '✅' : check.score >= 60 ? '⚠️' : '❌';
    report += `${status} **${check.endpoint}** (${check.method}): ${check.score}/100\n`;
  });

  // Critical issues
  if (criticalIssues.length > 0) {
    report += `\n## Critical Issues (Immediate Action Required)\n\n`;
    criticalIssues.forEach((issue, index) => {
      report += `${index + 1}. **${issue.description}**\n`;
      report += `   - Type: ${issue.type}\n`;
      report += `   - Severity: ${issue.severity}\n\n`;
    });
  }

  // Recommendations
  const allRecommendations = checks.flatMap(check => check.recommendations);
  if (allRecommendations.length > 0) {
    report += `\n## Security Recommendations\n\n`;
    [...new Set(allRecommendations)].forEach((rec, index) => {
      report += `${index + 1}. ${rec}\n`;
    });
  }

  return report;
}
