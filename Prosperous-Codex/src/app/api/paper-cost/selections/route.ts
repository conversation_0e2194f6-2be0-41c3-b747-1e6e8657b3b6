import { NextRequest, NextResponse } from 'next/server';
import { PaperCostService } from '@/lib/database/paper-cost-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const paperCostService = new PaperCostService();
    
    // Get user's selections (shopping cart)
    const selections = paperCostService.getUserSelections(parseInt(user.id));
    
    return NextResponse.json({ selections });
    
  } catch (error) {
    console.error('Get selections error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { component_type, component_id, component_data } = await request.json();
    
    // Validate required fields
    if (!component_type || !component_id || !component_data) {
      return NextResponse.json(
        { error: 'Missing required fields: component_type, component_id, component_data' },
        { status: 400 }
      );
    }
    
    // Validate component_type
    if (!['innerText', 'cover', 'endpapers'].includes(component_type)) {
      return NextResponse.json(
        { error: 'Invalid component_type. Must be innerText, cover, or endpapers' },
        { status: 400 }
      );
    }
    
    const paperCostService = new PaperCostService();
    
    // Update user selection
    const success = paperCostService.updateUserSelection(
      parseInt(user.id),
      component_type,
      component_id,
      component_data
    );
    
    if (success) {
      return NextResponse.json({ 
        message: 'Selection updated successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to update selection' },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Update selection error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { searchParams } = new URL(request.url);
    const componentType = searchParams.get('component_type');
    const componentId = searchParams.get('component_id');
    
    // Validate required fields
    if (!componentType || !componentId) {
      return NextResponse.json(
        { error: 'Missing required query parameters: component_type, component_id' },
        { status: 400 }
      );
    }
    
    // Validate component_type
    if (!['innerText', 'cover', 'endpapers'].includes(componentType)) {
      return NextResponse.json(
        { error: 'Invalid component_type. Must be innerText, cover, or endpapers' },
        { status: 400 }
      );
    }
    
    const paperCostService = new PaperCostService();
    
    // Remove user selection
    const success = paperCostService.removeUserSelection(
      parseInt(user.id),
      componentType,
      componentId
    );
    
    if (success) {
      return NextResponse.json({ 
        message: 'Selection removed successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Selection not found or failed to remove' },
        { status: 404 }
      );
    }
    
  } catch (error) {
    console.error('Remove selection error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
