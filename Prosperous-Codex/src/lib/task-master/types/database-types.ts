/**
 * Database Types
 * 
 * Type definitions for database operations
 * Extracted from validation-types.ts for better organization
 */

// Re-export database types from validation-types
export type {
  ProjectDbFields,
  TaskDbFields,
  CommentDbFields,
  FileDbFields,
  TeamMemberDbFields,
  ActivityLogDbFields
} from '../validation-types';

/**
 * Additional database-specific types
 */

/**
 * Database connection configuration
 */
export interface DatabaseConfig {
  filename: string;
  readonly?: boolean;
  fileMustExist?: boolean;
  timeout?: number;
  verbose?: (message?: any, ...additionalArgs: any[]) => void;
}

/**
 * Transaction options
 */
export interface TransactionOptions {
  immediate?: boolean;
  deferred?: boolean;
  exclusive?: boolean;
}

/**
 * Query result metadata
 */
export interface QueryResult {
  changes: number;
  lastInsertRowid?: number;
  duration?: number;
  sql?: string;
}

/**
 * Database migration
 */
export interface Migration {
  id: string;
  name: string;
  up: string;
  down: string;
  timestamp: string;
  checksum?: string;
  applied?: boolean;
}

/**
 * Database backup options
 */
export interface BackupOptions {
  filename: string;
  progress?: (remaining: number, pageCount: number) => void;
  attached?: string;
}

/**
 * Database index definition
 */
export interface IndexDefinition {
  name: string;
  table: string;
  columns: string[];
  unique?: boolean;
  partial?: string;
}

/**
 * Database constraint definition
 */
export interface ConstraintDefinition {
  name: string;
  type: 'PRIMARY KEY' | 'FOREIGN KEY' | 'UNIQUE' | 'CHECK' | 'NOT NULL';
  columns: string[];
  references?: {
    table: string;
    columns: string[];
    onDelete?: 'CASCADE' | 'SET NULL' | 'RESTRICT' | 'NO ACTION';
    onUpdate?: 'CASCADE' | 'SET NULL' | 'RESTRICT' | 'NO ACTION';
  };
  expression?: string;
}

/**
 * Database column definition
 */
export interface ColumnDefinition {
  name: string;
  type: string;
  nullable: boolean;
  defaultValue?: any;
  primaryKey: boolean;
  autoIncrement?: boolean;
  unique?: boolean;
  check?: string;
}

/**
 * Database table definition
 */
export interface TableDefinition {
  name: string;
  columns: ColumnDefinition[];
  constraints?: ConstraintDefinition[];
  indexes?: IndexDefinition[];
}

/**
 * Database schema definition
 */
export interface DatabaseSchema {
  version: string;
  tables: TableDefinition[];
  views?: Array<{
    name: string;
    definition: string;
  }>;
  triggers?: Array<{
    name: string;
    table: string;
    event: 'INSERT' | 'UPDATE' | 'DELETE';
    timing: 'BEFORE' | 'AFTER' | 'INSTEAD OF';
    definition: string;
  }>;
}

/**
 * Database performance metrics
 */
export interface PerformanceMetrics {
  queryCount: number;
  averageQueryTime: number;
  slowQueries: Array<{
    sql: string;
    duration: number;
    timestamp: string;
  }>;
  cacheHitRatio: number;
  connectionCount: number;
  memoryUsage?: number;
  diskUsage?: number;
}

/**
 * Database health status
 */
export interface DatabaseHealth {
  connected: boolean;
  version: string;
  size: number;
  lastBackup?: string;
  integrityCheck: boolean;
  performance?: PerformanceMetrics;
  errors?: string[];
}

/**
 * Query builder types
 */
export interface QueryBuilder {
  select(columns?: string[]): QueryBuilder;
  from(table: string): QueryBuilder;
  where(condition: string, ...params: any[]): QueryBuilder;
  join(table: string, condition: string): QueryBuilder;
  leftJoin(table: string, condition: string): QueryBuilder;
  orderBy(column: string, direction?: 'ASC' | 'DESC'): QueryBuilder;
  groupBy(columns: string[]): QueryBuilder;
  having(condition: string, ...params: any[]): QueryBuilder;
  limit(count: number): QueryBuilder;
  offset(count: number): QueryBuilder;
  build(): { sql: string; params: any[] };
}

/**
 * Database connection pool
 */
export interface ConnectionPool {
  acquire(): Promise<DatabaseConnection>;
  release(connection: DatabaseConnection): void;
  destroy(): Promise<void>;
  size: number;
  available: number;
  pending: number;
}

/**
 * Database connection
 */
export interface DatabaseConnection {
  query<T = any>(sql: string, params?: any[]): Promise<T[]>;
  execute(sql: string, params?: any[]): Promise<QueryResult>;
  transaction<T>(fn: (conn: DatabaseConnection) => Promise<T>): Promise<T>;
  close(): Promise<void>;
  isConnected: boolean;
}

/**
 * Database event types
 */
export type DatabaseEvent = 
  | { type: 'connection_opened'; timestamp: string }
  | { type: 'connection_closed'; timestamp: string }
  | { type: 'query_executed'; sql: string; duration: number; timestamp: string }
  | { type: 'transaction_started'; timestamp: string }
  | { type: 'transaction_committed'; timestamp: string }
  | { type: 'transaction_rolled_back'; timestamp: string }
  | { type: 'error'; error: string; timestamp: string };

/**
 * Database event listener
 */
export interface DatabaseEventListener {
  (event: DatabaseEvent): void;
}

/**
 * Database audit log entry
 */
export interface AuditLogEntry {
  id: number;
  table: string;
  operation: 'INSERT' | 'UPDATE' | 'DELETE';
  recordId: number;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  userId?: number;
  timestamp: string;
  metadata?: Record<string, any>;
}
