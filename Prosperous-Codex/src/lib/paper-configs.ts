// Paper Options Card Configurations
// This file contains the configuration objects for different paper categories
// to be used with the PaperOptionsCard component

import { PaperOption, paperDataUtils, innerTextPapers, coverPapers, endpaperPapers } from './paper-data';

export interface PaperOptionsConfig {
  title: string;
  sources: string[];
  grainDirections: string[];
  additionalFields?: {
    [key: string]: {
      label: string;
      type: 'select' | 'input' | 'number';
      options?: string[];
      defaultValue?: any;
    };
  };
  defaultValues: Partial<PaperOption>;
  initialData: PaperOption[];
}

// Inner Text Paper Configuration
export const innerTextConfig: PaperOptionsConfig = {
  title: 'Inner Text Paper Options',
  sources: ['Pre-Cut', 'Roll'],
  grainDirections: ['Height', 'Width'],
  additionalFields: {
    costPerTon: {
      label: 'Cost/Ton ($)',
      type: 'number',
      defaultValue: null
    }
  },
  defaultValues: {
    id: '',
    name: '',
    source: 'Pre-Cut',
    sheetHeight: null,
    sheetWidth: 0,
    grainDirection: 'Height',
    caliper: 100,
    costPerReam: null,
    gsm: 80,
    costPerTon: null
  },
  initialData: innerTextPapers
};

// Cover Paper Configuration
export const coverConfig: PaperOptionsConfig = {
  title: 'Cover Paper Options',
  sources: ['Pre-Cut'],
  grainDirections: ['Height', 'Width'],
  additionalFields: {
    costPerTon: {
      label: 'Cost/Ton ($)',
      type: 'number',
      defaultValue: null
    }
  },
  defaultValues: {
    id: '',
    name: '',
    source: 'Pre-Cut',
    sheetHeight: 635,
    sheetWidth: 965,
    grainDirection: 'Height',
    caliper: 280,
    costPerReam: null,
    gsm: 250,
    costPerTon: null
  },
  initialData: coverPapers
};

// Endpaper Configuration
export const endpaperConfig: PaperOptionsConfig = {
  title: 'Endpaper Options',
  sources: ['Pre-Cut'],
  grainDirections: ['Height', 'Width'],
  additionalFields: {
    costPerTon: {
      label: 'Cost/Ton ($)',
      type: 'number',
      defaultValue: null
    }
  },
  defaultValues: {
    id: '',
    name: '',
    source: 'Pre-Cut',
    sheetHeight: 787.4,
    sheetWidth: 1092.2,
    grainDirection: 'Height',
    caliper: 140,
    costPerReam: null,
    gsm: 120,
    costPerTon: null
  },
  initialData: endpaperPapers
};

// Configuration factory function
export const createPaperConfig = (category: 'Inner Text' | 'Cover' | 'Endpapers'): PaperOptionsConfig => {
  switch (category) {
    case 'Inner Text':
      return innerTextConfig;
    case 'Cover':
      return coverConfig;
    case 'Endpapers':
      return endpaperConfig;
    default:
      throw new Error(`Unknown paper category: ${category}`);
  }
};

// Dynamic configuration generator based on existing data
export const generateConfigFromData = (category: string): PaperOptionsConfig => {
  const papers = paperDataUtils.getPapersByCategory(category);
  const sources = [...new Set(papers.map(p => p.source))];
  const grainDirections = [...new Set(papers.map(p => p.grainDirection))];

  // Calculate average values for defaults
  const avgGsm = papers.length > 0 ? Math.round(papers.reduce((sum, p) => sum + p.gsm, 0) / papers.length) : 80;
  const avgCaliper = papers.length > 0 ? Math.round(papers.reduce((sum, p) => sum + p.caliper, 0) / papers.length) : 100;

  return {
    title: `${category} Paper Options`,
    sources,
    grainDirections,
    additionalFields: {
      costPerTon: {
        label: 'Cost/Ton ($)',
        type: 'number',
        defaultValue: null
      }
    },
    defaultValues: {
      id: '',
      name: '',
      source: sources[0] || 'Pre-Cut',
      sheetHeight: null,
      sheetWidth: 0,
      grainDirection: grainDirections[0] || 'Height',
      caliper: avgCaliper,
      costPerReam: null,
      gsm: avgGsm,
      costPerTon: null
    },
    initialData: papers
  };
};

// Export all configurations
export const paperConfigs = {
  innerText: innerTextConfig,
  cover: coverConfig,
  endpaper: endpaperConfig
};

// Helper function to get all available configurations
export const getAllConfigs = (): { [key: string]: PaperOptionsConfig } => {
  return paperConfigs;
};

// Helper function to get configuration by category name
export const getConfigByCategory = (category: string): PaperOptionsConfig | null => {
  const normalizedCategory = category.toLowerCase().replace(/\s+/g, '');
  
  switch (normalizedCategory) {
    case 'innertext':
      return innerTextConfig;
    case 'cover':
      return coverConfig;
    case 'endpapers':
    case 'endpaper':
      return endpaperConfig;
    default:
      return null;
  }
};