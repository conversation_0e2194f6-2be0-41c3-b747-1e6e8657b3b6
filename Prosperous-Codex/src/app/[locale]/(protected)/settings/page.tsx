"use client";

import { useState, useEffect } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { User, Settings, Shield, Database, Monitor, Clock, Info } from 'lucide-react';

export default function SettingsPage() {
  const { data: session } = useSession();
  const currentUser = session?.user;
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations('settings');
  const tAdmin = useTranslations('admin');
  const [isLoading, setIsLoading] = useState(false);

  // Password change form
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Profile form
  const [profileForm, setProfileForm] = useState({
    username: currentUser?.name || '',
    email: currentUser?.email || ''
  });

  // Sessions state
  const [sessions, setSessions] = useState<any[]>([]);

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast({
        title: "Error",
        description: "New passwords do not match",
        variant: "destructive"
      });
      return;
    }
    
    if (passwordForm.newPassword.length < 6) {
      toast({
        title: "Error", 
        description: "Password must be at least 6 characters long",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/user/change-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        })
      });
      
      const result = await response.json();
      
      if (response.ok) {
        toast({
          title: "Success",
          description: "Password changed successfully"
        });
        setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to change password",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: profileForm.username
        })
      });

      const result = await response.json();

      if (response.ok) {
        // Show informational toast about session refresh behavior
        toast({
          title: "Profile Updated Successfully!",
          description: "Your name change will be visible after your next login session.",
          // Using default variant for informational message
        });

        // Update the form to reflect the change locally
        setProfileForm(prev => ({ ...prev, username: profileForm.username }));
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to update profile",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSessions = async () => {
    try {
      const response = await fetch('/api/user/sessions');
      const data = await response.json();

      if (response.ok) {
        setSessions(data.sessions);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch sessions",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const handleInvalidateAllSessions = async () => {
    if (!confirm('Are you sure you want to sign out of all devices? You will need to log in again.')) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/user/sessions', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId: 'all' })
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: "Success",
          description: "All sessions invalidated. Please log in again."
        });
        // Redirect to login since all sessions are invalidated
        window.location.href = '/auth/login';
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to invalidate sessions",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!currentUser) {
    return null;
  }

  return (
    <div className="container max-w-4xl mx-auto py-6 space-y-6">
      <div className="flex items-center gap-3">
        <Settings className="h-6 w-6 text-[#5E6AD2]" />
        <h1 className="text-2xl font-bold">{t('title')}</h1>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            {t('profile.title')}
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Security
          </TabsTrigger>
          <TabsTrigger value="sessions" className="flex items-center gap-2">
            <Monitor className="h-4 w-4" />
            Sessions
          </TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          {(currentUser.role === 'admin' || currentUser.role === 'moderator') && (
            <TabsTrigger value="admin" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              {t('admin.title')}
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>{t('profile.title')} Information</CardTitle>
              <CardDescription>
                Update your profile information and preferences.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleProfileUpdate} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">{t('profile.email')}</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profileForm.email}
                    disabled
                    className="bg-gray-50 dark:bg-gray-800"
                  />
                  <p className="text-sm text-muted-foreground">
                    Email cannot be changed. Contact an administrator if needed.
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="username">{t('profile.name')}</Label>
                  <Input
                    id="username"
                    value={profileForm.username}
                    onChange={(e) => setProfileForm(prev => ({ ...prev, username: e.target.value }))}
                    placeholder="Enter your username"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>{t('profile.role')}</Label>
                  <Input
                    value={currentUser.role || 'user'}
                    disabled
                    className="bg-gray-50 dark:bg-gray-800 capitalize"
                  />
                </div>
                
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? 'Updating...' : t('profile.save')}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>
                Update your password to keep your account secure.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordChange} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input
                    id="currentPassword"
                    type="password"
                    value={passwordForm.currentPassword}
                    onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    value={passwordForm.newPassword}
                    onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                    required
                    minLength={6}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={passwordForm.confirmPassword}
                    onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    required
                    minLength={6}
                  />
                </div>
                
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? 'Changing...' : 'Change Password'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sessions">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                Active Sessions
              </CardTitle>
              <CardDescription>
                Manage your active login sessions across different devices.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Session Management</h4>
                    <p className="text-sm text-muted-foreground">
                      View and manage your active sessions across all devices
                    </p>
                  </div>
                  <Button
                    onClick={fetchSessions}
                    variant="outline"
                    size="sm"
                  >
                    Refresh Sessions
                  </Button>
                </div>

                {sessions.length > 0 ? (
                  <div className="space-y-3">
                    {sessions.map((session, index) => (
                      <div key={session.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Monitor className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="text-sm font-medium">
                              Session {index + 1}
                            </div>
                            <div className="text-xs text-muted-foreground flex items-center gap-2">
                              <Clock className="h-3 w-3" />
                              Created: {new Date(session.createdAt).toLocaleDateString()}
                              • Expires: {new Date(session.expiresAt).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Monitor className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No active sessions found</p>
                    <Button
                      onClick={fetchSessions}
                      variant="outline"
                      size="sm"
                      className="mt-2"
                    >
                      Load Sessions
                    </Button>
                  </div>
                )}

                <div className="pt-4 border-t">
                  <div className="space-y-2">
                    <h4 className="font-medium text-red-600">Danger Zone</h4>
                    <p className="text-sm text-muted-foreground">
                      Sign out of all devices. You will need to log in again on all devices.
                    </p>
                    <Button
                      onClick={handleInvalidateAllSessions}
                      variant="destructive"
                      size="sm"
                      disabled={isLoading}
                    >
                      {isLoading ? 'Signing Out...' : 'Sign Out All Devices'}
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle>Application Preferences</CardTitle>
              <CardDescription>
                Customize your application experience.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Preference settings will be implemented in future updates.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        {(currentUser.role === 'admin' || currentUser.role === 'moderator') && (
          <TabsContent value="admin">
            <Card>
              <CardHeader>
                <CardTitle>Administration</CardTitle>
                <CardDescription>
                  Administrative functions and system management.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button
                    onClick={() => router.push('/admin')}
                    variant="outline"
                  >
                    {t('admin.access')}
                  </Button>
                  <p className="text-sm text-muted-foreground">
                    Access user management, system settings, and administrative tools.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
