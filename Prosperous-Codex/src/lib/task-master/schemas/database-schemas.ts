/**
 * Database Schemas
 * 
 * Schemas for database operations and validation
 */

import { z } from 'zod';

/**
 * Database connection schema
 */
export const DatabaseConfigSchema = z.object({
  filename: z.string(),
  readonly: z.boolean().default(false),
  fileMustExist: z.boolean().default(false),
  timeout: z.number().default(5000),
  verbose: z.function().optional(),
});

/**
 * Database transaction options
 */
export const TransactionOptionsSchema = z.object({
  immediate: z.boolean().default(false),
  deferred: z.boolean().default(false),
  exclusive: z.boolean().default(false),
});

/**
 * Database query result schema
 */
export const QueryResultSchema = z.object({
  changes: z.number(),
  lastInsertRowid: z.number().optional(),
});

/**
 * Database backup schema
 */
export const BackupOptionsSchema = z.object({
  filename: z.string(),
  progress: z.function().optional(),
  attached: z.string().optional(),
});

/**
 * Database migration schema
 */
export const MigrationSchema = z.object({
  id: z.string(),
  name: z.string(),
  up: z.string(),
  down: z.string(),
  timestamp: z.string().datetime(),
  checksum: z.string().optional(),
});

/**
 * Database index schema
 */
export const IndexSchema = z.object({
  name: z.string(),
  table: z.string(),
  columns: z.array(z.string()),
  unique: z.boolean().default(false),
  partial: z.string().optional(),
});

/**
 * Database constraint schema
 */
export const ConstraintSchema = z.object({
  name: z.string(),
  type: z.enum(['PRIMARY KEY', 'FOREIGN KEY', 'UNIQUE', 'CHECK', 'NOT NULL']),
  columns: z.array(z.string()),
  references: z.object({
    table: z.string(),
    columns: z.array(z.string()),
    onDelete: z.enum(['CASCADE', 'SET NULL', 'RESTRICT', 'NO ACTION']).optional(),
    onUpdate: z.enum(['CASCADE', 'SET NULL', 'RESTRICT', 'NO ACTION']).optional(),
  }).optional(),
  expression: z.string().optional(),
});

/**
 * Database table schema
 */
export const TableSchema = z.object({
  name: z.string(),
  columns: z.array(z.object({
    name: z.string(),
    type: z.string(),
    nullable: z.boolean(),
    defaultValue: z.any(),
    primaryKey: z.boolean(),
    autoIncrement: z.boolean().optional(),
  })),
  constraints: z.array(ConstraintSchema).optional(),
  indexes: z.array(IndexSchema).optional(),
});

/**
 * Database schema validation
 */
export const DatabaseSchemaSchema = z.object({
  version: z.string(),
  tables: z.array(TableSchema),
  views: z.array(z.object({
    name: z.string(),
    definition: z.string(),
  })).optional(),
  triggers: z.array(z.object({
    name: z.string(),
    table: z.string(),
    event: z.enum(['INSERT', 'UPDATE', 'DELETE']),
    timing: z.enum(['BEFORE', 'AFTER', 'INSTEAD OF']),
    definition: z.string(),
  })).optional(),
});

/**
 * Database performance metrics
 */
export const PerformanceMetricsSchema = z.object({
  queryCount: z.number(),
  averageQueryTime: z.number(),
  slowQueries: z.array(z.object({
    sql: z.string(),
    duration: z.number(),
    timestamp: z.string().datetime(),
  })),
  cacheHitRatio: z.number().min(0).max(1),
  connectionCount: z.number(),
});

/**
 * Database health check
 */
export const DatabaseHealthSchema = z.object({
  connected: z.boolean(),
  version: z.string(),
  size: z.number(),
  lastBackup: z.string().datetime().optional(),
  integrityCheck: z.boolean(),
  performance: PerformanceMetricsSchema.optional(),
});

export type DatabaseConfig = z.infer<typeof DatabaseConfigSchema>;
export type TransactionOptions = z.infer<typeof TransactionOptionsSchema>;
export type QueryResult = z.infer<typeof QueryResultSchema>;
export type BackupOptions = z.infer<typeof BackupOptionsSchema>;
export type Migration = z.infer<typeof MigrationSchema>;
export type Index = z.infer<typeof IndexSchema>;
export type Constraint = z.infer<typeof ConstraintSchema>;
export type Table = z.infer<typeof TableSchema>;
export type DatabaseSchema = z.infer<typeof DatabaseSchemaSchema>;
export type PerformanceMetrics = z.infer<typeof PerformanceMetricsSchema>;
export type DatabaseHealth = z.infer<typeof DatabaseHealthSchema>;
