import React from 'react';

export type TaskStatus = 'todo' | 'inProgress' | 'completed';

export type TaskPriority = 'low' | 'medium' | 'high';

// New hierarchical task structure
export interface Task {
  id: number;
  project_id: number;
  parent_task_id?: number;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  due_date?: string;
  completed_date?: string;
  assigned_to?: number;
  created_by: number;
  created_at: string;
  updated_at: string;
  assigned_to_username?: string;
  created_by_username?: string;
  subtasks?: Task[];
}

export interface TaskMaster {
  id: string;
  title: string;
  description: string;
  status: TaskStatus;
  priority: TaskPriority;
  progress: number;
  dueDate?: string;
  completedDate?: string;
  visibility: 'public' | 'private';
  assignee?: {
    name: string;
    avatar: string;
  };
  details?: string;
  // Creator information for ownership validation
  createdBy: number;
  createdByUsername?: string;
  // Extended project flow board data
  projectDetails?: {
    fullDescription: string;
    tags: string[];
    teamMembers: TeamMember[];
    files: ProjectFile[];
    comments: ProjectComment[];
    eventLog: EventLogEntry[];
  };
  // Hierarchical tasks
  tasks?: Task[];
}

export interface TeamMember {
  id: number;
  userId: number;
  username: string;
  email: string;
  role: 'owner' | 'admin' | 'member';
  joinedAt: string;
}

export interface ProjectFile {
  id: number;
  project_id: number;
  file_name: string;
  file_type?: string;
  file_size?: number;
  file_path: string;
  thumbnail_path?: string;
  uploaded_by: number;
  uploaded_at: string;
  uploaded_by_username?: string;
}

export interface ProjectComment {
  id: string;
  author: TeamMember;
  content: string;
  createdAt: string;
  replies?: ProjectComment[];
}

export interface EventLogEntry {
  id: string;
  type: 'upload' | 'comment' | 'status_change' | 'assignment' | 'due_date' | 'completion';
  description: string;
  author: TeamMember;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface TaskMasterColumn {
  id: TaskStatus;
  title: string;
  icon: string; // Icon name as string
  variant: 'neutral' | 'warning' | 'success' | 'brand';
  count: number;
  tasks: TaskMaster[];
}

// Project Flow Board drawer props
export interface ProjectFlowBoardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task: TaskMaster | null;
  onProjectDetailsModalOpen?: (description: string) => void;
  projectDescription?: string;
  onProjectDescriptionChange?: (description: string) => void;
  onCreateTask?: (projectId: string, taskData: {
    title: string;
    description?: string;
    parent_task_id?: number;
  }) => Promise<void>;
  onUpdateTask?: (taskId: number, updateData: {
    status?: 'todo' | 'inProgress' | 'completed';
    title?: string;
    description?: string;
  }) => Promise<void>;
  onDeleteTask?: (taskId: number) => Promise<void>;
  onFileUploaded?: () => void;
  onFileDeleted?: (fileId: number) => void;
  onTeamUpdated?: () => void;
  onCommentAdded?: () => void;
  onOptimisticDescriptionUpdate?: React.MutableRefObject<((description: string) => void) | null>;
  onOptimisticStatusUpdate?: (taskId: string, newStatus: string) => Promise<void>;
  currentUserId?: string;
  // Parsing functionality
  onParseContent?: React.MutableRefObject<((content: string) => Promise<void>) | null>;
  isParsing?: boolean;
  parseProgress?: string;
  // Workspace refresh after successful submissions
  onWorkspaceRefresh?: () => Promise<void>;
}
