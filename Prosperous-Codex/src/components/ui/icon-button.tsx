"use client"

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const iconButtonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
        destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90",
        outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
        secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
        "brand-primary": "bg-[#5E6AD2] text-white shadow-xs hover:bg-[#5E6AD2]/90 dark:bg-[#6E56CF] dark:hover:bg-[#9E8CFC]",
        "brand-secondary": "bg-[#5E6AD2]/10 text-[#5E6AD2] border border-[#5E6AD2]/20 shadow-xs hover:bg-[#5E6AD2]/20 dark:bg-[#6E56CF]/20 dark:text-[#9E8CFC] dark:border-[#6E56CF]/30 dark:hover:bg-[#6E56CF]/30",
        "brand-tertiary": "bg-transparent text-[#5E6AD2] hover:bg-[#5E6AD2]/10 dark:text-[#9E8CFC] dark:hover:bg-[#6E56CF]/20",
        "neutral-primary": "bg-gray-100 text-gray-700 shadow-xs hover:bg-gray-200 dark:bg-neutral-800 dark:text-neutral-300 dark:hover:bg-neutral-700",
        "neutral-secondary": "border border-gray-200 bg-white text-gray-700 shadow-xs hover:bg-gray-50 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-800",
        "neutral-tertiary": "bg-transparent text-gray-700 hover:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800",
        "destructive-tertiary": "bg-transparent text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20",
      },
      size: {
        default: "h-9 w-9",
        sm: "h-8 w-8",
        lg: "h-10 w-10",
        small: "h-7 w-7", // Subframe compatibility
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface IconButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof iconButtonVariants> {
  asChild?: boolean
  icon?: React.ReactNode
}

const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ className, variant, size, asChild = false, icon, children, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    return (
      <Comp
        className={cn(iconButtonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      >
        {icon || children}
      </Comp>
    )
  }
)
IconButton.displayName = "IconButton"

export { IconButton, iconButtonVariants }
