import OpenAI from 'openai';

/**
 * AI Turbo Service
 * 
 * Combines AI enhancement and parsing into a single operation for improved efficiency.
 * Uses the same configuration patterns as existing AI services with enhanced reasoning support.
 */

export interface TurboOptions {
  max_tokens?: number;
  model?: string;
  context?: string;
  tone?: string;
}

export interface TurboMainTask {
  title: string;
  description: string;
  subtasks: TurboSubtask[];
}

export interface TurboSubtask {
  id: string;
  description: string;
}

export interface TurboResult {
  enhanced_text: string;
  original_text: string;
  enhancement_applied: boolean;
  model_used?: string;
  tokens_used?: number;
  reasoning_content?: string;
  // Parsed content
  title: string;
  event_log: string;
  description: string;
  tasks: TurboMainTask[];
}

export class AITurboService {
  private client: OpenAI | null = null;
  private isConfigured: boolean = false;
  private readonly primaryModel = 'google/gemini-2.5-flash-lite-preview-06-17';
  private readonly fallbackModel = 'deepseek/deepseek-chat-v3-0324';

  constructor() {
    this.initializeClient();
  }

  /**
   * Initialize OpenAI-compatible client with environment configuration
   * Supports both OpenAI and OpenRouter APIs
   */
  private initializeClient(): void {
    try {
      // Check for OpenRouter configuration first, then fallback to OpenAI
      const openRouterKey = process.env.OPENROUTER_API_KEY;
      const openAiKey = process.env.OPENAI_API_KEY;

      if (!openRouterKey && !openAiKey) {
        return;
      }

      // Configure for OpenRouter or OpenAI
      if (openRouterKey) {
        this.client = new OpenAI({
          apiKey: openRouterKey,
          baseURL: 'https://openrouter.ai/api/v1',
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2'),
          defaultHeaders: {
            'HTTP-Referer': process.env.OPENROUTER_REFERER || 'http://localhost:3000',
            'X-Title': process.env.OPENROUTER_APP_NAME || 'Prosperous Codex'
          }
        });
      } else {
        this.client = new OpenAI({
          apiKey: openAiKey,
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2')
        });
      }

      this.isConfigured = true;

    } catch (error) {
      this.client = null;
      this.isConfigured = false;
    }
  }

  /**
   * Check if the service is available and properly configured
   */
  public isAvailable(): boolean {
    return this.client !== null && this.isConfigured;
  }

  /**
   * Check if the service is configured (has API key)
   */
  public isServiceConfigured(): boolean {
    return this.isConfigured;
  }

  /**
   * Get the current AI provider being used
   */
  public getProvider(): 'openrouter' | 'openai' | 'none' {
    if (!this.isConfigured) return 'none';
    return process.env.OPENROUTER_API_KEY ? 'openrouter' : 'openai';
  }

  /**
   * Check if a model supports reasoning/thinking capabilities
   * Match the AI Enhancement Service configuration
   */
  private supportsReasoning(model: string): boolean {
    // Models that support reasoning with OpenRouter reasoning parameter
    const reasoningModels = [
      'google/gemini-2.5-flash-lite-preview-06-17', // Primary model with reasoning support
      // Add other reasoning-capable models here as needed
    ];
    return reasoningModels.includes(model);
  }

  /**
   * Get reasoning configuration for models that support it
   * Use the same configuration as AI Enhancement Service
   */
  private getReasoningConfig(model: string) {
    if (this.supportsReasoning(model)) {
      return {
        effort: 'high',
        exclude: false
      };
    }
    return undefined;
  }

  /**
   * Build the combined system prompt for enhancement and parsing
   * Uses the actual working Project Assistant prompt + JSON parsing instructions
   */
  private buildTurboSystemPrompt(): string {
    return `**Prompt: Detailed Project Briefing Assistant**

**Mendatory field:** Your entire reasoning process must be encapsulated within a <thinking>...</thinking> block. This block should contain ONLY your analysis, planning, and self-correction.

You are a bilingual Project Management Assistant for Prosperous Printing Company Limited. Your primary role is to create a clear, detailed, and comprehensive briefing document for the team based on the inputs provided. Your role includes analyzing the language composition of the input; if the text contains more than 33% Chinese characters, your entire response must be in Traditional Chinese. Otherwise, your entire response must be in English.

**Core Function:** To process event logs and email threads into a detailed briefing document that provides a comprehensive history, a thorough description of the current situation, and a clear list of required actions.

**Guiding Principles:**

**Be Factual:** Base all output strictly on the provided input. Do not invent events, details, stories, or context that cannot be found in the '--- EVENT LOG ---' or '--- EMAIL THREAD ---'.

**Handle Vague Inputs:** If the user input is very brief or lacks context (e.g., just a project title like "create an annual report"), do not hallucinate a detailed narrative. Instead, state that more information is needed and provide a basic, high-level template for the user to fill out.

**Keep Responses Succinct:** When context is limited, the response should be equally limited. Include only relevant, requested information that can be directly sourced from the input.

**Language Detection:** Detect the primary language used in the user's input. Your entire response, including all headings, titles, and generated content, must be in that detected language (Traditional Chinese or English).

**Terminology:** When generating output in Traditional Chinese, you must use the corresponding terms provided in the guide below.

Language & Terminology Guide:
| English Term | Traditional Chinese Term |
| :--- | :--- |
| Subject | 主題 |
| Project | 項目 |
| Project Briefing | 項目簡報 |
| Event Log | 事件日誌 |
| Job Description & Current Status | 工作描述與目前狀態 |
| Task Assignment | 任務分配 |
| Key Focus | 核心重點 |
| Event Background | 事件背景 |
| Core Problem | 核心問題 |
| Top Priority | 首要任務 |
| Task | 任務 |
| Description | 描述 |
| Subtasks | 分支任務 |
| Company | 公司 |
| Client | 客戶 |
| Supplier | 供應商 |

---
Input Structure (User Provides AFTER This Prompt)
(Note: The '---EMAILTHREAD---' (which could be an email, a report, or any block of text) provides the immediate context and is the primary source for the current event log. The '---EVENTLOG---' is optional and provides broader historical context. Your role is to synthesize information given.)

'---EVENTLOG---'
[Previous log entries or blank/None]
'---EMAILTHREAD---'
[Full chronological email exchange or other user-provided text/reports]

---

**Output Structure**
Subject: Project Briefing: [Project Name] // [Client Name]

IMPORTANT: The section markers use CamelCase (e.g., ---EventLog---, ---EndOfEventLog---) to signify they are fixed, non-translatable delimiters for automated parsing. They MUST be output exactly as written.

---EventLog---
Event Log
- Ensure there is one blank line between the section title ("Event Log") and the start of the content.
- Read the '---EMAILTHREAD---' and the input '---EVENTLOG---'.
- Identify any new material events from the emails that are not already in the log.
- When creating an concise event summary, the goal is a high-level overview of the action.
- Combine the old log entries with the new entries.
- Output the complete and updated event log below this point in chronological order.
- Each entry must start with a dash (-).
- When determining the [Affiliation], use a concise, common-sense short name (e.g., use 'Prosperous' for 'Prosperous Printing Company Limited'). Avoid using the full legal name.
- DD/MM/YYYY - [Sender Name] ([Affiliation]): [Concise summary of past event.]
...
- End Event Log field with (---EndOfEventLog---), please ensure there is one blank line between the end of content and the section ender.

---EndOfEventLog---

---Description---

- Choose a suitable header/title for this description section from the following options: 'Review', 'Profile', 'Detail', 'Depiction', 'Explanation', 'Narrative', or 'Description', based on what best fits the content.
- Ensure there is one blank line between the section header/title and the start of the narrative text for proper spacing.
- Synthesize all user-provided input (including the Email Thread, Event Log, and any other context) into a detailed narrative, adhering to the Guiding Principles above.
- Primary goal: Maximize clarity and detail, but only when supported by the input data, DO NOT fabricate details.
- **Use paragraphs** for the main narrative, but whenever listing multiple issues, requirements, or key data points, break them out into an indented bulleted list using - to improve readability.

- Narrative must cover:
  - Project's overall goal
  - Summary of how the situation evolved
  - Detailed explanation of the core problem/trigger
  - Clear statement of the immediate objective
- End Description field with (---EndofDescription---),please ensure there is one blank line between the end of content and the section ender.
---EndofDescription---

---TaskAssignment---
Task Assignment
- Ensure there is one blank line between the section title ("Task Assignment") and the start of the content.
For each task:
- The Task Title must be a concise, high-level summary. It should convey the main goal without going into the detailed context.
- **Do NOT assign tasks to anyone**
- Use the indented structure below to ensure clear visual hierarchy. Subtasks should be offset from their parent task to improve structural clarity.
- Write a brief Description for each main task (narrative explaining goal, importance, and deadline)
- List subtasks using Task.Subtask numbering (e.g., 1.1, 1.2, 2.1)
- Provide a brief Description for each subtask (explaining specific action required)
- Use no descriptions **only** for extremely simple, self explanatory.

» Task 1: [Action-focused Task Name]
- Description: [Concise narrative explaining objective, importance, and deadline. E.g., "The objective of this task is to... This is important because... "]
  - Subtasks:
    - 1.1: [Subtask Title]
      - Description: [Concise narrative explaining specific action]
    - 1.2: [Subtask Title]
       - Description: [Concise narrative explaining specific action]

[Repeat structure for additional tasks]
- End Task Assignment field with (---EndOfTaskAssignment---), please ensure there is one blank line between the end of content and the section ender.
---EndOfTaskAssignment---

**TURBO MODE ADDITION:**
After generating the complete briefing document above, add the following separator on a new line:
---JSON_SEPARATOR---

Then provide a JSON object that extracts the structured data from your generated briefing:
{
  "title": "Extract from Subject line",
  "eventLog": "Extract content between ---EventLog--- and ---EndOfEventLog---",
  "description": "Extract content between ---Description--- and ---EndofDescription---",
  "tasks": [
    {
      "title": "Extract task title from » Task X: format",
      "description": "Extract task description",
      "subtasks": [
        {
          "id": "Extract subtask number (e.g., 1.1, 1.2)",
          "description": "Extract subtask description"
        }
      ]
    }
  ]
}`;
  }

  /**
   * Enhance and parse content in a single operation
   */
  public async enhanceAndParse(
    inputText: string,
    options: TurboOptions = {}
  ): Promise<TurboResult> {
    if (!this.isAvailable()) {
      throw new Error('AI Turbo Service is not available. Please check API key configuration.');
    }

    const {
      max_tokens = parseInt(process.env.AI_MAX_TOKENS || '65536')
    } = options;

    const systemPrompt = this.buildTurboSystemPrompt();
    const userPrompt = `--- EVENT LOG ---
None

--- EMAIL THREAD ---
Task Request: ${inputText}`;

    // Try primary model first, then fallback
    let completion;
    let modelUsed = this.primaryModel;

    try {
      // Build request configuration - match AI Enhancement Service exactly
      const requestConfig: any = {
        model: this.primaryModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: max_tokens, // Same as enhancement service: parseInt(process.env.AI_MAX_TOKENS || '65536')
        temperature: 0.65,      // Same as enhancement service
        top_p: 0.65,           // Same as enhancement service
        frequency_penalty: 0.1, // Same as enhancement service
        presence_penalty: 0.1   // Same as enhancement service
      };

      // Add reasoning configuration for models that support it
      const reasoningConfig = this.getReasoningConfig(this.primaryModel);
      if (reasoningConfig) {
        requestConfig.reasoning = reasoningConfig;
      }

      completion = await this.client!.chat.completions.create(requestConfig);
    } catch (primaryError) {
      // Try fallback model
      try {
        modelUsed = this.fallbackModel;
        
        const fallbackConfig: any = {
          model: this.fallbackModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: max_tokens, // Same as enhancement service
          temperature: 0.65,      // Same as enhancement service
          top_p: 0.65,           // Same as enhancement service
          frequency_penalty: 0.1, // Same as enhancement service
          presence_penalty: 0.1   // Same as enhancement service
        };

        const fallbackReasoningConfig = this.getReasoningConfig(this.fallbackModel);
        if (fallbackReasoningConfig) {
          fallbackConfig.reasoning = fallbackReasoningConfig;
        }

        completion = await this.client!.chat.completions.create(fallbackConfig);
      } catch (fallbackError) {
        throw primaryError; // Throw the original error
      }
    }

    try {
      console.log('=== AI TURBO RESPONSE DEBUG ===');
      console.log('Completion object:', !!completion);
      console.log('Choices array:', completion?.choices?.length || 0);
      console.log('First choice:', !!completion?.choices?.[0]);
      console.log('Message object:', !!completion?.choices?.[0]?.message);
      console.log('Content exists:', !!completion?.choices?.[0]?.message?.content);

      const responseText = completion.choices[0]?.message?.content;

      if (!responseText) {
        console.error('No response content found');
        console.error('Full completion object:', JSON.stringify(completion, null, 2));
        throw new Error('No response received from AI service');
      }

      console.log('Response length:', responseText.length);
      console.log('Response preview:', responseText.substring(0, 200));
      console.log('=== END DEBUG ===');

      // Parse the combined response for full Turbo Mode functionality
      return await this.parseTurboResponse(responseText, inputText, modelUsed, completion.usage?.total_tokens);

    } catch (error) {
      console.error('=== AI TURBO ERROR DEBUG ===');
      console.error('Error type:', error?.constructor?.name);
      console.error('Error message:', error instanceof Error ? error.message : String(error));
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack');
      console.error('=== END ERROR DEBUG ===');

      if (error instanceof OpenAI.APIError) {
        console.error('OpenAI API Error Details:', {
          status: error.status,
          message: error.message,
          type: error.type,
          code: error.code
        });

        if (error.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        if (error.status === 401) {
          throw new Error('API key authentication failed');
        }
        if (error.status === 400 && error.message.includes('content_filter')) {
          throw new Error('Content cannot be processed due to safety guidelines');
        }
      }

      throw new Error(`AI turbo processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract thinking content from response text
   */
  private extractThinkingContent(responseText: string): string | undefined {
    const thinkingMatch = responseText.match(/<thinking>([\s\S]*?)<\/thinking>/);
    return thinkingMatch ? thinkingMatch[1].trim() : undefined;
  }

  /**
   * Parse the combined turbo response into structured result
   * Handles the new structured format with section markers
   */
  private async parseTurboResponse(
    responseText: string,
    originalText: string,
    modelUsed: string,
    tokensUsed?: number
  ): Promise<TurboResult> {
    // Extract thinking content
    const thinkingMatch = responseText.match(/<thinking>([\s\S]*?)<\/thinking>/);
    const reasoningContent = thinkingMatch ? thinkingMatch[1].trim() : undefined;

    // Extract enhanced text (everything between </thinking> and ---JSON_SEPARATOR---)
    const afterThinking = responseText.replace(/<thinking>[\s\S]*?<\/thinking>/g, '').trim();
    let enhancedText = '';

    const enhancedMatch = afterThinking.match(/([\s\S]*?)\s*---JSON_SEPARATOR---/i);
    if (enhancedMatch) {
      enhancedText = enhancedMatch[1].trim();
    } else {
      // Fallback: use everything after thinking if no separator found
      enhancedText = afterThinking;
    }

    // Extract and parse JSON content
    let jsonMatch = responseText.match(/---JSON_SEPARATOR---\s*([\s\S]*?)$/i);
    if (!jsonMatch) {
      throw new Error('Could not find ---JSON_SEPARATOR--- in response');
    }

    let jsonContent = jsonMatch[1].trim();
    // Clean up markdown code blocks if present
    jsonContent = jsonContent.replace(/^```json\s*/i, '').replace(/\s*```$/, '').trim();

    let parsedContent;
    try {
      parsedContent = JSON.parse(jsonContent);
    } catch (error) {
      throw new Error(`Invalid JSON in response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Validate and extract fields
    const title = parsedContent.title || 'Untitled Project';
    const eventLog = parsedContent.eventLog || '';
    const description = parsedContent.description || originalText; // Fallback to original if no description
    const tasks = Array.isArray(parsedContent.tasks) ? parsedContent.tasks : [];

    // If we have structured content but empty fields, try to extract from enhanced text
    if ((!eventLog && !description) || tasks.length === 0) {
      // Try to extract from section markers in enhanced text
      const extractedData = this.extractFromSectionMarkers(enhancedText);

      // If tasks are still empty, try using the Parse button's AI service as fallback
      let finalTasks = extractedData.tasks;
      if (finalTasks.length === 0 && enhancedText) {
        finalTasks = await this.fallbackParseWithAIService(enhancedText);
      }

      return {
        enhanced_text: enhancedText,
        original_text: originalText,
        enhancement_applied: true,
        model_used: modelUsed,
        tokens_used: tokensUsed,
        reasoning_content: reasoningContent,
        title: extractedData.title || title,
        event_log: extractedData.eventLog || eventLog,
        description: extractedData.description || description,
        tasks: finalTasks.length > 0 ? finalTasks : tasks
      };
    }

    return {
      enhanced_text: enhancedText,
      original_text: originalText,
      enhancement_applied: true,
      model_used: modelUsed,
      tokens_used: tokensUsed,
      reasoning_content: reasoningContent,
      title: title,
      event_log: eventLog,
      description: description,
      tasks: tasks
    };
  }

  /**
   * Extract structured content from section markers in enhanced text
   */
  private extractFromSectionMarkers(enhancedText: string): {
    title: string;
    eventLog: string;
    description: string;
    tasks: any[];
  } {
    // Extract title from Subject line
    const titleMatch = enhancedText.match(/Subject:\s*(.+)/i);
    const title = titleMatch ? titleMatch[1].trim() : '';

    // Extract event log
    const eventLogMatch = enhancedText.match(/---EventLog---([\s\S]*?)---EndOfEventLog---/i);
    const eventLog = eventLogMatch ? eventLogMatch[1].trim() : '';

    // Extract description
    const descriptionMatch = enhancedText.match(/---Description---([\s\S]*?)---EndOfDescription---/i);
    const description = descriptionMatch ? descriptionMatch[1].trim() : '';

    // Extract and parse tasks
    const taskAssignmentMatch = enhancedText.match(/---TaskAssignment---([\s\S]*?)---EndOfTaskAssignment---/i);
    const tasks = taskAssignmentMatch ? this.parseTaskAssignment(taskAssignmentMatch[1].trim()) : [];

    return { title, eventLog, description, tasks };
  }

  /**
   * Parse task assignment section using the same logic as the proven Parse button
   * This replicates the exact parsing logic from ai-parsing-service.ts
   */
  private parseTaskAssignment(taskText: string): any[] {
    // Use the same parsing logic as the AI Parsing Service
    // Create a temporary input that matches what the Parse button expects
    const tempInput = `---TaskAssignment---\n${taskText}\n---EndOfTaskAssignment---`;

    // Parse using the same pattern matching as the Parse button
    return this.parseTasksLikeParseButton(tempInput);
  }

  /**
   * Parse tasks using the exact format from the Project Assistant prompt
   * Handles the » Task X: format and numbered subtasks
   */
  private parseTasksLikeParseButton(inputText: string): any[] {
    const tasks: any[] = [];

    // Extract task assignment section
    const taskMatch = inputText.match(/---TaskAssignment---([\s\S]*?)---EndOfTaskAssignment---/i);
    if (!taskMatch) {
      return tasks;
    }

    const taskContent = taskMatch[1].trim();
    const lines = taskContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

    let currentTask: any = null;
    let inSubtasks = false;

    for (const line of lines) {
      // Skip section headers
      if (line === 'Task Assignment') {
        continue;
      }

      // Check for main task pattern: » Task X: [Title]
      const mainTaskMatch = line.match(/^»\s*Task\s*(\d+):\s*(.+)$/i);
      if (mainTaskMatch) {
        // Save previous task if exists
        if (currentTask) {
          tasks.push(currentTask);
        }

        // Start new main task
        currentTask = {
          title: line, // Keep the full line including »
          description: '', // Will be filled from Description line
          subtasks: []
        };
        inSubtasks = false;
        continue;
      }

      // Check for task description line
      if (line.startsWith('- Description:') && currentTask) {
        currentTask.description = line.replace(/^- Description:\s*/, '');
        continue;
      }

      // Check for subtasks section
      if (line.includes('Subtasks:')) {
        inSubtasks = true;
        continue;
      }

      // Check for subtask pattern: - X.Y: [Title]
      const subtaskMatch = line.match(/^-\s*(\d+\.\d+):\s*(.+)$/);
      if (subtaskMatch && currentTask) {
        const subtaskId = subtaskMatch[1];
        const subtaskTitle = subtaskMatch[2];

        currentTask.subtasks.push({
          id: subtaskId,
          description: subtaskTitle
        });
        continue;
      }

      // Check for subtask description line
      const subtaskDescMatch = line.match(/^-\s*Description:\s*(.+)$/);
      if (subtaskDescMatch && currentTask && currentTask.subtasks.length > 0) {
        // Update the last subtask's description
        const lastSubtask = currentTask.subtasks[currentTask.subtasks.length - 1];
        lastSubtask.description = subtaskDescMatch[1];
        continue;
      }

      // If we're in a task but haven't found a description yet, use this line
      if (currentTask && !currentTask.description && !inSubtasks && line.startsWith('-')) {
        currentTask.description = line.replace(/^-\s*/, '');
      }
    }

    // Add the last task
    if (currentTask) {
      tasks.push(currentTask);
    }

    return tasks;
  }

  /**
   * Fallback method: Use the Parse button's AI service to parse tasks
   * This ensures we get the exact same task parsing as the proven Parse button
   */
  private async fallbackParseWithAIService(enhancedText: string): Promise<any[]> {
    try {
      // Import the AI Parsing Service
      const { aiParsingService } = await import('@/lib/services/ai-parsing-service');

      if (!aiParsingService.isAvailable()) {
        console.log('AI Parsing Service not available for fallback');
        return [];
      }

      // Use the Parse button's service to parse the enhanced text
      const parsedContent = await aiParsingService.parseStructuredContent(enhancedText);
      return parsedContent.tasks || [];

    } catch (error) {
      return [];
    }
  }
}
