/**
 * API Contract Tests for Task Master
 * 
 * Tests field name consistency between frontend and backend,
 * validates API request/response structures, and ensures
 * proper field mapping between camelCase and snake_case.
 */

import { describe, it, expect, beforeAll } from '@jest/globals';
import { ApiValidator, validateApiContract } from '@/lib/task-master/api-validator';
import { FieldMapper } from '@/lib/task-master/field-mapping';
import { TypeSafetyEnforcer } from '@/lib/task-master/type-safety-enforcer';
import { ValidationSchemas, TypeGuards } from '@/lib/task-master/validation-types';

describe('API Contract Tests', () => {
  describe('Field Name Consistency', () => {
    it('should validate project field mappings', () => {
      const apiFields = [
        'id', 'title', 'description', 'fullDescription', 'eventLog',
        'status', 'priority', 'progress', 'dueDate', 'completedDate',
        'createdBy', 'assignedTo', 'createdAt', 'updatedAt'
      ];
      
      const dbFields = [
        'id', 'title', 'description', 'full_description', 'event_log',
        'status', 'priority', 'progress', 'due_date', 'completed_date',
        'created_by', 'assigned_to', 'created_at', 'updated_at'
      ];

      const validation = FieldMapper.validateConsistency(apiFields, dbFields);
      
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should validate task field mappings', () => {
      const apiFields = [
        'id', 'title', 'description', 'status', 'priority', 'progress',
        'dueDate', 'completedDate', 'projectId', 'parentTaskId',
        'createdBy', 'assignedTo', 'createdAt', 'updatedAt'
      ];
      
      const dbFields = [
        'id', 'title', 'description', 'status', 'priority', 'progress',
        'due_date', 'completed_date', 'project_id', 'parent_task_id',
        'created_by', 'assigned_to', 'created_at', 'updated_at'
      ];

      const validation = FieldMapper.validateConsistency(apiFields, dbFields);
      
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect missing field mappings', () => {
      const apiFields = ['newCamelCaseField', 'anotherField'];
      const dbFields = ['existing_field'];

      const validation = FieldMapper.validateConsistency(apiFields, dbFields);
      
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.suggestions).toHaveProperty('newCamelCaseField');
    });

    it('should suggest correct field name conversions', () => {
      const apiField = 'fullDescription';
      const dbField = FieldMapper.getDbFieldName(apiField);
      
      expect(dbField).toBe('full_description');
      
      const reverseApiField = FieldMapper.getApiFieldName(dbField);
      expect(reverseApiField).toBe('fullDescription');
    });
  });

  describe('API Endpoint Validation', () => {
    it('should validate project creation request fields', () => {
      const requestData = {
        title: 'Test Project',
        description: 'Test description',
        fullDescription: 'Full test description',
        eventLog: 'Initial event',
        status: 'todo',
        priority: 'medium',
        dueDate: '2024-12-31T23:59:59Z',
        assignedTo: 1
      };

      const result = ApiValidator.validateRequestFields(
        '/api/task-master/projects',
        requestData
      );

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate project response fields', () => {
      const responseData = {
        id: 1,
        title: 'Test Project',
        description: 'Test description',
        fullDescription: 'Full test description',
        eventLog: 'Initial event',
        status: 'todo',
        priority: 'medium',
        progress: 0,
        dueDate: '2024-12-31T23:59:59Z',
        createdBy: 1,
        assignedTo: 1,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      };

      const result = ApiValidator.validateResponseFields(
        '/api/task-master/projects/1',
        responseData
      );

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid request fields', () => {
      const requestData = {
        title: 'Test Project',
        invalid_snake_case_field: 'should not be here',
        anotherInvalidField: 'also invalid'
      };

      const result = ApiValidator.validateRequestFields(
        '/api/task-master/projects',
        requestData,
        'POST'
      );

      expect(result.valid).toBe(false);
      expect(result.warnings.length).toBeGreaterThan(0);
    });

    it('should validate task creation with proper field mapping', () => {
      const requestData = {
        title: 'Test Task',
        description: 'Test task description',
        status: 'todo',
        priority: 'high',
        dueDate: '2024-12-31T23:59:59Z',
        assignedTo: 2,
        parentTaskId: 1
      };

      const result = ApiValidator.validateRequestFields(
        '/api/task-master/projects/1/tasks',
        requestData
      );

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('Type Safety Enforcement', () => {
    let enforcer: TypeSafetyEnforcer;

    beforeAll(() => {
      enforcer = new TypeSafetyEnforcer({
        strictMode: true,
        enforceFieldNaming: true,
        validateAtRuntime: true,
        throwOnMismatch: false, // Don't throw in tests
        logWarnings: false
      });
    });

    it('should enforce API to DB field mapping for projects', () => {
      const apiData = {
        title: 'Test Project',
        fullDescription: 'Full description',
        eventLog: 'Event log content',
        dueDate: '2024-12-31T23:59:59Z',
        assignedTo: 1
      };

      const result = enforcer.enforceApiToDb(apiData, 'project');

      expect(result.valid).toBe(true);
      expect(result.correctedData).toHaveProperty('full_description');
      expect(result.correctedData).toHaveProperty('event_log');
      expect(result.correctedData).toHaveProperty('due_date');
      expect(result.correctedData).toHaveProperty('assigned_to');
      expect(result.metadata.correctionsMade).toBeGreaterThan(0);
    });

    it('should enforce DB to API field mapping for tasks', () => {
      const dbData = {
        id: 1,
        title: 'Test Task',
        project_id: 1,
        parent_task_id: 2,
        due_date: '2024-12-31T23:59:59Z',
        created_by: 1,
        assigned_to: 2,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const result = enforcer.enforceDbToApi(dbData, 'task');

      expect(result.valid).toBe(true);
      expect(result.correctedData).toHaveProperty('projectId');
      expect(result.correctedData).toHaveProperty('parentTaskId');
      expect(result.correctedData).toHaveProperty('dueDate');
      expect(result.correctedData).toHaveProperty('createdBy');
      expect(result.correctedData).toHaveProperty('assignedTo');
      expect(result.correctedData).toHaveProperty('createdAt');
      expect(result.correctedData).toHaveProperty('updatedAt');
    });

    it('should detect field naming violations', () => {
      const invalidApiData = {
        title: 'Test',
        invalid_snake_case: 'should be camelCase',
        'another-invalid': 'kebab case not allowed'
      };

      const result = enforcer.enforceApiToDb(invalidApiData, 'project');

      expect(result.valid).toBe(false);
      expect(result.violations.some(v => v.type === 'field_naming')).toBe(true);
    });
  });

  describe('Runtime Type Validation', () => {
    it('should validate project API fields with Zod schema', () => {
      const validProjectData = {
        id: 1,
        title: 'Valid Project',
        description: 'Valid description',
        status: 'todo',
        priority: 'medium',
        progress: 50,
        createdBy: 1,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      };

      const result = ValidationSchemas.projectApiFields.safeParse(validProjectData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid project data', () => {
      const invalidProjectData = {
        id: 'not-a-number',
        title: '',
        status: 'invalid-status',
        priority: 'invalid-priority',
        progress: 150 // Over 100
      };

      const result = ValidationSchemas.projectApiFields.safeParse(invalidProjectData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors.length).toBeGreaterThan(0);
      }
    });

    it('should validate task DB fields with Zod schema', () => {
      const validTaskData = {
        id: 1,
        title: 'Valid Task',
        status: 'in_progress',
        priority: 'high',
        progress: 75,
        project_id: 1,
        created_by: 1,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const result = ValidationSchemas.taskDbFields.safeParse(validTaskData);
      expect(result.success).toBe(true);
    });

    it('should use type guards correctly', () => {
      const projectApiData = {
        id: 1,
        title: 'Test Project',
        status: 'todo',
        priority: 'medium',
        progress: 0,
        createdBy: 1,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      };

      expect(TypeGuards.isApiProjectFields(projectApiData)).toBe(true);
      expect(TypeGuards.isDbProjectFields(projectApiData)).toBe(false);
    });
  });

  describe('Cross-Entity Consistency', () => {
    it('should maintain consistent field naming across entities', () => {
      const commonFields = ['createdBy', 'assignedTo', 'createdAt', 'updatedAt', 'dueDate'];
      const commonDbFields = ['created_by', 'assigned_to', 'created_at', 'updated_at', 'due_date'];

      for (let i = 0; i < commonFields.length; i++) {
        const apiField = commonFields[i];
        const expectedDbField = commonDbFields[i];
        const actualDbField = FieldMapper.getDbFieldName(apiField);
        
        expect(actualDbField).toBe(expectedDbField);
      }
    });

    it('should validate relationship field consistency', () => {
      // Test that foreign key fields are consistently named
      const projectIdMapping = FieldMapper.getDbFieldName('projectId');
      expect(projectIdMapping).toBe('project_id');

      const parentTaskIdMapping = FieldMapper.getDbFieldName('parentTaskId');
      expect(parentTaskIdMapping).toBe('parent_task_id');

      const userIdMapping = FieldMapper.getDbFieldName('userId');
      expect(userIdMapping).toBe('user_id');
    });
  });

  describe('Integration with API Routes', () => {
    it('should generate comprehensive contract tests', () => {
      const testSuite = ApiValidator.generateContractTests();
      
      expect(testSuite.name).toBe('Task Master API Contract Tests');
      expect(testSuite.tests.length).toBeGreaterThan(0);
      expect(testSuite.setup.length).toBeGreaterThan(0);
      
      // Check that tests cover major endpoints
      const testNames = testSuite.tests.map(t => t.name);
      expect(testNames.some(name => name.includes('projects'))).toBe(true);
      expect(testNames.some(name => name.includes('tasks'))).toBe(true);
      expect(testNames.some(name => name.includes('Request Validation'))).toBe(true);
      expect(testNames.some(name => name.includes('Response Validation'))).toBe(true);
    });

    it('should validate contract for utility function', () => {
      const requestData = {
        title: 'Test',
        description: 'Short description',
        fullDescription: 'Description',
        eventLog: 'Event log',
        status: 'todo',
        priority: 'medium',
        dueDate: '2024-12-31T23:59:59Z',
        assignedTo: 1
      };

      const requestResult = validateApiContract(
        '/api/task-master/projects',
        requestData,
        'request'
      );

      expect(requestResult.valid).toBe(true);

      const responseData = {
        id: 1,
        title: 'Test',
        fullDescription: 'Description',
        createdAt: '2024-01-01T00:00:00Z'
      };

      const responseResult = validateApiContract(
        '/api/task-master/projects/1',
        responseData,
        'response'
      );

      expect(responseResult.valid).toBe(true);
    });
  });

  describe('Error Handling and Suggestions', () => {
    it('should provide helpful suggestions for field name mismatches', () => {
      const apiFields = ['fullDescription', 'eventLog', 'dueDate'];
      const dbFields = ['description', 'log', 'date']; // Intentionally wrong

      const validation = FieldMapper.validateConsistency(apiFields, dbFields);

      expect(validation.valid).toBe(false);
      expect(validation.suggestions).toBeDefined();
      expect(Object.keys(validation.suggestions).length).toBeGreaterThan(0);
    });

    it('should handle unknown endpoints gracefully', () => {
      const result = ApiValidator.validateRequestFields(
        '/api/unknown/endpoint',
        { test: 'data' }
      );

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Unknown endpoint: /api/unknown/endpoint');
    });

    it('should provide detailed violation information', () => {
      const enforcer = new TypeSafetyEnforcer({
        throwOnMismatch: false,
        logWarnings: false
      });

      const invalidData = {
        title: 'Test',
        invalid_field: 'value',
        'another-invalid': 'value'
      };

      const result = enforcer.enforceApiToDb(invalidData, 'project');
      
      expect(result.violations.length).toBeGreaterThan(0);
      
      const violation = result.violations[0];
      expect(violation).toHaveProperty('type');
      expect(violation).toHaveProperty('field');
      expect(violation).toHaveProperty('expected');
      expect(violation).toHaveProperty('actual');
      expect(violation).toHaveProperty('severity');
    });
  });
});
