/**
 * Data Transformation Utilities
 * 
 * Utilities for transforming data between different formats and structures
 */

import { FieldMapper } from '../field-mapping';
import { TypeSafetyEnforcer } from '../type-safety-enforcer';
import type { Request, Response, NextFunction } from 'express';

/**
 * Data transformation options
 */
export interface TransformationOptions {
  direction: 'apiToDb' | 'dbToApi';
  entityType: 'project' | 'task' | 'comment' | 'file' | 'team_member' | 'activity_log';
  validateTypes?: boolean;
  throwOnError?: boolean;
  preserveUnknownFields?: boolean;
}

/**
 * Transformation result
 */
export interface TransformationResult<T = any> {
  success: boolean;
  data?: T;
  errors?: string[];
  warnings?: string[];
  metadata: {
    originalFieldCount: number;
    transformedFieldCount: number;
    mappingsApplied: number;
    unknownFields: string[];
  };
}

/**
 * Data Transformer Class
 */
export class DataTransformer {
  private enforcer: TypeSafetyEnforcer;

  constructor() {
    this.enforcer = new TypeSafetyEnforcer({
      strictMode: false,
      throwOnMismatch: false,
      logWarnings: false,
    });
  }

  /**
   * Transform data between API and database formats
   */
  transform<T = any>(
    data: Record<string, any>,
    options: TransformationOptions
  ): TransformationResult<T> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const unknownFields: string[] = [];
    let transformedData: Record<string, any>;
    let mappingsApplied = 0;

    try {
      if (options.direction === 'apiToDb') {
        // Transform API data to database format
        if (options.validateTypes) {
          const typeResult = this.enforcer.enforceApiToDb(data, options.entityType);
          if (!typeResult.valid) {
            errors.push(...typeResult.violations.map(v => v.suggestion));
          }
          transformedData = typeResult.correctedData || {};
          mappingsApplied = typeResult.metadata.correctionsMade;
        } else {
          transformedData = FieldMapper.apiToDb(data);
          mappingsApplied = this.countMappingsApplied(data, transformedData);
        }
      } else {
        // Transform database data to API format
        if (options.validateTypes) {
          const typeResult = this.enforcer.enforceDbToApi(data, options.entityType);
          if (!typeResult.valid) {
            errors.push(...typeResult.violations.map(v => v.suggestion));
          }
          transformedData = typeResult.correctedData || {};
          mappingsApplied = typeResult.metadata.correctionsMade;
        } else {
          transformedData = FieldMapper.dbToApi(data);
          mappingsApplied = this.countMappingsApplied(data, transformedData);
        }
      }

      // Handle unknown fields
      if (!options.preserveUnknownFields) {
        const knownFields = Object.keys(transformedData);
        unknownFields.push(...Object.keys(data).filter(key => !knownFields.includes(key)));
      } else {
        // Preserve unknown fields as-is
        for (const [key, value] of Object.entries(data)) {
          if (!(key in transformedData)) {
            transformedData[key] = value;
            unknownFields.push(key);
            warnings.push(`Preserved unknown field: ${key}`);
          }
        }
      }

      const result: TransformationResult<T> = {
        success: errors.length === 0,
        data: transformedData as T,
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined,
        metadata: {
          originalFieldCount: Object.keys(data).length,
          transformedFieldCount: Object.keys(transformedData).length,
          mappingsApplied,
          unknownFields,
        },
      };

      if (options.throwOnError && !result.success) {
        throw new Error(`Transformation failed: ${errors.join(', ')}`);
      }

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      if (options.throwOnError) {
        throw error;
      }

      return {
        success: false,
        errors: [errorMessage],
        metadata: {
          originalFieldCount: Object.keys(data).length,
          transformedFieldCount: 0,
          mappingsApplied: 0,
          unknownFields: [],
        },
      };
    }
  }

  /**
   * Batch transform multiple data objects
   */
  transformBatch<T = any>(
    dataArray: Record<string, any>[],
    options: TransformationOptions
  ): {
    success: boolean;
    results: TransformationResult<T>[];
    summary: {
      total: number;
      successful: number;
      failed: number;
      totalMappings: number;
    };
  } {
    const results: TransformationResult<T>[] = [];
    let successful = 0;
    let failed = 0;
    let totalMappings = 0;

    for (const data of dataArray) {
      const result = this.transform<T>(data, options);
      results.push(result);
      
      if (result.success) {
        successful++;
      } else {
        failed++;
      }
      
      totalMappings += result.metadata.mappingsApplied;
    }

    return {
      success: failed === 0,
      results,
      summary: {
        total: dataArray.length,
        successful,
        failed,
        totalMappings,
      },
    };
  }

  /**
   * Transform with custom field mapping
   */
  transformWithMapping<T = any>(
    data: Record<string, any>,
    fieldMapping: Record<string, string>,
    options?: Partial<TransformationOptions>
  ): TransformationResult<T> {
    const transformedData: Record<string, any> = {};
    const errors: string[] = [];
    const warnings: string[] = [];
    const unknownFields: string[] = [];
    let mappingsApplied = 0;

    try {
      // Apply custom mapping
      for (const [sourceField, targetField] of Object.entries(fieldMapping)) {
        if (sourceField in data) {
          transformedData[targetField] = data[sourceField];
          mappingsApplied++;
        }
      }

      // Handle unmapped fields
      for (const [key, value] of Object.entries(data)) {
        if (!(key in fieldMapping)) {
          if (options?.preserveUnknownFields) {
            transformedData[key] = value;
            unknownFields.push(key);
            warnings.push(`Preserved unmapped field: ${key}`);
          } else {
            unknownFields.push(key);
            warnings.push(`Skipped unmapped field: ${key}`);
          }
        }
      }

      return {
        success: errors.length === 0,
        data: transformedData as T,
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined,
        metadata: {
          originalFieldCount: Object.keys(data).length,
          transformedFieldCount: Object.keys(transformedData).length,
          mappingsApplied,
          unknownFields,
        },
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      return {
        success: false,
        errors: [errorMessage],
        metadata: {
          originalFieldCount: Object.keys(data).length,
          transformedFieldCount: 0,
          mappingsApplied: 0,
          unknownFields: [],
        },
      };
    }
  }

  /**
   * Count how many field mappings were applied
   */
  private countMappingsApplied(
    original: Record<string, any>,
    transformed: Record<string, any>
  ): number {
    let count = 0;
    
    for (const originalKey of Object.keys(original)) {
      const transformedKeys = Object.keys(transformed);
      if (!transformedKeys.includes(originalKey)) {
        // Field was mapped to a different name
        count++;
      }
    }
    
    return count;
  }
}

/**
 * Utility functions for common transformations
 */
const TransformationUtils = {
  /**
   * Quick API to DB transformation
   */
  apiToDb: <T = any>(
    data: Record<string, any>,
    entityType: TransformationOptions['entityType']
  ): T => {
    const transformer = new DataTransformer();
    const result = transformer.transform<T>(data, {
      direction: 'apiToDb',
      entityType,
      validateTypes: true,
      throwOnError: true,
    });
    return result.data!;
  },

  /**
   * Quick DB to API transformation
   */
  dbToApi: <T = any>(
    data: Record<string, any>,
    entityType: TransformationOptions['entityType']
  ): T => {
    const transformer = new DataTransformer();
    const result = transformer.transform<T>(data, {
      direction: 'dbToApi',
      entityType,
      validateTypes: true,
      throwOnError: true,
    });
    return result.data!;
  },

  /**
   * Safe transformation that doesn't throw
   */
  safeTransform: <T = any>(
    data: Record<string, any>,
    options: TransformationOptions
  ): { data?: T; error?: string } => {
    const transformer = new DataTransformer();
    const result = transformer.transform<T>(data, {
      ...options,
      throwOnError: false,
    });
    
    if (result.success) {
      return { data: result.data };
    } else {
      return { error: result.errors?.join(', ') || 'Transformation failed' };
    }
  },
};

/**
 * Create a transformation middleware for API routes
 */
export function createTransformationMiddleware(
  options: TransformationOptions
) {
  const transformer = new DataTransformer();

  return function transformationMiddleware(req: Request, res: Response, next: NextFunction) {
    if (req.body && typeof req.body === 'object') {
      const result = transformer.transform(req.body, options);
      
      if (result.success) {
        req.body = result.data;
        if (result.warnings) {
          req.transformationWarnings = result.warnings;
        }
      } else {
        return res.status(400).json({
          error: 'Data transformation failed',
          details: result.errors,
        });
      }
    }

    next();
  };
}

/**
 * Export the main transformer class and utilities
 */
export { DataTransformer as default, TransformationUtils };
