import { renderHook, act } from '@testing-library/react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import { useTaskFilters } from '../use-task-filters';

// Mock Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
  usePathname: jest.fn(),
}));

// Mock the debounce hook
jest.mock('../use-debounce', () => ({
  useDebounce: jest.fn((value) => value),
}));

describe('useTaskFilters', () => {
  const mockReplace = jest.fn();
  const mockSearchParams = new URLSearchParams();
  const mockPathname = '/task-master';

  beforeEach(() => {
    jest.clearAllMocks();
    
    (useRouter as jest.Mock).mockReturnValue({
      replace: mockReplace,
    });
    
    (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
    (usePathname as jest.Mock).mockReturnValue(mockPathname);
  });

  it('initializes with empty filters', () => {
    const { result } = renderHook(() => useTaskFilters());
    
    expect(result.current.filters).toEqual({});
    expect(result.current.activeFilterCount).toBe(0);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('initializes filters from URL parameters', () => {
    const searchParams = new URLSearchParams('priority=high&dueDate=true');
    (useSearchParams as jest.Mock).mockReturnValue(searchParams);
    
    const { result } = renderHook(() => useTaskFilters());
    
    expect(result.current.filters).toEqual({
      priority: 'high',
      dueDate: true,
    });
    expect(result.current.activeFilterCount).toBe(2);
  });

  it('handles invalid URL parameters gracefully', () => {
    const searchParams = new URLSearchParams('priority=invalid&dueDate=maybe');
    (useSearchParams as jest.Mock).mockReturnValue(searchParams);
    
    const { result } = renderHook(() => useTaskFilters());
    
    expect(result.current.filters).toEqual({});
    expect(result.current.activeFilterCount).toBe(0);
  });

  it('sets filters and updates URL', async () => {
    const { result } = renderHook(() => useTaskFilters());
    
    await act(async () => {
      result.current.setFilters({ priority: 'medium', dueDate: true });
    });
    
    expect(result.current.filters).toEqual({
      priority: 'medium',
      dueDate: true,
    });
    
    expect(mockReplace).toHaveBeenCalledWith(
      '/task-master?priority=medium&dueDate=true',
      { scroll: false }
    );
  });

  it('clears all filters', async () => {
    const { result } = renderHook(() => useTaskFilters());
    
    // Set some filters first
    await act(async () => {
      result.current.setFilters({ priority: 'high', completion: true });
    });
    
    // Clear filters
    await act(async () => {
      result.current.clearFilters();
    });
    
    expect(result.current.filters).toEqual({});
    expect(result.current.activeFilterCount).toBe(0);
  });

  it('builds API parameters correctly', () => {
    const { result } = renderHook(() => useTaskFilters());
    
    act(() => {
      result.current.setFilters({
        priority: 'high',
        dueDate: true,
        lastUpdated: true,
      });
    });
    
    const apiParams = result.current.buildApiParams();
    
    expect(apiParams.get('sortBy')).toBe('priority,dueDate,lastUpdated');
    expect(apiParams.get('order')).toBe('asc');
  });

  it('handles empty filters in API parameters', () => {
    const { result } = renderHook(() => useTaskFilters());
    
    const apiParams = result.current.buildApiParams();
    
    expect(apiParams.toString()).toBe('');
  });

  it('sanitizes invalid filter values', async () => {
    const { result } = renderHook(() => useTaskFilters());
    
    await act(async () => {
      // Try to set invalid priority
      result.current.setFilters({ 
        priority: 'invalid' as any,
        dueDate: 'maybe' as any,
      });
    });
    
    expect(result.current.filters).toEqual({});
  });

  it('handles loading states correctly', async () => {
    const { result } = renderHook(() => useTaskFilters());
    
    act(() => {
      result.current.setFilters({ priority: 'high' });
    });
    
    expect(result.current.isLoading).toBe(true);
    
    // Wait for loading to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 200));
    });
    
    expect(result.current.isLoading).toBe(false);
  });

  it('counts active filters correctly', () => {
    const { result } = renderHook(() => useTaskFilters());
    
    act(() => {
      result.current.setFilters({
        priority: 'high',
        dueDate: true,
        lastUpdated: false, // This should not count
        completion: true,
      });
    });
    
    expect(result.current.activeFilterCount).toBe(3);
  });

  it('handles error states', async () => {
    // Mock URL update to throw an error
    const mockError = new Error('URL update failed');
    mockReplace.mockImplementation(() => {
      throw mockError;
    });
    
    const { result } = renderHook(() => useTaskFilters());
    
    await act(async () => {
      result.current.setFilters({ priority: 'high' });
    });
    
    expect(result.current.error).toBe('Failed to apply filters. Please try again.');
    expect(result.current.isLoading).toBe(false);
  });

  it('clears errors', async () => {
    const { result } = renderHook(() => useTaskFilters());
    
    // Set an error state first
    await act(async () => {
      result.current.setFilters({ priority: 'high' });
    });
    
    // Clear the error
    act(() => {
      result.current.clearError();
    });
    
    expect(result.current.error).toBe(null);
  });

  it('prevents unnecessary updates when filters are the same', async () => {
    const { result } = renderHook(() => useTaskFilters());
    
    const initialFilters = { priority: 'high' as const };
    
    await act(async () => {
      result.current.setFilters(initialFilters);
    });
    
    mockReplace.mockClear();
    
    // Set the same filters again
    await act(async () => {
      result.current.setFilters(initialFilters);
    });
    
    // URL should not be updated again
    expect(mockReplace).not.toHaveBeenCalled();
  });
});
