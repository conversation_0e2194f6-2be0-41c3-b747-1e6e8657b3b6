"use client";

import { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UnitToggle } from "@/components/ui/unit-toggle";
import { useUnit } from "@/lib/context/unit-context";
import {
  EndpaperJobSpecificationData,
  TabSpecificCardProps
} from "@/lib/types/ui-components";

export interface EndpaperJobSpecificationsRef {
  getCurrentMmValues: () => EndpaperJobSpecificationData;
}

const EndpaperJobSpecificationsCard = forwardRef<EndpaperJobSpecificationsRef, TabSpecificCardProps<EndpaperJobSpecificationData>>(({
  className = "",
  onDataChange,
  initialData
}, ref) => {
  const { unit, convertMmToDisplayValue, convertDisplayValueToMm } = useUnit();

  // Store display values (what user sees)
  const [endpaperHeightDisplay, setEndpaperHeightDisplay] = useState("");
  const [endpaperWidthDisplay, setEndpaperWidthDisplay] = useState("");
  const [quantity, setQuantity] = useState(initialData?.quantity?.toString() || "1000");
  const [endpaperType, setEndpaperType] = useState<'single' | 'double'>(initialData?.endpaperType || "double");
  const [spoilage, setSpoilage] = useState((initialData?.spoilagePct ? initialData.spoilagePct * 100 : 10).toString());

  // Initialize display values from initial data
  useEffect(() => {
    if (initialData?.trimH) {
      setEndpaperHeightDisplay(convertMmToDisplayValue(initialData.trimH.toString(), unit));
    } else {
      setEndpaperHeightDisplay(convertMmToDisplayValue("225", unit));
    }

    if (initialData?.trimW) {
      setEndpaperWidthDisplay(convertMmToDisplayValue(initialData.trimW.toString(), unit));
    } else {
      setEndpaperWidthDisplay(convertMmToDisplayValue("150", unit));
    }
  }, [initialData, unit, convertMmToDisplayValue]);

  // Convert display values when unit changes (instant conversion on toggle)
  useEffect(() => {
    if (endpaperHeightDisplay) {
      const mmValue = convertDisplayValueToMm(endpaperHeightDisplay, unit === 'mm' ? 'in' : 'mm');
      setEndpaperHeightDisplay(convertMmToDisplayValue(mmValue, unit));
    }
    if (endpaperWidthDisplay) {
      const mmValue = convertDisplayValueToMm(endpaperWidthDisplay, unit === 'mm' ? 'in' : 'mm');
      setEndpaperWidthDisplay(convertMmToDisplayValue(mmValue, unit));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [unit]); // Only depend on unit changes

  // Function to get current mm values for calculation (called externally)
  const getCurrentMmValues = useCallback(() => {
    return {
      trimH: parseFloat(convertDisplayValueToMm(endpaperHeightDisplay, unit)) || 0,
      trimW: parseFloat(convertDisplayValueToMm(endpaperWidthDisplay, unit)) || 0,
      quantity: parseInt(quantity) || 0,
      endpaperType,
      spoilagePct: (parseFloat(spoilage) || 0) / 100
    };
  }, [convertDisplayValueToMm, endpaperHeightDisplay, endpaperWidthDisplay, unit, quantity, endpaperType, spoilage]);

  // Expose getCurrentMmValues function via ref
  useImperativeHandle(ref, () => ({
    getCurrentMmValues
  }));

  // Notify parent component when data changes (for real-time updates of non-dimension fields)
  useEffect(() => {
    if (onDataChange) {
      onDataChange(getCurrentMmValues());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [quantity, endpaperType, spoilage, onDataChange]);

  // Input validation function
  const validateNumberInput = (value: string) => {
    // Allow empty string, numbers, decimal points, and negative signs
    return value === '' || /^-?\d*\.?\d*$/.test(value);
  };

  return (
    <Card className={className} data-glow>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center justify-between w-full">
          <div className="flex items-center">
            <span className="w-2 h-2 bg-[#5E6AD2]/60 dark:bg-[#C4B5FD] rounded-full mr-2"></span>
            Job Specifications
          </div>
          <UnitToggle className="ml-auto" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Endpaper Dimensions Row - Height and Width side by side */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="endpaper-height" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                Height (H)
              </Label>
              <span className="text-xs text-gray-500 dark:text-neutral-400">{unit}</span>
            </div>
            <Input
              id="endpaper-height"
              type="number"
              step={unit === 'mm' ? "0.1" : "0.01"}
              placeholder={unit === 'mm' ? "225" : "8.86"}
              value={endpaperHeightDisplay}
              onChange={(e) => {
                if (validateNumberInput(e.target.value)) {
                  setEndpaperHeightDisplay(e.target.value);
                }
              }}
              className="h-9 text-sm"
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="endpaper-width" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                Width (W)
              </Label>
              <span className="text-xs text-gray-500 dark:text-neutral-400">{unit}</span>
            </div>
            <Input
              id="endpaper-width"
              type="number"
              step={unit === 'mm' ? "0.1" : "0.01"}
              placeholder={unit === 'mm' ? "150" : "5.91"}
              value={endpaperWidthDisplay}
              onChange={(e) => {
                if (validateNumberInput(e.target.value)) {
                  setEndpaperWidthDisplay(e.target.value);
                }
              }}
              className="h-9 text-sm bg-white dark:bg-neutral-700 border-gray-300 dark:border-neutral-600"
              data-glow
            />
            <p className="text-xs text-gray-500 mt-1">Typically same as inner text page width</p>
          </div>
        </div>



        {/* Endpaper Type and Quantity Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="endpaper-type" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
              Endpaper Type
            </Label>
            <Select value={endpaperType} onValueChange={(value: 'single' | 'double') => setEndpaperType(value)}>
              <SelectTrigger className="mt-1 h-9 text-sm" data-glow>
                <SelectValue placeholder="Select endpaper type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="single">Single Leaf (4pp)</SelectItem>
                <SelectItem value="double">Double Leaf (8pp)</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500 mt-1"></p>
          </div>
          <div>
            <Label htmlFor="quantity" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
              Quantity
            </Label>
            <Input
              id="quantity"
              type="number"
              placeholder="1000"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              className="mt-1 h-9 text-sm bg-white dark:bg-neutral-800 border-gray-300 dark:border-neutral-600"
              data-glow
            />
            <p className="text-xs text-gray-500 mt-1">Number of books</p>
          </div>
        </div>

        {/* Spoilage */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="spoilage" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
              Spoilage (%)
            </Label>
            <Input
              id="spoilage"
              type="number"
              placeholder="10"
              value={spoilage}
              onChange={(e) => setSpoilage(e.target.value)}
              className="mt-1 h-9 text-sm"
            />
            <p className="text-xs text-gray-500 mt-1">Typically higher for specialty papers</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

EndpaperJobSpecificationsCard.displayName = 'EndpaperJobSpecificationsCard';

export default EndpaperJobSpecificationsCard;
