#!/usr/bin/env node

/**
 * Database initialization script
 * Run this to set up the database for the first time
 */

const path = require('path');
const fs = require('fs');

// Set up the environment
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

async function initializeDatabase() {
  try {
    console.log('🚀 Initializing Prosperous Codex database...');
    
    // Ensure data directory exists
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log('📁 Created data directory');
    }
    
    // Import and initialize database
    const { initializeApp } = require('../../src/lib/database/init.ts');
    const result = await initializeApp();
    
    if (result.success) {
      console.log('✅ Database initialized successfully!');
      console.log('');
      console.log('Default users created:');
      console.log('  Admin: <EMAIL> / password');
      console.log('  Moderator: <EMAIL> / moderator123');
      console.log('');
      console.log('⚠️  Please change these passwords in production!');
    } else {
      console.error('❌ Database initialization failed:', result.error);
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Error during initialization:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  initializeDatabase();
}

module.exports = { initializeDatabase };
