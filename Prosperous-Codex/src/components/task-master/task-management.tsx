"use client";

import React, { useState } from 'react';
import { Plus, ChevronDown, ChevronRight, Trash2, Edit, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { FormattedText } from '@/components/ui/formatted-text';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Task } from '@/lib/types/task-master';

// Extended Task type to handle temporary tasks with string IDs
type TaskWithFlexibleId = Omit<Task, 'id'> & {
  id: string | number;
  isTemporary?: boolean;
};
import { TaskTransformationUtils } from '@/lib/task-master/task-hierarchy-transformer';

interface TaskManagementProps {
  projectId: string;
  tasks: TaskWithFlexibleId[];
  onCreateTask: (projectId: string, taskData: {
    title: string;
    description?: string;
    parentTaskId?: string | number;
  }) => Promise<void>;
  onUpdateTask: (taskId: string | number, updateData: {
    status?: 'todo' | 'inProgress' | 'completed';
    title?: string;
    description?: string;
  }) => Promise<void>;
  onDeleteTask: (taskId: string | number) => Promise<void>;
}

interface TaskItemProps {
  task: TaskWithFlexibleId;
  onUpdate: (taskId: string | number, updateData: {
    status?: 'todo' | 'inProgress' | 'completed';
    title?: string;
    description?: string;
  }) => Promise<void>;
  onDelete: (taskId: string | number) => Promise<void>;
  onCreateSubtask: (parentId: string | number, taskData: {
    title: string;
    description?: string;
  }) => Promise<void>;
  level?: number;
}

function TaskItem({ task, onUpdate, onDelete, onCreateSubtask, level = 0 }: TaskItemProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isCreatingSubtask, setIsCreatingSubtask] = useState(false);
  const [editTitle, setEditTitle] = useState(task.title);
  const [editDescription, setEditDescription] = useState(task.description || '');
  const [newSubtaskTitle, setNewSubtaskTitle] = useState('');
  const [newSubtaskDescription, setNewSubtaskDescription] = useState('');

  const hasSubtasks = task.subtasks && task.subtasks.length > 0;
  const isCompleted = task.status === 'completed';

  const handleStatusChange = async (checked: boolean) => {
    // If trying to complete a main task (level 0) that has incomplete subtasks
    if (checked && level === 0 && hasSubtasks) {
      const incompleteSubtasks = task.subtasks?.filter(subtask => subtask.status !== 'completed') || [];
      if (incompleteSubtasks.length > 0) {
        // Instead of completing the task, expand the subtask section to show what's not done
        setIsExpanded(true);
        return;
      }
    }

    // Check if this is a temporary task (string ID starting with 'temp-')
    const isTemporaryTask = typeof task.id === 'string' && task.id.startsWith('temp-');

    if (isTemporaryTask) {
      // For temporary tasks, only update frontend state - no API call
      console.log('🔧 Temporary task status update (frontend only):', task.id, checked ? 'completed' : 'todo');
      // The onUpdate function should handle temporary tasks differently
      // but we still call it to maintain the same interface
    }

    await onUpdate(task.id, {
      status: checked ? 'completed' : 'todo'
    });
  };

  const handleSaveEdit = async () => {
    // Check if this is a temporary task (string ID starting with 'temp-')
    const isTemporaryTask = typeof task.id === 'string' && task.id.startsWith('temp-');

    if (isTemporaryTask) {
      // For temporary tasks, only update frontend state - no API call
      console.log('🔧 Temporary task edit (frontend only):', task.id, editTitle);
    }

    await onUpdate(task.id, {
      title: editTitle,
      description: editDescription
    });
    setIsEditing(false);
  };

  const handleCreateSubtask = async () => {
    if (newSubtaskTitle.trim()) {
      // Handle both numeric and string IDs (for temporary tasks)
      const parentId = typeof task.id === 'string' ? task.id : task.id.toString();

      console.log('🔧 TaskManagement.handleCreateSubtask:', {
        taskId: task.id,
        taskIdType: typeof task.id,
        parentId,
        parentIdType: typeof parentId,
        title: newSubtaskTitle
      });

      await onCreateSubtask(parentId, {
        title: newSubtaskTitle,
        description: newSubtaskDescription
      });
      setNewSubtaskTitle('');
      setNewSubtaskDescription('');
      setIsCreatingSubtask(false);
      setIsExpanded(true);
    }
  };

  return (
    <div className={level > 0 ? 'relative' : ''}>
      {/* No connector here - handled by parent container */}

      {/* Clickable task card wrapper */}
      <div
        className={`flex items-center gap-3 p-3 rounded-lg border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] transition-colors ${
          !isEditing ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-[#1F1F1F] hover:border-[#5E6AD2] dark:hover:border-[#6E56CF]' : ''
        }`}
        onClick={!isEditing ? (e) => {
          e.preventDefault();
          handleStatusChange(!isCompleted);
        } : undefined}
        role={!isEditing ? "button" : undefined}
        aria-label={!isEditing ? `Mark task "${task.title}" as ${isCompleted ? 'incomplete' : 'complete'}` : undefined}
        tabIndex={!isEditing ? 0 : undefined}
        onKeyDown={!isEditing ? (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleStatusChange(!isCompleted);
          }
        } : undefined}
      >
        {/* Checkbox - now a visual indicator */}
        <input
          type="checkbox"
          checked={isCompleted}
          readOnly
          tabIndex={-1}
          className="h-4 w-4 rounded border-gray-300 text-[#5E6AD2] focus:ring-[#5E6AD2] dark:border-gray-600 dark:bg-gray-700 pointer-events-none"
        />

        <div className="flex-1">
          {isEditing ? (
            <div
              className="space-y-2"
              onClick={(e) => e.stopPropagation()}
            >
              <Input
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                className="text-sm"
                placeholder="Task title"
                onClick={(e) => e.stopPropagation()}
              />
              <Textarea
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                className="text-xs"
                placeholder="Task description (optional)"
                rows={2}
                onClick={(e) => e.stopPropagation()}
              />
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSaveEdit();
                  }}
                >
                  Save
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsEditing(false);
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div className="flex flex-col">
                <span className={`text-sm font-medium ${isCompleted ? 'line-through text-gray-500' : 'text-black dark:text-white'}`}>
                  {task.title}
                </span>
                {task.description && (
                  <FormattedText
                    text={task.description}
                    className="text-xs text-gray-600 dark:text-gray-400 mt-1"
                    variant="muted"
                  />
                )}
              </div>
              {/* Consolidated action menu */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                    onClick={(e) => e.stopPropagation()}
                    aria-label="Task actions"
                  >
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-40 p-1 z-[450]"
                  align="end"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="space-y-1">
                    {level === 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start h-8 px-2 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          setIsCreatingSubtask(true);
                        }}
                      >
                        <Plus className="h-3 w-3 mr-2" />
                        Add Subtask
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start h-8 px-2 text-xs"
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsEditing(true);
                      }}
                    >
                      <Edit className="h-3 w-3 mr-2" />
                      Edit
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start h-8 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                      onClick={(e) => {
                        console.log('🔍 DELETE BUTTON CLICKED in TaskManagement:', {
                          taskId: task.id,
                          taskIdType: typeof task.id,
                          taskTitle: task.title,
                          level: level,
                          isSubtask: level > 0,
                          parentTaskId: task.parentTaskId,
                          timestamp: new Date().toISOString()
                        });
                        e.stopPropagation();
                        onDelete(task.id);
                      }}
                    >
                      <Trash2 className="h-3 w-3 mr-2" />
                      Delete
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          )}
        </div>
      </div>

      {/* Expandable subtasks label */}
      {hasSubtasks && level === 0 && (
        <div className="ml-4 mt-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setIsExpanded(!isExpanded);
            }}
            className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400 hover:text-[#5E6AD2] dark:hover:text-[#6E56CF] transition-colors"
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
            {task.subtasks?.length} subtask{task.subtasks?.length !== 1 ? 's' : ''}
          </button>
        </div>
      )}

      {/* Create subtask form with proper tree connector */}
      {isCreatingSubtask && (
        <div className="relative ml-4 mt-2">
          {/* Tree connector for subtask form - always use leaf style for form */}
          <div className="absolute -left-4 top-3 w-4 h-0.5 bg-gray-300 dark:bg-gray-600"></div>
          <div className="absolute -left-4 top-0 w-0.5 h-3 bg-gray-300 dark:bg-gray-600"></div>
          <div className="absolute -left-4 top-3 w-0.5 h-3 bg-gray-300 dark:bg-gray-600 rounded-bl-sm"></div>

          <div
            className="p-3 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="space-y-2">
              <Input
                value={newSubtaskTitle}
                onChange={(e) => setNewSubtaskTitle(e.target.value)}
                placeholder="Subtask title"
                className="text-sm"
                autoFocus
                onClick={(e) => e.stopPropagation()}
              />
              <Textarea
                value={newSubtaskDescription}
                onChange={(e) => setNewSubtaskDescription(e.target.value)}
                placeholder="Subtask description (optional)"
                className="text-xs"
                rows={2}
                onClick={(e) => e.stopPropagation()}
              />
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCreateSubtask();
                  }}
                >
                  Add Subtask
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsCreatingSubtask(false);
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Subtasks with proper tree-like vertical connectors */}
      {isExpanded && hasSubtasks && (
        <div className="relative ml-4 mt-2">
          {/* Main vertical line extending from parent through all subtasks */}
          <div className="absolute -left-2 top-0 bottom-0 w-0.5 bg-gray-300 dark:bg-gray-600"></div>

          <div className="space-y-2">
            {task.subtasks!.map((subtask, index) => {
              const isLastSubtask = index === task.subtasks!.length - 1;

              return (
                <div key={subtask.id} className="relative flex justify-end">
                  {/* Branch connector connecting to vertical line */}
                  <div className="absolute -left-2 top-3 w-3 h-0.5 bg-gray-300 dark:bg-gray-600"></div>
                  {isLastSubtask && (
                    /* Terminate vertical line after last subtask with proper thickness */
                    <div className="absolute -left-2 top-3 w-0.5 h-full bg-white dark:bg-[#1A1A1A]"></div>
                  )}

                  <div className="w-[calc(100%-0.5rem)]">
                    <TaskItem
                      task={subtask}
                      onUpdate={onUpdate}
                      onDelete={onDelete}
                      onCreateSubtask={onCreateSubtask}
                      level={level + 1}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

export default function TaskManagement({
  projectId,
  tasks,
  onCreateTask,
  onUpdateTask,
  onDeleteTask
}: TaskManagementProps) {
  const [isCreatingTask, setIsCreatingTask] = useState(false);
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [newTaskDescription, setNewTaskDescription] = useState('');



  const handleCreateTask = async () => {
    if (newTaskTitle.trim()) {
      await onCreateTask(projectId, {
        title: newTaskTitle,
        description: newTaskDescription
      });
      setNewTaskTitle('');
      setNewTaskDescription('');
      setIsCreatingTask(false);
    }
  };

  const handleCreateSubtask = async (parentId: string | number, taskData: {
    title: string;
    description?: string;
  }) => {
    console.log('🔧 TaskManagement.handleCreateSubtask (main):', {
      parentId,
      parentIdType: typeof parentId,
      taskData,
      projectId
    });

    await onCreateTask(projectId, {
      ...taskData,
      parentTaskId: parentId
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-base font-semibold text-black dark:text-white">
          Tasks
        </h3>
        <Button
          size="sm"
          onClick={() => setIsCreatingTask(true)}
          className="bg-[#5E6AD2] hover:bg-[#5E6AD2]/90 dark:bg-[#6E56CF] dark:hover:bg-[#6E56CF]/90"
        >
          <Plus className="h-3 w-3 mr-1" />
          Add Task
        </Button>
      </div>

      {/* Create new task form */}
      {isCreatingTask && (
        <div className="p-3 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          <div className="space-y-2">
            <Input
              value={newTaskTitle}
              onChange={(e) => setNewTaskTitle(e.target.value)}
              placeholder="Task title"
              className="text-sm"
            />
            <Textarea
              value={newTaskDescription}
              onChange={(e) => setNewTaskDescription(e.target.value)}
              placeholder="Task description (optional)"
              className="text-xs"
              rows={2}
            />
            <div className="flex gap-2">
              <Button size="sm" onClick={handleCreateTask}>Add Task</Button>
              <Button size="sm" variant="outline" onClick={() => setIsCreatingTask(false)}>Cancel</Button>
            </div>
          </div>
        </div>
      )}

      {/* Task list */}
      <div className="space-y-4">
        {(() => {
          // Use transformation utility to convert flat tasks to hierarchical structure for display
          console.log('🔍 TASK TRANSFORMATION - Input flat tasks:', tasks.map(t => ({
            id: t.id,
            idType: typeof t.id,
            title: t.title,
            parentTaskId: t.parentTaskId,
            isTemporary: t.isTemporary
          })));

          const hierarchicalTasks = TaskTransformationUtils.toHierarchical(tasks);

          console.log('🔍 TASK TRANSFORMATION - Output hierarchical tasks:', hierarchicalTasks.map(t => ({
            id: t.id,
            idType: typeof t.id,
            title: t.title,
            subtasksCount: t.subtasks?.length || 0,
            subtasks: t.subtasks?.map(st => ({
              id: st.id,
              idType: typeof st.id,
              title: st.title
            })) || []
          })));

          if (hierarchicalTasks.length === 0) {
            return (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <p className="text-sm">No tasks yet. Add your first task to get started!</p>
              </div>
            );
          }

          return hierarchicalTasks.map((task, index) => (
            <div key={task.id} className={`${index < hierarchicalTasks.length - 1 ? 'border-b border-gray-100 dark:border-gray-800 pb-4' : ''}`}>
              <TaskItem
                task={task}
                onUpdate={onUpdateTask}
                onDelete={onDeleteTask}
                onCreateSubtask={handleCreateSubtask}
              />
            </div>
          ));
        })()}
      </div>
    </div>
  );
}
