/**
 * Migration: Add Performance Indexes
 * Adds comprehensive indexes for Task Master performance optimization
 */

import { Database } from 'better-sqlite3';

export function addPerformanceIndexes(db: Database): void {
  console.log('🔧 Adding performance indexes for Task Master...');

  try {
    // Check if indexes already exist to avoid errors
    const indexExists = (indexName: string): boolean => {
      const result = db.prepare(`
        SELECT COUNT(*) as count 
        FROM sqlite_master 
        WHERE type='index' AND name=?
      `).get(indexName) as { count: number };
      return result.count > 0;
    };

    // Project indexes
    if (!indexExists('idx_projects_updated_at')) {
      db.exec('CREATE INDEX idx_projects_updated_at ON projects(updated_at)');
      console.log('✅ Added index: idx_projects_updated_at');
    }

    if (!indexExists('idx_projects_due_date')) {
      db.exec('CREATE INDEX idx_projects_due_date ON projects(due_date)');
      console.log('✅ Added index: idx_projects_due_date');
    }

    if (!indexExists('idx_projects_priority')) {
      db.exec('CREATE INDEX idx_projects_priority ON projects(priority)');
      console.log('✅ Added index: idx_projects_priority');
    }

    // Task indexes
    if (!indexExists('idx_tasks_created_by')) {
      db.exec('CREATE INDEX idx_tasks_created_by ON tasks(created_by)');
      console.log('✅ Added index: idx_tasks_created_by');
    }

    if (!indexExists('idx_tasks_updated_at')) {
      db.exec('CREATE INDEX idx_tasks_updated_at ON tasks(updated_at)');
      console.log('✅ Added index: idx_tasks_updated_at');
    }

    if (!indexExists('idx_tasks_due_date')) {
      db.exec('CREATE INDEX idx_tasks_due_date ON tasks(due_date)');
      console.log('✅ Added index: idx_tasks_due_date');
    }

    // Activity log indexes
    if (!indexExists('idx_activity_log_created_at')) {
      db.exec('CREATE INDEX idx_activity_log_created_at ON activity_log(created_at)');
      console.log('✅ Added index: idx_activity_log_created_at');
    }

    if (!indexExists('idx_activity_log_activity_type')) {
      db.exec('CREATE INDEX idx_activity_log_activity_type ON activity_log(activity_type)');
      console.log('✅ Added index: idx_activity_log_activity_type');
    }

    // Project team member indexes
    if (!indexExists('idx_project_team_members_project_id')) {
      db.exec('CREATE INDEX idx_project_team_members_project_id ON project_team_members(project_id)');
      console.log('✅ Added index: idx_project_team_members_project_id');
    }

    if (!indexExists('idx_project_team_members_user_id')) {
      db.exec('CREATE INDEX idx_project_team_members_user_id ON project_team_members(user_id)');
      console.log('✅ Added index: idx_project_team_members_user_id');
    }

    if (!indexExists('idx_project_team_members_role')) {
      db.exec('CREATE INDEX idx_project_team_members_role ON project_team_members(role)');
      console.log('✅ Added index: idx_project_team_members_role');
    }

    // Project file indexes
    if (!indexExists('idx_project_files_project_id')) {
      db.exec('CREATE INDEX idx_project_files_project_id ON project_files(project_id)');
      console.log('✅ Added index: idx_project_files_project_id');
    }

    if (!indexExists('idx_project_files_uploaded_by')) {
      db.exec('CREATE INDEX idx_project_files_uploaded_by ON project_files(uploaded_by)');
      console.log('✅ Added index: idx_project_files_uploaded_by');
    }

    if (!indexExists('idx_project_files_uploaded_at')) {
      db.exec('CREATE INDEX idx_project_files_uploaded_at ON project_files(uploaded_at)');
      console.log('✅ Added index: idx_project_files_uploaded_at');
    }

    // Project comment indexes
    if (!indexExists('idx_project_comments_project_id')) {
      db.exec('CREATE INDEX idx_project_comments_project_id ON project_comments(project_id)');
      console.log('✅ Added index: idx_project_comments_project_id');
    }

    if (!indexExists('idx_project_comments_author_id')) {
      db.exec('CREATE INDEX idx_project_comments_author_id ON project_comments(author_id)');
      console.log('✅ Added index: idx_project_comments_author_id');
    }

    if (!indexExists('idx_project_comments_parent_comment_id')) {
      db.exec('CREATE INDEX idx_project_comments_parent_comment_id ON project_comments(parent_comment_id)');
      console.log('✅ Added index: idx_project_comments_parent_comment_id');
    }

    if (!indexExists('idx_project_comments_created_at')) {
      db.exec('CREATE INDEX idx_project_comments_created_at ON project_comments(created_at)');
      console.log('✅ Added index: idx_project_comments_created_at');
    }

    // Project tag indexes
    if (!indexExists('idx_project_tags_project_id')) {
      db.exec('CREATE INDEX idx_project_tags_project_id ON project_tags(project_id)');
      console.log('✅ Added index: idx_project_tags_project_id');
    }

    if (!indexExists('idx_project_tags_tag_name')) {
      db.exec('CREATE INDEX idx_project_tags_tag_name ON project_tags(tag_name)');
      console.log('✅ Added index: idx_project_tags_tag_name');
    }

    // Composite indexes for common query patterns
    if (!indexExists('idx_projects_status_updated_at')) {
      db.exec('CREATE INDEX idx_projects_status_updated_at ON projects(status, updated_at)');
      console.log('✅ Added composite index: idx_projects_status_updated_at');
    }

    if (!indexExists('idx_tasks_project_status')) {
      db.exec('CREATE INDEX idx_tasks_project_status ON tasks(project_id, status)');
      console.log('✅ Added composite index: idx_tasks_project_status');
    }

    if (!indexExists('idx_tasks_assigned_status')) {
      db.exec('CREATE INDEX idx_tasks_assigned_status ON tasks(assigned_to, status)');
      console.log('✅ Added composite index: idx_tasks_assigned_status');
    }

    if (!indexExists('idx_activity_log_project_created')) {
      db.exec('CREATE INDEX idx_activity_log_project_created ON activity_log(project_id, created_at)');
      console.log('✅ Added composite index: idx_activity_log_project_created');
    }

    console.log('✅ Performance indexes migration completed successfully');

  } catch (error) {
    console.error('❌ Failed to add performance indexes:', error);
    throw error;
  }
}

/**
 * Analyze query performance and suggest additional indexes
 */
export function analyzeQueryPerformance(db: Database): void {
  console.log('📊 Analyzing query performance...');

  try {
    // Enable query plan analysis
    db.pragma('query_only = ON');

    // Test common queries and analyze their execution plans
    const commonQueries = [
      {
        name: 'Get user projects',
        query: 'SELECT * FROM projects WHERE created_by = ? ORDER BY updated_at DESC',
        params: [1]
      },
      {
        name: 'Get project tasks',
        query: 'SELECT * FROM tasks WHERE project_id = ? AND parent_task_id IS NULL ORDER BY created_at ASC',
        params: [1]
      },
      {
        name: 'Get project activity',
        query: 'SELECT * FROM activity_log WHERE project_id = ? ORDER BY created_at DESC LIMIT 50',
        params: [1]
      },
      {
        name: 'Get assigned tasks',
        query: 'SELECT * FROM tasks WHERE assigned_to = ? AND status != ? ORDER BY due_date ASC',
        params: [1, 'completed']
      },
      {
        name: 'Get project team members',
        query: 'SELECT * FROM project_team_members WHERE project_id = ?',
        params: [1]
      }
    ];

    for (const queryTest of commonQueries) {
      try {
        const plan = db.prepare(`EXPLAIN QUERY PLAN ${queryTest.query}`).all(...queryTest.params);
        console.log(`📋 Query: ${queryTest.name}`);
        console.log('   Plan:', plan);
        
        // Check for table scans (performance warning)
        const hasTableScan = plan.some((step: any) => 
          step.detail && step.detail.includes('SCAN TABLE')
        );
        
        if (hasTableScan) {
          console.warn(`⚠️  Query "${queryTest.name}" uses table scan - consider adding indexes`);
        }
      } catch (error) {
        console.warn(`⚠️  Failed to analyze query "${queryTest.name}":`, error);
      }
    }

    // Disable query-only mode
    db.pragma('query_only = OFF');

    console.log('✅ Query performance analysis completed');

  } catch (error) {
    console.error('❌ Failed to analyze query performance:', error);
    // Ensure query_only is disabled even if analysis fails
    try {
      db.pragma('query_only = OFF');
    } catch (pragmaError) {
      console.error('❌ Failed to disable query_only mode:', pragmaError);
    }
  }
}

/**
 * Get database statistics for performance monitoring
 */
export function getDatabaseStats(db: Database): any {
  try {
    const stats = {
      tables: {},
      indexes: {},
      performance: {}
    };

    // Get table row counts
    const tables = ['projects', 'tasks', 'project_team_members', 'project_files', 'project_comments', 'activity_log'];
    
    for (const table of tables) {
      try {
        const result = db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get() as { count: number };
        (stats.tables as any)[table] = result.count;
      } catch (error) {
        console.warn(`Failed to get count for table ${table}:`, error);
      }
    }

    // Get index information
    const indexQuery = db.prepare(`
      SELECT name, tbl_name 
      FROM sqlite_master 
      WHERE type='index' AND name LIKE 'idx_%'
      ORDER BY tbl_name, name
    `);
    
    const indexes = indexQuery.all() as { name: string; tbl_name: string }[];
    for (const index of indexes) {
      if (!(stats.indexes as any)[index.tbl_name]) {
        (stats.indexes as any)[index.tbl_name] = [];
      }
      (stats.indexes as any)[index.tbl_name].push(index.name);
    }

    // Get database size
    const sizeResult = db.prepare('PRAGMA page_count').get() as { page_count: number };
    const pageSizeResult = db.prepare('PRAGMA page_size').get() as { page_size: number };
    (stats.performance as any).databaseSize = sizeResult.page_count * pageSizeResult.page_size;

    return stats;

  } catch (error) {
    console.error('Failed to get database stats:', error);
    return null;
  }
}
