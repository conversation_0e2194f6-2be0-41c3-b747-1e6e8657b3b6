"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { DayPicker } from "react-day-picker"

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"

export type CalendarProps = React.ComponentProps<typeof DayPicker>

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn("p-3 rounded-xl", className)}
      classNames={{
        month: "space-y-4",
        months: "flex flex-col sm:flex-row space-y-4 sm:space-y-0 relative gap-2",
        month_caption: "flex justify-center pt-1 relative items-center",
        month_grid: "w-full border-collapse space-y-1",
        caption_label: "text-sm font-bold",
        nav: "flex items-center justify-between absolute inset-x-0",
        button_previous: cn(
          buttonVariants({ variant: "outline" }),
          "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 z-10"
        ),
        button_next: cn(
          buttonVariants({ variant: "outline" }),
          "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 z-10"
        ),
        weeks: "w-full border-collapse",
        weekdays: "flex",
        weekday: "text-muted-foreground rounded-lg w-8 font-bold text-[0.8rem]",
        week: "flex w-full mt-2",
        day: "h-8 w-8 p-0 font-normal aria-selected:opacity-100 rounded-lg first:aria-selected:rounded-lg last:aria-selected:rounded-lg",
        day_button: cn(
          buttonVariants({ variant: "ghost" }),
          "h-8 w-8 text-center text-sm p-0 relative focus-within:relative focus-within:z-20 rounded-lg"
        ),
        range_start:
          "day-range-start !bg-accent rounded-lg [&>button]:bg-[#5E6AD2] dark:[&>button]:bg-[#6E56CF] [&>button]:text-white [&>button]:hover:bg-[#5E6AD2]/90 dark:[&>button]:hover:bg-[#9E8CFC] [&>button]:hover:text-white",
        range_end:
          "day-range-end !bg-accent rounded-lg [&>button]:bg-[#5E6AD2] dark:[&>button]:bg-[#6E56CF] [&>button]:text-white [&>button]:hover:bg-[#5E6AD2]/90 dark:[&>button]:hover:bg-[#9E8CFC] [&>button]:hover:text-white",
        range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
        selected: cn(
          props.mode === "range"
            ? "bg-[#5E6AD2] dark:bg-[#6E56CF] hover:bg-[#5E6AD2]/90 dark:hover:bg-[#9E8CFC] hover:text-white focus:bg-[#5E6AD2]/90 dark:focus:bg-[#9E8CFC] focus:text-white rounded-lg"
            : "[&>button]:bg-[#5E6AD2] dark:[&>button]:bg-[#6E56CF] [&>button]:text-white [&>button]:hover:bg-[#5E6AD2]/90 dark:[&>button]:hover:bg-[#9E8CFC] [&>button]:hover:text-white [&>button]:focus:bg-[#5E6AD2]/90 dark:[&>button]:focus:bg-[#9E8CFC] [&>button]:focus:text-white [&>button]:rounded-lg"
        ),
        today: "bg-accent text-accent-foreground !rounded-lg",
        outside:
          "day-outside text-muted-foreground opacity-50 !aria-selected:bg-accent/50 !aria-selected:text-muted-foreground !aria-selected:opacity-30",
        disabled: "text-muted-foreground opacity-50",
        hidden: "invisible",
        ...classNames,
      }}
      components={{
        Chevron: ({ ...chevronProps }) =>
          chevronProps.orientation === "left" ? (
            <ChevronLeft {...chevronProps} className="h-4 w-4" />
          ) : (
            <ChevronRight {...chevronProps} className="h-4 w-4" />
          ),
      }}
      {...props}
    />
  )
}
Calendar.displayName = "Calendar"

export { Calendar }
