"use client";
import { useSession } from 'next-auth/react';
import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { MetricsGrid } from '@/components/dashboard/metrics-grid';
import { RecentActivity } from '@/components/dashboard/recent-activity';
import { QuickActions } from '@/components/dashboard/quick-actions';

export default function DashboardPage() {
  const { data: session } = useSession();

  if (!session?.user) {
    return null; // ProtectedLayout handles redirection
  }

  return (
    <div className="container max-w-none flex h-full w-full flex-col items-start gap-6 px-8 pt-1.5 pb-3">
      {/* Dashboard Header */}
      <DashboardHeader />

      {/* Metrics Grid */}
      <MetricsGrid />

      {/* Recent Activity and Quick Actions */}
      <div className="grid w-full grid-cols-3 items-start gap-6">
        <div className="col-span-2">
          <RecentActivity />
        </div>
        <div className="col-span-1">
          <QuickActions />
        </div>
      </div>
    </div>
  );
}
