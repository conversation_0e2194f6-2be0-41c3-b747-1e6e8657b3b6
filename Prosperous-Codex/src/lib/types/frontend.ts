/**
 * Frontend Types (camelCase)
 * 
 * These types represent the frontend/UI layer structure with camelCase field names.
 * They extend API types with additional UI-specific properties and state management.
 * 
 * Use these types in React components, hooks, and frontend utilities.
 */

import { 
  Project as ApiProject, 
  Task as ApiTask, 
  ProjectTeamMember as ApiProjectTeamMember,
  ProjectFile as ApiProjectFile,
  ProjectComment as ApiProjectComment,
  User as ApiUser,
  TaskStatus,
  TaskPriority,
  TeamMemberRole
} from './api';

/**
 * Frontend Project with UI state
 */
export interface Project extends ApiProject {
  // UI state
  isLoading?: boolean;
  isExpanded?: boolean;
  isDirty?: boolean;
  hasUnsavedChanges?: boolean;
  
  // Optimistic updates
  optimisticUpdates?: Partial<Project>;
  
  // UI-specific computed properties
  progressPercentage?: string;
  statusColor?: string;
  priorityColor?: string;
  isOverdue?: boolean;
  daysUntilDue?: number;
  
  // Drag and drop state
  isDragging?: boolean;
  dragPosition?: { x: number; y: number };
}

/**
 * Frontend Task with UI state
 */
export interface Task extends ApiTask {
  // UI state
  isLoading?: boolean;
  isExpanded?: boolean;
  isDirty?: boolean;
  hasUnsavedChanges?: boolean;
  
  // Optimistic updates
  optimisticUpdates?: Partial<Task>;
  
  // UI-specific computed properties
  statusColor?: string;
  priorityColor?: string;
  isOverdue?: boolean;
  daysUntilDue?: number;
  
  // Drag and drop state
  isDragging?: boolean;
  canMoveToColumn?: string[];
  
  // Hierarchy display
  indentLevel?: number;
  hasChildren?: boolean;
  isCollapsed?: boolean;
}

/**
 * Frontend Team Member with UI state
 */
export interface TeamMember extends ApiProjectTeamMember {
  // UI state
  isLoading?: boolean;
  isRemoving?: boolean;
  
  // UI-specific computed properties
  roleColor?: string;
  initials?: string;
  displayName?: string;
  
  // Permission checks
  canEdit?: boolean;
  canRemove?: boolean;
}

/**
 * Frontend Project File with UI state
 */
export interface ProjectFile extends ApiProjectFile {
  // UI state
  isUploading?: boolean;
  uploadProgress?: number;
  isDeleting?: boolean;
  
  // UI-specific computed properties
  fileSizeFormatted?: string;
  fileIcon?: string;
  canPreview?: boolean;
  
  // Preview state
  previewUrl?: string;
  isPreviewLoading?: boolean;
}

/**
 * Frontend Comment with UI state
 */
export interface ProjectComment extends ApiProjectComment {
  // UI state
  isEditing?: boolean;
  isDeleting?: boolean;
  isReplying?: boolean;
  showReplies?: boolean;
  
  // Edit state
  editContent?: string;
  replyContent?: string;
  
  // UI-specific computed properties
  timeAgo?: string;
  canEdit?: boolean;
  canDelete?: boolean;
  canReply?: boolean;
  
  // Nested display
  indentLevel?: number;
  isCollapsed?: boolean;
}

/**
 * Frontend User with UI state
 */
export interface User extends ApiUser {
  // UI state
  isOnline?: boolean;
  
  // UI-specific computed properties
  displayName?: string;
  initials?: string;
  avatarUrl?: string;
  
  // Permission checks
  canManageUsers?: boolean;
  canManageProjects?: boolean;
  canAccessAdmin?: boolean;
}

/**
 * UI Component Props Types
 */

/**
 * Project card component props
 */
export interface ProjectCardProps {
  project: Project;
  isSelected?: boolean;
  isDraggable?: boolean;
  onSelect?: (project: Project) => void;
  onEdit?: (project: Project) => void;
  onDelete?: (project: Project) => void;
  onStatusChange?: (project: Project, status: TaskStatus) => void;
  onPriorityChange?: (project: Project, priority: TaskPriority) => void;
}

/**
 * Task card component props
 */
export interface TaskCardProps {
  task: Task;
  isSelected?: boolean;
  isDraggable?: boolean;
  showProject?: boolean;
  onSelect?: (task: Task) => void;
  onEdit?: (task: Task) => void;
  onDelete?: (task: Task) => void;
  onStatusChange?: (task: Task, status: TaskStatus) => void;
  onMove?: (task: Task, targetColumn: string) => void;
}

/**
 * Team member card component props
 */
export interface TeamMemberCardProps {
  member: TeamMember;
  isOwner?: boolean;
  canManage?: boolean;
  onRoleChange?: (member: TeamMember, role: TeamMemberRole) => void;
  onRemove?: (member: TeamMember) => void;
}

/**
 * UI State Management Types
 */

/**
 * Task Master UI state
 */
export interface TaskMasterState {
  // Current view
  currentView: 'projects' | 'tasks' | 'calendar' | 'reports';
  
  // Selected items
  selectedProject?: Project;
  selectedTask?: Task;
  
  // Filters and sorting
  filters: {
    status?: TaskStatus[];
    priority?: TaskPriority[];
    assignedTo?: number[];
    dueDate?: {
      from?: string;
      to?: string;
    };
    search?: string;
  };
  
  sorting: {
    field: string;
    direction: 'asc' | 'desc';
  };
  
  // UI state
  isLoading: boolean;
  isSidebarOpen: boolean;
  isDrawerOpen: boolean;
  drawerContent?: 'project' | 'task' | 'team' | 'files' | 'comments';
  
  // Drag and drop
  dragState?: {
    isDragging: boolean;
    draggedItem?: Project | Task;
    dropTarget?: string;
  };
}

/**
 * Form state types
 */
export interface ProjectFormState {
  data: Partial<Project>;
  errors: Record<string, string>;
  isSubmitting: boolean;
  isDirty: boolean;
}

export interface TaskFormState {
  data: Partial<Task>;
  errors: Record<string, string>;
  isSubmitting: boolean;
  isDirty: boolean;
}

export interface CommentFormState {
  content: string;
  isSubmitting: boolean;
  error?: string;
}

/**
 * Modal and Dialog Types
 */
export interface ModalState {
  isOpen: boolean;
  type?: 'create' | 'edit' | 'delete' | 'confirm';
  title?: string;
  content?: React.ReactNode;
  data?: Record<string, unknown>;
  onConfirm?: () => void;
  onCancel?: () => void;
}

export interface ToastNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * Theme and Styling Types
 */
export interface ThemeColors {
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  
  // Status colors
  todo: string;
  inProgress: string;
  completed: string;
  
  // Priority colors
  low: string;
  medium: string;
  high: string;
  
  // Role colors
  owner: string;
  admin: string;
  member: string;
  viewer: string;
}

/**
 * Responsive breakpoints
 */
export interface Breakpoints {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
}

/**
 * Animation and transition types
 */
export interface AnimationConfig {
  duration: number;
  easing: string;
  delay?: number;
}

export interface TransitionConfig {
  enter: AnimationConfig;
  exit: AnimationConfig;
}

/**
 * Accessibility types
 */
export interface A11yProps {
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-selected'?: boolean;
  'aria-disabled'?: boolean;
  role?: string;
  tabIndex?: number;
}

/**
 * Event handler types
 */
export type ProjectEventHandler = (project: Project) => void;
export type TaskEventHandler = (task: Task) => void;
export type TeamMemberEventHandler = (member: TeamMember) => void;
export type FileEventHandler = (file: ProjectFile) => void;
export type CommentEventHandler = (comment: ProjectComment) => void;

/**
 * Generic event handlers
 */
export type ChangeHandler<T> = (value: T) => void;
export type SubmitHandler<T> = (data: T) => void | Promise<void>;
export type ErrorHandler = (error: Error | string) => void;
