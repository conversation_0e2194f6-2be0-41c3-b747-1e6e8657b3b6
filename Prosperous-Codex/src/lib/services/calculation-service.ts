// Client-side Calculation Service
// Handles communication with the calculation API and data transformation

import {
  CalculationRequest,
  CalculationResponse,
  PaperCandidate,
  JobInputs,
  InnerTextJobInputs,
  CoverJobInputs,
  EndpaperJobInputs
} from '../types/calculation';

// Extract job inputs from form data
// This function is no longer used directly in page.tsx, but might be used elsewhere or for testing.
// Keeping it for now, but removing its export if it's truly only for internal use.
/*
export function extractJobInputs(
  tabId: 'innerText' | 'cover' | 'endpapers',
  formData: FormData | any
): JobInputs | InnerTextJobInputs | CoverJobInputs | EndpaperJobInputs {
  const baseInputs = {
    trimH: parseFloat(formData.get?.('trimH') || formData.trimH || '0'),
    trimW: parseFloat(formData.get?.('trimW') || formData.trimW || '0'),
    quantity: parseInt(formData.get?.('quantity') || formData.quantity || '0'),
    bleed: parseFloat(formData.get?.('bleed') || formData.bleed || '0'),
    gripper: parseFloat(formData.get?.('gripper') || formData.gripper || '0'),
    colorBar: parseFloat(formData.get?.('colorBar') || formData.colorBar || '0'),
    spoilagePct: parseFloat(formData.get?.('spoilagePct') || formData.spoilagePct || '0') / 100,
    alignmentMode: (formData.get?.('alignmentMode') || formData.alignmentMode || 'Aligned') as 'Aligned' | 'Misaligned'
  };

  if (tabId === 'innerText') {
    return {
      ...baseInputs,
      totalPages: parseInt(formData.get?.('totalPages') || formData.totalPages || '0'),
      lip: parseFloat(formData.get?.('lip') || formData.lip || '0'),
      bindingMethod: formData.get?.('bindingMethod') || formData.bindingMethod || 'perfectBound',
      isDoubleLipActive: Boolean(formData.get?.('isDoubleLipActive') || formData.isDoubleLipActive)
    } as InnerTextJobInputs;
  } else if (tabId === 'cover') {
    return {
      ...baseInputs,
      spineThickness: parseFloat(formData.get?.('spineThickness') || formData.spineThickness || '0'),
      coverType: formData.get?.('coverType') || formData.coverType || 'paperback',
      turnInAllowance: parseFloat(formData.get?.('turnInAllowance') || formData.turnInAllowance || '0'),
      flapWidth: parseFloat(formData.get?.('flapWidth') || formData.flapWidth || '0')
    } as CoverJobInputs;
  } else {
    return {
      ...baseInputs,
      endpaperType: formData.get?.('endpaperType') || formData.endpaperType || 'single'
    } as EndpaperJobInputs;
  }
}
*/
import { PaperOption } from '../paper-data';

// Transform PaperOption to PaperCandidate for calculations
function transformPaperOption(option: PaperOption, tabId?: string): PaperCandidate {
  // Infer category from tabId if not present
  let category = option.category;
  if (!category && tabId) {
    category = tabId === 'innerText' ? 'Inner Text' :
               tabId === 'cover' ? 'Cover' :
               'Endpapers';
  }

  return {
    id: option.id,
    name: option.name,
    source: option.source as 'Pre-Cut' | 'Roll',
    sheetH: option.sheetHeight,
    sheetW: option.sheetWidth,
    grainDirection: option.grainDirection as 'Height' | 'Width',
    caliperMicrons: option.caliper,
    costReam: option.costPerReam,
    gsm: option.gsm,
    costTonne: option.costPerTon || null,
    category: category
  };
}

// Extract job inputs from form data
export function extractJobInputs(
  tabId: 'innerText' | 'cover' | 'endpapers',
  formData: FormData | any
): JobInputs | InnerTextJobInputs | CoverJobInputs | EndpaperJobInputs {
  const baseInputs = {
    trimH: parseFloat(formData.get?.('trimH') || formData.trimH || '0'),
    trimW: parseFloat(formData.get?.('trimW') || formData.trimW || '0'),
    quantity: parseInt(formData.get?.('quantity') || formData.quantity || '0'),
    bleed: parseFloat(formData.get?.('bleed') || formData.bleed || '0'),
    gripper: parseFloat(formData.get?.('gripper') || formData.gripper || '0'),
    colorBar: parseFloat(formData.get?.('colorBar') || formData.colorBar || '0'),
    spoilagePct: parseFloat(formData.get?.('spoilagePct') || formData.spoilagePct || '0') / 100,
    alignmentMode: (formData.get?.('alignmentMode') || formData.alignmentMode || 'Aligned') as 'Aligned' | 'Misaligned'
  };

  if (tabId === 'innerText') {
    return {
      ...baseInputs,
      totalPages: parseInt(formData.get?.('totalPages') || formData.totalPages || '0'),
      lip: parseFloat(formData.get?.('lip') || formData.lip || '0'),
      bindingMethod: formData.get?.('bindingMethod') || formData.bindingMethod || 'perfectBound',
      isDoubleLipActive: Boolean(formData.get?.('isDoubleLipActive') || formData.isDoubleLipActive)
    } as InnerTextJobInputs;
  } else if (tabId === 'cover') {
    return {
      ...baseInputs,
      spineThickness: parseFloat(formData.get?.('spineThickness') || formData.spineThickness || '0'),
      coverType: formData.get?.('coverType') || formData.coverType || 'paperback',
      turnInAllowance: parseFloat(formData.get?.('turnInAllowance') || formData.turnInAllowance || '0'),
      flapWidth: parseFloat(formData.get?.('flapWidth') || formData.flapWidth || '0')
    } as CoverJobInputs;
  } else {
    return {
      ...baseInputs,
      endpaperType: formData.get?.('endpaperType') || formData.endpaperType || 'single'
    } as EndpaperJobInputs;
  }
}

// Main calculation service class
export class CalculationService {
  private static instance: CalculationService;
  private baseUrl: string;

  private constructor() {
    this.baseUrl = process.env.NODE_ENV === 'production' 
      ? '' // Use relative URLs in production
      : 'http://localhost:3000'; // Use full URL in development
  }

  public static getInstance(): CalculationService {
    if (!CalculationService.instance) {
      CalculationService.instance = new CalculationService();
    }
    return CalculationService.instance;
  }

  // Calculate paper costs for given options
  public async calculateCosts(
    tabId: 'innerText' | 'cover' | 'endpapers',
    jobInputs: JobInputs | InnerTextJobInputs | CoverJobInputs | EndpaperJobInputs,
    paperOptions: PaperOption[]
  ): Promise<CalculationResponse> {
    try {
      const request: CalculationRequest = {
        tabId,
        jobInputs,
        paperOptions: paperOptions.map(option => transformPaperOption(option, tabId))
      };

      const response = await fetch(`${this.baseUrl}/api/calculate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result: CalculationResponse = await response.json();
      return result;
    } catch (error) {
      console.error('Calculation service error:', error);
      throw new Error(
        error instanceof Error 
          ? error.message 
          : 'Failed to calculate paper costs'
      );
    }
  }

  // Calculate costs with form data extraction
  public async calculateFromFormData(
    tabId: 'innerText' | 'cover' | 'endpapers',
    formData: FormData | any,
    paperOptions: PaperOption[]
  ): Promise<CalculationResponse> {
    const jobInputs = extractJobInputs(tabId, formData);
    return this.calculateCosts(tabId, jobInputs, paperOptions);
  }

  // Validate inputs before calculation
  public validateInputs(
    tabId: 'innerText' | 'cover' | 'endpapers',
    jobInputs: JobInputs | InnerTextJobInputs | CoverJobInputs | EndpaperJobInputs
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Basic validation
    if (jobInputs.trimH <= 0) errors.push('Trim height must be positive');
    if (jobInputs.trimW <= 0) errors.push('Trim width must be positive');
    if (jobInputs.quantity <= 0) errors.push('Quantity must be positive');
    if (jobInputs.bleed < 0) errors.push('Bleed cannot be negative');
    if (jobInputs.gripper < 0) errors.push('Gripper margin cannot be negative');
    if (jobInputs.colorBar < 0) errors.push('Color bar margin cannot be negative');
    if (jobInputs.spoilagePct < 0) errors.push('Spoilage percentage cannot be negative');

    // Tab-specific validation
    if (tabId === 'innerText') {
      const innerInputs = jobInputs as InnerTextJobInputs;
      if (innerInputs.totalPages <= 0) errors.push('Total pages must be positive');
      if (innerInputs.totalPages % 2 !== 0) errors.push('Total pages must be even');
      if (innerInputs.lip < 0) errors.push('Side lip margin cannot be negative');
    } else if (tabId === 'cover') {
      const coverInputs = jobInputs as CoverJobInputs;
      if (coverInputs.spineThickness < 0) errors.push('Spine thickness cannot be negative');
      if (coverInputs.turnInAllowance < 0) errors.push('Turn-in allowance cannot be negative');
      if (coverInputs.flapWidth < 0) errors.push('Flap width cannot be negative');
    }

    return { isValid: errors.length === 0, errors };
  }
}

// Export singleton instance
export const calculationService = CalculationService.getInstance();
