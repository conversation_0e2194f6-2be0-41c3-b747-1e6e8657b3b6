import { NextRequest, NextResponse } from 'next/server';
import { TaskMasterService } from '@/lib/database/task-master-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ fileId: string }> }
) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);

    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;
    const { fileId } = await params;
    const fileIdNum = parseInt(fileId);
    
    if (isNaN(fileIdNum)) {
      return NextResponse.json(
        { error: 'Invalid file ID' },
        { status: 400 }
      );
    }
    
    const taskMasterService = new TaskMasterService();
    
    // Delete file
    const success = await taskMasterService.deleteProjectFile(fileIdNum, parseInt(user.id));
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete file or access denied' },
        { status: 403 }
      );
    }
    
    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error('Delete file error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
