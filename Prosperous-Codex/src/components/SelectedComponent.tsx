"use client";

import React from "react";
import { X, Trash2, ShoppingCart } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { CalculationResult } from "@/components/calculation-results-card";
import {
  InnerTextJobSpecificationData,
  CoverJobSpecificationData,
  EndpaperJobSpecificationData,
  TabId
} from "@/lib/types/ui-components";

// Extended interface for selected components with job specifications
export interface SelectedComponent extends CalculationResult {
  componentType: TabId;
  jobSpecs: InnerTextJobSpecificationData | CoverJobSpecificationData | EndpaperJobSpecificationData;
  selectedAt: Date;
}

interface SelectedComponentDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedComponents: SelectedComponent[];
  onRemoveComponent: (componentId: string, componentType: TabId) => void;
}

// Floating drawer overlay component with clean animations
const DrawerOverlay = ({ children, open, onOpenChange }: {
  children: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) => {
  const [shouldRender, setShouldRender] = React.useState(false);

  React.useEffect(() => {
    if (open) {
      setShouldRender(true);
    } else {
      // Wait for exit animation to complete
      const timer = setTimeout(() => setShouldRender(false), 300);
      return () => clearTimeout(timer);
    }
  }, [open]);

  if (!shouldRender) return null;

  return (
    <div
      className={`fixed inset-0 z-[300] flex items-start justify-end pt-20 pb-8 pr-1 ${
        open
          ? 'animate-in fade-in-0 duration-300'
          : 'animate-out fade-out-0 duration-300'
      }`}
      onClick={() => onOpenChange(false)}
    >
      <div
        className={`w-[380px] h-full max-h-[calc(100vh-6rem)] bg-white dark:bg-[#1A1A1A] border border-gray-100 dark:border-[#2A2A2A] shadow-xl rounded-lg flex flex-col ${
          open
            ? 'animate-in slide-in-from-right-4 fade-in-0 zoom-in-95 duration-300'
            : 'animate-out slide-out-to-right-4 fade-out-0 zoom-out-95 duration-300'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>
  );
};

export default function SelectedComponentDrawer({
  open,
  onOpenChange,
  selectedComponents,
  onRemoveComponent
}: SelectedComponentDrawerProps) {
  // Group components by type
  const innerTextComponents = selectedComponents.filter(c => c.componentType === 'innerText');
  const coverComponents = selectedComponents.filter(c => c.componentType === 'cover');
  const endpaperComponents = selectedComponents.filter(c => c.componentType === 'endpapers');

  // Calculate total cost
  const totalCost = selectedComponents.reduce((sum, component) => {
    return sum + (component.costPerBook || 0);
  }, 0);

  const formatCurrency = (value: number | undefined) => {
    if (value === undefined || value === null) return 'N/A';
    return `$${value.toFixed(2)}`;
  };

  const formatDimensions = (heightMm: number | undefined, widthMm: number | undefined) => {
    if (heightMm === undefined || widthMm === undefined || heightMm === null || widthMm === null) {
      return 'N/A';
    }
    return `${heightMm.toFixed(1)} × ${widthMm.toFixed(1)} mm`;
  };

  const getComponentTypeLabel = (type: TabId) => {
    switch (type) {
      case 'innerText': return 'Inner Text';
      case 'cover': return 'Cover';
      case 'endpapers': return 'Endpapers';
      default: return type;
    }
  };

  const getComponentTypeColor = (type: TabId) => {
    // Use consistent purple color scheme for all component types
    return 'bg-[#5E6AD2]/10 text-[#5E6AD2] dark:bg-[#6E56CF]/10 dark:text-[#6E56CF]';
  };

  return (
    <DrawerOverlay open={open} onOpenChange={onOpenChange}>
      <div className="flex h-full flex-col">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-100 dark:border-[#2A2A2A] px-6 py-4 bg-white dark:bg-[#1A1A1A] rounded-t-lg">
          <div className="flex items-center gap-3">
            <ShoppingCart className="h-5 w-5 text-[#5E6AD2] dark:text-[#6E56CF]" />
            <h2 className="text-lg font-semibold text-foreground">
              Selected Components
            </h2>
            <Badge variant="secondary" className="bg-[#5E6AD2]/10 text-[#5E6AD2] dark:bg-[#6E56CF]/10 dark:text-[#6E56CF]">
              {selectedComponents.length}
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onOpenChange(false)}
            className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-[#2A2A2A]"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full overflow-hidden">
          {selectedComponents.length === 0 ? (
            <div className="flex flex-col items-center justify-center flex-1 text-center px-6 py-8">
              <ShoppingCart className="h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
              <h3 className="text-lg font-medium text-black dark:text-white mb-2">
                No components selected yet
              </h3>
              <p className="text-gray-600 dark:text-gray-300 max-w-sm">
                Select paper options from the calculation results to add them to your component summary.
              </p>
            </div>
          ) : (
            <>
              {/* Component Sections - Scrollable */}
              <div className="flex-1 overflow-y-auto px-6 py-4">
                <div className="space-y-6">
                  {innerTextComponents.length > 0 && (
                    <>
                      <ComponentSection
                        title="Inner Text"
                        components={innerTextComponents}
                        onRemoveComponent={onRemoveComponent}
                        formatCurrency={formatCurrency}
                        formatDimensions={formatDimensions}
                        getComponentTypeColor={getComponentTypeColor}
                      />
                      {(coverComponents.length > 0 || endpaperComponents.length > 0) && (
                        <div className="border-t border-dashed border-gray-300 dark:border-gray-600 my-6"></div>
                      )}
                    </>
                  )}

                  {coverComponents.length > 0 && (
                    <>
                      <ComponentSection
                        title="Cover"
                        components={coverComponents}
                        onRemoveComponent={onRemoveComponent}
                        formatCurrency={formatCurrency}
                        formatDimensions={formatDimensions}
                        getComponentTypeColor={getComponentTypeColor}
                      />
                      {endpaperComponents.length > 0 && (
                        <div className="border-t border-dashed border-gray-300 dark:border-gray-600 my-6"></div>
                      )}
                    </>
                  )}

                  {endpaperComponents.length > 0 && (
                    <ComponentSection
                      title="Endpapers"
                      components={endpaperComponents}
                      onRemoveComponent={onRemoveComponent}
                      formatCurrency={formatCurrency}
                      formatDimensions={formatDimensions}
                      getComponentTypeColor={getComponentTypeColor}
                    />
                  )}
                </div>
              </div>

              {/* Total Cost Summary at Bottom with Thick Divider */}
              <div className="flex-shrink-0 px-6 py-4">
                <div className="border-t-2 border-gray-300 dark:border-gray-600 pt-4">
                  <div className="flex items-center justify-between">
                    <span className="text-base font-semibold text-black dark:text-white">
                      Total Cost per Book:
                    </span>
                    <span className="text-xl font-bold text-[#5E6AD2] dark:text-[#6E56CF]">
                      {formatCurrency(totalCost)}
                    </span>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </DrawerOverlay>
  );
}

// Component Section for grouping components by type
interface ComponentSectionProps {
  title: string;
  components: SelectedComponent[];
  onRemoveComponent: (componentId: string, componentType: TabId) => void;
  formatCurrency: (value: number | undefined) => string;
  formatDimensions: (heightMm: number | undefined, widthMm: number | undefined) => string;
  getComponentTypeColor: (type: TabId) => string;
}

function ComponentSection({
  title,
  components,
  onRemoveComponent,
  formatCurrency,
  formatDimensions,
  getComponentTypeColor
}: ComponentSectionProps) {
  return (
    <div>
      <h3 className="text-sm font-semibold text-black dark:text-white mb-3 flex items-center">
        <span className="w-2 h-2 bg-[#5E6AD2] dark:bg-[#6E56CF] rounded-full mr-2"></span>
        {title} ({components.length})
      </h3>
      <div className="space-y-4">
        {components.map((component, index) => (
          <div key={`${component.componentType}-${component.id}`}>
            <ComponentCard
              component={component}
              onRemoveComponent={onRemoveComponent}
              formatCurrency={formatCurrency}
              formatDimensions={formatDimensions}
              getComponentTypeColor={getComponentTypeColor}
            />
            {index < components.length - 1 && (
              <div className="border-t border-dashed border-gray-300 dark:border-gray-600 my-4"></div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

// Individual Component Card
interface ComponentCardProps {
  component: SelectedComponent;
  onRemoveComponent: (componentId: string, componentType: TabId) => void;
  formatCurrency: (value: number | undefined) => string;
  formatDimensions: (heightMm: number | undefined, widthMm: number | undefined) => string;
  getComponentTypeColor: (type: TabId) => string;
}

function ComponentCard({
  component,
  onRemoveComponent,
  formatCurrency,
  formatDimensions,
  getComponentTypeColor
}: ComponentCardProps) {
  const renderJobSpecs = () => {
    const specs = component.jobSpecs;

    if (component.componentType === 'innerText') {
      const innerSpecs = specs as InnerTextJobSpecificationData;
      return (
        <div className="grid grid-cols-2 gap-2 text-xs text-black dark:text-white">
          <div><span className="text-gray-600 dark:text-gray-300">Pages:</span> {innerSpecs.totalPages}</div>
          <div><span className="text-gray-600 dark:text-gray-300">Binding:</span> {innerSpecs.bindingMethod}</div>
          <div><span className="text-gray-600 dark:text-gray-300">Size:</span> {formatDimensions(innerSpecs.trimH, innerSpecs.trimW)}</div>
          <div><span className="text-gray-600 dark:text-gray-300">Quantity:</span> {innerSpecs.quantity.toLocaleString()}</div>
        </div>
      );
    } else if (component.componentType === 'cover') {
      const coverSpecs = specs as CoverJobSpecificationData;
      return (
        <div className="grid grid-cols-2 gap-2 text-xs text-black dark:text-white">
          <div><span className="text-gray-600 dark:text-gray-300">Type:</span> {coverSpecs.coverType}</div>
          <div><span className="text-gray-600 dark:text-gray-300">Spine:</span> {coverSpecs.spineThickness}mm</div>
          <div><span className="text-gray-600 dark:text-gray-300">Size:</span> {formatDimensions(coverSpecs.trimH, coverSpecs.trimW)}</div>
          <div><span className="text-gray-600 dark:text-gray-300">Quantity:</span> {coverSpecs.quantity.toLocaleString()}</div>
        </div>
      );
    } else if (component.componentType === 'endpapers') {
      const endpaperSpecs = specs as EndpaperJobSpecificationData;
      return (
        <div className="grid grid-cols-2 gap-2 text-xs text-black dark:text-white">
          <div><span className="text-gray-600 dark:text-gray-300">Type:</span> {endpaperSpecs.endpaperType}</div>
          <div><span className="text-gray-600 dark:text-gray-300">Size:</span> {formatDimensions(endpaperSpecs.trimH, endpaperSpecs.trimW)}</div>
          <div><span className="text-gray-600 dark:text-gray-300">Quantity:</span> {endpaperSpecs.quantity.toLocaleString()}</div>
          <div><span className="text-gray-600 dark:text-gray-300">Spoilage:</span> {endpaperSpecs.spoilagePct}%</div>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="py-2">
      {/* Header with paper name, badge, and delete button */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-black dark:text-white text-sm mb-1 truncate">
            {component.paperName}
          </h4>
          <div className="flex items-center gap-2 text-xs text-black dark:text-white">
            <span className="truncate">{component.source} | {component.grainDirection} Grain | {component.gsm}g/m²</span>
          </div>
        </div>
        <div className="flex items-center gap-2 ml-3 flex-shrink-0">
          <Badge className={getComponentTypeColor(component.componentType)} variant="secondary">
            {component.componentType === 'innerText' ? 'Inner Text' :
             component.componentType === 'cover' ? 'Cover' : 'Endpapers'}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRemoveComponent(component.id, component.componentType)}
            className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Job Specifications */}
      <div className="mb-3">
        <div className="text-xs font-medium text-black dark:text-white mb-1">Job Specifications</div>
        {renderJobSpecs()}
      </div>

      {/* Paper Details */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
        <div className="text-xs font-medium text-black dark:text-white mb-1">Paper Details</div>
        <div className="grid grid-cols-2 gap-2 text-xs text-black dark:text-white">
          <div><span className="text-gray-600 dark:text-gray-300">Utilization:</span> {((component.sheetUtilization || 0) * 100).toFixed(1)}%</div>
          <div><span className="text-gray-600 dark:text-gray-300">Layout:</span> {component.layoutFit || 'N/A'}</div>
          <div><span className="text-gray-600 dark:text-gray-300">Sheets:</span> {component.totalSheets?.toLocaleString() || 'N/A'}</div>
          <div><span className="text-gray-600 dark:text-gray-300">Cost per Book:</span> <span className="font-semibold text-[#5E6AD2] dark:text-[#6E56CF]">{formatCurrency(component.costPerBook)}</span></div>
          <div><span className="text-gray-600 dark:text-gray-300">Total Cost:</span> <span className="font-semibold text-[#5E6AD2] dark:text-[#6E56CF]">{formatCurrency(component.totalCost)}</span></div>
        </div>
      </div>
    </div>
  );
}
