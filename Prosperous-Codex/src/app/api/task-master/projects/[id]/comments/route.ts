/**
 * Task Master Project Comments API
 *
 * GET: Retrieve all comments for a project (accessible to all authenticated users)
 * POST: Create a new comment (universal access - all authenticated users can comment)
 */

import { NextRequest, NextResponse } from 'next/server';
import { TaskMasterService } from '@/lib/database/task-master-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);

    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;
    const { id } = await params;
    const projectId = parseInt(id);

    if (isNaN(projectId)) {
      return NextResponse.json(
        { error: 'Invalid project ID' },
        { status: 400 }
      );
    }

    const taskMasterService = new TaskMasterService();

    // Universal comment access: Allow ALL authenticated users to view comments
    // Check if project exists (but don't restrict access to team members only)
    const project = await taskMasterService.getProjectById(projectId);

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Parse pagination parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Validate pagination parameters
    if (limit < 1 || limit > 50) {
      return NextResponse.json(
        { error: 'Limit must be between 1 and 50' },
        { status: 400 }
      );
    }

    if (offset < 0) {
      return NextResponse.json(
        { error: 'Offset must be non-negative' },
        { status: 400 }
      );
    }

    // Get paginated comments for the project
    const comments = taskMasterService.getProjectComments(projectId, {
      limit,
      offset
    });

    return NextResponse.json({
      success: true,
      data: {
        comments,
        pagination: {
          limit,
          offset,
          hasMore: comments.length === limit
        }
      }
    });

  } catch (error) {
    // Log error for debugging but don't expose internal details
    console.error('Get comments error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve comments'
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { id } = await params;
    const projectId = parseInt(id);
    
    if (isNaN(projectId)) {
      return NextResponse.json(
        { error: 'Invalid project ID' },
        { status: 400 }
      );
    }
    
    const { content, parent_comment_id } = await request.json();
    
    // Validate content
    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      return NextResponse.json(
        { error: 'Comment content is required' },
        { status: 400 }
      );
    }
    
    if (content.length > 2000) {
      return NextResponse.json(
        { error: 'Comment content must be less than 2000 characters' },
        { status: 400 }
      );
    }
    
    // Validate parent_comment_id if provided
    if (parent_comment_id !== undefined && parent_comment_id !== null) {
      const parentId = parseInt(parent_comment_id);
      if (isNaN(parentId)) {
        return NextResponse.json(
          { error: 'Invalid parent comment ID' },
          { status: 400 }
        );
      }
    }
    
    const taskMasterService = new TaskMasterService();

    // Universal comment access: Allow ALL authenticated users to comment on projects
    // Check if project exists (but don't restrict access to team members only)
    const project = await taskMasterService.getProjectById(projectId);

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }
    
    // Add comment
    const comment = await taskMasterService.addComment(
      projectId,
      user,
      content.trim(),
      parent_comment_id ? parseInt(parent_comment_id) : undefined
    );
    
    if (comment) {
      return NextResponse.json({
        success: true,
        data: { comment },
        message: 'Comment added successfully'
      }, { status: 201 });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to add comment' },
        { status: 500 }
      );
    }
    
  } catch (error) {
    // Log error for debugging but don't expose internal details
    console.error('Add comment error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to add comment'
      },
      { status: 500 }
    );
  }
}
