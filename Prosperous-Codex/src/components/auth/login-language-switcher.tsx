"use client";

import { useState, useEffect, useMemo } from 'react';
import { useRouter, usePathname } from '@/i18n/navigation';
import { Globe } from 'lucide-react';

interface LoginLanguageSwitcherProps {
  currentLocale: string;
}

export function LoginLanguageSwitcher({ currentLocale }: LoginLanguageSwitcherProps) {
  const router = useRouter();
  const pathname = usePathname();
  
  const languages = useMemo(() => [
    { code: 'en', label: 'English', flag: '🇺🇸' },
    { code: 'zh-cn', label: '简体中文', flag: '🇨🇳' },
    { code: 'zh-tw', label: '繁體中文', flag: '🇹🇼' }
  ], []);

  const [currentLanguageIndex, setCurrentLanguageIndex] = useState(0);

  // Initialize current language index based on locale
  useEffect(() => {
    const index = languages.findIndex(lang => lang.code === currentLocale);
    setCurrentLanguageIndex(index >= 0 ? index : 0);
  }, [currentLocale, languages]);

  // Load saved language preference from localStorage on mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem('login-language-preference');
    if (savedLanguage) {
      const index = languages.findIndex(lang => lang.code === savedLanguage);
      if (index >= 0 && index !== currentLanguageIndex) {
        // Navigate to the saved language if different from current
        router.replace(pathname, { locale: savedLanguage });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run on mount

  const handleLanguageSwitch = () => {
    const nextIndex = (currentLanguageIndex + 1) % languages.length;
    const nextLanguage = languages[nextIndex];
    
    // Save preference to localStorage
    localStorage.setItem('login-language-preference', nextLanguage.code);
    
    // Update state
    setCurrentLanguageIndex(nextIndex);
    
    // Navigate to new locale
    router.replace(pathname, { locale: nextLanguage.code });
  };

  const currentLanguage = languages[currentLanguageIndex];

  return (
    <button
      onClick={handleLanguageSwitch}
      className="fixed bottom-6 right-6 z-50 flex items-center gap-2 px-3 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white/80 hover:text-white hover:bg-white/20 transition-all duration-200 text-sm font-medium shadow-lg"
      title={`Switch to next language (Current: ${currentLanguage.label})`}
    >
      <Globe className="w-4 h-4" />
      <span className="flex items-center gap-1">
        <span className="text-base">{currentLanguage.flag}</span>
        <span className="hidden sm:inline">{currentLanguage.label}</span>
      </span>
    </button>
  );
}
