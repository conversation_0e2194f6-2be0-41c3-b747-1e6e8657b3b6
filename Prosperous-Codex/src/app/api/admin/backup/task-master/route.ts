import { NextRequest, NextResponse } from 'next/server';
import { requireRole } from '@/lib/auth/middleware';
import { existsSync, mkdirSync, writeFileSync, statSync, readdirSync, unlinkSync } from 'fs';
import { join } from 'path';
import { getDatabase } from '@/lib/database/database';
import Database from 'better-sqlite3';

/**
 * Create Task Master selective backup
 */
export async function POST(request: NextRequest) {
  try {
    // Require admin or moderator role
    const authResult = await requireRole(request, 'moderator');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;
    
    // Create Task Master backup
    const backupResult = await createTaskMasterBackup(user.id);
    
    if (backupResult.success) {
      return NextResponse.json({
        message: 'Task Master backup created successfully',
        backupName: backupResult.backupName,
        backupPath: backupResult.backupPath,
        size: backupResult.size,
        recordCount: backupResult.recordCount
      });
    } else {
      return NextResponse.json(
        { error: backupResult.error || 'Failed to create Task Master backup' },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Task Master backup creation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * List available Task Master backups
 */
export async function GET(request: NextRequest) {
  try {
    // Require admin or moderator role
    const authResult = await requireRole(request, 'moderator');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const backups = await listTaskMasterBackups();
    
    return NextResponse.json({
      backups,
      count: backups.length
    });
    
  } catch (error) {
    console.error('List Task Master backups error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Create a Task Master selective backup
 */
export async function createTaskMasterBackup(userId: string) {
  try {
    const backupDir = join(process.cwd(), 'data', 'backups', 'task-master');
    
    // Ensure backup directory exists
    if (!existsSync(backupDir)) {
      mkdirSync(backupDir, { recursive: true });
    }
    
    // Get database instance
    const db = getDatabase();
    
    // Extract Task Master data with relationships
    const backupData = await extractTaskMasterData(db);
    
    // Generate backup filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `task-master-backup-${timestamp}.json`;
    const backupPath = join(backupDir, backupName);
    
    // Create structured backup object
    const structuredBackup = {
      metadata: {
        backupType: 'task-master-selective',
        createdAt: new Date().toISOString(),
        createdBy: userId,
        version: '1.0.0',
        description: 'Task Master selective backup including projects, comments, activities, and user relationships'
      },
      statistics: {
        totalProjects: backupData.projects.length,
        totalComments: backupData.comments.length,
        totalActivities: backupData.activities.length,
        totalUsers: backupData.users.length
      },
      data: backupData
    };
    
    // Write backup file
    writeFileSync(backupPath, JSON.stringify(structuredBackup, null, 2), 'utf-8');
    
    // Get file size
    const stats = statSync(backupPath);
    const size = stats.size;
    
    console.log(`✅ Task Master backup created: ${backupName} (${(size / 1024).toFixed(2)} KB)`);
    
    return {
      success: true,
      backupName,
      backupPath,
      size,
      recordCount: backupData.projects.length + backupData.comments.length + backupData.activities.length
    };
    
  } catch (error) {
    console.error('❌ Task Master backup failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Extract Task Master data with relationships
 */
async function extractTaskMasterData(db: Database.Database) {
  try {
    // Get all projects with creator information
    const projects = db.prepare(`
      SELECT 
        p.*,
        creator.username as creator_username,
        creator.email as creator_email,
        assignee.username as assignee_username,
        assignee.email as assignee_email
      FROM projects p
      LEFT JOIN users creator ON p.created_by = creator.id
      LEFT JOIN users assignee ON p.assigned_to = assignee.id
      ORDER BY p.created_at DESC
    `).all();

    // Get all comments with author information
    const comments = db.prepare(`
      SELECT 
        pc.*,
        u.username as author_username,
        u.email as author_email,
        p.title as project_title
      FROM project_comments pc
      LEFT JOIN users u ON pc.author_id = u.id
      LEFT JOIN projects p ON pc.project_id = p.id
      ORDER BY pc.created_at DESC
    `).all();

    // Get all activity logs with user information
    const activities = db.prepare(`
      SELECT 
        al.*,
        u.username as user_username,
        u.email as user_email,
        p.title as project_title
      FROM activity_log al
      LEFT JOIN users u ON al.user_id = u.id
      LEFT JOIN projects p ON al.project_id = p.id
      ORDER BY al.created_at DESC
    `).all();

    // Get relevant users (those involved in Task Master)
    const users = db.prepare(`
      SELECT DISTINCT
        u.id,
        u.username,
        u.email,
        u.role,
        u.created_at
      FROM users u
      WHERE u.id IN (
        SELECT DISTINCT created_by FROM projects
        UNION
        SELECT DISTINCT assigned_to FROM projects WHERE assigned_to IS NOT NULL
        UNION
        SELECT DISTINCT author_id FROM project_comments
        UNION
        SELECT DISTINCT user_id FROM activity_log
      )
      ORDER BY u.username
    `).all();

    return {
      projects,
      comments,
      activities,
      users
    };
    
  } catch (error) {
    console.error('Error extracting Task Master data:', error);
    throw error;
  }
}

/**
 * List all available Task Master backups
 */
export async function listTaskMasterBackups() {
  try {
    const backupDir = join(process.cwd(), 'data', 'backups', 'task-master');
    
    if (!existsSync(backupDir)) {
      return [];
    }
    
    const files = readdirSync(backupDir);
    
    const backups = files
      .filter(file => file.endsWith('.json') && file.startsWith('task-master-backup-'))
      .map(file => {
        const filePath = join(backupDir, file);
        const stats = statSync(filePath);
        
        return {
          name: file,
          path: filePath,
          size: stats.size,
          created: stats.birthtime,
          type: 'task-master-selective',
          sizeFormatted: `${(stats.size / 1024).toFixed(2)} KB`
        };
      })
      .sort((a, b) => b.created.getTime() - a.created.getTime()); // Most recent first
    
    return backups;
    
  } catch (error) {
    console.error('Error listing Task Master backups:', error);
    return [];
  }
}

/**
 * Clean up old Task Master backups (keep last 5)
 */
export async function cleanupOldTaskMasterBackups() {
  try {
    const backups = await listTaskMasterBackups();
    
    // Keep last 5 Task Master backups, delete older ones
    if (backups.length > 5) {
      const toDelete = backups.slice(5);
      let deletedCount = 0;
      
      for (const backup of toDelete) {
        try {
          unlinkSync(backup.path);
          deletedCount++;
          console.log(`🗑️ Deleted old Task Master backup: ${backup.name}`);
        } catch (error) {
          console.error(`Failed to delete Task Master backup ${backup.name}:`, error);
        }
      }
      
      console.log(`✅ Cleaned up ${deletedCount} old Task Master backups`);
      return deletedCount;
    }
    
    return 0;
    
  } catch (error) {
    console.error('Error cleaning up Task Master backups:', error);
    return 0;
  }
}
