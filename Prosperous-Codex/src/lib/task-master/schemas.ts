/**
 * Shared validation schemas for Task Master system
 * Based on Zod best practices for TypeScript-first validation
 */

import { z } from 'zod';

/**
 * Common validation patterns
 */
export const CommonSchemas = {
  // Positive integer ID validation
  positiveId: z.number().int().positive('ID must be a positive integer'),
  
  // Non-empty string validation
  nonEmptyString: z.string().min(1, 'This field is required').trim(),
  
  // Optional non-empty string
  optionalNonEmptyString: z.string().trim().optional(),
  
  // Email validation
  email: z.string().email('Please enter a valid email address'),
  
  // Date string validation (ISO format)
  dateString: z.string().datetime('Invalid date format').optional(),
  
  // Progress percentage (0-100)
  progress: z.number().int().min(0).max(100, 'Progress must be between 0 and 100'),
  
  // File size validation (max 10MB)
  fileSize: z.number().max(10 * 1024 * 1024, 'File size must be less than 10MB'),
  
  // Comment content validation
  commentContent: z.string().min(1, 'Comment cannot be empty').max(2000, 'Comment must be less than 2000 characters').trim(),
};

/**
 * Enum validations
 */
export const EnumSchemas = {
  status: z.enum(['todo', 'inProgress', 'completed'], {
    errorMap: () => ({ message: 'Status must be todo, inProgress, or completed' })
  }),

  priority: z.enum(['low', 'medium', 'high'], {
    errorMap: () => ({ message: 'Priority must be low, medium, or high' })
  }),

  visibility: z.enum(['public', 'private'], {
    errorMap: () => ({ message: 'Visibility must be public or private' })
  }),

  role: z.enum(['member', 'admin', 'viewer'], {
    errorMap: () => ({ message: 'Role must be member, admin, or viewer' })
  }),

  activityType: z.enum([
    'upload', 'comment', 'comment_edit', 'comment_delete', 'status_change', 'assignment',
    'due_date', 'completion', 'creation', 'update',
    'task_creation', 'task_completion'
  ]),
};

/**
 * Project validation schemas
 */
export const ProjectSchemas = {
  // Create project request (API layer - camelCase only)
  create: z.object({
    title: CommonSchemas.nonEmptyString.max(200, 'Title must be less than 200 characters'),
    description: z.string().trim().max(2000, 'Description must be less than 2000 characters').optional(),
    fullDescription: z.string().max(50000, 'Full description must be less than 50000 characters').optional(),
    eventLog: z.string().max(100000, 'Event log must be less than 100000 characters').optional(),
    status: EnumSchemas.status.default('todo'),
    priority: EnumSchemas.priority.default('medium'),
    progress: CommonSchemas.progress.default(0),
    dueDate: CommonSchemas.dateString.optional(),
    assignedTo: CommonSchemas.positiveId.optional(),
    visibility: EnumSchemas.visibility.default('public'),
    tags: z.array(z.string().trim().min(1).max(50)).max(10, 'Maximum 10 tags allowed').default([]),
  }),

  // Update project request (API layer - camelCase only)
  update: z.object({
    title: CommonSchemas.nonEmptyString.max(200, 'Title must be less than 200 characters').optional(),
    description: z.string().trim().max(2000, 'Description must be less than 2000 characters').optional(),
    fullDescription: z.string().max(50000, 'Full description must be less than 50000 characters').optional(),
    eventLog: z.string().max(100000, 'Event log must be less than 100000 characters').optional(),
    status: EnumSchemas.status.optional(),
    priority: EnumSchemas.priority.optional(),
    progress: CommonSchemas.progress.optional(),
    dueDate: CommonSchemas.dateString.optional(),
    assignedTo: CommonSchemas.positiveId.optional(),
    completedDate: CommonSchemas.dateString.optional(),
    visibility: EnumSchemas.visibility.optional(),
  }),

  // Project ID parameter
  idParam: z.object({
    id: z.string().transform((val, ctx) => {
      const parsed = parseInt(val);
      if (isNaN(parsed) || parsed <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Project ID must be a positive integer',
        });
        return z.NEVER;
      }
      return parsed;
    }),
  }),
};

/**
 * Task validation schemas
 */
export const TaskSchemas = {
  // Create task request (API layer - camelCase only)
  create: z.object({
    title: CommonSchemas.nonEmptyString.max(200, 'Title must be less than 200 characters'),
    description: z.string().trim().max(10000, 'Description must be less than 10000 characters').optional(),
    status: EnumSchemas.status.default('todo'),
    priority: EnumSchemas.priority.default('medium'),
    dueDate: CommonSchemas.dateString.optional(),
    assignedTo: CommonSchemas.positiveId.optional(),
    parentTaskId: CommonSchemas.positiveId.optional(),
  }),

  // Update task request (API layer - camelCase only)
  update: z.object({
    title: CommonSchemas.nonEmptyString.max(200, 'Title must be less than 200 characters').optional(),
    description: z.string().trim().max(10000, 'Description must be less than 10000 characters').optional(),
    status: EnumSchemas.status.optional(),
    priority: EnumSchemas.priority.optional(),
    dueDate: CommonSchemas.dateString.optional(),
    assignedTo: CommonSchemas.positiveId.optional(),
    completedDate: CommonSchemas.dateString.optional(),
  }),

  // Task creation with project ID (API layer - camelCase only)
  createWithProject: z.object({
    projectId: CommonSchemas.positiveId,
    title: CommonSchemas.nonEmptyString.max(200, 'Title must be less than 200 characters'),
    description: z.string().trim().max(1000, 'Description must be less than 1000 characters').optional(),
    status: EnumSchemas.status.default('todo'),
    priority: EnumSchemas.priority.default('medium'),
    dueDate: CommonSchemas.dateString.optional(),
    assignedTo: CommonSchemas.positiveId.optional(),
    parentTaskId: CommonSchemas.positiveId.optional(),
  }),

  // Task ID parameter
  idParam: z.object({
    taskId: z.string().transform((val, ctx) => {
      const parsed = parseInt(val);
      if (isNaN(parsed) || parsed <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Task ID must be a positive integer',
        });
        return z.NEVER;
      }
      return parsed;
    }),
  }),
};

/**
 * Comment validation schemas
 */
export const CommentSchemas = {
  // Create comment request (API layer - camelCase only)
  create: z.object({
    content: CommonSchemas.commentContent,
    parentCommentId: CommonSchemas.positiveId.optional(),
  }),

  // Update comment request
  update: z.object({
    content: CommonSchemas.commentContent,
  }),

  // Comment ID parameter
  idParam: z.object({
    commentId: z.string().transform((val, ctx) => {
      const parsed = parseInt(val);
      if (isNaN(parsed) || parsed <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Comment ID must be a positive integer',
        });
        return z.NEVER;
      }
      return parsed;
    }),
  }),
};

/**
 * File validation schemas
 */
export const FileSchemas = {
  // File upload validation
  upload: z.object({
    file: z.instanceof(File, { message: 'File is required' })
      .refine((file) => file.size <= 10 * 1024 * 1024, 'File size must be less than 10MB')
      .refine((file) => file.name.length > 0, 'File must have a name'),
  }),

  // File ID parameter
  idParam: z.object({
    fileId: z.string().transform((val, ctx) => {
      const parsed = parseInt(val);
      if (isNaN(parsed) || parsed <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'File ID must be a positive integer',
        });
        return z.NEVER;
      }
      return parsed;
    }),
  }),
};

/**
 * Team member validation schemas
 */
export const TeamMemberSchemas = {
  // Add team member request
  add: z.object({
    email: CommonSchemas.email,
    role: EnumSchemas.role.default('member'),
  }),

  // Member ID parameter
  idParam: z.object({
    memberId: z.string().transform((val, ctx) => {
      const parsed = parseInt(val);
      if (isNaN(parsed) || parsed <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Member ID must be a positive integer',
        });
        return z.NEVER;
      }
      return parsed;
    }),
  }),
};

/**
 * Query parameter schemas
 */
export const QuerySchemas = {
  // Project ID query parameter - supports both camelCase and snake_case
  projectId: z.object({
    project_id: z.string().transform((val, ctx) => {
      const parsed = parseInt(val);
      if (isNaN(parsed) || parsed <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Project ID must be a positive integer',
        });
        return z.NEVER;
      }
      return parsed;
    }).optional(),
    projectId: z.string().transform((val, ctx) => {
      const parsed = parseInt(val);
      if (isNaN(parsed) || parsed <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Project ID must be a positive integer',
        });
        return z.NEVER;
      }
      return parsed;
    }).optional(),
  }).refine(data => data.project_id || data.projectId, {
    message: "Either project_id or projectId is required"
  }),

  // Pagination parameters
  pagination: z.object({
    page: z.string().transform((val) => Math.max(1, parseInt(val) || 1)).optional(),
    limit: z.string().transform((val) => Math.min(100, Math.max(1, parseInt(val) || 10))).optional(),
  }),

  // Activity query parameters
  activity: z.object({
    project_id: z.string().transform((val, ctx) => {
      const parsed = parseInt(val);
      if (isNaN(parsed) || parsed <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Project ID must be a positive integer',
        });
        return z.NEVER;
      }
      return parsed;
    }).optional(),
    limit: z.string().transform((val) => Math.min(100, Math.max(1, parseInt(val) || 50))).optional(),
    offset: z.string().transform((val) => Math.max(0, parseInt(val) || 0)).optional(),
  }),

  // Task listing query parameters - supports both camelCase and snake_case
  taskListing: z.object({
    project_id: z.string().transform((val, ctx) => {
      const parsed = parseInt(val);
      if (isNaN(parsed) || parsed <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Project ID must be a positive integer',
        });
        return z.NEVER;
      }
      return parsed;
    }).optional(),
    projectId: z.string().transform((val, ctx) => {
      const parsed = parseInt(val);
      if (isNaN(parsed) || parsed <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Project ID must be a positive integer',
        });
        return z.NEVER;
      }
      return parsed;
    }).optional(),
  }).refine(data => data.project_id || data.projectId, {
    message: "Either project_id or projectId is required"
  }),
};

/**
 * Type inference helpers
 */
export type CreateProjectData = z.infer<typeof ProjectSchemas.create>;
export type UpdateProjectData = z.infer<typeof ProjectSchemas.update>;
export type CreateTaskData = z.infer<typeof TaskSchemas.create>;
export type UpdateTaskData = z.infer<typeof TaskSchemas.update>;
export type CreateCommentData = z.infer<typeof CommentSchemas.create>;
export type UpdateCommentData = z.infer<typeof CommentSchemas.update>;
export type AddTeamMemberData = z.infer<typeof TeamMemberSchemas.add>;

/**
 * Validation helper functions
 */
export const ValidationHelpers = {
  /**
   * Safely parse and validate data with a schema
   */
  safeParse: <T>(schema: z.ZodSchema<T>, data: unknown) => {
    const result = schema.safeParse(data);
    if (!result.success) {
      const errors = result.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
      }));
      return { success: false, errors };
    }
    return { success: true, data: result.data };
  },

  /**
   * Parse data and throw validation error if invalid
   */
  parseOrThrow: <T>(schema: z.ZodSchema<T>, data: unknown) => {
    return schema.parse(data);
  },

  /**
   * Validate form data from FormData object
   */
  parseFormData: (formData: FormData, schema: z.ZodSchema) => {
    const data: Record<string, any> = {};
    
    for (const [key, value] of formData.entries()) {
      if (value instanceof File) {
        data[key] = value;
      } else {
        // Handle multiple values for the same key
        if (data[key]) {
          if (Array.isArray(data[key])) {
            data[key].push(value);
          } else {
            data[key] = [data[key], value];
          }
        } else {
          data[key] = value;
        }
      }
    }

    return schema.parse(data);
  },
};
