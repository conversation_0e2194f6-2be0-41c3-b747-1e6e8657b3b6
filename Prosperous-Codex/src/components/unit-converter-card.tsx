"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Calculator, Maximize2, RotateCcw } from "lucide-react";
import ExpandedUnitConverter from "./expanded-unit-converter";

interface UnitConverterCardProps {
  className?: string;
}

// Paper weight conversion factors (Text vs Cover paper)
const PAPER_WEIGHT_FACTORS = {
  text: {
    lbsToGsm: 1.48,
    gsmToLbs: 0.675
  },
  cover: {
    lbsToGsm: 2.7,
    gsmToLbs: 0.37
  }
};

// Thickness conversion factors
const THICKNESS_FACTORS = {
  ptToMicrons: 25.4,
  micronsToMm: 0.001,
  mmToInches: 0.0393701,
  inchesToMm: 25.4
};

export default function UnitConverterCard({ className }: UnitConverterCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [paperWeightType, setPaperWeightType] = useState<'text' | 'cover'>('text');

  // Card view states
  const [cardLbs, setCardLbs] = useState('');
  const [cardGsm, setCardGsm] = useState('');
  const [cardPt, setCardPt] = useState('');
  const [cardMicrons, setCardMicrons] = useState('');
  const [cardInches, setCardInches] = useState('');
  const [cardMm, setCardMm] = useState('');

  // Expanded view states
  const [expandedInput, setExpandedInput] = useState('');
  const [expandedFromUnit, setExpandedFromUnit] = useState('mm');
  const [expandedCategory, setExpandedCategory] = useState('length');

  const cardRef = useRef<HTMLDivElement>(null);

  // Track which field was last edited to prevent infinite loops
  const [lastEditedWeight, setLastEditedWeight] = useState<'lbs' | 'gsm' | null>(null);
  const [lastEditedThickness, setLastEditedThickness] = useState<'pt' | 'microns' | 'inches' | 'mm' | null>(null);

  // Handle paper weight conversions for card view
  useEffect(() => {
    if (lastEditedWeight === 'lbs' && cardLbs && !isNaN(Number(cardLbs)) && Number(cardLbs) > 0) {
      const factor = PAPER_WEIGHT_FACTORS[paperWeightType];
      const gsm = Number(cardLbs) * factor.lbsToGsm;
      setCardGsm(gsm.toFixed(2));
    } else if (cardLbs === '') {
      setCardGsm('');
    }
  }, [cardLbs, paperWeightType, lastEditedWeight]);

  useEffect(() => {
    if (lastEditedWeight === 'gsm' && cardGsm && !isNaN(Number(cardGsm)) && Number(cardGsm) > 0) {
      const factor = PAPER_WEIGHT_FACTORS[paperWeightType];
      const lbs = Number(cardGsm) * factor.gsmToLbs;
      setCardLbs(lbs.toFixed(2));
    } else if (cardGsm === '') {
      setCardLbs('');
    }
  }, [cardGsm, paperWeightType, lastEditedWeight]);

  // Handle thickness conversions for card view with debouncing to prevent infinite loops
  useEffect(() => {
    if (lastEditedThickness === 'pt' && cardPt && !isNaN(Number(cardPt)) && Number(cardPt) > 0) {
      const microns = Number(cardPt) * THICKNESS_FACTORS.ptToMicrons;
      setCardMicrons(microns.toFixed(0));
    } else if (cardPt === '') {
      setCardMicrons('');
    }
  }, [cardPt, lastEditedThickness]);

  useEffect(() => {
    if (lastEditedThickness === 'microns' && cardMicrons && !isNaN(Number(cardMicrons)) && Number(cardMicrons) > 0) {
      const pt = Number(cardMicrons) / THICKNESS_FACTORS.ptToMicrons;
      setCardPt(pt.toFixed(2));
    } else if (cardMicrons === '') {
      setCardPt('');
    }
  }, [cardMicrons, lastEditedThickness]);

  // Simple conversion without infinite loops - only convert when user explicitly edits a field
  useEffect(() => {
    if (lastEditedThickness === 'inches' && cardInches && !isNaN(Number(cardInches)) && Number(cardInches) > 0) {
      const mm = Number(cardInches) * THICKNESS_FACTORS.inchesToMm;
      setCardMm(mm.toFixed(2));
    } else if (cardInches === '') {
      setCardMm('');
    }
  }, [cardInches, lastEditedThickness]);

  useEffect(() => {
    if (lastEditedThickness === 'mm' && cardMm && !isNaN(Number(cardMm)) && Number(cardMm) > 0) {
      const inches = Number(cardMm) * THICKNESS_FACTORS.mmToInches;
      setCardInches(inches.toFixed(4));
    } else if (cardMm === '') {
      setCardInches('');
    }
  }, [cardMm, lastEditedThickness]);

  const handleExpand = () => {
    setIsExpanded(true);
  };

  const handleCollapse = () => {
    setIsExpanded(false);
  };

  const clearAllFields = () => {
    setCardLbs('');
    setCardGsm('');
    setCardPt('');
    setCardMicrons('');
    setCardInches('');
    setCardMm('');
    setLastEditedWeight(null);
    setLastEditedThickness(null);
  };

  // Input validation function
  const validateNumberInput = (value: string) => {
    // Allow empty string, numbers, decimal points, and negative signs
    return value === '' || /^-?\d*\.?\d*$/.test(value);
  };

  if (isExpanded) {
    return <ExpandedUnitConverter onClose={handleCollapse} />;
  }

  return (
    <Card ref={cardRef} className={className} data-glow>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center justify-between">
          <div className="flex items-center">
            <Calculator className="w-5 h-5 mr-2 text-[#5E6AD2] dark:text-[#6E56CF]" />
            Unit Converter
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFields}
              className="h-8 w-8 p-0"
              title="Clear all fields"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleExpand}
              className="h-8 w-8 p-0"
              title="Expand converter"
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Paper Weight Type Selection */}
        <div>
          <Label className="text-sm font-medium text-gray-600 dark:text-neutral-300 mb-2 block">
            Paper Weight Type
          </Label>
          <div className="flex space-x-4">
            <button
              type="button"
              className={`inline-flex items-center px-3 py-1 rounded-md text-sm transition-colors ${
                paperWeightType === 'text'
                  ? 'bg-[#5E6AD2]/10 text-[#5E6AD2] dark:bg-[#6E56CF]/10 dark:text-[#6E56CF]'
                  : 'text-gray-700 dark:text-neutral-300 hover:bg-gray-100 dark:hover:bg-neutral-700'
              }`}
              onClick={() => setPaperWeightType('text')}
            >
              <div className={`w-3 h-3 rounded-full border-2 mr-2 ${
                paperWeightType === 'text'
                  ? 'border-[#5E6AD2] bg-[#5E6AD2] dark:border-[#6E56CF] dark:bg-[#6E56CF]'
                  : 'border-gray-400'
              }`}>
                {paperWeightType === 'text' && (
                  <div className="w-full h-full rounded-full bg-white scale-50"></div>
                )}
              </div>
              Text
            </button>
            <button
              type="button"
              className={`inline-flex items-center px-3 py-1 rounded-md text-sm transition-colors ${
                paperWeightType === 'cover'
                  ? 'bg-[#5E6AD2]/10 text-[#5E6AD2] dark:bg-[#6E56CF]/10 dark:text-[#6E56CF]'
                  : 'text-gray-700 dark:text-neutral-300 hover:bg-gray-100 dark:hover:bg-neutral-700'
              }`}
              onClick={() => setPaperWeightType('cover')}
            >
              <div className={`w-3 h-3 rounded-full border-2 mr-2 ${
                paperWeightType === 'cover'
                  ? 'border-[#5E6AD2] bg-[#5E6AD2] dark:border-[#6E56CF] dark:bg-[#6E56CF]'
                  : 'border-gray-400'
              }`}>
                {paperWeightType === 'cover' && (
                  <div className="w-full h-full rounded-full bg-white scale-50"></div>
                )}
              </div>
              Cover
            </button>
          </div>
        </div>

        {/* Paper Weight Conversion */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="card-lbs" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                Pounds
              </Label>
              <span className="text-xs text-gray-500 dark:text-neutral-400 font-medium">
                lbs
              </span>
            </div>
            <Input
              id="card-lbs"
              type="number"
              placeholder="0.00"
              value={cardLbs}
              onChange={(e) => {
                if (validateNumberInput(e.target.value)) {
                  setCardLbs(e.target.value);
                  setLastEditedWeight('lbs');
                }
              }}
              className="h-9 text-sm"
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="card-gsm" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                GSM
              </Label>
              <span className="text-xs text-gray-500 dark:text-neutral-400 font-medium">
                g/m²
              </span>
            </div>
            <Input
              id="card-gsm"
              type="number"
              placeholder="0.00"
              value={cardGsm}
              onChange={(e) => {
                if (validateNumberInput(e.target.value)) {
                  setCardGsm(e.target.value);
                  setLastEditedWeight('gsm');
                }
              }}
              className="h-9 text-sm"
            />
          </div>
        </div>

        {/* Thickness Conversion */}
        <div>
          <Label className="text-sm font-medium text-gray-600 dark:text-neutral-300 mb-2 block">
            Thickness
          </Label>
          <div className="grid grid-cols-2 gap-3 mb-2">
            <div>
              <div className="flex items-center justify-between mb-1">
                <Label htmlFor="card-pt" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                  Points
                </Label>
                <span className="text-xs text-gray-500 dark:text-neutral-400 font-medium">
                  pt
                </span>
              </div>
              <Input
                id="card-pt"
                type="number"
                placeholder="0.00"
                value={cardPt}
                onChange={(e) => {
                  if (validateNumberInput(e.target.value)) {
                    setCardPt(e.target.value);
                    setLastEditedThickness('pt');
                  }
                }}
                className="h-9 text-sm"
              />
            </div>
            <div>
              <div className="flex items-center justify-between mb-1">
                <Label htmlFor="card-microns" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                  Microns
                </Label>
                <span className="text-xs text-gray-500 dark:text-neutral-400 font-medium">
                  µm
                </span>
              </div>
              <Input
                id="card-microns"
                type="number"
                placeholder="0.00"
                value={cardMicrons}
                onChange={(e) => {
                  if (validateNumberInput(e.target.value)) {
                    setCardMicrons(e.target.value);
                    setLastEditedThickness('microns');
                  }
                }}
                className="h-9 text-sm"
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <div className="flex items-center justify-between mb-1">
                <Label htmlFor="card-inches" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                  Inches
                </Label>
                <span className="text-xs text-gray-500 dark:text-neutral-400 font-medium">
                  in
                </span>
              </div>
              <Input
                id="card-inches"
                type="number"
                placeholder="0.00"
                value={cardInches}
                onChange={(e) => {
                  if (validateNumberInput(e.target.value)) {
                    setCardInches(e.target.value);
                    setLastEditedThickness('inches');
                  }
                }}
                className="h-9 text-sm"
              />
            </div>
            <div>
              <div className="flex items-center justify-between mb-1">
                <Label htmlFor="card-mm" className="text-sm font-medium text-gray-700 dark:text-neutral-300">
                  Millimeters
                </Label>
                <span className="text-xs text-gray-500 dark:text-neutral-400 font-medium">
                  mm
                </span>
              </div>
              <Input
                id="card-mm"
                type="number"
                placeholder="0.00"
                value={cardMm}
                onChange={(e) => {
                  if (validateNumberInput(e.target.value)) {
                    setCardMm(e.target.value);
                    setLastEditedThickness('mm');
                  }
                }}
                className="h-9 text-sm"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
