import {NextConfig} from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig: NextConfig = {
  eslint: {
    // Disable ESLint during builds - linting issues should be fixed separately
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Disable TypeScript errors during builds for development
    ignoreBuildErrors: true,
  },
};

export default withNextIntl(nextConfig);
