/**
 * Validation middleware for Task Master API routes
 * Provides reusable validation functions for Next.js API routes
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { ValidationError, isTaskMasterError } from './errors';
import { AuthorizationService, Action, Resource, Scope } from './authorization';
import { User } from '@/lib/types/auth';

/**
 * Validation result types
 */
export interface ValidationSuccess<T> {
  success: true;
  data: T;
}

export interface ValidationFailure {
  success: false;
  error: ValidationError;
  response: NextResponse;
}

export type ValidationResult<T> = ValidationSuccess<T> | ValidationFailure;

/**
 * Validate request body against a Zod schema
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<ValidationResult<T>> {
  try {
    const body = await request.json();
    const result = schema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => ({
        field: err.path.join('.') || 'root',
        message: err.message,
        code: err.code,
      }));

      const validationError = new ValidationError(
        `Validation failed: ${errors.map(e => e.message).join(', ')}`,
        {
          context: { errors, body }
        }
      );

      return {
        success: false,
        error: validationError,
        response: NextResponse.json(
          {
            error: 'Validation failed',
            details: errors,
          },
          { status: 400 }
        ),
      };
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    const validationError = new ValidationError(
      'Invalid JSON in request body',
      {
        cause: error instanceof Error ? error : undefined,
        context: { error: String(error) }
      }
    );

    return {
      success: false,
      error: validationError,
      response: NextResponse.json(
        {
          error: 'Invalid JSON in request body',
        },
        { status: 400 }
      ),
    };
  }
}

/**
 * Validate URL parameters against a Zod schema
 */
export function validateParams<T>(
  params: Record<string, string>,
  schema: z.ZodSchema<T>
): ValidationResult<T> {
  const result = schema.safeParse(params);

  if (!result.success) {
    const errors = result.error.errors.map(err => ({
      field: err.path.join('.') || 'root',
      message: err.message,
      code: err.code,
    }));

    const validationError = new ValidationError(
      `Parameter validation failed: ${errors.map(e => e.message).join(', ')}`,
      {
        context: { errors, params }
      }
    );

    return {
      success: false,
      error: validationError,
      response: NextResponse.json(
        {
          error: 'Invalid parameters',
          details: errors,
        },
        { status: 400 }
      ),
    };
  }

  return {
    success: true,
    data: result.data,
  };
}

/**
 * Validate query parameters against a Zod schema
 */
export function validateQuery<T>(
  searchParams: URLSearchParams,
  schema: z.ZodSchema<T>
): ValidationResult<T> {
  // Convert URLSearchParams to plain object
  const query: Record<string, string> = {};
  for (const [key, value] of searchParams.entries()) {
    query[key] = value;
  }

  const result = schema.safeParse(query);

  if (!result.success) {
    const errors = result.error.errors.map(err => ({
      field: err.path.join('.') || 'root',
      message: err.message,
      code: err.code,
    }));

    const validationError = new ValidationError(
      `Query validation failed: ${errors.map(e => e.message).join(', ')}`,
      {
        context: { errors, query }
      }
    );

    return {
      success: false,
      error: validationError,
      response: NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: errors,
        },
        { status: 400 }
      ),
    };
  }

  return {
    success: true,
    data: result.data,
  };
}

/**
 * Validate FormData against a Zod schema
 */
export async function validateFormData<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<ValidationResult<T>> {
  try {
    const formData = await request.formData();
    const data: Record<string, any> = {};

    // Convert FormData to object
    for (const [key, value] of formData.entries()) {
      if (value instanceof File) {
        data[key] = value;
      } else {
        // Handle multiple values for the same key
        if (data[key]) {
          if (Array.isArray(data[key])) {
            data[key].push(value);
          } else {
            data[key] = [data[key], value];
          }
        } else {
          data[key] = value;
        }
      }
    }

    const result = schema.safeParse(data);

    if (!result.success) {
      const errors = result.error.errors.map(err => ({
        field: err.path.join('.') || 'root',
        message: err.message,
        code: err.code,
      }));

      const validationError = new ValidationError(
        `Form validation failed: ${errors.map(e => e.message).join(', ')}`,
        {
          context: { errors, formData: Object.fromEntries(formData.entries()) }
        }
      );

      return {
        success: false,
        error: validationError,
        response: NextResponse.json(
          {
            error: 'Form validation failed',
            details: errors,
          },
          { status: 400 }
        ),
      };
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    const validationError = new ValidationError(
      'Invalid form data',
      {
        cause: error instanceof Error ? error : undefined,
        context: { error: String(error) }
      }
    );

    return {
      success: false,
      error: validationError,
      response: NextResponse.json(
        {
          error: 'Invalid form data',
        },
        { status: 400 }
      ),
    };
  }
}

/**
 * Higher-order function to create validated API route handlers
 */
export function withValidation<TBody = any, TParams = any, TQuery = any>(options: {
  body?: z.ZodSchema<TBody>;
  params?: z.ZodSchema<TParams>;
  query?: z.ZodSchema<TQuery>;
}) {
  return function <THandler extends (
    request: NextRequest,
    context: {
      params?: TParams;
      body?: TBody;
      query?: TQuery;
      rawParams?: Record<string, string>;
    }
  ) => Promise<NextResponse>>(handler: THandler) {
    return async function (
      request: NextRequest,
      routeContext: { params?: Promise<Record<string, string>> | Record<string, string> }
    ): Promise<NextResponse> {
      try {
        const context: {
          params?: TParams;
          body?: TBody;
          query?: TQuery;
          rawParams?: Record<string, string>;
        } = {};

        // Resolve params if it's a Promise (Next.js 15+ pattern)
        const rawParams = routeContext.params 
          ? (routeContext.params instanceof Promise ? await routeContext.params : routeContext.params)
          : {};
        
        context.rawParams = rawParams;

        // Validate params
        if (options.params && rawParams) {
          const paramsResult = validateParams(rawParams, options.params);
          if (!paramsResult.success) {
            return paramsResult.response;
          }
          context.params = paramsResult.data;
        }

        // Validate query
        if (options.query) {
          const { searchParams } = new URL(request.url);
          const queryResult = validateQuery(searchParams, options.query);
          if (!queryResult.success) {
            return queryResult.response;
          }
          context.query = queryResult.data;
        }

        // Validate body (only for methods that typically have a body)
        if (options.body && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
          const bodyResult = await validateRequestBody(request, options.body);
          if (!bodyResult.success) {
            return bodyResult.response;
          }
          context.body = bodyResult.data;
        }

        // Call the actual handler with validated data
        return await handler(request, context);
      } catch (error) {
        console.error('Validation middleware error:', error);

        // Handle TaskMaster errors
        if (isTaskMasterError(error)) {
          const statusCode = getStatusCodeForError(error);
          return NextResponse.json(
            {
              error: error.message,
              type: error.type,
              ...(error.context && { context: error.context }),
            },
            { status: statusCode }
          );
        }

        // Handle unexpected errors
        return NextResponse.json(
          {
            error: 'Internal server error',
          },
          { status: 500 }
        );
      }
    };
  };
}

/**
 * Map TaskMaster error types to HTTP status codes
 */
function getStatusCodeForError(error: { type?: string; [key: string]: unknown }): number {
  switch (error.type) {
    case 'validation-error':
      return 400;
    case 'authorization-error':
      return 403;
    case 'not-found-error':
      return 404;
    case 'business-logic-error':
      return 422;
    case 'file-operation-error':
      return 400;
    case 'database-error':
    default:
      return 500;
  }
}

/**
 * Utility function to create a simple validation response
 */
export function createValidationErrorResponse(message: string, details?: Record<string, unknown>): NextResponse {
  return NextResponse.json(
    {
      error: message,
      ...(details && { details }),
    },
    { status: 400 }
  );
}

/**
 * Authorization middleware functions
 * Note: Advanced authorization middleware functions will be implemented in Phase 4
 * to avoid circular dependencies. For now, authorization is handled directly in service methods.
 */
