// Re-export types from ai-turbo-service for consistency
export type { 
  TurboOptions, 
  TurboResult, 
  TurboMainTask, 
  TurboSubtask 
} from '@/lib/services/ai-turbo-service';

// Additional types for API requests and responses
export interface TurboRequest {
  inputText: string;
  context?: string;
  tone?: string;
  maxTokens?: number;
}

export interface TurboResponse {
  success: boolean;
  data?: TurboResult;
  error?: string;
}

// Validation schemas using Zod
import { z } from 'zod';

export const TurboSubtaskSchema = z.object({
  id: z.string().min(1, 'Subtask id is required'),
  description: z.string()
});

export const TurboMainTaskSchema = z.object({
  title: z.string().min(1, 'Task title is required'),
  description: z.string(),
  subtasks: z.array(TurboSubtaskSchema).default([])
});

export const TurboOptionsSchema = z.object({
  max_tokens: z.number().optional(),
  model: z.string().optional(),
  context: z.string().optional(),
  tone: z.string().optional()
});

export const TurboResultSchema = z.object({
  enhanced_text: z.string(),
  original_text: z.string(),
  enhancement_applied: z.boolean(),
  model_used: z.string().optional(),
  tokens_used: z.number().optional(),
  reasoning_content: z.string().optional(),
  title: z.string(),
  event_log: z.string().default(''),
  description: z.string(),
  tasks: z.array(TurboMainTaskSchema).default([])
});

export const TurboRequestSchema = z.object({
  inputText: z.string()
    .min(1, 'Input text is required')
    .max(65536, 'Input text too long (maximum 65,536 characters)'),
  context: z.string().optional(),
  tone: z.string().optional(),
  maxTokens: z.number()
    .min(1, 'Max tokens must be at least 1')
    .max(131072, 'Max tokens too high (maximum 131,072)')
    .optional()
});

// Frontend-specific types for UI state management
export interface TurboProcessingState {
  isProcessing: boolean;
  processingCardId: string | null;
  progressText: string;
  attempt: number;
}

export interface TurboError {
  type: 'rate_limit' | 'auth_error' | 'content_filter' | 'parse_error' | 'network_error' | 'unknown';
  message: string;
  retryable: boolean;
}

// Type guards for runtime validation
export function isTurboResult(obj: any): obj is TurboResult {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.enhanced_text === 'string' &&
    typeof obj.original_text === 'string' &&
    typeof obj.enhancement_applied === 'boolean' &&
    typeof obj.title === 'string' &&
    typeof obj.description === 'string' &&
    Array.isArray(obj.tasks)
  );
}

export function isTurboError(obj: any): obj is TurboError {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.type === 'string' &&
    typeof obj.message === 'string' &&
    typeof obj.retryable === 'boolean'
  );
}

// Utility types for API integration
export type TurboApiResponse = TurboResponse;
export type TurboApiRequest = TurboRequest;

// Status types for service availability
export interface TurboServiceStatus {
  available: boolean;
  configured: boolean;
  provider: 'openrouter' | 'openai' | 'none';
}
