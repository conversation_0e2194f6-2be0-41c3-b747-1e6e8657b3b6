"use client"; // This layout uses client-side hooks for auth checks

import type { ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { SidebarProvider, useSidebar } from '@/contexts/sidebar-context';
import { SelectedComponentsProvider, useSelectedComponents } from '@/contexts/selected-components-context';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { ProtectedHeader } from '@/components/layout/protected-header';
import { Sidebar } from '@/components/layout/sidebar';
import { Skeleton } from '@/components/ui/skeleton';
import SelectedComponentDrawer from '@/components/SelectedComponent';

function ProtectedLayoutContent({ children }: { children: ReactNode }) {
  const { data: session, status } = useSession();
  const { isOpen, isCollapsed, close, toggleCollapse } = useSidebar();
  const { selectedComponents, isDrawerOpen, setIsDrawerOpen, removeComponent } = useSelectedComponents();
  const router = useRouter();

  useEffect(() => {
    if (status === "unauthenticated") {
      router.replace('/auth/login');
    }
  }, [status, router]);

  if (status === "loading" || status === "unauthenticated") {
    // Show a loading skeleton while checking auth state or if not authenticated
    return (
      <div className="flex flex-col min-h-screen" suppressHydrationWarning>
        <Skeleton className="h-16 w-full" />
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="space-y-4">
            <Skeleton className="h-12 w-1/2" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      <div className="flex flex-1 bg-white dark:bg-neutral-900">
        {/* Desktop sidebar - collapsible */}
        <div className="hidden md:block">
          <Sidebar isOpen={true} onClose={() => {}} isCollapsed={isCollapsed} onToggleCollapse={toggleCollapse} />
        </div>

        {/* Mobile sidebar - overlay */}
        <div className="md:hidden">
          <Sidebar isOpen={isOpen} onClose={close} isCollapsed={false} onToggleCollapse={() => {}} />
        </div>

        {/* Main content */}
        <div className="flex flex-col flex-1 min-w-0">
          <ProtectedHeader />
          <main className="flex-grow p-6">
            {children}
          </main>
        </div>
      </div>
      
      {/* Full width footer */}
      <footer className="py-4 text-center text-sm text-gray-600 dark:text-neutral-400 border-t bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm w-full" suppressHydrationWarning>
        © 2025 Prosperous Codex. All rights reserved.
      </footer>
      
      {/* Selected Components Drawer - rendered at layout level to avoid stacking context issues */}
      <SelectedComponentDrawer
        open={isDrawerOpen}
        onOpenChange={setIsDrawerOpen}
        selectedComponents={selectedComponents}
        onRemoveComponent={removeComponent}
      />
    </div>
  );
}

export default function ProtectedLayout({ children }: { children: ReactNode }) {
  return (
    <SidebarProvider>
      <SelectedComponentsProvider>
        <ProtectedLayoutContent>{children}</ProtectedLayoutContent>
      </SelectedComponentsProvider>
    </SidebarProvider>
  );
}
