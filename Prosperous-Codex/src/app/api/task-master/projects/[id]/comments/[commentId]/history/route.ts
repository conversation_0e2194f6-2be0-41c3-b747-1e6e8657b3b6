import { NextRequest, NextResponse } from 'next/server';
import { TaskMasterService } from '@/lib/database/task-master-service';
import { requireAuth } from '@/lib/auth/middleware';
import { FieldMapper } from '@/lib/task-master/field-mapping';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; commentId: string } }
) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { id, commentId } = await params;
    const projectId = parseInt(id);
    const commentIdNum = parseInt(commentId);
    
    if (isNaN(projectId)) {
      return NextResponse.json(
        { error: 'Invalid project ID' },
        { status: 400 }
      );
    }
    
    if (isNaN(commentIdNum)) {
      return NextResponse.json(
        { error: 'Invalid comment ID' },
        { status: 400 }
      );
    }
    
    const taskMasterService = new TaskMasterService();

    // Universal comment access: Allow ALL authenticated users to view comment edit history
    // Check if project exists (but don't restrict access to team members only)
    const project = await taskMasterService.getProjectById(projectId);

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }
    
    // Get comment edit history
    const editHistory = await taskMasterService.getCommentEditHistory(commentIdNum, user);

    // Apply field mapping to convert to camelCase for API response
    const mappedHistory = editHistory.map(item => FieldMapper.dbToApi(item));

    return NextResponse.json({
      success: true,
      data: {
        history: mappedHistory,
        count: mappedHistory.length
      },
      message: 'Comment edit history retrieved successfully'
    });
    
  } catch (error) {
    console.error('Get comment edit history error:', error);
    
    // Handle specific error types
    if (error && typeof error === 'object' && 'type' in error) {
      switch (error.type) {
        case 'NotFoundError':
          return NextResponse.json(
            { error: 'Comment not found' },
            { status: 404 }
          );
        case 'AuthorizationError':
          return NextResponse.json(
            { error: 'Access denied' },
            { status: 403 }
          );
        default:
          return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
          );
      }
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
