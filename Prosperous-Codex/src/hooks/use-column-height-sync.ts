"use client";

import { useState, useEffect, useCallback, useRef } from 'react';

interface UseColumnHeightSyncOptions {
  minHeight?: number;
  debounceMs?: number;
}

export function useColumnHeightSync(options: UseColumnHeightSyncOptions = {}) {
  const { minHeight = 600, debounceMs = 100 } = options;
  
  const [synchronizedHeight, setSynchronizedHeight] = useState(minHeight);
  const columnRefs = useRef<(HTMLDivElement | null)[]>([]);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Function to calculate the maximum height among all columns
  const calculateMaxHeight = useCallback(() => {
    let maxHeight = minHeight;
    
    columnRefs.current.forEach(ref => {
      if (ref) {
        // Get the natural height of the column content
        const contentHeight = ref.scrollHeight;
        maxHeight = Math.max(maxHeight, contentHeight);
      }
    });
    
    return maxHeight;
  }, [minHeight]);

  // Debounced height update function
  const updateHeight = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    
    debounceTimeoutRef.current = setTimeout(() => {
      const newHeight = calculateMaxHeight();
      setSynchronizedHeight(newHeight);
    }, debounceMs);
  }, [calculateMaxHeight, debounceMs]);

  // Function to register a column ref
  const registerColumnRef = useCallback((index: number) => {
    return (element: HTMLDivElement | null) => {
      columnRefs.current[index] = element;

      if (element && resizeObserverRef.current) {
        try {
          resizeObserverRef.current.observe(element);
        } catch (error) {
          console.warn('ResizeObserver failed to observe element:', error);
        }
      }

      // Trigger height calculation when a new ref is registered
      updateHeight();
    };
  }, [updateHeight]);

  // Initialize ResizeObserver
  useEffect(() => {
    if (typeof window !== 'undefined' && 'ResizeObserver' in window) {
      resizeObserverRef.current = new ResizeObserver((entries) => {
        // Only update if we have actual size changes
        const hasSignificantChange = entries.some(entry => {
          const { height } = entry.contentRect;
          return Math.abs(height - synchronizedHeight) > 10; // 10px threshold
        });

        if (hasSignificantChange) {
          updateHeight();
        }
      });
    }

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [updateHeight, synchronizedHeight]);

  // Handle window resize
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleWindowResize = () => {
      updateHeight();
    };

    window.addEventListener('resize', handleWindowResize);
    return () => window.removeEventListener('resize', handleWindowResize);
  }, [updateHeight]);

  // Force height recalculation (useful for external triggers)
  const recalculateHeight = useCallback(() => {
    updateHeight();
  }, [updateHeight]);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (resizeObserverRef.current) {
      resizeObserverRef.current.disconnect();
    }
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
  }, []);

  return {
    synchronizedHeight,
    registerColumnRef,
    recalculateHeight,
    cleanup
  };
}
