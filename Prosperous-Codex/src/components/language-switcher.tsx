"use client";

import { useCallback, useMemo } from "react";
import { useRouter, usePathname } from '@/i18n/navigation';
import { useParams } from 'next/navigation';
import { saveUserLanguagePreference, updateUserLanguageInDatabase, getLocaleDisplayName, type Locale } from '@/lib/locale-utils';
import { useToast } from '@/hooks/use-toast';

export function LanguageSwitcher() {
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();
  const { toast } = useToast();
  const currentLocale = params.locale as Locale;

  const languages = useMemo(() => [
    { code: "en" as Locale, short: "EN", full: "English" },
    { code: "zh-cn" as Locale, short: "简", full: "简体中文" },
    { code: "zh-tw" as Locale, short: "繁", full: "繁體中文" }
  ], []);

  const handleLanguageChange = useCallback(async (newLocale: Locale) => {
    if (newLocale === currentLocale) return;

    try {
      // Save preference locally
      saveUserLanguagePreference(newLocale);

      // Update in database (fire and forget)
      updateUserLanguageInDatabase(newLocale);

      // Navigate to new locale
      router.replace(pathname, { locale: newLocale });

      toast({
        title: "Language Changed",
        description: `Switched to ${getLocaleDisplayName(newLocale)}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to change language",
        variant: "destructive"
      });
    }
  }, [currentLocale, pathname, router, toast]);

  return (
    <div className="language-pill-switcher">
      {languages.map((language) => (
        <button
          key={language.code}
          className={`language-pill-option ${
            language.code === currentLocale ? 'active' : ''
          }`}
          onClick={() => handleLanguageChange(language.code)}
          aria-label={`Switch to ${language.full}`}
        >
          {language.short}
        </button>
      ))}
      <div
        className="language-pill-indicator"
        style={{
          transform: `translateX(${languages.findIndex(lang => lang.code === currentLocale) * 100}%)`
        }}
      />
    </div>
  );
}