'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Trash2, Edit, Plus, Save, X, Calculator } from 'lucide-react';

export interface PaperOption {
  id: string;
  name: string;
  source: string; // Source (Pre-Cut, Roll)
  sheetHeight: number | null; // Sheet H (mm) - null for rolls
  sheetWidth: number; // Sheet W (mm)
  grainDirection: string; // Grain direction (Height, Width)
  caliper: number; // Caliper (µm)
  costPerReam: number | null; // Cost/Ream ($) - null for rolls
  gsm: number; // GSM (g/m²)
  costPerTon?: number | null; // Cost/Ton ($) - used for rolls
  category: 'Inner Text' | 'Cover' | 'Endpapers';
  [key: string]: string | number | boolean | null; // Allow additional fields
}

export interface PaperOptionsConfig {
  title: string;
  sources: string[];
  grainDirections: string[];
  additionalFields?: {
    [key: string]: {
      label: string;
      type: 'select' | 'input' | 'number';
      options?: string[];
      defaultValue?: string | number | boolean;
    };
  };
  defaultValues: Partial<PaperOption>;
  initialData: PaperOption[];
}

interface PaperOptionsCardProps {
  config: PaperOptionsConfig;
  onDataChange?: (data: PaperOption[]) => void;
  onCalculate?: (options: PaperOption[]) => void;
  isCalculating?: boolean;
  isButtonDisabled?: boolean;
  countdownSeconds?: number;
}

const PaperOptionsCard: React.FC<PaperOptionsCardProps> = ({
  config,
  onDataChange,
  onCalculate,
  isCalculating = false,
  isButtonDisabled = false,
  countdownSeconds = 0
}) => {
  const [options, setOptions] = useState<PaperOption[]>(config.initialData);

  // Update options when config.initialData changes (tab switching)
  useEffect(() => {
    setOptions(config.initialData);
    onDataChange?.(config.initialData);
  }, [config.initialData, onDataChange]);

  // Call onDataChange with initial data when component mounts
  useEffect(() => {
    onDataChange?.(config.initialData);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps


  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [tempValue, setTempValue] = useState<string>('');
  const [isAdding, setIsAdding] = useState(false);
  const [newOption, setNewOption] = useState<Partial<PaperOption>>(config.defaultValues);
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});

  const updateOptions = (newOptions: PaperOption[]) => {
    setOptions(newOptions);
    onDataChange?.(newOptions);
  };

  // Validation functions
  const validateField = (field: string, value: string | number | null, source: string): string | null => {
    if (field === 'name' && (!value || value.trim() === '')) {
      return 'Paper name is required';
    }

    if (field === 'sheetHeight') {
      if (source === 'Pre-Cut') {
        if (!value || value <= 0) return 'Sheet height is required for Pre-Cut papers';
        // Note: Press limits are handled by server-side calculation logic (rotation/trimming)
      }
      // For rolls, sheetHeight should be null
      if (source === 'Roll' && value !== null) return 'Sheet height should be empty for Roll papers';
    }

    if (field === 'sheetWidth') {
      if (!value || value <= 0) return 'Sheet width is required';
      // Note: Press limits are handled by server-side calculation logic (rotation/trimming)
    }

    if (field === 'caliper') {
      if (!value || value <= 0) return 'Caliper is required';
      if (value < 10 || value > 1000) return 'Caliper must be between 10-1000 µm';
    }

    if (field === 'gsm') {
      if (!value || value <= 0) return 'GSM is required';
      if (value < 30 || value > 500) return 'GSM must be between 30-500 g/m²';
    }

    if (field === 'costPerReam') {
      if (source === 'Pre-Cut') {
        if (!value || value <= 0) return 'Cost per ream is required for Pre-Cut papers';
      }
      // For rolls, costPerReam should be null
      if (source === 'Roll' && value !== null) return 'Cost per ream should be empty for Roll papers';
    }

    if (field === 'costPerTon') {
      if (source === 'Roll') {
        if (!value || value <= 0) return 'Cost per ton is required for Roll papers';
      }
      // For pre-cut, costPerTon should be null
      if (source === 'Pre-Cut' && value !== null) return 'Cost per ton should be empty for Pre-Cut papers';
    }

    return null;
  };

  const validateOption = (option: Partial<PaperOption>): { [key: string]: string } => {
    const errors: { [key: string]: string } = {};
    const source = option.source || 'Pre-Cut';

    Object.keys(option).forEach(field => {
      const error = validateField(field, option[field as keyof PaperOption], source);
      if (error) {
        errors[field] = error;
      }
    });

    return errors;
  };

  const handleDoubleClick = (id: string, field: string, value: string | number) => {
    setEditingId(id);
    setEditingField(field);
    setTempValue(value.toString());
  };

  const handleSave = () => {
    if (editingId && editingField) {
      const option = options.find(opt => opt.id === editingId);
      if (!option) return;

      let newValue: string | number | null = tempValue;
      if (editingField === 'gsm' || editingField === 'caliper' || editingField === 'sheetHeight' || editingField === 'sheetWidth') {
        newValue = parseFloat(tempValue) || 0;
      } else if (editingField === 'costPerReam' || editingField === 'costPerTon') {
        newValue = tempValue ? parseFloat(tempValue) : null;
      }

      // Validate the new value
      const validationError = validateField(editingField, newValue, option.source);
      if (validationError) {
        setValidationErrors(prev => ({ ...prev, [`${editingId}-${editingField}`]: validationError }));
        return; // Don't save if validation fails
      }

      // Clear any existing validation error for this field
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${editingId}-${editingField}`];
        return newErrors;
      });

      updateOptions(options.map(opt => {
        if (opt.id === editingId) {
          const updatedOption = { ...opt };
          (updatedOption as any)[editingField] = newValue;
          return updatedOption;
        }
        return opt;
      }));
    }
    setEditingId(null);
    setEditingField(null);
    setTempValue('');
  };

  const handleCancel = () => {
    setEditingId(null);
    setEditingField(null);
    setTempValue('');
    // Clear validation error for the field being cancelled
    if (editingId && editingField) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${editingId}-${editingField}`];
        return newErrors;
      });
    }
  };

  // Handle source type change with automatic field adjustment
  const handleSourceChange = (optionId: string, newSource: string) => {
    updateOptions(options.map(option => {
      if (option.id === optionId) {
        const updatedOption = { ...option, source: newSource };

        // Auto-adjust fields based on source type
        if (newSource === 'Roll') {
          updatedOption.sheetHeight = null;
          updatedOption.costPerReam = null;
          // Ensure costPerTon has a default value for rolls
          if (!updatedOption.costPerTon) {
            updatedOption.costPerTon = 1200; // Default roll cost
          }
        } else if (newSource === 'Pre-Cut') {
          updatedOption.costPerTon = null;
          // Ensure required fields have default values for pre-cut
          if (!updatedOption.sheetHeight) {
            updatedOption.sheetHeight = 600; // Default sheet height
          }
          if (!updatedOption.costPerReam) {
            updatedOption.costPerReam = 50; // Default cost per ream
          }
        }

        return updatedOption;
      }
      return option;
    }));

    // Clear any validation errors for this option
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      Object.keys(newErrors).forEach(key => {
        if (key.startsWith(`${optionId}-`)) {
          delete newErrors[key];
        }
      });
      return newErrors;
    });
  };

  const handleDelete = (id: string) => {
    updateOptions(options.filter(option => option.id !== id));
  };

  const handleAddNew = () => {
    if (newOption.name) {
      const id = Date.now().toString();
      updateOptions([...options, { ...newOption, id } as PaperOption]);
      setNewOption(config.defaultValues);
      setIsAdding(false);
    }
  };

  const handleCalculate = () => {
    // Validate all options before calculating
    const errors: { [key: string]: string } = {};
    let hasErrors = false;

    options.forEach(option => {
      const optionErrors = validateOption(option);
      Object.keys(optionErrors).forEach(field => {
        errors[`${option.id}-${field}`] = optionErrors[field];
        hasErrors = true;
      });
    });

    setValidationErrors(errors);

    if (hasErrors) {
      // Show error message or highlight invalid fields
      return;
    }

    // All validations passed, trigger calculation
    onCalculate?.(options);


  };

  const handleResetToDefaults = () => {
    setOptions(config.initialData);
    setValidationErrors({});
  };

  const renderEditableField = (option: PaperOption, field: string, label: string) => {
    const isEditing = editingId === option.id && editingField === field;
    const value = option[field];
    const errorKey = `${option.id}-${field}`;
    const hasError = validationErrors[errorKey];

    if (isEditing) {
      // Handle select fields
      if (field === 'source') {
        return (
          <div className="relative">
            <Select
              value={tempValue}
              onValueChange={(newValue) => {
                setTempValue(newValue);
                // Immediately apply source change with field adjustments
                handleSourceChange(option.id, newValue);
                setEditingId(null);
                setEditingField(null);
                setTempValue('');
              }}
            >
              <SelectTrigger className={`h-8 text-sm border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-600 text-gray-900 dark:text-neutral-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 ${hasError ? 'border-red-500 dark:border-red-400' : ''}`} data-glow>
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white dark:bg-neutral-600 border-gray-300 dark:border-neutral-600">
                {config.sources.map(source => (
                  <SelectItem key={source} value={source} className="text-gray-900 dark:text-neutral-100 hover:bg-gray-100 dark:hover:bg-neutral-700 focus:bg-blue-100 dark:focus:bg-blue-900">{source}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            {hasError && (
              <div className="absolute top-full left-0 mt-1 text-xs text-red-600 dark:text-red-400 bg-white dark:bg-neutral-600 px-2 py-1 rounded shadow-lg border border-red-200 dark:border-red-700 z-10">
                {validationErrors[errorKey]}
              </div>
            )}
          </div>
        );
      }

      if (field === 'grainDirection') {
        return (
          <Select value={tempValue} onValueChange={setTempValue}>
            <SelectTrigger className="h-8 text-sm border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-600 text-gray-900 dark:text-neutral-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400" data-glow>
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-white dark:bg-neutral-600 border-gray-300 dark:border-neutral-600">
              {config.grainDirections.map(direction => (
                <SelectItem key={direction} value={direction} className="text-gray-900 dark:text-neutral-100 hover:bg-gray-100 dark:hover:bg-neutral-700 focus:bg-blue-100 dark:focus:bg-blue-900">{direction}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      }

      // Handle additional fields
      if (config.additionalFields?.[field]) {
        const fieldConfig = config.additionalFields[field];
        if (fieldConfig.type === 'select' && fieldConfig.options) {
          return (
            <Select value={tempValue} onValueChange={setTempValue}>
              <SelectTrigger className="h-8 text-sm border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-600 text-gray-900 dark:text-neutral-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400" data-glow>
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white dark:bg-neutral-600 border-gray-300 dark:border-neutral-600">
                {fieldConfig.options.map(option => (
                  <SelectItem key={option} value={option} className="text-gray-900 dark:text-neutral-100 hover:bg-gray-100 dark:hover:bg-neutral-700 focus:bg-blue-100 dark:focus:bg-blue-900">{option}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          );
        }
      }
      
      // Special handling for sheet height (can be null for rolls)
      if (field === 'sheetHeight') {
        return (
          <div className="relative">
            <Input
              value={tempValue}
              onChange={(e) => setTempValue(e.target.value)}
              className={`h-8 text-sm border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-600 text-gray-900 dark:text-neutral-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 ${hasError ? 'border-red-500 dark:border-red-400' : ''}`}
              type="number"
              step="0.1"
              placeholder={option.source === 'Roll' ? 'N/A for rolls' : 'Height (mm)'}
              disabled={option.source === 'Roll'}
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleSave();
                if (e.key === 'Escape') handleCancel();
              }}
              data-glow
            />
            {hasError && (
              <div className="absolute top-full left-0 mt-1 text-xs text-red-600 dark:text-red-400 bg-white dark:bg-neutral-600 px-2 py-1 rounded shadow-lg border border-red-200 dark:border-red-700 z-10">
                {validationErrors[errorKey]}
              </div>
            )}
          </div>
        );
      }

      return (
        <div className="relative">
          <Input
            value={tempValue}
            onChange={(e) => setTempValue(e.target.value)}
            className={`h-8 text-sm border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-600 text-gray-900 dark:text-neutral-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 ${hasError ? 'border-red-500 dark:border-red-400' : ''}`}
            type={field === 'gsm' || field === 'caliper' || field === 'sheetWidth' || field === 'costPerReam' || field === 'costPerTon' ? 'number' : 'text'}
            step={field === 'costPerReam' || field === 'costPerTon' ? '0.01' : field === 'sheetWidth' || field === 'sheetHeight' ? '0.1' : '1'}
            placeholder={
              field === 'costPerReam' && option.source === 'Roll' ? 'N/A for rolls' :
              field === 'costPerTon' && option.source === 'Pre-Cut' ? 'N/A for pre-cut' :
              field === 'sheetWidth' ? 'Width (mm)' :
              field === 'caliper' ? '10-1000' :
              field === 'gsm' ? '30-500' :
              ''
            }
            disabled={
              (field === 'costPerReam' && option.source === 'Roll') ||
              (field === 'costPerTon' && option.source === 'Pre-Cut')
            }
            onKeyDown={(e) => {
              if (e.key === 'Enter') handleSave();
              if (e.key === 'Escape') handleCancel();
            }}
            data-glow
          />
          {hasError && (
            <div className="absolute top-full left-0 mt-1 text-xs text-red-600 dark:text-red-400 bg-white dark:bg-neutral-600 px-2 py-1 rounded shadow-lg border border-red-200 dark:border-red-700 z-10">
              {validationErrors[errorKey]}
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="relative">
        <div
          className={`editable-field cursor-pointer px-2 py-1 rounded text-sm min-h-[24px] flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 border ${hasError ? 'border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/10' : 'border-transparent hover:border-blue-200 dark:hover:border-blue-700'}`}
          onDoubleClick={() => handleDoubleClick(option.id, field, value || '')}
          title={hasError ? `Error: ${validationErrors[errorKey]}` : "Double-click to edit"}
        >
          {field === 'sheetHeight' && option.source === 'Roll' ? (
            <span className="truncate text-gray-400 dark:text-neutral-500 italic">-</span>
          ) : field === 'costPerReam' && option.source === 'Roll' ? (
            <span className="truncate text-gray-400 dark:text-neutral-500 italic">-</span>
          ) : field === 'costPerTon' && option.source === 'Pre-Cut' ? (
            <span className="truncate text-gray-400 dark:text-neutral-500 italic">-</span>
          ) : value === null || value === undefined ? (
            <span className="truncate text-gray-400 dark:text-neutral-500 italic">-</span>
          ) : (
            <span className="truncate text-gray-900 dark:text-neutral-100">{value}</span>
          )}
        </div>
        {hasError && (
          <div className="absolute top-full left-0 mt-1 text-xs text-red-600 dark:text-red-400 bg-white dark:bg-neutral-600 px-2 py-1 rounded shadow-lg border border-red-200 dark:border-red-700 z-10">
            {validationErrors[errorKey]}
          </div>
        )}
      </div>
    );
  };

  const renderNewOptionField = (field: string, label: string) => {
    if (field === 'source') {
      return (
        <Select
          value={newOption.source}
          onValueChange={(value) => setNewOption(prev => ({ ...prev, source: value }))}
        >
          <SelectTrigger className="h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {config.sources.map(source => (
              <SelectItem key={source} value={source}>{source}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    if (field === 'grainDirection') {
      return (
        <Select
          value={newOption.grainDirection}
          onValueChange={(value) => setNewOption(prev => ({ ...prev, grainDirection: value }))}
        >
          <SelectTrigger className="h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {config.grainDirections.map(direction => (
              <SelectItem key={direction} value={direction}>{direction}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    if (config.additionalFields?.[field]) {
      const fieldConfig = config.additionalFields[field];
      if (fieldConfig.type === 'select' && fieldConfig.options) {
        return (
          <Select
            value={newOption[field]}
            onValueChange={(value) => setNewOption(prev => ({ ...prev, [field]: value }))}
          >
            <SelectTrigger className="h-8 text-sm border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-600 text-gray-900 dark:text-neutral-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400" data-glow>
          <SelectValue placeholder={label} />
        </SelectTrigger>
            <SelectContent className="bg-white dark:bg-neutral-600 border-gray-300 dark:border-neutral-600">
              {fieldConfig.options.map(option => (
                <SelectItem 
                  key={option} 
                  value={option}
                  className="text-gray-900 dark:text-neutral-100 hover:bg-gray-100 dark:hover:bg-neutral-700 focus:bg-blue-100 dark:focus:bg-blue-900"
                >
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      }
    }

    return (
      <Input
        placeholder={label}
        value={newOption[field] || ''}
        onChange={(e) => {
          let value: string | number = e.target.value;
          if (field === 'gsm' || field === 'caliper') {
            value = parseInt(e.target.value) || 0;
          } else if (field === 'sheetHeight' || field === 'sheetWidth') {
            value = parseFloat(e.target.value) || 0;
          } else if (field === 'costPerReam' || field === 'costPerTon') {
            value = e.target.value ? parseFloat(e.target.value) : null;
          }
          setNewOption(prev => ({ ...prev, [field]: value }));
        }}
        className="h-8 text-sm border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-600 text-gray-900 dark:text-neutral-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
        type={field === 'gsm' || field === 'caliper' || field === 'sheetHeight' || field === 'sheetWidth' || field === 'costPerReam' || field === 'costPerTon' ? 'number' : 'text'}
        step={field === 'costPerReam' || field === 'costPerTon' ? '0.01' : field === 'sheetHeight' || field === 'sheetWidth' ? '0.1' : '1'}
        data-glow
      />
    );
  };

  // Fixed 10-column grid structure for all paper types
  const baseFields = ['name', 'source', 'sheetHeight', 'sheetWidth', 'grainDirection', 'caliper', 'costPerReam', 'gsm', 'costPerTon'];
  const additionalFieldKeys = Object.keys(config.additionalFields || {});
  const gridCols = 10; // Fixed 10 columns: 9 data columns + 1 actions column

  const getFieldLabel = (field: string) => {
    const labels: { [key: string]: string } = {
      name: 'Paper Name',
      source: 'Source',
      sheetHeight: 'Sheet H (mm)',
      sheetWidth: 'Sheet W (mm)',
      grainDirection: 'Grain || To',
      caliper: 'Caliper (µm)',
      costPerReam: 'Cost/Ream ($)',
      gsm: 'GSM (g/m²)',
      costPerTon: 'Cost/Ton ($)'
    };
    return config.additionalFields?.[field]?.label || labels[field] || field;
  };

  return (
    <Card className="w-full" data-glow>
      <CardHeader className="pt-1 pb-1">
        <CardTitle className="flex items-center justify-between text-lg font-semibold text-gray-900 dark:text-neutral-100">
          {config.title}
          <div className="flex items-center gap-2">
            <Button
              onClick={handleCalculate}
              size="sm"
              className="flex items-center gap-2 h-8 px-3 text-xs bg-[#5E6AD2] hover:bg-[#5E6AD2]/90 dark:bg-[#6E56CF] dark:hover:bg-[#9E8CFC] text-white transition-colors duration-200"
              disabled={isCalculating || isButtonDisabled || options.length === 0}
            >
              <Calculator className="h-3 w-3" />
              {isCalculating ? 'Calculating...' :
               countdownSeconds > 0 ? `Wait ${countdownSeconds}s` :
               'Calculate Costs'}
            </Button>
            <Button
              onClick={handleResetToDefaults}
              size="sm"
              variant="outline"
              className="flex items-center gap-2 h-8 px-3 text-xs border-gray-200 dark:border-[#2A2A2A] hover:bg-gray-50 dark:hover:bg-[#2A2A2A] transition-colors duration-200"
              title="Reset to original paper list"
            >
              Reset ({config.initialData.length})
            </Button>
            <Button
              onClick={() => setIsAdding(true)}
              size="sm"
              className="flex items-center gap-2 h-8 px-3 text-xs bg-[#5E6AD2]/80 hover:bg-[#5E6AD2] dark:bg-[#9E8CFC] dark:hover:bg-[#C4B5FD] text-white transition-colors duration-200"
              disabled={isAdding}
            >
              <Plus className="h-3 w-3" />
              Add New
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div>
          <p className="text-sm text-gray-600 dark:text-neutral-400 mb-2">
            Enter full original sheet dimensions. Sheets larger than press limits (720x1020mm) will be automatically rotated or trimmed during calculation.
          </p>
          {/* Header Row */}
          <div className="mt-4 grid grid-cols-10 gap-0 text-xs font-medium text-gray-700 dark:text-neutral-300 bg-gray-100 dark:bg-neutral-600 border border-purple-300 dark:border-purple-600 rounded-t-lg">
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Paper Name</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Source</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Sheet H (mm)</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Sheet W (mm)</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Grain || To</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Caliper (µm)</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Cost/Ream ($)</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">GSM (g/m²)</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Cost/Ton ($)</div>
            <div className="col-span-1 px-3 py-3 font-semibold">Actions</div>
          </div>
          
          {/* Existing Options */}
          <div className="mt-0 border-l border-r border-b border-purple-300 dark:border-purple-600 rounded-b-lg overflow-hidden">
            {options.map((option, index) => (
              <div key={option.id} className={`grid grid-cols-10 gap-0 items-center hover:bg-gray-50 dark:hover:bg-neutral-800/50 transition-all duration-200 ${index !== options.length - 1 ? 'border-b border-purple-200 dark:border-purple-700' : ''}`}>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'name', getFieldLabel('name'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'source', getFieldLabel('source'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'sheetHeight', getFieldLabel('sheetHeight'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'sheetWidth', getFieldLabel('sheetWidth'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'grainDirection', getFieldLabel('grainDirection'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'caliper', getFieldLabel('caliper'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'costPerReam', getFieldLabel('costPerReam'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'gsm', getFieldLabel('gsm'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'costPerTon', getFieldLabel('costPerTon'))}
                </div>
                <div className="col-span-1 px-3 py-3 flex gap-1 justify-center">
                {editingId === option.id ? (
                  <>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0 hover:bg-green-100 dark:hover:bg-green-900/20"
                      onClick={handleSave}
                      data-glow
                    >
                      <Save className="w-3 h-3 text-emerald-600 dark:text-emerald-400" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0 hover:bg-red-100 dark:hover:bg-red-900/20"
                      onClick={handleCancel}
                      data-glow
                    >
                      <X className="w-3 h-3 text-red-600 dark:text-red-400" />
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/20"
                      onClick={() => handleDoubleClick(option.id, 'name', option.name)}
                      data-glow
                    >
                      <Edit className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0 hover:bg-red-100 dark:hover:bg-red-900/20"
                      onClick={() => handleDelete(option.id)}
                      data-glow
                    >
                      <Trash2 className="w-3 h-3 text-red-600 dark:text-red-400" />
                    </Button>
                  </>
                )}
                </div>
              </div>
            ))}
          </div>
          
          {/* Add New Row */}
          {isAdding && (
            <div className="mt-4 border border-purple-300 dark:border-purple-600 bg-purple-50 dark:bg-purple-900/10 rounded-lg overflow-hidden animate-fadeIn">
              <div className="grid grid-cols-10 gap-0 items-center">
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('name', getFieldLabel('name'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('source', getFieldLabel('source'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('sheetHeight', getFieldLabel('sheetHeight'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('sheetWidth', getFieldLabel('sheetWidth'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('grainDirection', getFieldLabel('grainDirection'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('caliper', getFieldLabel('caliper'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('costPerReam', getFieldLabel('costPerReam'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('gsm', getFieldLabel('gsm'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('costPerTon', getFieldLabel('costPerTon'))}
                </div>
                <div className="col-span-1 px-3 py-3 flex gap-1 justify-center">
                <Button 
                  size="sm" 
                  onClick={handleAddNew} 
                  className="h-6 w-6 p-0 bg-green-600 hover:bg-green-700 dark:bg-emerald-500 dark:hover:bg-emerald-600"
                  disabled={!newOption.name}
                >
                  <Save className="h-3 w-3" />
                </Button>
                <Button 
                   size="sm" 
                   variant="outline" 
                   onClick={() => setIsAdding(false)} 
                   className="h-6 w-6 p-0 border-gray-300 dark:border-neutral-600 hover:bg-gray-100 dark:hover:bg-neutral-800"
                 >
                   <X className="h-3 w-3" />
                 </Button>
               </div>
             </div>
           </div>
           )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PaperOptionsCard;