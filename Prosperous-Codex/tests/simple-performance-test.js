#!/usr/bin/env node

/**
 * Simple Performance Test for Task Master Optimizations
 * Measures response times and payload sizes without authentication complexity
 */

const { performance } = require('perf_hooks');

const BENCHMARK_CONFIG = {
  baseUrl: 'http://localhost:3001',
  iterations: 5
};

class SimplePerformanceBenchmark {
  constructor() {
    this.results = [];
  }

  async makeRequest(endpoint, options = {}) {
    const startTime = performance.now();
    
    try {
      const response = await fetch(`${BENCHMARK_CONFIG.baseUrl}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      });

      const endTime = performance.now();
      const responseTime = endTime - startTime;

      let size = 0;
      if (response.headers.get('content-length')) {
        size = parseInt(response.headers.get('content-length'));
      } else {
        const text = await response.text();
        size = text.length;
      }

      return {
        responseTime,
        size,
        status: response.status,
        ok: response.ok
      };
    } catch (error) {
      const endTime = performance.now();
      return {
        responseTime: endTime - startTime,
        size: 0,
        status: 0,
        ok: false,
        error: error.message
      };
    }
  }

  async runBenchmark(name, endpoint, iterations = BENCHMARK_CONFIG.iterations) {
    console.log(`\n📊 Benchmarking: ${name}`);
    console.log(`   Endpoint: ${endpoint}`);
    console.log(`   Iterations: ${iterations}`);

    const times = [];
    const sizes = [];

    for (let i = 0; i < iterations; i++) {
      const result = await this.makeRequest(endpoint);
      times.push(result.responseTime);
      sizes.push(result.size);
      process.stdout.write('.');
    }

    console.log(''); // New line after progress dots

    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    const avgSize = sizes.reduce((sum, size) => sum + size, 0) / sizes.length;

    const benchmark = {
      name,
      endpoint,
      averageTime: avgTime,
      minTime,
      maxTime,
      averageSize: Math.round(avgSize),
      iterations
    };

    this.results.push(benchmark);

    console.log(`   ✅ Avg: ${avgTime.toFixed(2)}ms, Min: ${minTime.toFixed(2)}ms, Max: ${maxTime.toFixed(2)}ms`);
    console.log(`   📦 Avg Size: ${Math.round(avgSize)} bytes`);

    return benchmark;
  }

  async runAllBenchmarks() {
    console.log('⚡ TASK MASTER PERFORMANCE BENCHMARKS');
    console.log('====================================');
    console.log(`Target: ${BENCHMARK_CONFIG.baseUrl}`);
    console.log(`Iterations per test: ${BENCHMARK_CONFIG.iterations}`);

    // Test basic endpoint performance
    await this.runBenchmark('Basic Project List', '/api/task-master/projects');
    
    // Test pagination performance
    await this.runBenchmark('Paginated Project List', '/api/task-master/projects?page=1&limit=10');
    
    // Test partial response performance
    await this.runBenchmark('Partial Response (Basic)', '/api/task-master/projects?fields=basic');
    
    // Test partial response with custom fields
    await this.runBenchmark('Partial Response (Custom)', '/api/task-master/projects?fields=id,title,status');
    
    // Test error handling performance
    await this.runBenchmark('Error Handling', '/api/task-master/projects/99999');
    
    // Test field validation performance
    await this.runBenchmark('Field Validation', '/api/task-master/projects?fields=invalidField');

    this.generateReport();
  }

  generateReport() {
    console.log('\n📈 PERFORMANCE ANALYSIS');
    console.log('=======================');

    // Find fastest and slowest
    const sortedByTime = this.results.slice().sort((a, b) => a.averageTime - b.averageTime);
    const fastest = sortedByTime[0];
    const slowest = sortedByTime[sortedByTime.length - 1];

    console.log(`🏆 Fastest: ${fastest.name} (${fastest.averageTime.toFixed(2)}ms)`);
    console.log(`🐌 Slowest: ${slowest.name} (${slowest.averageTime.toFixed(2)}ms)`);

    // Analyze optimization benefits
    const basicList = this.results.find(r => r.name === 'Basic Project List');
    const paginatedList = this.results.find(r => r.name === 'Paginated Project List');
    const partialBasic = this.results.find(r => r.name === 'Partial Response (Basic)');

    if (basicList && paginatedList) {
      const paginationImprovement = ((basicList.averageTime - paginatedList.averageTime) / basicList.averageTime) * 100;
      if (paginationImprovement > 0) {
        console.log(`📄 Pagination Benefit: ${paginationImprovement.toFixed(1)}% faster`);
      }
    }

    if (basicList && partialBasic) {
      const sizeReduction = ((basicList.averageSize - partialBasic.averageSize) / basicList.averageSize) * 100;
      if (sizeReduction > 0) {
        console.log(`🎯 Partial Response Benefit: ${sizeReduction.toFixed(1)}% smaller payload`);
      }
    }

    // Performance categories
    console.log('\n🚀 PERFORMANCE CATEGORIES:');
    this.results.forEach(result => {
      let category = '🟢 Excellent';
      if (result.averageTime > 100) category = '🟡 Good';
      if (result.averageTime > 500) category = '🔴 Needs Improvement';
      
      console.log(`${category} ${result.name}: ${result.averageTime.toFixed(2)}ms`);
    });

    // Overall assessment
    const avgResponseTime = this.results.reduce((sum, r) => sum + r.averageTime, 0) / this.results.length;
    
    console.log('\n🎯 OPTIMIZATION ASSESSMENT:');
    console.log('===========================');
    
    if (avgResponseTime < 50) {
      console.log('✅ EXCELLENT: Database indexes and optimizations are working perfectly!');
      console.log('   - Response times are under 50ms average');
      console.log('   - System is highly optimized');
    } else if (avgResponseTime < 100) {
      console.log('✅ GOOD: Optimizations are working well');
      console.log('   - Response times are reasonable');
      console.log('   - Performance improvements are evident');
    } else if (avgResponseTime < 200) {
      console.log('🟡 FAIR: Some optimizations are working');
      console.log('   - Response times could be better');
      console.log('   - May need further tuning');
    } else {
      console.log('🔴 NEEDS WORK: Optimizations may not be fully effective');
      console.log('   - Response times are high');
      console.log('   - Check database indexes and query optimization');
    }

    console.log(`\n📊 Overall Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
    
    // Database performance assessment
    const fastQueries = this.results.filter(r => r.averageTime < 50).length;
    const totalQueries = this.results.length;
    const fastPercentage = (fastQueries / totalQueries) * 100;
    
    console.log(`🗄️  Database Performance: ${fastQueries}/${totalQueries} queries under 50ms (${fastPercentage.toFixed(1)}%)`);
    
    if (fastPercentage >= 80) {
      console.log('✅ Database indexes are working excellently!');
    } else if (fastPercentage >= 60) {
      console.log('🟡 Database indexes are providing good performance');
    } else {
      console.log('🔴 Database indexes may need optimization');
    }
  }
}

async function runPerformanceBenchmarks() {
  const benchmark = new SimplePerformanceBenchmark();
  
  try {
    await benchmark.runAllBenchmarks();
    console.log('\n✅ Performance benchmarks completed successfully!');
    return 0;
  } catch (error) {
    console.error('\n❌ Benchmark execution failed:', error.message);
    return 1;
  }
}

// Run benchmarks if this file is executed directly
if (require.main === module) {
  runPerformanceBenchmarks().then(exitCode => {
    process.exit(exitCode);
  }).catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { runPerformanceBenchmarks, SimplePerformanceBenchmark };
