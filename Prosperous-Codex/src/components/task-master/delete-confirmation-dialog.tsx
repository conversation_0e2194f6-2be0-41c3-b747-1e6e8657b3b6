"use client";

import React from 'react';
import { AlertTriangle, Trash2, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { FormattedText } from '@/components/ui/formatted-text';
import { TaskMaster } from '@/lib/types/task-master';

interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  task: TaskMaster | null;
  onConfirm: () => void;
  onCancel: () => void;
  isDeleting?: boolean;
}

export function DeleteConfirmationDialog({
  isOpen,
  task,
  onConfirm,
  onCancel,
  isDeleting = false
}: DeleteConfirmationDialogProps) {
  if (!isOpen || !task) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-[10000] flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onCancel}
      />
      
      {/* Dialog */}
      <div className="relative bg-white dark:bg-[#1A1A1A] rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Delete Task
            </h3>
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            disabled={isDeleting}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="mb-6">
          <p className="text-gray-600 dark:text-gray-300 mb-3">
            Are you sure you want to delete this task? This action cannot be undone.
          </p>
          
          <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-3">
            <p className="font-medium text-gray-900 dark:text-white text-sm">
              {task.title}
            </p>
            {task.description && (
              <FormattedText
                text={task.description}
                className="text-gray-600 dark:text-gray-400 text-xs mt-1"
                variant="muted"
              />
            )}
          </div>

          <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
            <p>This will permanently delete:</p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>The task and all its data</li>
              <li>All comments and discussions</li>
              <li>All uploaded files</li>
              <li>All activity history</li>
            </ul>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-3 justify-end">
          <Button
            variant="neutral-secondary"
            onClick={onCancel}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            {isDeleting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Task
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
