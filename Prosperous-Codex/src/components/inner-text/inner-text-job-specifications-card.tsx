"use client";

import { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UnitToggle } from "@/components/ui/unit-toggle";
import { useUnit } from "@/lib/context/unit-context";
import {
  InnerTextJobSpecificationData,
  TabSpecificCardProps
} from "@/lib/types/ui-components";

// Add interface for ref methods
export interface InnerTextJobSpecificationsRef {
  getCurrentMmValues: () => InnerTextJobSpecificationData;
}

const InnerTextJobSpecificationsCard = forwardRef<InnerTextJobSpecificationsRef, TabSpecificCardProps<InnerTextJobSpecificationData>>(({
  className = "",
  onDataChange,
  initialData
}, ref) => {
  const { unit, convertMmToDisplayValue, convertDisplayValueToMm } = useUnit();

  // Store display values (what user sees)
  const [pageHeightDisplay, setPageHeightDisplay] = useState("");
  const [pageWidthDisplay, setPageWidthDisplay] = useState("");
  const [totalPages, setTotalPages] = useState(initialData?.totalPages?.toString() || "320");
  const [quantity, setQuantity] = useState(initialData?.quantity?.toString() || "1000");
  const [bindingMethod, setBindingMethod] = useState(initialData?.bindingMethod || "saddleStitch");
  const [spoilage, setSpoilage] = useState((initialData?.spoilagePct ? initialData.spoilagePct * 100 : 5).toString());

  // Initialize display values from initial data
  useEffect(() => {
    if (initialData?.trimH) {
      setPageHeightDisplay(convertMmToDisplayValue(initialData.trimH.toString(), unit));
    } else {
      setPageHeightDisplay(convertMmToDisplayValue("297", unit));
    }

    if (initialData?.trimW) {
      setPageWidthDisplay(convertMmToDisplayValue(initialData.trimW.toString(), unit));
    } else {
      setPageWidthDisplay(convertMmToDisplayValue("210", unit));
    }
  }, [initialData, unit, convertMmToDisplayValue]);

  // Convert display values when unit changes (instant conversion on toggle)
  useEffect(() => {
    if (pageHeightDisplay) {
      const mmValue = convertDisplayValueToMm(pageHeightDisplay, unit === 'mm' ? 'in' : 'mm');
      setPageHeightDisplay(convertMmToDisplayValue(mmValue, unit));
    }
    if (pageWidthDisplay) {
      const mmValue = convertDisplayValueToMm(pageWidthDisplay, unit === 'mm' ? 'in' : 'mm');
      setPageWidthDisplay(convertMmToDisplayValue(mmValue, unit));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [unit]); // Only depend on unit changes

  // Function to get current mm values for calculation (called externally)
  const getCurrentMmValues = useCallback(() => {
    return {
      trimH: parseFloat(convertDisplayValueToMm(pageHeightDisplay, unit)) || 0,
      trimW: parseFloat(convertDisplayValueToMm(pageWidthDisplay, unit)) || 0,
      totalPages: parseInt(totalPages) || 0,
      quantity: parseInt(quantity) || 0,
      bindingMethod,
      spoilagePct: (parseFloat(spoilage) || 0) / 100
    };
  }, [convertDisplayValueToMm, pageHeightDisplay, pageWidthDisplay, unit, totalPages, quantity, bindingMethod, spoilage]);

  // Expose getCurrentMmValues function via ref
  useImperativeHandle(ref, () => ({
    getCurrentMmValues
  }));

  // Notify parent component when data changes (for real-time updates of non-dimension fields)
  useEffect(() => {
    if (onDataChange) {
      onDataChange(getCurrentMmValues());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [totalPages, quantity, bindingMethod, spoilage, onDataChange]);

  // Input validation function
  const validateNumberInput = (value: string) => {
    // Allow empty string, numbers, decimal points, and negative signs
    return value === '' || /^-?\d*\.?\d*$/.test(value);
  };

  return (
    <Card className={className} data-glow>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-neutral-900 dark:text-white flex items-center justify-between w-full">
          <div className="flex items-center">
            <span className="w-2 h-2 bg-[#5E6AD2] dark:bg-[#6E56CF] rounded-full mr-2"></span>
            Job Specifications
          </div>
          <UnitToggle className="ml-auto" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Page Dimensions Row - Height and Width side by side */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="page-height" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                Height (H)
              </Label>
              <span className="text-xs text-neutral-500 dark:text-neutral-400">{unit}</span>
            </div>
            <Input
              id="page-height"
              type="number"
              step={unit === 'mm' ? "0.1" : "0.01"}
              placeholder={unit === 'mm' ? "297" : "11.69"}
              value={pageHeightDisplay}
              onChange={(e) => {
                if (validateNumberInput(e.target.value)) {
                  setPageHeightDisplay(e.target.value);
                }
              }}
              className="h-9 text-sm"
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="page-width" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                Width (W)
              </Label>
              <span className="text-xs text-gray-500 dark:text-neutral-400">{unit}</span>
            </div>
            <Input
              id="page-width"
              type="number"
              step={unit === 'mm' ? "0.1" : "0.01"}
              placeholder={unit === 'mm' ? "210" : "8.27"}
              value={pageWidthDisplay}
              onChange={(e) => {
                if (validateNumberInput(e.target.value)) {
                  setPageWidthDisplay(e.target.value);
                }
              }}
              className="h-9 text-sm"
            />
          </div>
        </div>



        {/* Total Pages and Quantity Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="total-pages" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              Total Pages
            </Label>
            <Input
              id="total-pages"
              type="number"
              step="2"
              placeholder="320"
              value={totalPages}
              onChange={(e) => setTotalPages(e.target.value)}
              className="mt-1 h-9 text-sm"
            />
            <p className="text-xs text-neutral-500 mt-1">Must be even number</p>
          </div>
          <div>
            <Label htmlFor="quantity" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              Quantity
            </Label>
            <Input
              id="quantity"
              type="number"
              placeholder="1000"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              className="mt-1 h-9 text-sm bg-white dark:bg-neutral-800 border-gray-300 dark:border-neutral-600"
              data-glow
            />
          </div>
        </div>

        {/* Binding Method and Spoilage Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="binding-method" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              Binding Method
            </Label>
            <Select value={bindingMethod} onValueChange={setBindingMethod}>
              <SelectTrigger className="mt-1 h-9 text-sm" data-glow>
                <SelectValue placeholder="Select binding" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="saddleStitch">Saddle Stitch</SelectItem>
                <SelectItem value="perfectBound">Perfect Bound</SelectItem>
                <SelectItem value="wireO">Wire-O / Coil</SelectItem>
                <SelectItem value="sectionSewn">Section Sewn</SelectItem>
                <SelectItem value="caseBound">Case Bound (Hardcover)</SelectItem>
                <SelectItem value="singlePage">Single Page / Flat Sheet</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="spoilage" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              Spoilage (%)
            </Label>
            <Input
              id="spoilage"
              type="number"
              step="0.1"
              placeholder="5"
              value={spoilage}
              onChange={(e) => setSpoilage(e.target.value)}
              className="mt-1 h-9 text-sm"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

InnerTextJobSpecificationsCard.displayName = 'InnerTextJobSpecificationsCard';

export default InnerTextJobSpecificationsCard;
