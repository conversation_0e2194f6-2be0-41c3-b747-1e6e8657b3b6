/**
 * JSDoc Configuration for Task Master
 * 
 * Generates comprehensive documentation for the Task Master codebase
 * including API documentation, type definitions, and usage examples.
 */

module.exports = {
  source: {
    include: [
      './src/lib/task-master/',
      './src/app/api/task-master/',
      './README.md'
    ],
    includePattern: '\\.(js|jsx|ts|tsx)$',
    exclude: [
      './src/lib/task-master/**/*.test.ts',
      './src/lib/task-master/**/*.test.js',
      './node_modules/'
    ]
  },
  opts: {
    destination: './docs/jsdoc/',
    recurse: true,
    readme: './README.md'
  },
  plugins: [
    'plugins/markdown',
    'plugins/summarize'
  ],
  templates: {
    cleverLinks: false,
    monospaceLinks: false,
    useLongnameInNav: false,
    showInheritedInNav: true
  },
  markdown: {
    parser: 'gfm',
    hardwrap: false,
    idInHeadings: true
  },
  metadata: {
    title: 'Task Master API Documentation',
    description: 'Comprehensive documentation for the Task Master project management system',
    version: '1.0.0',
    author: 'Task Master Development Team',
    license: 'MIT'
  },
  docdash: {
    static: true,
    sort: true,
    sectionOrder: [
      'Classes',
      'Modules',
      'Externals',
      'Events',
      'Namespaces',
      'Mixins',
      'Tutorials',
      'Interfaces'
    ],
    disqus: false,
    openGraph: {
      title: 'Task Master API Documentation',
      type: 'website',
      image: '',
      site_name: 'Task Master',
      url: ''
    },
    meta: {
      title: 'Task Master API Documentation',
      description: 'Comprehensive documentation for the Task Master project management system',
      keyword: 'task management, project management, API, documentation'
    },
    search: true,
    collapse: false,
    typedefs: true,
    removeQuotes: 'none',
    scripts: [],
    menu: {
      'API Reference': {
        href: '/api/',
        target: '_blank',
        class: 'menu-item',
        id: 'api-reference'
      },
      'GitHub': {
        href: 'https://github.com/your-org/task-master',
        target: '_blank',
        class: 'menu-item',
        id: 'github-link'
      }
    }
  }
};
