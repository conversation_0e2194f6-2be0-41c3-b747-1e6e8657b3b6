{"projects": [{"id": 2, "title": "New Task", "description": null, "full_description": "Subject: Project Briefing: Feelings deck (Playing cards) // Pivotal (HK) Ltd\n\n---EventLog---\nEvent Log:\n\n- 02/05/2025 - <PERSON> (Prosperous): Received new file and sample request from <PERSON>. Noted date code change and print area size query. Factory on holiday, feedback expected May 5th.\n- 02/05/2025 - <PERSON> (Pivotal): Acknowledged <PERSON>'s email.\n- 06/05/2025 - <PERSON> (Prosperous): Reviewed file and request. Noted print area in new file is smaller than 2024 production and requires confirmation on scaling. Proposed using 2024 images with new text for cards 1-7 due to RGB color. Updated date code to \"2505A\". Requested confirmation on scaling, file prep, and number of sample sets for quotation.\n- 07/05/2025 - <PERSON> (Prosperous): Sent comparison photo to <PERSON>, will send quotation and schedule for samples shortly.\n- 13/05/2025 - <PERSON> (Pivotal): Asked for update on sampling cost and schedule.\n- 14/05/2025 - <PERSON> (Prosperous): Provided pricing: HKD 420/set for 4 sets (total HKD 1680). Offered a deal: same cost for more sets, or reduced cost if intent to purchase is provided. Estimated 2-week sampling lead time.\n- 23/05/2025 - <PERSON> (Pivotal): Approved printing proofs for samples (4-6 sets). Confirmed print area size to follow new 2025 file. Confirmed cards 1-7 adjustments. Confirmed date code change to 25xxA. Confirmed no sticker needed. Emphasized embossing quality as per 2024. Requested white box with date code and shrink wrap. Asked for samples on/before June 10th.\n- 24/05/2025 - Harry Lam (Prosperous): Confirmed Lydia's approvals and requirements. Re-uploaded file link. Stated that if approved today, they will expedite production to meet June 10th target.\n- 26/05/2025 - Lydia Cheng (Pivotal): Confirmed all approved and to proceed with sampling.\n- 26/05/2025 - Harry Lam (Prosperous): Confirmed timeline ok. Samples completed printing. Next steps: embossing and corner rounding. Estimated dispatch on June 12th.\n- 04/06/2025 - Lydia Cheng (Pivotal): Confirmed timeline is ok.\n- 04/06/2025 - Harry Lam (Prosperous): Sample news report: printing completed. Embossing and corner rounding next. Dispatch on June 12th.\n- 13/06/2025 - Candy (Prosperous): 6 sets samples will be sent to Lydia's office today.\n- 17/06/2025 - Lydia Cheng (Pivotal): Reviewed samples. Noted issues with window box logo placement and printing color deviations (orange/red tones, dull backgrounds) and black outline on characters.\n- 17/06/2025 - Prosperous (Zoey Poon/Candy): Acknowledged feedback, stating corrections will be made in bulk production for logo placement and color issues. Will pay attention to black outline issue.\n- 18/06/2025 - Lydia Cheng (Pivotal): Requested Candy to confirm final information as she had not received an email.\n- 23/06/2025 - Lydia Cheng (Pivotal): Advised that the designer is on vacation until July 5th, and approval is not expected until mid-July.\n- 26/06/2025 - Candy (Prosperous): Acknowledged Lydia's email and looked forward to hearing from her.\n\n---EndOfEventLog---\n\n---Description---\n\nThis project involves the production of a new version (2025) of the \"Feelings deck (Playing cards)\" for the client, Pivotal (HK) Ltd. The project aims to incorporate specific updates and changes requested by the client compared to the previous year's production.\n\nThe process began with Lydia Cheng from Pivotal requesting samples for the 2025 edition of the Feelings deck. Harry Lam from Prosperous Printing provided an initial review of the client's new file, outlining pricing and schedules for sampling, and sought clarification on critical details such as print area scaling and file preparation adjustments for specific cards. Lydia Cheng subsequently approved the sampling process and provided detailed requirements for the samples, including desired color corrections, specific artwork placement, updated date codes, and packaging specifications (white box with date code, shrink wrap).\n\nFollowing these approvals, Prosperous Printing proceeded with sample production. The samples were completed and dispatched, with Lydia Cheng providing feedback upon review. Key issues identified included incorrect logo placement on the window box and printing color deviations (e.g., darker oranges and reds, duller backgrounds) compared to an approved sample. Additionally, a concern was raised about the black outline on characters appearing \"melted\" or showing excess powder. Prosperous Printing acknowledged these issues, indicating that the logo placement and color deviations would be corrected in the mass production files. They also committed to paying special attention to the black outline issue during bulk manufacturing.\n\nThe project is currently at a stage where final approval for mass production is pending. Lydia Cheng has communicated that her designer is on vacation until July 5th, and therefore, final approval is not expected until mid-July.\n\nThe core problem stems from the feedback received on the initial samples, highlighting discrepancies that need to be addressed and confirmed before mass production can commence. The immediate objective is to address Lydia Cheng's feedback, finalize any necessary adjustments to the production files, and be prepared for her final approval upon her designer's return.\n\n---EndOfDescription---\n\n---TaskAssignment---\n\n» Task 1: Address Sample Feedback and Confirm Production Adjustments\n- Description: Review and address the specific issues raised by Lydia Cheng regarding the sample cards. This includes confirming the planned corrections for logo placement, printing color deviations, and the black outline issue to ensure client satisfaction before mass production.\n  - Subtasks:\n    - 1.1: Verify the correction for the window box logo placement and confirm the updated file reflects the correct Kimochis logo orientation.\n      - Description: Ensure the production file is updated to show the correct logo placement as per client feedback.\n    - 1.2: Assess and confirm the planned adjustments for printing colors (orange and red tones, dull backgrounds) to meet the client's expectations.\n      - Description: Confirm that the file color adjustments will resolve the noted dullness and tone differences in the printing.\n    - 1.3: Investigate the \"melted\" black outline issue and confirm the specific measures to be taken during mass production to prevent this defect.\n      - Description: Detail the process or checks that will ensure the black outlines are sharp and free from excess powder in the final product.\n    - 1.4: Prepare a consolidated summary of all feedback addressed and the proposed solutions for client review.\n      - Description: Compile all corrections and confirmations into a clear document for Lydia Cheng's final approval.\n\n» Task 2: Finalize Production Readiness and Communication\n- Description: Ensure all production files and specifications are finalized based on the approved sample feedback and prepare for the client's final sign-off. This includes confirming all packaging and labeling requirements.\n  - Subtasks:\n    - 2.1: Update the master production files with all agreed-upon corrections from the sample review.\n      - Description: Implement the finalized artwork and color corrections into the print-ready files.\n    - 2.2: Confirm the final date code to be printed on the white box and cards for mass production.\n      - Description: Ensure the correct \"25xxA\" date code is correctly implemented across all relevant components.\n    - 2.3: Verify that the white box will be printed with the date code and that shrink wrapping is included as per client requirements.\n      - Description: Confirm adherence to the specific packaging and finishing instructions provided by the client.\n    - 2.4: Prepare for communication with the client once her designer returns, providing the updated information and readiness for final order confirmation.\n      - Description: Anticipate the client's designer's return and be ready to present the finalized details for approval.\n\n---EndOfTaskAssignment---", "event_log": null, "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "visibility": "public", "created_by": 1, "assigned_to": null, "created_at": "2025-06-30 22:29:19", "updated_at": "2025-06-30 22:29:19", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 0, "event_log_last_edited_at": null, "event_log_last_edited_by": null, "creator_username": "admin", "assignee_username": null}, {"id": 3, "title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]", "description": null, "full_description": "說明：\n此項目涉及「Feelings deck (Playing cards) 白云游戲咭」的印刷生產。\n\n事件背景：\n項目初期，李寧於 2025 年 5 月 5 日檢查了新的文件，發現多處問題，包括部分卡片的色彩模式 (RGB/CMYK) 不一致、印刷圖框尺寸縮小、文字使用 4C 黑、文件格式差異 (PDF vs INDD)、日期代碼錯誤 (2405A 應為 2505A)、共用背面文件和貼紙未更新，以及與舊文件相比的印刷尺寸調整。李寧隨後於 5 月 6 日更新了報告並重傳 PDF 給客人審批。\n\n在 5 月 23 日，李寧發送了更新的 54 張卡片文件，包含尺寸調整和紙盒上的印刷日期信息，並請求確認。同日，Zoey Poon 轉達了客戶的具體要求，確認需打 6 套樣品，並提供了詳細指示，包括按新文件尺寸製作、調整特定卡片、將日期代碼更改為 2508A、無需額外貼紙、確保壓紋工藝良好、白盒需印刷日期代碼，以及要求在 6 月 10 日前收到樣品。\n\n於 5 月 27 日，Zoey Poon 確認文件無誤並安排打樣。\n\n然而，在收到樣品後，客戶（透過 Zoey Poon 和 Candy 的郵件）提出了一系列問題。主要問題包括：\n- 窗盒設計錯誤：應為 Kimochis 標誌而非鑰匙 #1，且需背面朝上。\n- 印刷顏色偏差：\n    - Frustrated 卡片：橙色比舊版樣品更深。\n    - Shy 卡片：紅色比舊版樣品稍深。\n    - Big Feeling、Medium Feeling 和 Small Feeling 卡片：橙色背景顯得暗淡。\n- 角色黑色輪廓問題：Big Feeling 和 Small Feeling 卡片上的黑色輪廓出現暈染（粉末過量）。\n- 最後一張 Kimochis 卡片（帶日期碼）：橙色背景相比上次印刷顯得暗淡。\n\n團隊對這些問題進行了討論和回覆。李寧針對顏色偏差和黑色輪廓問題提供了文件改色和印刷調整的建議。Candy 在收到回覆後，要求李寧在大貨時特別留意第 3 和第 4 點的問題，並確認了部分顏色問題的改善方案。\n\n核心問題：\n項目目前面臨的主要問題是印刷樣品與客戶期望之間存在差異，特別是在印刷顏色準確性、特定圖案細節（如黑色輪廓）以及窗盒設計的準確性方面。客戶已提出具體要求，並設定了樣品交付的截止日期。\n\n核心重點：\n確保所有印刷顏色符合客戶樣品要求，修正窗盒設計錯誤，解決角色黑色輪廓的暈染問題，並準確處理日期代碼和白盒印刷。", "event_log": "事件日誌：\n- 23/05/2025 - 李寧 (Prosperous): 發送了更新的 54 張卡片文件，包含尺寸調整和紙盒上的印刷日期信息，請求確認是否可進行打稿。\n- 23/05/2025 - <PERSON><PERSON> Poon (Prosperous): 轉發了客人確認需打 6 套樣的要求，包含具體指示，如日期代碼 (2508A)、壓紋工藝、白盒印刷日期代碼等，並要求於 6 月 10 日前收到樣品。\n- 27/05/2025 - Zoey Poon (Prosperous): 確認文件已 OK，請安排打樣。\n- 06/05/2025 - 李寧 (Prosperous): 發送了報告的第 2 點更正及第 7 點補充，並重傳全書 PDF 給客人審批。\n- 05/05/2025 - 李寧 (Prosperous): 檢查新文件，發現 RGB/CMYK 問題、印刷圖框尺寸差異、4C 黑文字、PDF/INDD 格式差異、日期代碼變更 (2405A 改為 2505A)、共用背面文件和貼紙未改正，以及上次印刷尺寸的調整。\n- 17/06/2025 - <PERSON><PERSON> Poon (Prosperous): 客人收到樣品後提出問題，包括窗盒設計錯誤、部分印刷顏色偏差（Frustrated 卡片橙色、Shy 卡片紅色、Big/Medium/Small Feeling 卡片橙色背景暗淡）、角色黑色輪廓有暈染（粉末過量）以及最後一張 Kimochis 卡片的橙色背景暗淡。\n- 17/06/2025 - 李寧 (Prosperous): 回覆 Zoey Poon 提出的問題。\n- 23/06/2025 - Candy (Prosperous): 針對客人回覆，確認了窗盒設計錯誤及大貨需更正；詳細說明了印刷顏色偏差問題，包括 Frustrated 卡片（橙色）、Shy 卡片（紅色）、Big/Medium/Small Feeling 卡片（橙色背景暗淡），並就黑色輪廓暈染和橙色背景暗淡問題提出初步檢查和改善建議。\n- 24/06/2025 - 李寧 (Prosperous): 回覆 Candy 的意見，針對顏色偏差和黑色輪廓問題提供解釋和改善方案。\n- 26/06/2025 - Candy (Prosperous): 收到回覆，並請李寧在大貨時多加留意第 3 和第 4 點的問題。", "status": "todo", "priority": "medium", "progress": 10, "due_date": null, "completed_date": null, "visibility": "public", "created_by": 1, "assigned_to": null, "created_at": "2025-06-30 22:40:58", "updated_at": "2025-06-30 22:41:21", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-30 22:41:21", "full_description_last_edited_by": 1, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-30 22:41:16", "event_log_last_edited_by": 1, "creator_username": "admin", "assignee_username": null}, {"id": 4, "title": "Project Briefing: Book project // CallKodi", "description": null, "full_description": "This document outlines the current status and requirements for the book project initiated by CallKodi. The project aims to produce 10,000 units of a book with specific physical dimensions and page counts.\n\nThe situation evolved following an initial request for quotation (RFQ) from <PERSON> of CallKodi. The specifications provided included:\n- Quantity: 10,000 units\n- Trim Size: 7 x 10 inches\n- Page Count: 4,200 pages (equivalent to 2100 sheets)\n- Body Stock: 25 lb Uncoated Sheet\n- Cover: 12 PT Kivar Linen, 4/4 color plus coating\n- Binding: Perfect Bound with round cornering\n- Packaging: Individual carton pack mail\n\nProsperous Printing, represented by <PERSON>, reviewed these specifications and identified significant limitations with their current equipment. The core problem lies in the book's overall thickness. For 4,200 pages (2100 sheets), the book block thickness is estimated to be approximately 105mm. This far exceeds Prosperous Printing's binding machine capacity, which is limited to a maximum of 60mm. Additionally, the specified 25lb (38gsm) paper is below their typical handling range, which starts around 50gsm. <PERSON> noted that such a thick volume poses structural integrity risks even if manual assembly were considered, and that few printers can handle books exceeding 100mm in thickness.\n\nIn response, <PERSON> proposed a solution: splitting the book into three separate volumes to make it manageable and align with printing capabilities. Following this, <PERSON> clarified that the 4,200 pages equate to 2100 sheets, the paper GSM is adjustable by the client, and that the client wishes to send test rolls for quality verification. <PERSON> requested a quotation based on the material to be used.\n\nCurrently, <PERSON> has acknowledged <PERSON>'s feedback, confirmed it has been shared with the client, and is awaiting their comments. The immediate objective is to receive the client's decision on how to proceed with the revised specifications and subsequently obtain a revised quotation from Prosperous Printing.", "event_log": "Event Log:\n\n- 08/05/2024 - <PERSON> (CallKodi): Initiated RFQ for a book project, requesting quotes with vendor-supplied and customer-supplied paper, detailing quantity (10,000), size (7x10), pages (4,200), body stock (25 lb Uncoated Sheet), cover specs, binding (Perfect Bound), and packaging.\n- 08/05/2024 - <PERSON> (Prosperous): Informed <PERSON> Lu that the project's paper weight (25lb/38gsm) and book block thickness (~105mm for 4,200 pages) exceed Prosperous's equipment capabilities (max 60mm thickness, min ~50gsm paper). Offered to quote for different specifications.\n- 09/05/2024 - <PERSON> (CallKodi): Clarified the book has 4,200 pages (2100 sheets), paper GSM is adjustable, and the client wants to test rolls. Requested a quotation based on the material to be used.\n- 10/05/2024 - <PERSON> (CallKodi): Noted <PERSON>'s explanation and suggestions, shared them with the client, and is awaiting feedback. Thanked <PERSON> for support.", "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "visibility": "public", "created_by": 1, "assigned_to": null, "created_at": "2025-06-30 23:11:39", "updated_at": "2025-06-30 23:11:52", "full_description_edit_count": 1, "full_description_last_edited_at": "2025-06-30 23:11:52", "full_description_last_edited_by": 1, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-06-30 23:11:48", "event_log_last_edited_by": 1, "creator_username": "admin", "assignee_username": null}, {"id": 5, "title": "項目簡報: NGS存檔文件請求 // Qualibre Incorporated", "description": null, "full_description": "Subject: 項目簡報: NGS存檔文件請求 // Qualibre Incorporated\n\n---EventLog---\n事件日誌:\n\n- 24/06/2025 - <PERSON> (Qualibre): 請求提供書本 \"Little Kids First Big Book of Bugs\" 的存檔文件。\n- 24/06/2025 - <PERSON> (Qualibre): 新增書本 \"NGK everything SHARKS\" 和 \"NGK Everything Reptiles\" 的存檔文件請求。\n- 25/06/2025 - <PERSON> (Qualibre): 緊急請求提供多本書的存檔文件。\n- 25/06/2025 - <PERSON> (Qualibre): 新增書本 \"Weird But True Christmas\" 的緊急存檔文件請求。\n- 25/06/2025 - <PERSON> (Prosperous): 回覆請求，確認部分書本已處理，正檢查 \"The Coolest Stuff on Earth\"。\n- 26/06/2025 - <PERSON> (Qualibre): 詢問是否能在下班前找到 \"The Coolest Stuff on Earth\" 的存檔文件。\n- 26/06/2025 - <PERSON> (Prosperous): 確認 \"The Coolest Stuff on Earth\" 已找到，將處理並上傳。\n- 27/06/2025 - <PERSON> (Qualibre): 通知文件未上傳，要求盡快聯繫惠州廠處理。\n- 27/06/2025 - <PERSON><PERSON> (Prosperous): 確認文件已上傳至FTP。\n- 28/06/2025 - Lam Chuen (Qualibre): 請求提供長城生產的書本 \"Quiz Whiz 學能測驗\" 的存檔文件。\n- 28/06/2025 - Harry Lam (Prosperous): 確認將指派任務。\n- 01/07/2025 - Lam Chuen (Qualibre): 確認 \"Quiz Whiz 學能測驗\" 的存檔文件已成功下載。\n\n---EndOfEventLog---\n\n---Description---\n\n詳細說明\n\n項目目標是根據客戶匯科有限公司 (Qualibre Incorporated) 的要求，提供多本書籍的存檔文件。此請求於2025年6月24日開始，最初涉及幾本書籍，隨後在6月25日擴展為更緊急的要求，並包含更多書目。過程中，我們需要追蹤特定書籍，例如由長城生產的 \"Quiz Whiz 學能測驗\"，並確保其檔案能被順利處理和上傳。最初曾出現檔案上傳和處理上的延誤，需要後續跟進。\n\n核心問題觸發點是客戶匯科有限公司（由Lam Chuen代表）緊急需要多本書籍的存檔文件。其中一本由長城生產的 \"Quiz Whiz 學能測驗\" 是關鍵的挑戰，需要特別協調和跟進，以確保檔案的可用性。\n\n目前的即時目標是確認所有要求的存檔文件均已成功找到、處理並上傳至FTP供客戶檢索。最後一封郵件確認了最後一本要求的文件 (\"Quiz Whiz\") 已成功下載，表明此請求已完成。\n\n---EndofDescription---\n\n---TaskAssignment---\n任務分配:\n\n» 任務 1: 確認存檔文件交付完成\n- 描述: 正式確認所有要求的存檔文件已成功交付給匯科有限公司，並且客戶已確認收到。這有助於項目結案及妥善的記錄保存。\n  - 分支任務:\n    - 1.1: 核實客戶最終確認。\n      - 描述: 審閱Lam Chuen於2025年7月1日的最後通訊，確認 \"Quiz Whiz\" 文件已成功下載。\n    - 1.2: 更新內部項目狀態。\n      - 描述: 在項目管理系統中將存檔文件交付請求標記為已完成。\n    - 1.3: 歸檔相關通訊。\n      - 描述: 確保所有與此請求相關的電子郵件通訊均已妥善歸檔以供將來參考。\n\n---EndOfTaskAssignment---", "event_log": "事件日誌:\n\n- 24/06/2025 - <PERSON> (Qualibre): 請求提供書本 \"Little Kids First Big Book of Bugs\" 的存檔文件。\n- 24/06/2025 - <PERSON> (Qualibre): 新增書本 \"NGK everything SHARKS\" 和 \"NGK Everything Reptiles\" 的存檔文件請求。\n- 25/06/2025 - <PERSON> (Qualibre): 緊急請求提供多本書的存檔文件。\n- 25/06/2025 - <PERSON> (Qualibre): 新增書本 \"Weird But True Christmas\" 的緊急存檔文件請求。\n- 25/06/2025 - <PERSON> (Prosperous): 回覆請求，確認部分書本已處理，正檢查 \"The Coolest Stuff on Earth\"。\n- 26/06/2025 - <PERSON> (Qualibre): 詢問是否能在下班前找到 \"The Coolest Stuff on Earth\" 的存檔文件。\n- 26/06/2025 - <PERSON> (Prosperous): 確認 \"The Coolest Stuff on Earth\" 已找到，將處理並上傳。\n- 27/06/2025 - <PERSON> (Qualibre): 通知文件未上傳，要求盡快聯繫惠州廠處理。\n- 27/06/2025 - <PERSON><PERSON> (Prosperous): 確認文件已上傳至FTP。\n- 28/06/2025 - <PERSON> (Qualibre): 請求提供長城生產的書本 \"Quiz Whiz 學能測驗\" 的存檔文件。\n- 28/06/2025 - Harry Lam (Prosperous): 確認將指派任務。\n- 01/07/2025 - Lam Chuen (Qualibre): 確認 \"Quiz Whiz 學能測驗\" 的存檔文件已成功下載。", "status": "todo", "priority": "medium", "progress": 0, "due_date": null, "completed_date": null, "visibility": "public", "created_by": 1, "assigned_to": null, "created_at": "2025-07-01 10:53:37", "updated_at": "2025-07-01 10:53:55", "full_description_edit_count": 0, "full_description_last_edited_at": null, "full_description_last_edited_by": null, "event_log_edit_count": 1, "event_log_last_edited_at": "2025-07-01 10:53:55", "event_log_last_edited_by": 1, "creator_username": "admin", "assignee_username": null}], "comments": [], "activities": [{"id": 6, "project_id": 2, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-30 22:29:19", "user_username": "admin", "project_title": "New Task"}, {"id": 7, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-30 22:40:58", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 8, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "event_log_edit", "description": "edited Event Log (V1)", "metadata": "{\"fieldName\":\"event_log\",\"previousContent\":\"\",\"newContent\":\"事件日誌：\\n- 23/05/2025 - 李寧 (Prosperous): 發送了更新的 54 張卡片文件，包含尺寸調整和紙盒上的印刷日期信息，請求確認是否可進行打稿。\\n- 23/05/2025 - <PERSON><PERSON> (Prosperous): 轉發了客人確認需打 6 套樣的要求，包含具體指示，如日期代碼 (2508A)、壓紋工藝、白盒印刷日期代碼等，並要求於 6 月 10 日前收到樣品。\\n- 27/05/2025 - Zoey Poon (Prosperous): 確認文件已 OK，請安排打樣。\\n- 06/05/2025 - 李寧 (Prosperous): 發送了報告的第 2 點更正及第 7 點補充，並重傳全書 PDF 給客人審批。\\n- 05/05/2025 - 李寧 (Prosperous): 檢查新文件，發現 RGB/CMYK 問題、印刷圖框尺寸差異、4C 黑文字、PDF/INDD 格式差異、日期代碼變更 (2405A 改為 2505A)、共用背面文件和貼紙未改正，以及上次印刷尺寸的調整。\\n- 17/06/2025 - <PERSON><PERSON> (Prosperous): 客人收到樣品後提出問題，包括窗盒設計錯誤、部分印刷顏色偏差（Frustrated 卡片橙色、Shy 卡片紅色、Big/Medium/Small Feeling 卡片橙色背景暗淡）、角色黑色輪廓有暈染（粉末過量）以及最後一張 Kimochis 卡片的橙色背景暗淡。\\n- 17/06/2025 - 李寧 (Prosperous): 回覆 Zoey Poon 提出的問題。\\n- 23/06/2025 - Candy (Prosperous): 針對客人回覆，確認了窗盒設計錯誤及大貨需更正；詳細說明了印刷顏色偏差問題，包括 Frustrated 卡片（橙色）、Shy 卡片（紅色）、Big/Medium/Small Feeling 卡片（橙色背景暗淡），並就黑色輪廓暈染和橙色背景暗淡問題提出初步檢查和改善建議。\\n- 24/06/2025 - 李寧 (Prosperous): 回覆 Candy 的意見，針對顏色偏差和黑色輪廓問題提供解釋和改善方案。\\n- 26/06/2025 - Candy (Prosperous): 收到回覆，並請李寧在大貨時多加留意第 3 和第 4 點的問題。\",\"versionNumber\":1,\"editCount\":1}", "created_at": "2025-06-30 22:41:16", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 9, "project_id": 3, "task_id": 3, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"» 任務 1: 確認並修正客戶對樣品的印刷問題\"", "metadata": null, "created_at": "2025-06-30 22:41:17", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 10, "project_id": 3, "task_id": 4, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"1.1: 修正窗盒設計\" in task \"» 任務 1: 確認並修正客戶對樣品的印刷問題\"", "metadata": null, "created_at": "2025-06-30 22:41:17", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 11, "project_id": 3, "task_id": 5, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"1.2: 校準印刷顏色\" in task \"» 任務 1: 確認並修正客戶對樣品的印刷問題\"", "metadata": null, "created_at": "2025-06-30 22:41:17", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 12, "project_id": 3, "task_id": 6, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"1.3: 解決黑色輪廓暈染問題\" in task \"» 任務 1: 確認並修正客戶對樣品的印刷問題\"", "metadata": null, "created_at": "2025-06-30 22:41:17", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 13, "project_id": 3, "task_id": 7, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"1.4: 調整暗淡的橙色背景\" in task \"» 任務 1: 確認並修正客戶對樣品的印刷問題\"", "metadata": null, "created_at": "2025-06-30 22:41:17", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 14, "project_id": 3, "task_id": 8, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"» 任務 2: 驗證並更新所有文件以供大貨生產\"", "metadata": null, "created_at": "2025-06-30 22:41:17", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 15, "project_id": 3, "task_id": 9, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"2.1: 確認最終文件無誤\" in task \"» 任務 2: 驗證並更新所有文件以供大貨生產\"", "metadata": null, "created_at": "2025-06-30 22:41:17", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 16, "project_id": 3, "task_id": 10, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"2.2: 更新日期代碼\" in task \"» 任務 2: 驗證並更新所有文件以供大貨生產\"", "metadata": null, "created_at": "2025-06-30 22:41:17", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 17, "project_id": 3, "task_id": 11, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"2.3: 確保壓紋工藝質量\" in task \"» 任務 2: 驗證並更新所有文件以供大貨生產\"", "metadata": null, "created_at": "2025-06-30 22:41:17", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 18, "project_id": 3, "task_id": 12, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"2.4: 安排大貨生產前的最終確認\" in task \"» 任務 2: 驗證並更新所有文件以供大貨生產\"", "metadata": null, "created_at": "2025-06-30 22:41:17", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 19, "project_id": 3, "task_id": null, "user_id": 1, "activity_type": "project_details_edit", "description": "edited Project Details (V1)", "metadata": "{\"fieldName\":\"full_description\",\"previousContent\":\"主旨：項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]\\n\\n---EventLog---\\n事件日誌：\\n- 23/05/2025 - 李寧 (Prosperous): 發送了更新的 54 張卡片文件，包含尺寸調整和紙盒上的印刷日期信息，請求確認是否可進行打稿。\\n- 23/05/2025 - Zoey Poon (Prosperous): 轉發了客人確認需打 6 套樣的要求，包含具體指示，如日期代碼 (2508A)、壓紋工藝、白盒印刷日期代碼等，並要求於 6 月 10 日前收到樣品。\\n- 27/05/2025 - Zoey Poon (Prosperous): 確認文件已 OK，請安排打樣。\\n- 06/05/2025 - 李寧 (Prosperous): 發送了報告的第 2 點更正及第 7 點補充，並重傳全書 PDF 給客人審批。\\n- 05/05/2025 - 李寧 (Prosperous): 檢查新文件，發現 RGB/CMYK 問題、印刷圖框尺寸差異、4C 黑文字、PDF/INDD 格式差異、日期代碼變更 (2405A 改為 2505A)、共用背面文件和貼紙未改正，以及上次印刷尺寸的調整。\\n- 17/06/2025 - Zoey Poon (Prosperous): 客人收到樣品後提出問題，包括窗盒設計錯誤、部分印刷顏色偏差（Frustrated 卡片橙色、Shy 卡片紅色、Big/Medium/Small Feeling 卡片橙色背景暗淡）、角色黑色輪廓有暈染（粉末過量）以及最後一張 Kimochis 卡片的橙色背景暗淡。\\n- 17/06/2025 - 李寧 (Prosperous): 回覆 Zoey Poon 提出的問題。\\n- 23/06/2025 - Candy (Prosperous): 針對客人回覆，確認了窗盒設計錯誤及大貨需更正；詳細說明了印刷顏色偏差問題，包括 Frustrated 卡片（橙色）、Shy 卡片（紅色）、Big/Medium/Small Feeling 卡片（橙色背景暗淡），並就黑色輪廓暈染和橙色背景暗淡問題提出初步檢查和改善建議。\\n- 24/06/2025 - 李寧 (Prosperous): 回覆 Candy 的意見，針對顏色偏差和黑色輪廓問題提供解釋和改善方案。\\n- 26/06/2025 - Candy (Prosperous): 收到回覆，並請李寧在大貨時多加留意第 3 和第 4 點的問題。\\n---EndOfEventLog---\\n\\n---Description---\\n說明：\\n此項目涉及「Feelings deck (Playing cards) 白云游戲咭」的印刷生產。\\n\\n事件背景：\\n項目初期，李寧於 2025 年 5 月 5 日檢查了新的文件，發現多處問題，包括部分卡片的色彩模式 (RGB/CMYK) 不一致、印刷圖框尺寸縮小、文字使用 4C 黑、文件格式差異 (PDF vs INDD)、日期代碼錯誤 (2405A 應為 2505A)、共用背面文件和貼紙未更新，以及與舊文件相比的印刷尺寸調整。李寧隨後於 5 月 6 日更新了報告並重傳 PDF 給客人審批。\\n\\n在 5 月 23 日，李寧發送了更新的 54 張卡片文件，包含尺寸調整和紙盒上的印刷日期信息，並請求確認。同日，Zoey Poon 轉達了客戶的具體要求，確認需打 6 套樣品，並提供了詳細指示，包括按新文件尺寸製作、調整特定卡片、將日期代碼更改為 2508A、無需額外貼紙、確保壓紋工藝良好、白盒需印刷日期代碼，以及要求在 6 月 10 日前收到樣品。\\n\\n於 5 月 27 日，Zoey Poon 確認文件無誤並安排打樣。\\n\\n然而，在收到樣品後，客戶（透過 Zoey Poon 和 Candy 的郵件）提出了一系列問題。主要問題包括：\\n- 窗盒設計錯誤：應為 Kimochis 標誌而非鑰匙 #1，且需背面朝上。\\n- 印刷顏色偏差：\\n    - Frustrated 卡片：橙色比舊版樣品更深。\\n    - Shy 卡片：紅色比舊版樣品稍深。\\n    - Big Feeling、Medium Feeling 和 Small Feeling 卡片：橙色背景顯得暗淡。\\n- 角色黑色輪廓問題：Big Feeling 和 Small Feeling 卡片上的黑色輪廓出現暈染（粉末過量）。\\n- 最後一張 Kimochis 卡片（帶日期碼）：橙色背景相比上次印刷顯得暗淡。\\n\\n團隊對這些問題進行了討論和回覆。李寧針對顏色偏差和黑色輪廓問題提供了文件改色和印刷調整的建議。Candy 在收到回覆後，要求李寧在大貨時特別留意第 3 和第 4 點的問題，並確認了部分顏色問題的改善方案。\\n\\n核心問題：\\n項目目前面臨的主要問題是印刷樣品與客戶期望之間存在差異，特別是在印刷顏色準確性、特定圖案細節（如黑色輪廓）以及窗盒設計的準確性方面。客戶已提出具體要求，並設定了樣品交付的截止日期。\\n\\n核心重點：\\n確保所有印刷顏色符合客戶樣品要求，修正窗盒設計錯誤，解決角色黑色輪廓的暈染問題，並準確處理日期代碼和白盒印刷。\\n---EndofDescription---\\n\\n---TaskAssignment---\\n任務分配：\\n» 任務 1: 確認並修正客戶對樣品的印刷問題\\n- 描述：客戶指出窗盒設計錯誤、多張卡片的印刷顏色偏差（特別是橙色和紅色）、以及角色黑色輪廓的暈染問題。必須根據客戶的反饋和團隊的討論，修正文件並確保大貨印刷達到客戶要求。\\n  - 分支任務：\\n    - 1.1: 修正窗盒設計\\n      - 描述：檢查並確保窗盒上的設計為正確的 Kimochis 標誌，並按客戶要求背面朝上印刷。\\n    - 1.2: 校準印刷顏色\\n      - 描述：根據客戶提供的舊樣品和反饋，調整 Frustrated 卡片（橙色）、Shy 卡片（紅色）以及 Big/Medium/Small Feeling 卡片的橙色背景顏色，確保與舊版樣品盡量一致。\\n    - 1.3: 解決黑色輪廓暈染問題\\n      - 描述：檢查 Big Feeling 和 Small Feeling 卡片上的角色黑色輪廓，修正文件或印刷工藝，避免暈染情況出現。\\n    - 1.4: 調整暗淡的橙色背景\\n      - 描述：針對 Big Feeling、Medium Feeling 和 Small Feeling 卡片以及最後一張 Kimochis 卡片，評估並調整橙色背景的飽和度，使其不再顯得暗淡。\\n\\n» 任務 2: 驗證並更新所有文件以供大貨生產\\n- 描述：在解決了所有客戶提出的樣品問題後，需要最終驗證所有相關文件，包括卡片設計、顏色設置、日期代碼和紙盒印刷信息，確保所有內容準確無誤，符合最終確認的規格。\\n  - 分支任務：\\n    - 2.1: 確認最終文件無誤\\n      - 描述：綜合所有修改，最終檢查所有卡片文件、窗盒文件、紙盒文件，確保所有尺寸、顏色、文字、圖像和特殊工藝要求均已正確應用。\\n    - 2.2: 更新日期代碼\\n      - 描述：確保所有需要印刷日期代碼的文件（包括紙盒）已更新為客戶指定的 2508A。\\n    - 2.3: 確保壓紋工藝質量\\n      - 描述：根據客戶的友情提醒，在大貨生產時特別關注壓紋工藝的質量，避免出現 2024 年校樣因工藝不佳被拒收的情況。\\n    - 2.4: 安排大貨生產前的最終確認\\n      - 描述：在文件準備就緒後，安排一次最終的內部審核或與客戶的最終確認，以避免在大貨生產中出現任何錯誤。\\n---EndOfTaskAssignment---\",\"newContent\":\"說明：\\n此項目涉及「Feelings deck (Playing cards) 白云游戲咭」的印刷生產。\\n\\n事件背景：\\n項目初期，李寧於 2025 年 5 月 5 日檢查了新的文件，發現多處問題，包括部分卡片的色彩模式 (RGB/CMYK) 不一致、印刷圖框尺寸縮小、文字使用 4C 黑、文件格式差異 (PDF vs INDD)、日期代碼錯誤 (2405A 應為 2505A)、共用背面文件和貼紙未更新，以及與舊文件相比的印刷尺寸調整。李寧隨後於 5 月 6 日更新了報告並重傳 PDF 給客人審批。\\n\\n在 5 月 23 日，李寧發送了更新的 54 張卡片文件，包含尺寸調整和紙盒上的印刷日期信息，並請求確認。同日，Zoey Poon 轉達了客戶的具體要求，確認需打 6 套樣品，並提供了詳細指示，包括按新文件尺寸製作、調整特定卡片、將日期代碼更改為 2508A、無需額外貼紙、確保壓紋工藝良好、白盒需印刷日期代碼，以及要求在 6 月 10 日前收到樣品。\\n\\n於 5 月 27 日，Zoey Poon 確認文件無誤並安排打樣。\\n\\n然而，在收到樣品後，客戶（透過 Zoey Poon 和 Candy 的郵件）提出了一系列問題。主要問題包括：\\n- 窗盒設計錯誤：應為 Kimochis 標誌而非鑰匙 #1，且需背面朝上。\\n- 印刷顏色偏差：\\n    - Frustrated 卡片：橙色比舊版樣品更深。\\n    - Shy 卡片：紅色比舊版樣品稍深。\\n    - Big Feeling、Medium Feeling 和 Small Feeling 卡片：橙色背景顯得暗淡。\\n- 角色黑色輪廓問題：Big Feeling 和 Small Feeling 卡片上的黑色輪廓出現暈染（粉末過量）。\\n- 最後一張 Kimochis 卡片（帶日期碼）：橙色背景相比上次印刷顯得暗淡。\\n\\n團隊對這些問題進行了討論和回覆。李寧針對顏色偏差和黑色輪廓問題提供了文件改色和印刷調整的建議。Candy 在收到回覆後，要求李寧在大貨時特別留意第 3 和第 4 點的問題，並確認了部分顏色問題的改善方案。\\n\\n核心問題：\\n項目目前面臨的主要問題是印刷樣品與客戶期望之間存在差異，特別是在印刷顏色準確性、特定圖案細節（如黑色輪廓）以及窗盒設計的準確性方面。客戶已提出具體要求，並設定了樣品交付的截止日期。\\n\\n核心重點：\\n確保所有印刷顏色符合客戶樣品要求，修正窗盒設計錯誤，解決角色黑色輪廓的暈染問題，並準確處理日期代碼和白盒印刷。\",\"versionNumber\":1,\"editCount\":1}", "created_at": "2025-06-30 22:41:21", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 20, "project_id": 3, "task_id": 12, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"2.4: 安排大貨生產前的最終確認\" status to completed", "metadata": null, "created_at": "2025-06-30 22:41:27", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 21, "project_id": 3, "task_id": 11, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"2.3: 確保壓紋工藝質量\" status to completed", "metadata": null, "created_at": "2025-06-30 22:41:27", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 22, "project_id": 3, "task_id": 10, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"2.2: 更新日期代碼\" status to completed", "metadata": null, "created_at": "2025-06-30 22:41:27", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 23, "project_id": 3, "task_id": 9, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"2.1: 確認最終文件無誤\" status to completed", "metadata": null, "created_at": "2025-06-30 22:41:28", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 24, "project_id": 3, "task_id": 12, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"2.4: 安排大貨生產前的最終確認\" status to todo", "metadata": null, "created_at": "2025-06-30 22:41:31", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 25, "project_id": 3, "task_id": 11, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"2.3: 確保壓紋工藝質量\" status to todo", "metadata": null, "created_at": "2025-06-30 22:41:32", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 26, "project_id": 3, "task_id": 9, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"2.1: 確認最終文件無誤\" status to todo", "metadata": null, "created_at": "2025-06-30 22:41:33", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 27, "project_id": 3, "task_id": 4, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"1.1: 修正窗盒設計\" status to completed", "metadata": null, "created_at": "2025-06-30 22:41:45", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 28, "project_id": 3, "task_id": 5, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"1.2: 校準印刷顏色\" status to completed", "metadata": null, "created_at": "2025-06-30 22:41:46", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 29, "project_id": 3, "task_id": 6, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"1.3: 解決黑色輪廓暈染問題\" status to completed", "metadata": null, "created_at": "2025-06-30 22:41:47", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 30, "project_id": 3, "task_id": 7, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"1.4: 調整暗淡的橙色背景\" status to completed", "metadata": null, "created_at": "2025-06-30 22:41:48", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 31, "project_id": 3, "task_id": 3, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"» 任務 1: 確認並修正客戶對樣品的印刷問題\" status to completed", "metadata": null, "created_at": "2025-06-30 22:41:50", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 32, "project_id": 3, "task_id": 3, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"» 任務 1: 確認並修正客戶對樣品的印刷問題\" status to todo", "metadata": null, "created_at": "2025-06-30 22:41:52", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 33, "project_id": 3, "task_id": 4, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"1.1: 修正窗盒設計\" status to todo", "metadata": null, "created_at": "2025-06-30 22:41:53", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 34, "project_id": 3, "task_id": 5, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"1.2: 校準印刷顏色\" status to todo", "metadata": null, "created_at": "2025-06-30 22:41:53", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 35, "project_id": 3, "task_id": 6, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"1.3: 解決黑色輪廓暈染問題\" status to todo", "metadata": null, "created_at": "2025-06-30 22:41:54", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 36, "project_id": 3, "task_id": 7, "user_id": 1, "activity_type": "status_change", "description": "Changed task \"1.4: 調整暗淡的橙色背景\" status to todo", "metadata": null, "created_at": "2025-06-30 22:41:54", "user_username": "admin", "project_title": "項目簡報：Feelings deck (Playing cards) 白云游戲咭 // [客戶名稱]"}, {"id": 37, "project_id": 4, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-06-30 23:11:39", "user_username": "admin", "project_title": "Project Briefing: Book project // CallKodi"}, {"id": 38, "project_id": 4, "task_id": null, "user_id": 1, "activity_type": "event_log_edit", "description": "edited Event Log (V1)", "metadata": "{\"fieldName\":\"event_log\",\"previousContent\":\"\",\"newContent\":\"Event Log:\\n\\n- 08/05/2024 - <PERSON> (CallKodi): Initiated RFQ for a book project, requesting quotes with vendor-supplied and customer-supplied paper, detailing quantity (10,000), size (7x10), pages (4,200), body stock (25 lb Uncoated Sheet), cover specs, binding (Perfect Bound), and packaging.\\n- 08/05/2024 - <PERSON> (Prosperous): Informed <PERSON> Lu that the project's paper weight (25lb/38gsm) and book block thickness (~105mm for 4,200 pages) exceed Prosperous's equipment capabilities (max 60mm thickness, min ~50gsm paper). Offered to quote for different specifications.\\n- 09/05/2024 - <PERSON> (CallKodi): Clarified the book has 4,200 pages (2100 sheets), paper GSM is adjustable, and the client wants to test rolls. Requested a quotation based on the material to be used.\\n- 10/05/2024 - <PERSON> (CallKodi): Noted <PERSON>'s explanation and suggestions, shared them with the client, and is awaiting feedback. Thanked <PERSON> for support.\",\"versionNumber\":1,\"editCount\":1}", "created_at": "2025-06-30 23:11:48", "user_username": "admin", "project_title": "Project Briefing: Book project // CallKodi"}, {"id": 39, "project_id": 4, "task_id": 13, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"» Task 1: Await and Confirm Client Decision on Book Specifications\"", "metadata": null, "created_at": "2025-06-30 23:11:48", "user_username": "admin", "project_title": "Project Briefing: Book project // CallKodi"}, {"id": 40, "project_id": 4, "task_id": 14, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"1.1: Receive client feedback on Prosperous Printing's recommendations.\" in task \"» Task 1: Await and Confirm Client Decision on Book Specifications\"", "metadata": null, "created_at": "2025-06-30 23:11:48", "user_username": "admin", "project_title": "Project Briefing: Book project // CallKodi"}, {"id": 41, "project_id": 4, "task_id": 15, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"1.2: Document the agreed-upon revised specifications.\" in task \"» Task 1: Await and Confirm Client Decision on Book Specifications\"", "metadata": null, "created_at": "2025-06-30 23:11:48", "user_username": "admin", "project_title": "Project Briefing: Book project // CallKodi"}, {"id": 42, "project_id": 4, "task_id": 16, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"» Task 2: Prepare and Submit Revised Quotation\"", "metadata": null, "created_at": "2025-06-30 23:11:48", "user_username": "admin", "project_title": "Project Briefing: Book project // CallKodi"}, {"id": 43, "project_id": 4, "task_id": 17, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"2.1: Calculate pricing for revised book specifications.\" in task \"» Task 2: Prepare and Submit Revised Quotation\"", "metadata": null, "created_at": "2025-06-30 23:11:49", "user_username": "admin", "project_title": "Project Briefing: Book project // CallKodi"}, {"id": 44, "project_id": 4, "task_id": 18, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"2.2: Provide updated lead time information.\" in task \"» Task 2: Prepare and Submit Revised Quotation\"", "metadata": null, "created_at": "2025-06-30 23:11:49", "user_username": "admin", "project_title": "Project Briefing: Book project // CallKodi"}, {"id": 45, "project_id": 4, "task_id": 19, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"2.3: Issue the revised quotation to CallKodi.\" in task \"» Task 2: Prepare and Submit Revised Quotation\"", "metadata": null, "created_at": "2025-06-30 23:11:49", "user_username": "admin", "project_title": "Project Briefing: Book project // CallKodi"}, {"id": 46, "project_id": 4, "task_id": null, "user_id": 1, "activity_type": "project_details_edit", "description": "edited Project Details (V1)", "metadata": "{\"fieldName\":\"full_description\",\"previousContent\":\"Subject: Project Briefing: Book project // CallKodi\\n\\n---EventLog---\\nEvent Log:\\n\\n- 08/05/2024 - <PERSON> (CallKodi): Initiated RFQ for a book project, requesting quotes with vendor-supplied and customer-supplied paper, detailing quantity (10,000), size (7x10), pages (4,200), body stock (25 lb Uncoated Sheet), cover specs, binding (Perfect Bound), and packaging.\\n- 08/05/2024 - <PERSON> (Prosperous): Informed <PERSON> that the project's paper weight (25lb/38gsm) and book block thickness (~105mm for 4,200 pages) exceed Prosperous's equipment capabilities (max 60mm thickness, min ~50gsm paper). Offered to quote for different specifications.\\n- 09/05/2024 - Mark <PERSON> (CallKodi): Clarified the book has 4,200 pages (2100 sheets), paper GSM is adjustable, and the client wants to test rolls. Requested a quotation based on the material to be used.\\n- 10/05/2024 - <PERSON> (CallKodi): Noted <PERSON>'s explanation and suggestions, shared them with the client, and is awaiting feedback. Thanked <PERSON> for support.\\n\\n---EndOfEventLog---\\n\\n---Description---\\n\\nThis document outlines the current status and requirements for the book project initiated by CallKodi. The project aims to produce 10,000 units of a book with specific physical dimensions and page counts.\\n\\nThe situation evolved following an initial request for quotation (RFQ) from Mark Lu of CallKodi. The specifications provided included:\\n- Quantity: 10,000 units\\n- Trim Size: 7 x 10 inches\\n- Page Count: 4,200 pages (equivalent to 2100 sheets)\\n- Body Stock: 25 lb Uncoated Sheet\\n- Cover: 12 PT Kivar Linen, 4/4 color plus coating\\n- Binding: Perfect Bound with round cornering\\n- Packaging: Individual carton pack mail\\n\\nProsperous Printing, represented by Harry Lam, reviewed these specifications and identified significant limitations with their current equipment. The core problem lies in the book's overall thickness. For 4,200 pages (2100 sheets), the book block thickness is estimated to be approximately 105mm. This far exceeds Prosperous Printing's binding machine capacity, which is limited to a maximum of 60mm. Additionally, the specified 25lb (38gsm) paper is below their typical handling range, which starts around 50gsm. Harry noted that such a thick volume poses structural integrity risks even if manual assembly were considered, and that few printers can handle books exceeding 100mm in thickness.\\n\\nIn response, Harry Lam proposed a solution: splitting the book into three separate volumes to make it manageable and align with printing capabilities. Following this, Mark Lu clarified that the 4,200 pages equate to 2100 sheets, the paper GSM is adjustable by the client, and that the client wishes to send test rolls for quality verification. Mark requested a quotation based on the material to be used.\\n\\nCurrently, Mark Lu has acknowledged Harry's feedback, confirmed it has been shared with the client, and is awaiting their comments. The immediate objective is to receive the client's decision on how to proceed with the revised specifications and subsequently obtain a revised quotation from Prosperous Printing.\\n\\n---EndofDescription---\\n\\n---TaskAssignment---\\nTask Assignment:\\n\\n» Task 1: Await and Confirm Client Decision on Book Specifications\\n- Description: The client, CallKodi, needs to review the feedback provided by Prosperous Printing regarding the book's physical specifications, particularly its thickness and paper GSM. They must decide on a revised approach, such as splitting the book into multiple volumes, to align with Prosperous Printing's manufacturing capabilities. This confirmation is essential for proceeding with the project.\\n  - Subtasks:\\n    - 1.1: Receive client feedback on Prosperous Printing's recommendations.\\n      - Description: Obtain the client's decision on whether to adopt the suggestion of splitting the book into three volumes or to explore alternative modifications.\\n    - 1.2: Document the agreed-upon revised specifications.\\n      - Description: Once the client confirms their preferred direction, formally record the new specifications for the book project.\\n\\n» Task 2: Prepare and Submit Revised Quotation\\n- Description: Following the client's confirmation of revised specifications, Prosperous Printing is required to prepare and submit a new quotation. This quote should accurately reflect the adjusted book format, paper choices, and any other changes agreed upon, along with the estimated lead time.\\n  - Subtasks:\\n    - 2.1: Calculate pricing for revised book specifications.\\n      - Description: Re-evaluate costs based on the client's approved changes, such as a multi-volume structure or adjusted paper GSM.\\n    - 2.2: Provide updated lead time information.\\n      - Description: Estimate and communicate the production timeline based on the revised project scope.\\n    - 2.3: Issue the revised quotation to CallKodi.\\n      - Description: Send the updated pricing and timeline details to Mark Lu for review and approval.\\n\\n---EndOfTaskAssignment---\",\"newContent\":\"This document outlines the current status and requirements for the book project initiated by CallKodi. The project aims to produce 10,000 units of a book with specific physical dimensions and page counts.\\n\\nThe situation evolved following an initial request for quotation (RFQ) from Mark Lu of CallKodi. The specifications provided included:\\n- Quantity: 10,000 units\\n- Trim Size: 7 x 10 inches\\n- Page Count: 4,200 pages (equivalent to 2100 sheets)\\n- Body Stock: 25 lb Uncoated Sheet\\n- Cover: 12 PT Kivar Linen, 4/4 color plus coating\\n- Binding: Perfect Bound with round cornering\\n- Packaging: Individual carton pack mail\\n\\nProsperous Printing, represented by Harry Lam, reviewed these specifications and identified significant limitations with their current equipment. The core problem lies in the book's overall thickness. For 4,200 pages (2100 sheets), the book block thickness is estimated to be approximately 105mm. This far exceeds Prosperous Printing's binding machine capacity, which is limited to a maximum of 60mm. Additionally, the specified 25lb (38gsm) paper is below their typical handling range, which starts around 50gsm. Harry noted that such a thick volume poses structural integrity risks even if manual assembly were considered, and that few printers can handle books exceeding 100mm in thickness.\\n\\nIn response, Harry Lam proposed a solution: splitting the book into three separate volumes to make it manageable and align with printing capabilities. Following this, Mark Lu clarified that the 4,200 pages equate to 2100 sheets, the paper GSM is adjustable by the client, and that the client wishes to send test rolls for quality verification. Mark requested a quotation based on the material to be used.\\n\\nCurrently, Mark Lu has acknowledged Harry's feedback, confirmed it has been shared with the client, and is awaiting their comments. The immediate objective is to receive the client's decision on how to proceed with the revised specifications and subsequently obtain a revised quotation from Prosperous Printing.\",\"versionNumber\":1,\"editCount\":1}", "created_at": "2025-06-30 23:11:52", "user_username": "admin", "project_title": "Project Briefing: Book project // CallKodi"}, {"id": 47, "project_id": 5, "task_id": null, "user_id": 1, "activity_type": "creation", "description": "Created project \"New Task\"", "metadata": null, "created_at": "2025-07-01 10:53:37", "user_username": "admin", "project_title": "項目簡報: NGS存檔文件請求 // Qualibre Incorporated"}, {"id": 48, "project_id": 5, "task_id": null, "user_id": 1, "activity_type": "event_log_edit", "description": "edited Event Log (V1)", "metadata": "{\"fieldName\":\"event_log\",\"previousContent\":\"\",\"newContent\":\"事件日誌:\\n\\n- 24/06/2025 - <PERSON> (Qualibre): 請求提供書本 \\\"Little Kids First Big Book of Bugs\\\" 的存檔文件。\\n- 24/06/2025 - <PERSON> (Qualibre): 新增書本 \\\"NGK everything SHARKS\\\" 和 \\\"NGK Everything Reptiles\\\" 的存檔文件請求。\\n- 25/06/2025 - <PERSON> (Qualibre): 緊急請求提供多本書的存檔文件。\\n- 25/06/2025 - <PERSON> (Qualibre): 新增書本 \\\"Weird But True Christmas\\\" 的緊急存檔文件請求。\\n- 25/06/2025 - <PERSON> (Prosperous): 回覆請求，確認部分書本已處理，正檢查 \\\"The Coolest Stuff on Earth\\\"。\\n- 26/06/2025 - <PERSON> (Qualibre): 詢問是否能在下班前找到 \\\"The Coolest Stuff on Earth\\\" 的存檔文件。\\n- 26/06/2025 - <PERSON> (Prosperous): 確認 \\\"The Coolest Stuff on Earth\\\" 已找到，將處理並上傳。\\n- 27/06/2025 - <PERSON> (Qualibre): 通知文件未上傳，要求盡快聯繫惠州廠處理。\\n- 27/06/2025 - Zoey Poon (Prosperous): 確認文件已上傳至FTP。\\n- 28/06/2025 - Lam Chuen (Qualibre): 請求提供長城生產的書本 \\\"Quiz Whiz 學能測驗\\\" 的存檔文件。\\n- 28/06/2025 - Harry Lam (Prosperous): 確認將指派任務。\\n- 01/07/2025 - Lam Chuen (Qualibre): 確認 \\\"Quiz Whiz 學能測驗\\\" 的存檔文件已成功下載。\",\"versionNumber\":1,\"editCount\":1}", "created_at": "2025-07-01 10:53:55", "user_username": "admin", "project_title": "項目簡報: NGS存檔文件請求 // Qualibre Incorporated"}, {"id": 49, "project_id": 5, "task_id": 20, "user_id": 1, "activity_type": "task_creation", "description": "Created task \"» 任務 1: 確認存檔文件交付完成\"", "metadata": null, "created_at": "2025-07-01 10:53:56", "user_username": "admin", "project_title": "項目簡報: NGS存檔文件請求 // Qualibre Incorporated"}, {"id": 50, "project_id": 5, "task_id": 21, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"1.1: 核實客戶最終確認。\" in task \"» 任務 1: 確認存檔文件交付完成\"", "metadata": null, "created_at": "2025-07-01 10:53:56", "user_username": "admin", "project_title": "項目簡報: NGS存檔文件請求 // Qualibre Incorporated"}, {"id": 51, "project_id": 5, "task_id": 22, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"1.2: 更新內部項目狀態。\" in task \"» 任務 1: 確認存檔文件交付完成\"", "metadata": null, "created_at": "2025-07-01 10:53:56", "user_username": "admin", "project_title": "項目簡報: NGS存檔文件請求 // Qualibre Incorporated"}, {"id": 52, "project_id": 5, "task_id": 23, "user_id": 1, "activity_type": "task_creation", "description": "Created subtask \"1.3: 歸檔相關通訊。\" in task \"» 任務 1: 確認存檔文件交付完成\"", "metadata": null, "created_at": "2025-07-01 10:53:56", "user_username": "admin", "project_title": "項目簡報: NGS存檔文件請求 // Qualibre Incorporated"}]}