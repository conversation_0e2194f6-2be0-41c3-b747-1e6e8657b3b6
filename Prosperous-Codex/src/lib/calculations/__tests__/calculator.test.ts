// Test suite for calculation functions
// Ensures accuracy and correctness of paper cost calculations

import { calculatePaperCosts } from '../calculator';
import {
  CalculationRequest,
  InnerTextJobInputs,
  CoverJobInputs,
  EndpaperJobInputs
} from '../../types/calculation';

// Test data - only valid pre-cut papers for basic tests
// Using PaperOption interface properties
const testPaperOptions = [
  {
    id: 'test-1',
    name: 'Test Paper 1',
    source: 'Pre-Cut',
    sheetHeight: 787.4,
    sheetWidth: 546.1,
    grainDirection: 'Height',
    caliper: 100,
    costPerReam: 50.00,
    gsm: 80,
    costPerTon: null,
    category: 'Inner Text' as const
  },
  {
    id: 'test-2',
    name: 'Test Paper 2',
    source: 'Pre-Cut',
    sheetHeight: 889.0,
    sheetWidth: 596.9,
    grainDirection: 'Height',
    caliper: 100,
    costPerReam: 22.50,
    gsm: 80,
    costPerTon: null,
    category: 'Inner Text' as const
  }
];

const testInnerTextInputs: InnerTextJobInputs = {
  trimH: 210,
  trimW: 148,
  quantity: 1000,
  bleed: 3,
  gripper: 12,
  colorBar: 8,
  spoilagePct: 0.05,
  alignmentMode: 'Aligned',
  totalPages: 200,
  lip: 12,
  bindingMethod: 'perfectBound',
  isDoubleLipActive: false
};

const testCoverInputs: CoverJobInputs = {
  trimH: 210,
  trimW: 148,
  quantity: 1000,
  bleed: 5,
  gripper: 12,
  colorBar: 8,
  spoilagePct: 0.05,
  alignmentMode: 'Aligned',
  spineThickness: 10,
  coverType: 'paperback',
  turnInAllowance: 18,
  flapWidth: 74
};

const testEndpaperInputs: EndpaperJobInputs = {
  trimH: 210,
  trimW: 148,
  quantity: 1000,
  bleed: 3,
  gripper: 12,
  colorBar: 8,
  spoilagePct: 0.05,
  alignmentMode: 'Aligned',
  endpaperType: 'single'
};

describe('Paper Cost Calculator', () => {
  describe('Inner Text Calculations', () => {
    it('should calculate inner text costs correctly', async () => {
      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: testPaperOptions
      };

      const result = await calculatePaperCosts(request);

      expect(result.results).toHaveLength(2);
      expect(result.benchmarkValue).toBeGreaterThan(0);
      expect(result.errors).toBeUndefined();

      // Check first result
      const firstResult = result.results[0];
      expect(firstResult.error).toBeFalsy();
      expect(firstResult.maxItemsPerSide).toBeGreaterThan(0);
      expect(firstResult.totalSheetsNeeded).toBeGreaterThan(0);
      expect(firstResult.costPerSheet).toBeGreaterThan(0);
      expect(firstResult.totalCost).toBeGreaterThan(0);
      expect(firstResult.costPerBook).toBeGreaterThan(0);
    });

    it('should respect folding limits for inner text', async () => {
      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: testPaperOptions
      };

      const result = await calculatePaperCosts(request);
      
      result.results.forEach(paperResult => {
        if (!paperResult.error) {
          expect(paperResult.maxItemsPerSide).toBeLessThanOrEqual(16);
        }
      });
    });

    it('should calculate book block thickness correctly', async () => {
      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: testPaperOptions
      };

      const result = await calculatePaperCosts(request);
      
      result.results.forEach(paperResult => {
        if (!paperResult.error) {
          const expectedThickness = (testInnerTextInputs.totalPages / 2) * (paperResult.caliperMicrons / 1000);
          expect(paperResult.bookBlockThickness_mm).toBeCloseTo(expectedThickness, 2);
        }
      });
    });
  });

  describe('Cover Calculations', () => {
    it('should calculate cover costs correctly', async () => {
      const request: CalculationRequest = {
        tabId: 'cover',
        jobInputs: testCoverInputs,
        paperOptions: testPaperOptions
      };

      const result = await calculatePaperCosts(request);
      
      expect(result.results).toHaveLength(2);
      expect(result.benchmarkValue).toBeGreaterThan(0);

      result.results.forEach(paperResult => {
        if (!paperResult.error) {
          expect(paperResult.maxItemsPerSide).toBeGreaterThan(0);
          expect(paperResult.totalSheetsNeeded).toBeGreaterThan(0);
          expect(paperResult.costPerSheet).toBeGreaterThan(0);
          expect(paperResult.totalCost).toBeGreaterThan(0);
        }
      });
    });

    it('should handle dust jacket calculations', async () => {
      const dustJacketInputs: CoverJobInputs = {
        ...testCoverInputs,
        coverType: 'dustJacket',
        flapWidth: 74
      };

      const request: CalculationRequest = {
        tabId: 'cover',
        jobInputs: dustJacketInputs,
        paperOptions: testPaperOptions
      };

      const result = await calculatePaperCosts(request);
      
      expect(result.results).toHaveLength(2);
      // Dust jacket should require larger layout dimensions
      result.results.forEach(paperResult => {
        if (!paperResult.error) {
          expect(paperResult.maxItemsPerSide).toBeGreaterThanOrEqual(0);
        }
      });
    });
  });

  describe('Endpaper Calculations', () => {
    it('should calculate endpaper costs correctly', async () => {
      const request: CalculationRequest = {
        tabId: 'endpapers',
        jobInputs: testEndpaperInputs,
        paperOptions: testPaperOptions
      };

      const result = await calculatePaperCosts(request);
      
      expect(result.results).toHaveLength(2);
      expect(result.benchmarkValue).toBeGreaterThan(0);

      result.results.forEach(paperResult => {
        if (!paperResult.error) {
          expect(paperResult.maxItemsPerSide).toBeGreaterThan(0);
          expect(paperResult.totalSheetsNeeded).toBeGreaterThan(0);
          expect(paperResult.costPerSheet).toBeGreaterThan(0);
          expect(paperResult.totalCost).toBeGreaterThan(0);
        }
      });
    });

    it('should handle double leaf endpapers', async () => {
      const doubleLeafInputs: EndpaperJobInputs = {
        ...testEndpaperInputs,
        endpaperType: 'double'
      };

      const request: CalculationRequest = {
        tabId: 'endpapers',
        jobInputs: doubleLeafInputs,
        paperOptions: testPaperOptions
      };

      const result = await calculatePaperCosts(request);
      
      expect(result.results).toHaveLength(2);
      result.results.forEach(paperResult => {
        if (!paperResult.error) {
          expect(paperResult.totalSheetsNeeded).toBeGreaterThan(0);
        }
      });
    });
  });

  describe('Roll Paper Optimization', () => {
    it('should optimize roll height when only width is provided', async () => {
      const rollPaper = {
        id: 'test-roll-height-opt',
        name: 'Roll Paper - Height Optimization',
        source: 'Roll',
        sheetHeight: null, // Height will be inferred and optimized
        sheetWidth: 640, // Fixed roll width
        grainDirection: 'Height',
        caliper: 100,
        costPerReam: null,
        gsm: 80,
        costPerTon: 1200,
        category: 'Inner Text' as const
      };

      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: [rollPaper]
      };

      const result = await calculatePaperCosts(request);

      expect(result.results).toHaveLength(1);
      const rollResult = result.results[0];

      expect(rollResult.error).toBeFalsy();
      expect(rollResult.rollDimensionOptimizedForFitting).toBeTruthy();
      expect(rollResult.inputNoteKey).toBe('resNoteHeightInferred');
      expect(rollResult.rollOptimizationNoteKey).toBe('rollDimensionsOptimized');

      // The optimized height should be less than the maximum press dimension
      // but sufficient to accommodate the layout plus margins
      expect(rollResult.pressH).toBeLessThan(Math.max(720, 1020)); // Less than max initial canvas
      expect(rollResult.pressH).toBeGreaterThan(0);

      // Cost calculation should work correctly for rolls
      expect(rollResult.costPerSheet).toBeGreaterThan(0);
      expect(rollResult.totalCost).toBeGreaterThan(0);
    });

    it('should optimize roll width when only height is provided', async () => {
      const rollPaper = {
        id: 'test-roll-width-opt',
        name: 'Roll Paper - Width Optimization',
        source: 'Roll',
        sheetHeight: 640, // Fixed roll width (manufactured dimension)
        sheetWidth: 0, // Width will be inferred and optimized
        grainDirection: 'Height',
        caliper: 100,
        costPerReam: null,
        gsm: 80,
        costPerTon: 1200,
        category: 'Inner Text' as const
      };

      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: [rollPaper]
      };

      const result = await calculatePaperCosts(request);

      expect(result.results).toHaveLength(1);
      const rollResult = result.results[0];

      expect(rollResult.error).toBeFalsy();
      expect(rollResult.rollDimensionOptimizedForFitting).toBeTruthy();
      expect(rollResult.inputNoteKey).toBe('resNoteWidthInferred');
      expect(rollResult.rollOptimizationNoteKey).toBe('rollDimensionsOptimized');

      // The optimized width should be less than the maximum press dimension
      // but sufficient to accommodate the layout plus margins
      expect(rollResult.pressW).toBeLessThan(Math.max(720, 1020)); // Less than max initial canvas
      expect(rollResult.pressW).toBeGreaterThan(0);

      // Cost calculation should work correctly for rolls
      expect(rollResult.costPerSheet).toBeGreaterThan(0);
      expect(rollResult.totalCost).toBeGreaterThan(0);
    });

    it('should not optimize when both dimensions are provided for rolls', async () => {
      const rollPaper = {
        id: 'test-roll-fixed',
        name: 'Roll Paper - Fixed Dimensions',
        source: 'Roll',
        sheetHeight: 640, // Both dimensions provided
        sheetWidth: 900, // Both dimensions provided
        grainDirection: 'Height',
        caliper: 100,
        costPerReam: null,
        gsm: 80,
        costPerTon: 1200,
        category: 'Inner Text' as const
      };

      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: [rollPaper]
      };

      const result = await calculatePaperCosts(request);

      expect(result.results).toHaveLength(1);
      const rollResult = result.results[0];

      expect(rollResult.error).toBeFalsy();
      expect(rollResult.rollDimensionOptimizedForFitting).toBeFalsy();
      expect(rollResult.inputNoteKey).toBeUndefined();
      expect(rollResult.rollOptimizationNoteKey).toBeUndefined();

      // Dimensions should remain as provided (or rotated if needed for press fit)
      const expectedDims = [640, 900];
      expect(expectedDims).toContain(rollResult.pressH);
      expect(expectedDims).toContain(rollResult.pressW);
    });

    it('should handle roll paper cost calculations correctly', async () => {
      const rollPaper = {
        id: 'test-roll-cost',
        name: 'Roll Paper - Cost Test',
        source: 'Roll',
        sheetHeight: null,
        sheetWidth: 640,
        grainDirection: 'Height',
        caliper: 100,
        costPerReam: null,
        gsm: 80,
        costPerTon: 1200,
        category: 'Inner Text' as const
      };

      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: [rollPaper]
      };

      const result = await calculatePaperCosts(request);
      const rollResult = result.results[0];

      if (!rollResult.error) {
        // Verify cost calculation formula: area (m²) × GSM × cost per tonne / 1000
        const areaM2 = (rollResult.pressW! / 1000) * (rollResult.pressH! / 1000);
        const weightKg = areaM2 * (rollResult.gsm / 1000);
        const expectedCostPerSheet = weightKg * (rollResult.costTonne! / 1000);

        expect(rollResult.costPerSheet).toBeCloseTo(expectedCostPerSheet, 6);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid trim dimensions', async () => {
      const invalidInputs: InnerTextJobInputs = {
        ...testInnerTextInputs,
        trimH: 0,
        trimW: 0
      };

      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: invalidInputs,
        paperOptions: testPaperOptions
      };

      const result = await calculatePaperCosts(request);

      expect(result.errors).toBeDefined();
      expect(result.errors!.length).toBeGreaterThan(0);
    });

    it('should handle invalid paper options', async () => {
      const invalidPaper = {
        id: 'invalid',
        name: 'Invalid Paper',
        source: 'Pre-Cut',
        sheetHeight: 0,
        sheetWidth: 0,
        grainDirection: 'Height',
        caliper: 0,
        costPerReam: 0,
        gsm: 0,
        costPerTon: null,
        category: 'Inner Text' as const
      };

      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: [invalidPaper]
      };

      const result = await calculatePaperCosts(request);

      expect(result.results).toHaveLength(1);
      expect(result.results[0].error).toBeTruthy();
    });

    it('should handle invalid roll paper data', async () => {
      const invalidRollPaper = {
        id: 'invalid-roll',
        name: 'Invalid Roll Paper',
        source: 'Roll',
        sheetHeight: null,
        sheetWidth: 0, // Invalid width
        grainDirection: 'Height',
        caliper: 100,
        costPerReam: null,
        gsm: 80,
        costPerTon: 1200,
        category: 'Inner Text' as const
      };

      const request: CalculationRequest = {
        tabId: 'innerText',
        jobInputs: testInnerTextInputs,
        paperOptions: [invalidRollPaper]
      };

      const result = await calculatePaperCosts(request);

      expect(result.results).toHaveLength(1);
      expect(result.results[0].error).toBeTruthy();
    });
  });
});
