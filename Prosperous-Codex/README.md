# Prosperous Codex

A comprehensive paper cost estimation platform built with Next.js 15, TypeScript, and Tailwind CSS. Features secure server-side calculations, modern UI with purple theme, and complete authentication flow.

## 🏗️ Project Architecture

```
Authentication Flow:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Root (/)  │ -> │ Login Page  │ -> │  Dashboard  │ -> │ Calculator  │
│             │    │ (Vanta.js)  │    │ (Nav Hub)   │    │ (Main App)  │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

### Technology Stack
- **Framework**: Next.js 15.3.3 with App Router
- **Language**: TypeScript 5 (strict mode)
- **Styling**: Tailwind CSS 4 with custom design system
- **UI Components**: Custom components built on Radix UI primitives
- **Authentication**: NextAuth.js v5 with Credentials provider
- **Internationalization**: next-intl with English, Simplified Chinese, Traditional Chinese
- **Database**: SQLite with better-sqlite3 driver
- **State Management**: React Context (Auth, Units, Sidebar, Components)
- **Testing**: Jest with React Testing Library

## ✨ Current Features

### 🔐 Authentication System
- **Secure Login**: Vanta.js animated background with form validation
- **Session Management**: NextAuth.js v5 with JWT strategy and HTTP-only cookies
- **Protected Routes**: Middleware-based authentication with NextAuth.js
- **User Dashboard**: Navigation hub with project overview
- **Database Integration**: SQLite database with user management and role-based access

### 🧮 Calculator Engine
- **Multi-Component Tabs**: Inner Text, Cover, Endpapers
- **Server-Side Security**: All calculations processed server-side
- **Paper Database**: Comprehensive paper options and configurations
- **Unit Conversion**: Complete metric/imperial conversion system
- **Real-time Results**: Instant cost calculations and breakdowns

### 🛒 Component Management
- **Shopping Cart**: Header-based component selection system
- **Drawer Interface**: 420px floating panels with fade animations
- **Component Summary**: Organized by type with cost totals
- **Selection Persistence**: Maintains selections across tabs

### 📋 Task Master
- **Standalone Page**: Full-width task management interface with optimized performance
- **Drag & Drop**: Adjacent-column task movement with clean, professional animations
- **Optimistic UI**: Instant badge updates with batched API calls for improved responsiveness
- **Visual Enhancements**: Hash-based accent colors and natural text wrapping around priority badges
- **Column Layout**: To Do, In Progress, Completed task columns with synchronized heights
- **Project Flow Board**: 500px drawer with detailed project management and real-time sync
- **Performance Optimized**: ~80% reduction in API calls through intelligent batching
- **Purple Theme**: Consistent with application design system

### 🎨 Design System
- **Purple Theme**: Royal purple (#5E6AD2) primary color scheme
- **Dark/Light Modes**: Complete theme support with smooth transitions
- **Collapsible Sidebar**: Expandable navigation with icon-only collapsed state
- **Responsive Design**: Desktop-first with mobile adaptations
- **Typography**: Geist Sans/Mono fonts with consistent hierarchy

### 🌐 Internationalization
- **Multi-Language Support**: English, Simplified Chinese, Traditional Chinese
- **Dynamic Language Switching**: Login page cycling and authenticated user preferences
- **Database Integration**: User language preferences stored and synchronized
- **Server-Side Translation**: Optimized message loading with next-intl

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd prosperous-codex
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   # Create .env.local file
   cp .env.example .env.local

   # Add required environment variables:
   NEXTAUTH_URL=http://localhost:3000
   AUTH_SECRET=your-secret-key-here
   ```

4. **Initialize the database**
   ```bash
   npm run init-db
   ```

5. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Default Login Credentials
- **Admin**: `<EMAIL>` / `password`
- **Moderator**: `<EMAIL>` / `moderator123`

⚠️ **Important**: Change these credentials in production!

## 📁 Project Structure

```
prosperous-codex/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── (protected)/        # Auth-required routes
│   │   │   ├── calculator/     # Main calculator interface
│   │   │   ├── dashboard/      # User dashboard
│   │   │   └── task-master/    # Task management page
│   │   ├── auth/               # Authentication pages
│   │   │   └── login/          # Login with Vanta.js
│   │   └── api/                # Server-side API routes
│   ├── components/             # Reusable UI components
│   │   ├── ui/                 # Base components (shadcn/ui style)
│   │   ├── auth/               # Authentication components
│   │   ├── dashboard/          # Dashboard-specific components
│   │   ├── layout/             # Layout components (header, sidebar)
│   │   └── task-master/        # Task management components
│   ├── contexts/               # React contexts
│   │   ├── sidebar-context.tsx # Sidebar state
│   │   └── selected-components-context.tsx # Component selection
│   ├── lib/                    # Business logic and utilities
│   │   ├── calculations/       # Server-side calculation engine
│   │   ├── database/           # Database services and schema
│   │   ├── types/              # TypeScript definitions
│   │   └── services/           # Service layer
│   ├── auth.ts                 # NextAuth.js configuration
│   ├── middleware.ts           # Route protection middleware
│   └── i18n/                   # Internationalization configuration
└── Subframe/                   # External task management components
```

## 🧪 Testing

Run the test suite:

```bash
npm test              # Run all tests
npm run test:watch    # Watch mode for development
npm run test:integration # Integration tests
```

## 🗄️ Database Management

The application uses SQLite for data persistence:

```bash
npm run init-db       # Initialize database with default users
npm run reset-db      # Reset database (development only)
```

**Database Location**: `data/prosperous-codex.db`

## 🔧 Development Guidelines

### Security Requirements
- **Server-Side Calculations**: All cost calculations must remain server-side
- **Input Validation**: Validate on both client and server
- **Authentication**: NextAuth.js v5 with JWT strategy and secure session management
- **Database Security**: SQLite with prepared statements and input sanitization

### Component Patterns
- **Drawer Components**: 420px width, fade animations, no blur backgrounds
- **Purple Theme**: Use `#5E6AD2` for primary elements
- **Responsive**: Desktop-first approach with mobile considerations

### State Management
- Use React Context for global state
- Keep component state minimal and focused
- No external state management libraries

## 📚 Documentation

- **`.augment-guidelines`**: Comprehensive development guidelines
- **`docs/`**: Organized documentation by category
  - **`implementation/`**: Feature implementation summaries
  - **`migration/`**: System migration guides and summaries
  - **`testing/`**: Testing procedures and validation
  - **`analysis/`**: Code analysis and architectural documentation
  - **`guides/`**: User and developer guides
- **Component docs**: Individual component documentation in respective directories

## 🚀 Deployment

The application is optimized for deployment on Vercel:

1. **Set up environment variables**
   ```bash
   # Production environment variables
   NEXTAUTH_URL=https://your-domain.com
   AUTH_SECRET=your-production-secret-key
   DATABASE_PATH=/path/to/production/database.db
   ```

2. **Build the application**
   ```bash
   npm run build
   ```

3. **Deploy to Vercel**
   ```bash
   vercel deploy
   ```

For other platforms, ensure Node.js 18+ support and proper environment configuration.

**Important**:
- Change default login credentials before production deployment
- Use a secure `AUTH_SECRET` for production
- Set up proper database backup procedures
