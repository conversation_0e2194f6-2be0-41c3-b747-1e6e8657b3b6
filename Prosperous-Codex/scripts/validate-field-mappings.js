#!/usr/bin/env node

/**
 * Field Mapping Validation Script
 * 
 * Validates that all field mappings are consistent and bidirectional
 * between API and database layers.
 */

const fs = require('fs');
const path = require('path');

/**
 * Logger utility
 */
const logger = {
  info: (message) => console.log(`ℹ️  ${message}`),
  success: (message) => console.log(`✅ ${message}`),
  error: (message) => console.error(`❌ ${message}`),
  warn: (message) => console.warn(`⚠️  ${message}`),
};

/**
 * Load field mappings from the source file
 */
function loadFieldMappings() {
  const mappingFile = path.join(__dirname, '../src/lib/task-master/field-mapping.ts');
  
  if (!fs.existsSync(mappingFile)) {
    throw new Error('Field mapping file not found');
  }

  const content = fs.readFileSync(mappingFile, 'utf8');
  
  // Extract FIELD_MAPPINGS object
  const mappingMatch = content.match(/export const FIELD_MAPPINGS = \{([^}]+)\}/s);
  if (!mappingMatch) {
    throw new Error('Could not find FIELD_MAPPINGS in file');
  }

  // Parse the mappings (simplified parsing)
  const mappingsText = mappingMatch[1];
  const mappings = {};
  
  const lines = mappingsText.split('\n');
  for (const line of lines) {
    const match = line.match(/^\s*(\w+):\s*['"]([^'"]+)['"],?\s*$/);
    if (match) {
      mappings[match[1]] = match[2];
    }
  }

  return mappings;
}

/**
 * Validate field naming conventions
 */
function validateNamingConventions(mappings) {
  const errors = [];
  const warnings = [];

  for (const [apiField, dbField] of Object.entries(mappings)) {
    // Validate API field (camelCase)
    if (!/^[a-z][a-zA-Z0-9]*$/.test(apiField)) {
      errors.push(`API field '${apiField}' does not follow camelCase convention`);
    }

    // Validate DB field (snake_case)
    if (!/^[a-z][a-z0-9_]*$/.test(dbField)) {
      errors.push(`DB field '${dbField}' does not follow snake_case convention`);
    }

    // Check for logical consistency
    const expectedDbField = apiField.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    if (dbField !== expectedDbField) {
      warnings.push(`Mapping '${apiField}' -> '${dbField}' doesn't follow standard conversion (expected: '${expectedDbField}')`);
    }
  }

  return { errors, warnings };
}

/**
 * Validate bidirectional mapping consistency
 */
function validateBidirectionalMappings(mappings) {
  const errors = [];
  const reverseMappings = {};

  // Create reverse mapping
  for (const [apiField, dbField] of Object.entries(mappings)) {
    if (reverseMappings[dbField]) {
      errors.push(`Duplicate DB field '${dbField}' mapped from both '${reverseMappings[dbField]}' and '${apiField}'`);
    }
    reverseMappings[dbField] = apiField;
  }

  // Check that reverse mapping is consistent
  for (const [dbField, apiField] of Object.entries(reverseMappings)) {
    if (mappings[apiField] !== dbField) {
      errors.push(`Bidirectional mapping inconsistency: '${apiField}' -> '${mappings[apiField]}' but reverse is '${dbField}' -> '${apiField}'`);
    }
  }

  return { errors, reverseMappings };
}

/**
 * Check for missing mappings in database schema
 */
function validateDatabaseSchema(mappings) {
  const errors = [];
  const warnings = [];

  // This is a simplified check - in a real implementation, you'd parse the actual schema
  const commonDbFields = [
    'id', 'title', 'description', 'status', 'priority', 'progress',
    'created_at', 'updated_at', 'created_by', 'assigned_to',
    'due_date', 'completed_date', 'project_id', 'parent_task_id',
    'full_description', 'event_log', 'file_name', 'file_size',
    'mime_type', 'uploaded_by', 'uploaded_at', 'user_id', 'joined_at'
  ];

  const mappedDbFields = Object.values(mappings);

  // Check for unmapped database fields that should have mappings
  for (const dbField of commonDbFields) {
    if (dbField.includes('_') && !mappedDbFields.includes(dbField)) {
      warnings.push(`Database field '${dbField}' might need an API mapping`);
    }
  }

  return { errors, warnings };
}

/**
 * Validate TypeScript type consistency
 */
function validateTypeConsistency() {
  const errors = [];
  const warnings = [];

  const typesFile = path.join(__dirname, '../src/lib/task-master/validation-types.ts');
  
  if (!fs.existsSync(typesFile)) {
    warnings.push('TypeScript types file not found, skipping type validation');
    return { errors, warnings };
  }

  const content = fs.readFileSync(typesFile, 'utf8');

  // Check for database and API type interfaces (converted from namespaces)
  if (!content.includes('export interface ProjectDbFields')) {
    errors.push('ProjectDbFields interface not found in validation-types.ts');
  }

  if (!content.includes('export interface ProjectApiFields')) {
    errors.push('ProjectApiFields interface not found in validation-types.ts');
  }

  return { errors, warnings };
}

/**
 * Generate validation report
 */
function generateReport(results) {
  const totalErrors = results.reduce((sum, result) => sum + result.errors.length, 0);
  const totalWarnings = results.reduce((sum, result) => sum + result.warnings.length, 0);

  console.log('\n📊 Field Mapping Validation Report');
  console.log('=====================================\n');

  for (const result of results) {
    if (result.section) {
      console.log(`\n🔍 ${result.section}`);
      console.log('-'.repeat(result.section.length + 4));
    }

    if (result.errors.length > 0) {
      console.log('\n❌ Errors:');
      result.errors.forEach(error => console.log(`   • ${error}`));
    }

    if (result.warnings.length > 0) {
      console.log('\n⚠️  Warnings:');
      result.warnings.forEach(warning => console.log(`   • ${warning}`));
    }

    if (result.errors.length === 0 && result.warnings.length === 0) {
      console.log('   ✅ No issues found');
    }
  }

  console.log('\n📈 Summary');
  console.log('===========');
  console.log(`Total Errors: ${totalErrors}`);
  console.log(`Total Warnings: ${totalWarnings}`);

  if (totalErrors === 0) {
    logger.success('Field mapping validation passed!');
  } else {
    logger.error(`Field mapping validation failed with ${totalErrors} errors`);
  }

  return totalErrors === 0;
}

/**
 * Main validation function
 */
function validateFieldMappings() {
  try {
    logger.info('Starting field mapping validation...');

    // Load field mappings
    const mappings = loadFieldMappings();
    logger.info(`Loaded ${Object.keys(mappings).length} field mappings`);

    // Run validations
    const results = [
      {
        section: 'Naming Conventions',
        ...validateNamingConventions(mappings)
      },
      {
        section: 'Bidirectional Mapping Consistency',
        ...validateBidirectionalMappings(mappings)
      },
      {
        section: 'Database Schema Consistency',
        ...validateDatabaseSchema(mappings)
      },
      {
        section: 'TypeScript Type Consistency',
        ...validateTypeConsistency()
      }
    ];

    // Generate report
    const success = generateReport(results);

    if (!success) {
      process.exit(1);
    }

  } catch (error) {
    logger.error(`Validation failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  validateFieldMappings();
}

module.exports = { validateFieldMappings };
