import { DefaultSession, DefaultUser } from "next-auth"
import { JWT, DefaultJWT } from "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      role: 'user' | 'moderator' | 'admin'
      username?: string
    } & DefaultSession["user"]
  }

  interface User extends Default<PERSON>ser {
    role: 'user' | 'moderator' | 'admin'
    username?: string
  }
}

declare module "next-auth/jwt" {
  interface JWT extends DefaultJWT {
    id: string
    role: 'user' | 'moderator' | 'admin'
    username?: string
  }
}
