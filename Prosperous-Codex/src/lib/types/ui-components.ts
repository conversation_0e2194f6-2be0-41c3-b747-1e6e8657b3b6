// UI Component Types for Tab-Specific Cards
// This file defines the interfaces for tab-specific job specification and production parameter cards

// Base interfaces
export interface BaseJobSpecificationData {
  trimH: number;
  trimW: number;
  quantity: number;
  spoilagePct: number;
}

export interface BaseProductionParameterData {
  bleed: number;
  gripper: number;
  colorBar: number;
  alignmentMode: 'Aligned' | 'Misaligned';
}

// Inner Text specific interfaces
export interface InnerTextJobSpecificationData extends BaseJobSpecificationData {
  totalPages: number;
  bindingMethod: string;
}

export interface InnerTextProductionParameterData extends BaseProductionParameterData {
  lip: number;
  sideLipMultiplier: number; // 1, 2, or 3
  isDoubleLipActive: boolean; // Backward compatibility
}

// Cover specific interfaces
export interface CoverJobSpecificationData extends BaseJobSpecificationData {
  spineThickness: number;
  coverType: 'paperback' | 'hardcover' | 'dustJacket';
  flapWidth?: number; // Optional, only for dust jackets
}

export interface CoverProductionParameterData extends BaseProductionParameterData {
  turnInAllowance?: number; // Optional, only for hardcover
}

// Endpaper specific interfaces
export interface EndpaperJobSpecificationData extends BaseJobSpecificationData {
  endpaperType: 'single' | 'double';
}

export interface EndpaperProductionParameterData extends BaseProductionParameterData {
  // Endpapers don't use side lip, so only base parameters
}

// Component prop interfaces
export interface TabSpecificCardProps<T> {
  className?: string;
  onDataChange?: (data: T) => void;
  initialData?: Partial<T>;
}

// Tab identifier type
export type TabId = 'innerText' | 'cover' | 'endpapers';
