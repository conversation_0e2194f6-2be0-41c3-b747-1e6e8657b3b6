import NextAuth from "next-auth"
import { Zod<PERSON>rror } from "zod"
import Credentials from "next-auth/providers/credentials"
import { LoginSchema } from "@/lib/schemas/auth"
import { UserService } from "@/lib/database/user-service"

const userService = new UserService()

export const { handlers, auth, signIn, signOut } = NextAuth({
  providers: [
    Credentials({
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      authorize: async (credentials) => {
        try {
          const { email, password } = await LoginSchema.parseAsync(credentials)

          // Authenticate user using existing UserService
          const user = await userService.authenticateUser(email, password)

          if (!user) {
            return null
          }

          // Return user object that will be stored in the session
          return {
            id: user.id,
            email: user.email,
            name: user.username || user.email,
            role: user.role || 'user',
          }
        } catch (error) {
          if (error instanceof ZodError) {
            return null
          }
          return null
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    jwt({ token, user, trigger, session }) {
      // Initial sign in
      if (user) {
        token.role = user.role
        token.id = user.id
        token.username = user.name // Store username in token
      }

      // Handle session updates (when profile is updated)
      if (trigger === "update" && session) {
        // Update token with new session data
        if (session.username) {
          token.username = session.username
          token.name = session.username
        }
      }

      return token
    },
    session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.role = token.role as any
        session.user.name = token.username as string || token.name as string
        // Add username property for backward compatibility
        session.user.username = token.username as string || token.name as string
      }
      return session
    }
  },
  pages: {
    signIn: "/auth/login",
  },
})
