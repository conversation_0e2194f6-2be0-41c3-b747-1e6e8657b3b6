#!/usr/bin/env node

/**
 * <PERSON>ript to test the access request system
 * Usage: node scripts/test-access-request.js
 */

const fs = require('fs');
const path = require('path');

async function testAccessRequest() {
  try {
    console.log('🧪 Testing Access Request System...');
    console.log('');
    
    // Test data
    const testRequest = {
      email: '<EMAIL>',
      name: 'Test User',
      reason: 'Testing the access request system functionality'
    };
    
    console.log('📝 Submitting test access request...');
    console.log(`   Email: ${testRequest.email}`);
    console.log(`   Name: ${testRequest.name}`);
    console.log(`   Reason: ${testRequest.reason}`);
    console.log('');
    
    // Make request to the API
    const response = await fetch('http://localhost:3000/api/auth/request-access', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testRequest),
    });
    
    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log('✅ Access request submitted successfully!');
      console.log(`   Response: ${result.message}`);
      console.log('');
      console.log('📋 Next steps:');
      console.log('   1. Login as admin/moderator (<EMAIL> / moderator123)');
      console.log('   2. Go to Admin Panel > Access Requests tab');
      console.log('   3. You should see the test request pending approval');
      console.log('   4. You can approve/reject the request from there');
    } else {
      console.log('❌ Access request failed:');
      console.log(`   Error: ${result.error || 'Unknown error'}`);
      console.log(`   Status: ${response.status}`);
    }
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Connection failed - make sure the development server is running:');
      console.log('   npm run dev');
    } else {
      console.log('❌ Test failed:', error.message);
    }
  }
}

// Check if we're running this as a script
if (require.main === module) {
  testAccessRequest();
}

module.exports = { testAccessRequest };
