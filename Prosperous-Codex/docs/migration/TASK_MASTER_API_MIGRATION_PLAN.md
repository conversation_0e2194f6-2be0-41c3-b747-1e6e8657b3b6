# Task Master API Route Migration Plan

## Objective
Migrate all remaining Task Master API routes from inline validation patterns to use the modular validation middleware system (`withTaskMasterMiddleware` and Zod validation schemas).

## Current State Analysis

### Existing Infrastructure
- ✅ **Middleware**: `src/lib/task-master/api-middleware.ts` - `withTaskMasterMiddleware` function
- ✅ **Schemas**: `src/lib/task-master/schemas.ts` - Comprehensive Zod validation schemas
- ✅ **Example**: `src/app/api/task-master/projects/[id]/route.ts` - Partially migrated (needs completion)

### Routes Requiring Migration

#### **High Priority (Core Functionality)**
1. **`/api/task-master/projects/route.ts`** - Project listing and creation
2. **`/api/task-master/tasks/route.ts`** - Task listing and creation  
3. **`/api/task-master/tasks/[taskId]/route.ts`** - Individual task operations
4. **`/api/task-master/projects/[id]/route.ts`** - Individual project operations (complete migration)

#### **Medium Priority (Extended Functionality)**
5. **`/api/task-master/activity/route.ts`** - Activity tracking
6. **`/api/task-master/projects/[id]/activity/route.ts`** - Project-specific activity
7. **`/api/task-master/projects/[id]/team/route.ts`** - Team management
8. **`/api/task-master/projects/[id]/files/route.ts`** - File management
9. **`/api/task-master/projects/[id]/comments/route.ts`** - Comment management

#### **Lower Priority (Specialized Functionality)**
10. **`/api/task-master/files/[fileId]/route.ts`** - Individual file operations

## Migration Pattern

### Current Pattern (To Be Replaced)
```typescript
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }
    
    const { user } = authResult;
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }
    
    // Business logic
    return NextResponse.json({ data });
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

### Target Pattern (After Migration)
```typescript
import { withTaskMasterMiddleware, createApiResponse, ApiContext } from '@/lib/task-master/api-middleware';
import { ProjectSchemas, ValidationHelpers } from '@/lib/task-master/schemas';

export const GET = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    // Validated params available in context.params (already parsed as numbers)
    // User available in context.user
    // No manual auth or param validation needed
    
    const { id } = context.params; // Already validated as positive integer
    
    // Business logic here
    const result = await service.getData(id);
    
    return createApiResponse(result);
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateInput: true
  }
);
```

## Phase 1: Middleware Enhancement (If Needed)

### Task 1.1: Extend Middleware for Schema Integration
**File**: `src/lib/task-master/api-middleware.ts`

**Enhancement**: Add support for Zod schema validation in middleware options:
```typescript
export interface MiddlewareOptions {
  requireAuth?: boolean;
  requireRole?: 'user' | 'moderator' | 'admin';
  validateParams?: z.ZodSchema;
  validateQuery?: z.ZodSchema;
  validateBody?: z.ZodSchema;
  sanitizeInput?: boolean;
}
```

### Task 1.2: Add Missing Validation Schemas
**File**: `src/lib/task-master/schemas.ts`

**Add schemas for**:
- Activity query parameters
- File upload with project association
- Team member role updates
- Bulk operation schemas (if needed)

## Phase 2: High Priority Route Migrations

### Task 2.1: Migrate `/api/task-master/projects/route.ts`
**Current Issues**: Manual auth, inline POST body validation
**Target Schemas**: `ProjectSchemas.create` for POST validation
**Testing Focus**: Project creation and listing with various validation scenarios

**Migration Steps**:
1. Create backup: `cp route.ts route.ts.backup`
2. Replace GET handler with middleware pattern
3. Replace POST handler with `ProjectSchemas.create` validation
4. Test all validation scenarios
5. Verify response format consistency

### Task 2.2: Migrate `/api/task-master/tasks/route.ts`
**Current Issues**: Manual project_id validation, inline task validation
**Target Schemas**: `QuerySchemas.projectId`, `TaskSchemas.createWithProject`
**Testing Focus**: Task creation and listing with project access validation

**Migration Steps**:
1. Create backup: `cp route.ts route.ts.backup`
2. Implement query parameter validation for project_id
3. Replace task creation validation with schema
4. Test project access authorization
5. Verify task listing functionality

### Task 2.3: Migrate `/api/task-master/tasks/[taskId]/route.ts`
**Current Issues**: Manual taskId parsing, inline update validation
**Target Schemas**: `TaskSchemas.idParam`, `TaskSchemas.update`
**Testing Focus**: Task CRUD operations with comprehensive validation

**Migration Steps**:
1. Create backup: `cp route.ts route.ts.backup`
2. Replace parameter parsing with schema validation
3. Implement update validation with `TaskSchemas.update`
4. Test all CRUD operations
5. Verify authorization checks

### Task 2.4: Complete `/api/task-master/projects/[id]/route.ts` Migration
**Current Issues**: Partially migrated, still has inline validation in PUT/DELETE
**Target Schemas**: `ProjectSchemas.idParam`, `ProjectSchemas.update`
**Testing Focus**: Complete project CRUD, partial response functionality

**Migration Steps**:
1. Create backup: `cp route.ts route.ts.backup`
2. Complete PUT handler migration to use `ProjectSchemas.update`
3. Migrate DELETE handler to middleware pattern
4. Test partial response functionality
5. Verify all project operations

## Testing Strategy

### Test Categories (Per Route)
1. **Valid Requests**: Proper auth and data
2. **Authentication Failures**: Missing/expired tokens
3. **Parameter Validation**: Malformed IDs, missing required fields
4. **Request Body Validation**: Invalid JSON, missing/invalid values
5. **Authorization Failures**: Accessing unauthorized data
6. **Error Handling**: Database failures, server errors

### Testing Commands
```bash
# Run specific route tests
npm test -- --testPathPattern=task-master/api

# Run integration tests
npm run test:integration

# Performance comparison
npm run test:performance -- --route=/api/task-master/projects
```

## Success Criteria

### Functional Requirements
- ✅ All routes use consistent middleware pattern
- ✅ All input validation uses Zod schemas (no inline validation)
- ✅ Consistent error response format across all routes
- ✅ Proper authentication and authorization enforcement
- ✅ No performance degradation from middleware overhead

### Quality Requirements
- ✅ Reduced code duplication
- ✅ Easier maintenance and future route additions
- ✅ Comprehensive test coverage
- ✅ Consistent API documentation

## Rollback Strategy

### Backup Approach
```bash
# Create backups before migration
find src/app/api/task-master -name "route.ts" -exec cp {} {}.backup \;

# Rollback if needed
find src/app/api/task-master -name "route.ts.backup" -exec sh -c 'mv "$1" "${1%.backup}"' _ {} \;
```

### Monitoring
- Response time comparison before/after migration
- Error rate monitoring for each migrated route
- Integration test results
- User feedback on API behavior

## Execution Timeline

### Week 1: Infrastructure
- Day 1-2: Middleware enhancements
- Day 3-4: Additional schema creation
- Day 5: Testing infrastructure setup

### Week 2: Core Migrations
- Day 1: Projects route migration
- Day 2: Tasks route migration  
- Day 3: Individual task route migration
- Day 4: Complete project route migration
- Day 5: Integration testing and validation

### Week 3: Extended Functionality
- Day 1-2: Activity routes migration
- Day 3-4: Team and file management routes
- Day 5: Final testing and documentation

## Next Steps for Implementation Session

1. **Start with Task 1.1**: Enhance middleware to accept validation schemas
2. **Verify existing schemas**: Ensure all needed schemas exist in `schemas.ts`
3. **Begin with highest priority route**: `/api/task-master/projects/route.ts`
4. **Test thoroughly**: Each route before moving to the next
5. **Monitor performance**: Ensure no regressions

## Context References
- **Middleware**: `src/lib/task-master/api-middleware.ts`
- **Schemas**: `src/lib/task-master/schemas.ts`
- **Example Implementation**: `src/app/api/task-master/projects/[id]/route.ts`
- **Service Layer**: `src/lib/database/task-master-service.ts`
