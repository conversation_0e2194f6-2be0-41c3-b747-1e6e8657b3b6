"use client";

import React, { createContext, useContext, useState, useCallback } from 'react';
import { SelectedComponent } from '@/components/SelectedComponent';
import { TabId } from '@/lib/types/ui-components';

interface SelectedComponentsContextType {
  selectedComponents: SelectedComponent[];
  isDrawerOpen: boolean;
  setIsDrawerOpen: (open: boolean) => void;
  addComponent: (component: SelectedComponent) => void;
  removeComponent: (componentId: string, componentType: TabId) => void;
  clearComponents: () => void;
  getComponentForTab: (tabId: TabId) => SelectedComponent | undefined;
  getComponentsForTab: (tabId: TabId) => SelectedComponent[];
  isComponentSelected: (componentId: string, componentType: TabId) => boolean;
}

const SelectedComponentsContext = createContext<SelectedComponentsContextType | undefined>(undefined);

export function SelectedComponentsProvider({ children }: { children: React.ReactNode }) {
  const [selectedComponents, setSelectedComponents] = useState<SelectedComponent[]>([]);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const addComponent = useCallback((component: SelectedComponent) => {
    setSelectedComponents(prev => {
      // Remove existing component with same ID and type if it exists (for re-selection)
      const filtered = prev.filter(comp =>
        !(comp.id === component.id && comp.componentType === component.componentType)
      );
      return [...filtered, component];
    });
  }, []);

  const removeComponent = useCallback((componentId: string, componentType: TabId) => {
    setSelectedComponents(prev => 
      prev.filter(comp => !(comp.id === componentId && comp.componentType === componentType))
    );
  }, []);

  const clearComponents = useCallback(() => {
    setSelectedComponents([]);
  }, []);

  const getComponentForTab = useCallback((tabId: TabId) => {
    return selectedComponents.find(comp => comp.componentType === tabId);
  }, [selectedComponents]);

  const getComponentsForTab = useCallback((tabId: TabId) => {
    return selectedComponents.filter(comp => comp.componentType === tabId);
  }, [selectedComponents]);

  const isComponentSelected = useCallback((componentId: string, componentType: TabId) => {
    return selectedComponents.some(comp =>
      comp.id === componentId && comp.componentType === componentType
    );
  }, [selectedComponents]);

  const value = {
    selectedComponents,
    isDrawerOpen,
    setIsDrawerOpen,
    addComponent,
    removeComponent,
    clearComponents,
    getComponentForTab,
    getComponentsForTab,
    isComponentSelected
  };

  return (
    <SelectedComponentsContext.Provider value={value}>
      {children}
    </SelectedComponentsContext.Provider>
  );
}

export function useSelectedComponents() {
  const context = useContext(SelectedComponentsContext);
  if (context === undefined) {
    throw new Error('useSelectedComponents must be used within a SelectedComponentsProvider');
  }
  return context;
}
