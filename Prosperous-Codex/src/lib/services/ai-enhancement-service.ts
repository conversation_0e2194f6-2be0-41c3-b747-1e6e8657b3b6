import OpenAI from 'openai';

/**
 * AI Enhancement Service
 * 
 * Provides AI-powered text enhancement capabilities for task descriptions.
 * Uses OpenAI API with proper error handling and configuration management.
 */

export interface EnhancementOptions {
  max_tokens?: number;
  model?: string;
}

export interface ReasoningConfig {
  effort?: 'high' | 'medium' | 'low';
  max_tokens?: number;
  exclude?: boolean;
  enabled?: boolean;
}

export interface EnhancementResult {
  enhanced_text: string;
  original_text: string;
  enhancement_applied: boolean;
  model_used?: string;
  tokens_used?: number;
  reasoning_content?: string; // Reasoning process from AI models that support reasoning
}

export class AIEnhancementService {
  private client: OpenAI | null = null;
  private isConfigured: boolean = false;
  private readonly primaryModel = 'google/gemini-2.5-flash-lite-preview-06-17';
  private readonly fallbackModel = 'deepseek/deepseek-chat-v3-0324';

  constructor() {
    this.initializeClient();
  }

  /**
   * Initialize OpenAI-compatible client with environment configuration
   * Supports both OpenAI and OpenRouter APIs
   */
  private initializeClient(): void {
    try {
      // Check for OpenRouter configuration first, then fallback to OpenAI
      const openRouterKey = process.env.OPENROUTER_API_KEY;
      const openAiKey = process.env.OPENAI_API_KEY;

      if (!openRouterKey && !openAiKey) {
        return;
      }

      // Configure for OpenRouter or OpenAI
      if (openRouterKey) {
        this.client = new OpenAI({
          apiKey: openRouterKey,
          baseURL: 'https://openrouter.ai/api/v1',
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2'),
          defaultHeaders: {
            'HTTP-Referer': process.env.OPENROUTER_REFERER || 'http://localhost:3000',
            'X-Title': process.env.OPENROUTER_APP_NAME || 'Prosperous Codex'
          }
        });
      } else {
        this.client = new OpenAI({
          apiKey: openAiKey,
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2')
        });
      }

      this.isConfigured = true;

    } catch (error) {
      this.client = null;
      this.isConfigured = false;
    }
  }

  /**
   * Check if the service is available and properly configured
   */
  public isAvailable(): boolean {
    return this.client !== null && this.isConfigured;
  }

  /**
   * Check if the service is configured (has API key)
   */
  public isServiceConfigured(): boolean {
    return this.isConfigured;
  }

  /**
   * Get the current AI provider being used
   */
  public getProvider(): 'openrouter' | 'openai' | 'none' {
    if (!this.isConfigured) return 'none';
    return process.env.OPENROUTER_API_KEY ? 'openrouter' : 'openai';
  }



  /**
   * Check if a model supports reasoning configuration
   */
  private supportsReasoning(model: string): boolean {
    // Models that support reasoning with OpenRouter reasoning parameter
    const reasoningModels = [
      'google/gemini-2.5-flash-lite-preview-06-17', // Primary model with reasoning support
      // Add other reasoning-capable models here as needed
    ];

    return reasoningModels.includes(model);
  }

  /**
   * Get reasoning configuration for models that support it
   */
  private getReasoningConfig(model: string): ReasoningConfig | undefined {
    if (this.supportsReasoning(model)) {
      return {
        effort: 'high',
        exclude: false
      };
    }
    return undefined;
  }

  /**
   * Get list of available models based on configured provider
   */
  public getAvailableModels(): string[] {
    const isOpenRouter = !!process.env.OPENROUTER_API_KEY;

    if (isOpenRouter) {
      return [
        'openai/gpt-4o',
        'openai/gpt-4o-mini',
        'openai/gpt-4-turbo',
        'anthropic/claude-3.5-sonnet',
        'anthropic/claude-3-haiku',
        'meta-llama/llama-3.1-8b-instruct',
        'google/gemini-pro'
      ];
    } else {
      return [
        'gpt-4o',
        'gpt-4o-mini',
        'gpt-4-turbo',
        'gpt-3.5-turbo'
      ];
    }
  }

  /**
   * Enhance task description using AI
   */
  public async enhanceTaskDescription(
    inputText: string,
    options: EnhancementOptions = {}
  ): Promise<string> {
    if (!this.isAvailable()) {
      throw new Error('AI Enhancement Service is not available. Please check API key configuration.');
    }

    const {
      max_tokens = parseInt(process.env.AI_MAX_TOKENS || '65536')
    } = options;

    const systemPrompt = this.buildSystemPrompt();
    const userPrompt = `--- EVENT LOG ---
None

--- EMAIL THREAD ---
Task Request: ${inputText}`;

    // Try primary model first, then fallback
    let completion;

    try {
      // Build request configuration
      const requestConfig: any = {
        model: this.primaryModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: max_tokens,
        temperature: 0.65,
        top_p: 0.65,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      };

      // Add reasoning configuration for models that support it
      const reasoningConfig = this.getReasoningConfig(this.primaryModel);
      if (reasoningConfig) {
        requestConfig.reasoning = reasoningConfig;
      }

      completion = await this.client!.chat.completions.create(requestConfig);
    } catch (primaryError) {

      try {
        // Build fallback request configuration
        const fallbackRequestConfig: any = {
          model: this.fallbackModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: max_tokens,
          temperature: 0.65,
          top_p: 0.65,
          frequency_penalty: 0.1,
          presence_penalty: 0.1
        };

        // Add reasoning configuration for fallback model if it supports it
        const fallbackReasoningConfig = this.getReasoningConfig(this.fallbackModel);
        if (fallbackReasoningConfig) {
          fallbackRequestConfig.reasoning = fallbackReasoningConfig;
        }

        completion = await this.client!.chat.completions.create(fallbackRequestConfig);
      } catch (fallbackError) {
        const primaryMsg = primaryError instanceof Error ? primaryError.message : String(primaryError);
        const fallbackMsg = fallbackError instanceof Error ? fallbackError.message : String(fallbackError);
        throw new Error(`AI Enhancement failed: Both models unavailable. Primary error: ${primaryMsg}, Fallback error: ${fallbackMsg}`);
      }
    }

    try {
      // Check for thinking content in various possible locations
      const choice = completion.choices[0];
      const message = choice?.message;

      const enhancedText = message?.content;

      if (!enhancedText) {
        throw new Error('No enhanced text received from AI service');
      }

      // Extract reasoning content from API response if available
      let reasoningContent = null;

      // Check for reasoning content in the message (OpenRouter standard location)
      if ((message as any)?.reasoning) {
        reasoningContent = (message as any).reasoning;
      } else if ((choice as any)?.reasoning) {
        reasoningContent = (choice as any).reasoning;
      } else if ((completion as any)?.reasoning) {
        reasoningContent = (completion as any).reasoning;
      }

      // Clean up the response (remove quotes if AI wrapped the response)
      let cleanedText = enhancedText
        .replace(/^["']|["']$/g, '')
        .trim();

      // If we found reasoning content in a separate field, format it with the response
      if (reasoningContent && typeof reasoningContent === 'string') {
        cleanedText = `<thinking>\n${reasoningContent.trim()}\n</thinking>\n\n${cleanedText}`;
      }

      return cleanedText;

    } catch (error) {

      if (error instanceof OpenAI.APIError) {
        if (error.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        if (error.status === 401) {
          throw new Error('API key authentication failed');
        }
        if (error.status === 400 && error.message.includes('content_filter')) {
          throw new Error('Content cannot be processed due to safety guidelines');
        }
      }

      throw new Error(`AI enhancement failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build system prompt using your exact Project Assistant Prompt
   */
  private buildSystemPrompt(): string {
    // Your exact Project Assistant Prompt
    const taskEnhancementPrompt = `**Prompt: Detailed Project Briefing Assistant**

**Mandatory**: Your process must be two distinct steps.
Step 1: The Thinking Step. Your entire reasoning process must be encapsulated within a <thinking>...</thinking> block. This block should contain ONLY your analysis, planning, and self-correction.
Step 2: The Generation Step. After outputting the closing </thinking> tag with no other characters on the line, you will begin generating the final, user-facing document. It is a critical failure to include any part of the final document from Step 2 inside the thinking block of Step 1.

You are a bilingual Project Management Assistant for Prosperous Printing Company Limited. Your primary role is to create a clear, detailed, and comprehensive briefing document for the team based on the inputs provided. Your role includes analyzing the language composition of the input; if the text contains more than 33% Chinese characters, your entire response must be in Traditional Chinese. Otherwise, your entire response must be in English.
**Core Function:** To process event logs and email threads into a detailed briefing document that provides a comprehensive history, a thorough description of the current situation, and a clear list of required actions.

**Guiding Principles:**

**Be Factual:** Base all output strictly on the provided input. Do not invent events, details, stories, or context that cannot be found in the '--- EVENT LOG ---' or '--- EMAIL THREAD ---'.

**Handle Vague Inputs:** If the user input is very brief or lacks context (e.g., just a project title like "create an annual report"), do not hallucinate a detailed narrative. Instead, state that more information is needed and provide a basic, high-level template for the user to fill out.

**Keep Responses Succinct:** When context is limited, the response should be equally limited. Include only relevant, requested information that can be directly sourced from the input.

**Language Detection:** Detect the primary language used in the user's input. Your entire response, including all headings, titles, and generated content, must be in that detected language (Traditional Chinese or English).

**Terminology:** When generating output in Traditional Chinese, you must use the corresponding terms provided in the guide below.

Language & Terminology Guide:
| English Term | Traditional Chinese Term |
| :--- | :--- |
| Subject | 主題 |
| Project | 項目 |
| Project Briefing | 項目簡報 |
| Event Log | 事件日誌 |
| Job Description & Current Status | 工作描述與目前狀態 |
| Task Assignment | 任務分配 |
| Key Focus | 核心重點 |
| Event Background | 事件背景 |
| Core Problem | 核心問題 |
| Top Priority | 首要任務 |
| Task | 任務 |
| Description | 描述 |
| Subtasks | 分支任務 |
| Company | 公司 |
| Client | 客戶 |
| Supplier | 供應商 |

---
Input Structure (User Provides AFTER This Prompt)
(Note: The '---EMAILTHREAD---' (which could be an email, a report, or any block of text) provides the immediate context and is the primary source for the current event log. The '---EVENTLOG---' is optional and provides broader historical context. Your role is to synthesize information given.)

'---EVENTLOG---'
[Previous log entries or blank/None]
'---EMAILTHREAD---'
[Full chronological email exchange or other user-provided text/reports]

---

**Output Structure**
Subject: Project Briefing: [Project Name] // [Client Name]

IMPORTANT: The section markers use CamelCase (e.g., ---EventLog---, ---EndOfEventLog---) to signify they are fixed, non-translatable delimiters for automated parsing. They MUST be output exactly as written.

---EventLog---
Event Log:
[line space]
- Read the '--- EMAIL THREAD ---' and the input '--- EVENT LOG ---'.
- Identify any new material events from the emails that are not already in the log.
- When creating an concise event summary, the goal is a high-level overview of the action.
- Combine the old log entries with the new entries.
- Output the complete and updated event log below this point in chronological order.
- Each entry must start with a dash (-).
- When determining the [Affiliation], use a concise, common-sense short name (e.g., use 'Prosperous' for 'Prosperous Printing Company Limited'). Avoid using the full legal name.
- Format:
- DD/MM/YYYY - [Sender Name] ([Affiliation]): [Concise summary of past event.]
...
- End Event Log field with (---EndOfEventLog---), please ensure there is one blank line between the end of content and the section ender.
[line space]
---EndOfEventLog---

---Description---
[line space]
- Choose a suitable header/title for this description section from the following options: 'Review', 'Profile', 'Detail', 'Depiction', 'Explanation', 'Narrative', or 'Description', based on what best fits the content.
- Synthesize all user-provided input (including the Email Thread, Event Log, and any other context) into a detailed narrative, adhering to the Guiding Principles above.
- Primary goal: Maximize clarity and detail, but only when supported by the input data, DO NOT fabricate details.
- **Use paragraphs** for the main narrative, but whenever listing multiple issues, requirements, or key data points, break them out into an indented bulleted list using - to improve readability.

- Narrative must cover:
  - Project's overall goal
  - Summary of how the situation evolved
  - Detailed explanation of the core problem/trigger
  - Clear statement of the immediate objective
  - End Description field with (---EndofDescription---), please ensure there is one blank line between the end of content and the section ender.
  [line space]
---EndOfDescription---

---TaskAssignment---
Task Assignment:
[line space]
For each task:
- The Task Title must be a concise, high-level summary. It should convey the main goal without going into the detailed context.
- **Do NOT assign tasks to anyone**
- Use the indented structure below to ensure clear visual hierarchy. Subtasks should be offset from their parent task to improve structural clarity.
- Write a brief Description for each main task (narrative explaining goal, importance, and deadline)
- List subtasks using Task.Subtask numbering (e.g., 1.1, 1.2, 2.1)
- Provide a brief Description for each subtask (explaining specific action required)
- Use no descriptions **only** for extremely simple, self explanatory.

» Task 1: [Action-focused Task Name]
- Description: [Concise narrative explaining objective, importance, and deadline. E.g., "The objective of this task is to... This is important because... "]
  - Subtasks:
    - 1.1: [Subtask Title]
      - Description: [Concise narrative explaining specific action]
    - 1.2: [Subtask Title]
       - Description: [Concise narrative explaining specific action]

[Repeat structure for additional tasks]
- End Task Assignment field with (---EndOfTaskAssignment---), please ensure there is one blank line between the end of content and the section ender.
[line space]
---EndOfTaskAssignment---

Before generating the final response, perform a self-audit using the following checklist within your reasoning block:

First <thinking> tag is present at the start: Yes/No
Second <thinking> tag is present on the next line: Yes/No
First </thinking> tag (visual/fallback) is present at the end: Yes/No
Second </thinking> tag (functional) is present on the final line: Yes/No
Strict spacing requirements for closing tags are adhered to: Yes/No
Finalizing reasoning and preparing to generate output: Yes/No
Crucially, ensure the final user-facing output is clean and contains no meta-commentary or mention of this checklist or the reasoning process itself.`;

    // Return the task enhancement prompt
    return taskEnhancementPrompt;
  }
}
