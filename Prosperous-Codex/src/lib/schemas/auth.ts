import { z } from 'zod';

export const LoginSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
  password: z.string().min(1, { message: "Password is required." }),
  rememberMe: z.union([z.boolean(), z.string()]).transform((val) => {
    if (typeof val === 'string') {
      return val === 'true';
    }
    return val;
  }).default(false).optional(),
});

export type LoginFormData = z.infer<typeof LoginSchema>;

export const AccessRequestSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
  name: z.string().min(2, { message: "Name must be at least 2 characters long." }).optional(),
  reason: z.string().min(10, { message: "Please provide a reason (at least 10 characters)." }).optional(),
});

export type AccessRequestFormData = z.infer<typeof AccessRequestSchema>;
