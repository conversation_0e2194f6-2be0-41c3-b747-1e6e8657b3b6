"use client";

import { useState, useC<PERSON>back, useEffect, useRef } from "react";
import { SpotlightTracker } from "@/components/spotlight-tracker";
import TabTracker from "@/components/tab-tracker";
import { TabSpecificJobSpecificationsCard, TabSpecificProductionParametersCard, convertToCalculationInputs } from "@/components/tab-specific-cards";
import PaperOptionsCard from "@/components/paper-options-card";
import CalculationResultsCard from "@/components/calculation-results-card";
import UnitConverterCard from "@/components/unit-converter-card";
import { innerTextConfig, coverConfig, endpaperConfig } from "@/lib/paper-configs";
import {
  InnerTextJobSpecificationData,
  InnerTextProductionParameterData,
  CoverJobSpecificationData,
  CoverProductionParameterData,
  EndpaperJobSpecificationData,
  EndpaperProductionParameterData
} from "@/lib/types/ui-components";
import { PaperOption } from "@/components/paper-options-card";
import { PaperCandidate } from "@/lib/types/calculation";
import { CalculationResult } from "@/components/calculation-results-card";
import { SelectedComponent } from "@/components/SelectedComponent";
import { useSelectedComponents } from "@/contexts/selected-components-context";
// Import ref types for the job specification cards
import { InnerTextJobSpecificationsRef } from "@/components/inner-text/inner-text-job-specifications-card";
import { CoverJobSpecificationsRef } from "@/components/cover/cover-job-specifications-card";
import { EndpaperJobSpecificationsRef } from "@/components/endpaper/endpaper-job-specifications-card";

type TabId = 'innerText' | 'cover' | 'endpapers';

// Function to map PaperCandidate to CalculationResult
function mapPaperCandidateToCalculationResult(candidate: PaperCandidate): CalculationResult {
  return {
    id: candidate.id,
    paperName: candidate.name,
    source: candidate.source,
    sheetHeight: candidate.sheetH,
    sheetWidth: candidate.sheetW,
    grainDirection: candidate.grainDirection,
    gsm: candidate.gsm,
    caliper: candidate.caliperMicrons,
    costPerReam: candidate.costReam,
    costPerTon: candidate.costTonne,

    // Calculation results
    isOptimal: candidate.isOptimalCandidate,
    isError: candidate.error,
    errorMessage: candidate.errorMessageKey,

    // Layout results
    layoutFit: candidate.layoutDown && candidate.layoutAcross ? `${candidate.layoutDown} × ${candidate.layoutAcross}` : undefined,
    pagesPerSide: candidate.maxItemsPerSide,
    sheetUtilization: candidate.utilisationRate,
    wastePercent: candidate.wastePercent,

    // Cost results
    totalSheets: candidate.totalSheetsNeeded,
    costPerSheet: candidate.costPerSheet,
    totalCost: candidate.totalCost,
    costPerBook: candidate.costPerBook,

    // Technical details
    inputSheetH: candidate.sheetH ?? undefined,
    inputSheetW: candidate.sheetW,
    pressH: candidate.pressH,
    pressW: candidate.pressW,
    usableH: candidate.usableH,
    usableW: candidate.usableW,
    grainAlignment: candidate.grainAlignmentStatus,
    untrimmedPageH: candidate.layoutPageH,
    untrimmedPageW: candidate.layoutPageW,
    imposedAreaH: candidate.occupiedHeight,
    imposedAreaW: candidate.occupiedWidth,
    layoutDown: candidate.layoutDown,
    layoutAcross: candidate.layoutAcross,
    resultingSig: candidate.pagesPerSheetOutput ? `${candidate.pagesPerSheetOutput}p` : undefined,
    bookBlockThickness: candidate.bookBlockThickness_mm,
  };
}

const translations = {
  en: {
    title: "Paper Cost Estimator v23.0",
    language: "Language",
    theme: "Theme",
    tabInnerText: "Inner Text",
    tabCover: "Cover",
    tabEndpapers: "Endpapers",
    jobSpecs: "Job Specifications",
    productionParams: "Production Parameters",
    unitConverter: "Unit Converter",
    jobName: "Job Name",
    paperType: "Paper Type",
    paperWeight: "Paper Weight (gsm)",
    finishedSize: "Finished Size",
    quantity: "Quantity",
    colors: "Colors",
    sides: "Sides",
    calculate: "Calculate Cost"
  },
  "zh-cn": {
    title: "纸张成本估算器 v23.0",
    language: "语言",
    theme: "主题",
    tabInnerText: "内文",
    tabCover: "封面",
    tabEndpapers: "环衬",
    jobSpecs: "工作规格",
    productionParams: "生产参数",
    unitConverter: "单位转换器",
    jobName: "工作名称",
    paperType: "纸张类型",
    paperWeight: "纸张重量 (克)",
    finishedSize: "成品尺寸",
    quantity: "数量",
    colors: "颜色",
    sides: "面数",
    calculate: "计算成本"
  },
  "zh-tw": {
    title: "紙張成本估算器 v23.0",
    language: "語言",
    theme: "主題",
    tabInnerText: "內文",
    tabCover: "封面",
    tabEndpapers: "環襯",
    jobSpecs: "工作規格",
    productionParams: "生產參數",
    unitConverter: "單位轉換器",
    jobName: "工作名稱",
    paperType: "紙張類型",
    paperWeight: "紙張重量 (克)",
    finishedSize: "成品尺寸",
    quantity: "數量",
    colors: "顏色",
    sides: "面數",
    calculate: "計算成本"
  }
};

export default function CalculatorPage() {
  const [activeTab, setActiveTab] = useState<TabId>('innerText');
  const [language, setLanguage] = useState<"en" | "zh-cn" | "zh-tw">("en");
  const { selectedComponents, addComponent, removeComponent, getComponentForTab, getComponentsForTab, isComponentSelected } = useSelectedComponents();
  
  // Job specification data for each tab
  const [innerTextJobData, setInnerTextJobData] = useState<InnerTextJobSpecificationData>();
  const [coverJobData, setCoverJobData] = useState<CoverJobSpecificationData>();
  const [endpaperJobData, setEndpaperJobData] = useState<EndpaperJobSpecificationData>();
  
  // Production parameter data for each tab
  const [innerTextProdData, setInnerTextProdData] = useState<InnerTextProductionParameterData>();
  const [coverProdData, setCoverProdData] = useState<CoverProductionParameterData>();
  const [endpaperProdData, setEndpaperProdData] = useState<EndpaperProductionParameterData>();
  
  // Paper options for each tab - initialize with default data
  const [innerTextPaperOptions, setInnerTextPaperOptions] = useState<PaperOption[]>(innerTextConfig.initialData);
  const [coverPaperOptions, setCoverPaperOptions] = useState<PaperOption[]>(coverConfig.initialData);
  const [endpaperPaperOptions, setEndpaperPaperOptions] = useState<PaperOption[]>(endpaperConfig.initialData);
  
  // Calculation results
  const [calculationResults, setCalculationResults] = useState<CalculationResult[]>([]);
  const [isCalculating, setIsCalculating] = useState(false);
  const [selectedOptionIds, setSelectedOptionIds] = useState<string[]>([]);

  // Button state management with countdown timer
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);
  const [countdownSeconds, setCountdownSeconds] = useState(0);



  // Refs to access current mm values from job specification cards
  const innerTextJobRef = useRef<InnerTextJobSpecificationsRef>(null);
  const coverJobRef = useRef<CoverJobSpecificationsRef>(null);
  const endpaperJobRef = useRef<EndpaperJobSpecificationsRef>(null);

  const t = translations[language] || translations.en;

  // Handle tab switching
  const handleTabSwitch = useCallback((tabId: TabId) => {
    setActiveTab(tabId);
    setCalculationResults([]);

    // Update selectedOptionIds based on all selected components for the new tab
    const componentsForTab = getComponentsForTab(tabId);
    setSelectedOptionIds(componentsForTab.map(comp => comp.id));

    // Reset button states when switching tabs
    setIsButtonDisabled(false);
    setCountdownSeconds(0);
  }, [getComponentsForTab]);

  // Countdown timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (countdownSeconds > 0) {
      interval = setInterval(() => {
        setCountdownSeconds(prev => {
          if (prev <= 1) {
            setIsButtonDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [countdownSeconds]);

  // Get current paper config based on active tab
  const getCurrentPaperConfig = () => {
    switch (activeTab) {
      case 'innerText': return innerTextConfig;
      case 'cover': return coverConfig;
      case 'endpapers': return endpaperConfig;
      default: return innerTextConfig;
    }
  };

  // Handle component selection for the drawer
  const handleSelectOption = useCallback((result: CalculationResult) => {
    // Get current job specifications
    let currentJobSpecs: any = null;

    switch (activeTab) {
      case 'innerText':
        currentJobSpecs = innerTextJobRef.current?.getCurrentMmValues() || innerTextJobData;
        break;
      case 'cover':
        currentJobSpecs = coverJobRef.current?.getCurrentMmValues() || coverJobData;
        break;
      case 'endpapers':
        currentJobSpecs = endpaperJobRef.current?.getCurrentMmValues() || endpaperJobData;
        break;
    }

    if (!currentJobSpecs) {
      console.warn('No job specifications available for selection');
      return;
    }

    // Check if this exact component is already selected
    const isAlreadySelected = isComponentSelected(result.id, activeTab);

    if (isAlreadySelected) {
      // Remove if already selected
      removeComponent(result.id, activeTab);
      setSelectedOptionIds(prev => prev.filter(id => id !== result.id));
    } else {
      // Add to selected components
      const selectedComponent: SelectedComponent = {
        ...result,
        componentType: activeTab,
        jobSpecs: currentJobSpecs,
        selectedAt: new Date()
      };

      addComponent(selectedComponent);
      setSelectedOptionIds(prev => [...prev, result.id]);
    }
  }, [activeTab, innerTextJobData, coverJobData, endpaperJobData, isComponentSelected, addComponent, removeComponent]);

  // Handle component removal from drawer
  const handleRemoveComponent = useCallback((componentId: string, componentType: TabId) => {
    removeComponent(componentId, componentType);

    // Always update selected option IDs when removing from drawer
    // This ensures proper state synchronization regardless of active tab
    setSelectedOptionIds(prev => prev.filter(id => id !== componentId));
  }, [removeComponent]);

  // Main calculation handler - shared by both buttons
  const handleCalculation = useCallback(async (options: PaperOption[]) => {
    // Prevent spam clicking
    if (isCalculating || isButtonDisabled) {
      return;
    }

    setIsCalculating(true);
    setIsButtonDisabled(true);
    setCalculationResults([]);

    try {
      // Get current mm values from the job specification cards (handles unit conversion)
      // Always use the ref data if available, otherwise fall back to stored state
      const currentJobData = (() => {
        switch (activeTab) {
          case 'innerText':
            return innerTextJobRef.current?.getCurrentMmValues() || innerTextJobData;
          case 'cover':
            return coverJobRef.current?.getCurrentMmValues() || coverJobData;
          case 'endpapers':
            return endpaperJobRef.current?.getCurrentMmValues() || endpaperJobData;
          default:
            return null;
        }
      })();

      const currentProdData = (() => {
        switch (activeTab) {
          case 'innerText':
            return innerTextProdData;
          case 'cover':
            return coverProdData;
          case 'endpapers':
            return endpaperProdData;
          default:
            return null;
        }
      })();

      // Ensure we have valid data for the active tab
      const activeTabJobData = currentJobData;
      const activeTabProdData = currentProdData;

      if (!activeTabJobData || !activeTabProdData) {
        throw new Error(`Please fill in all job specifications and production parameters for the ${activeTab} tab`);
      }

      const calculationInputs = convertToCalculationInputs(
        activeTab,
        activeTab === 'innerText' ? (activeTabJobData as InnerTextJobSpecificationData) : innerTextJobData,
        innerTextProdData,
        activeTab === 'cover' ? (activeTabJobData as CoverJobSpecificationData) : coverJobData,
        coverProdData,
        activeTab === 'endpapers' ? (activeTabJobData as EndpaperJobSpecificationData) : endpaperJobData,
        endpaperProdData
      );

      if (!calculationInputs) {
        throw new Error('Please fill in all job specifications and production parameters');
      }

      const response = await fetch('/api/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tabId: activeTab,
          jobInputs: calculationInputs,
          paperOptions: options
        }),
      });

      if (!response.ok) {
        throw new Error(`Calculation failed: ${response.statusText}`);
      }

      const data = await response.json();
      const mappedResults = (data.results || []).map(mapPaperCandidateToCalculationResult);
      setCalculationResults(mappedResults);

      // Start countdown timer after successful calculation
      setCountdownSeconds(3); // 3-second countdown
    } catch (error) {
      console.error('Calculation error:', error);
      // Re-enable buttons immediately on error
      setIsButtonDisabled(false);
      setCountdownSeconds(0);
    } finally {
      setIsCalculating(false);
    }
  }, [
    activeTab,
    innerTextJobData,
    innerTextProdData,
    coverJobData,
    coverProdData,
    endpaperJobData,
    endpaperProdData,
    isCalculating,
    isButtonDisabled
  ]);

  return (
    <div className="max-w-6xl mx-auto">
      <SpotlightTracker />
      <TabTracker />

      {/* Tabs */}
      <div className="tabs-container mb-8">
        <div className="tabs-wrapper">
          <button
            className={`tab-item ${activeTab === 'innerText' ? 'active' : ''}`}
            data-tab="innerText"
            onClick={() => handleTabSwitch('innerText')}
          >
            <span className="translatable">{translations[language].tabInnerText || 'Inner Text'}</span>
          </button>
          <button
            className={`tab-item ${activeTab === 'cover' ? 'active' : ''}`}
            data-tab="cover"
            onClick={() => handleTabSwitch('cover')}
          >
            <span className="translatable">{translations[language].tabCover || 'Cover'}</span>
          </button>
          <button
            className={`tab-item ${activeTab === 'endpapers' ? 'active' : ''}`}
            data-tab="endpapers"
            onClick={() => handleTabSwitch('endpapers')}
          >
            <span className="translatable">{translations[language].tabEndpapers || 'Endpapers'}</span>
          </button>
          <div className="tab-indicator"></div>
        </div>
      </div>

      {/* First Row: Job Specifications, Production Parameters, Unit Converter (3 cards) */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <TabSpecificJobSpecificationsCard
          activeTab={activeTab}
          className="bg-white dark:bg-[#1A1A1A] border border-gray-100 dark:border-[#2A2A2A] rounded-lg shadow-[0_2px_8px_rgba(0,0,0,0.04)] dark:shadow-[0_2px_10px_rgba(0,0,0,0.2)] hover:shadow-[0_4px_12px_rgba(0,0,0,0.06)] dark:hover:shadow-[0_4px_20px_rgba(0,0,0,0.3)] transition-all hover:border-[#5E6AD2]/30 dark:hover:border-[#6E56CF]/50"
          onInnerTextDataChange={setInnerTextJobData}
          onCoverDataChange={setCoverJobData}
          onEndpaperDataChange={setEndpaperJobData}
          initialInnerTextData={innerTextJobData}
          initialCoverData={coverJobData}
          initialEndpaperData={endpaperJobData}
          innerTextRef={innerTextJobRef}
          coverRef={coverJobRef}
          endpaperRef={endpaperJobRef}
        />

        <TabSpecificProductionParametersCard
          activeTab={activeTab}
          className="bg-white dark:bg-[#1A1A1A] border border-gray-100 dark:border-[#2A2A2A] rounded-lg shadow-[0_2px_8px_rgba(0,0,0,0.04)] dark:shadow-[0_2px_10px_rgba(0,0,0,0.2)] hover:shadow-[0_4px_12px_rgba(0,0,0,0.06)] dark:hover:shadow-[0_4px_20px_rgba(0,0,0,0.3)] transition-all hover:border-[#5E6AD2]/30 dark:hover:border-[#6E56CF]/50"
          onInnerTextDataChange={setInnerTextProdData}
          onCoverDataChange={setCoverProdData}
          onEndpaperDataChange={setEndpaperProdData}
          initialInnerTextData={innerTextProdData}
          initialCoverData={coverProdData}
          initialEndpaperData={endpaperProdData}
        />

        <UnitConverterCard
          className="bg-white dark:bg-[#1A1A1A] border border-gray-100 dark:border-[#2A2A2A] rounded-lg shadow-[0_2px_8px_rgba(0,0,0,0.04)] dark:shadow-[0_2px_10px_rgba(0,0,0,0.2)] hover:shadow-[0_4px_12px_rgba(0,0,0,0.06)] dark:hover:shadow-[0_4px_20px_rgba(0,0,0,0.3)] transition-all hover:border-[#5E6AD2]/30 dark:hover:border-[#6E56CF]/50"
        />
      </div>

      {/* Second Row: Paper Options (full width) */}
      <div className="mb-8">
        <PaperOptionsCard
          config={getCurrentPaperConfig()}
          onDataChange={(options) => {
            switch (activeTab) {
              case 'innerText':
                setInnerTextPaperOptions(options);
                break;
              case 'cover':
                setCoverPaperOptions(options);
                break;
              case 'endpapers':
                setEndpaperPaperOptions(options);
                break;
            }
          }}
          onCalculate={handleCalculation}
          isCalculating={isCalculating}
          isButtonDisabled={isButtonDisabled}
          countdownSeconds={countdownSeconds}
        />
      </div>

      {/* Third Row: Calculation Results (full width) */}
      <div>
        <CalculationResultsCard
          title="Calculation Results"
          results={calculationResults}
          isCalculating={isCalculating}
          isButtonDisabled={isButtonDisabled}
          onCalculate={() => {
            // Get current paper options for the active tab
            // Only use frontend paper options - no fallback to backend data
            let currentOptions: PaperOption[] = [];
            switch (activeTab) {
              case 'innerText':
                currentOptions = innerTextPaperOptions;
                break;
              case 'cover':
                currentOptions = coverPaperOptions;
                break;
              case 'endpapers':
                currentOptions = endpaperPaperOptions;
                break;
            }

            if (currentOptions.length === 0) {
              console.error('No paper options available for calculation. Please ensure paper options are loaded in the frontend.');
              return;
            }

            handleCalculation(currentOptions);
          }}
          onSelectOption={handleSelectOption}
          selectedOptionIds={selectedOptionIds}
          countdownSeconds={countdownSeconds}
        />
      </div>
    </div>
  );
}
