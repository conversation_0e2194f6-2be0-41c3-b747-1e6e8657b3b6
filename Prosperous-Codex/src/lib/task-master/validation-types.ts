/**
 * TypeScript Validation Types for Task Master
 * 
 * Provides strict type definitions and validation utilities to enforce
 * field name consistency and prevent camelCase/snake_case mismatches.
 */

import { z } from 'zod';

/**
 * Base validation result interface
 */
export interface BaseValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

/**
 * Validation error details
 */
export interface ValidationError {
  field: string;
  message: string;
  code: string;
  severity: 'error';
  context?: Record<string, any>;
}

/**
 * Validation warning details
 */
export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
  severity: 'warning';
  suggestion?: string;
  context?: Record<string, any>;
}

/**
 * Field mapping validation result
 */
export interface FieldMappingValidationResult extends BaseValidationResult {
  mappedFields: Record<string, string>;
  unmappedFields: string[];
  suggestions: Record<string, string>;
}

/**
 * Schema validation result
 */
export interface SchemaValidationResult extends BaseValidationResult {
  tableName: string;
  fieldCount: number;
  mappedFieldCount: number;
  fieldDetails: FieldValidationDetail[];
}

/**
 * Field validation detail
 */
export interface FieldValidationDetail {
  name: string;
  type: string;
  nullable: boolean;
  hasMapping: boolean;
  mappingTarget?: string;
  issues: ValidationIssue[];
}

/**
 * Validation issue
 */
export interface ValidationIssue {
  type: 'naming' | 'mapping' | 'type' | 'constraint';
  severity: 'error' | 'warning' | 'info';
  message: string;
  suggestion?: string;
}

/**
 * API contract validation result
 */
export interface ApiContractValidationResult extends BaseValidationResult {
  endpoint: string;
  method: string;
  requestValidation?: FieldSetValidationResult;
  responseValidation?: FieldSetValidationResult;
}

/**
 * Field set validation result
 */
export interface FieldSetValidationResult {
  expectedFields: string[];
  actualFields: string[];
  missingFields: string[];
  extraFields: string[];
  fieldMappingIssues: FieldMappingIssue[];
}

/**
 * Field mapping issue
 */
export interface FieldMappingIssue {
  field: string;
  issue: 'missing_mapping' | 'incorrect_case' | 'inconsistent_naming';
  expected?: string;
  actual: string;
  suggestion: string;
}

/**
 * Strict type definitions for database fields (snake_case)
 */
export interface ProjectDbFields {
  id: number;
  title: string;
  description?: string;
  full_description?: string;
  event_log?: string;
  status: 'todo' | 'in_progress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  progress: number;
  due_date?: string;
  completed_date?: string;
  created_by: number;
  assigned_to?: number;
  created_at: string;
  updated_at: string;
}

export interface TaskDbFields {
  id: number;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  progress: number;
  due_date?: string;
  completed_date?: string;
  project_id: number;
  parent_task_id?: number;
  created_by: number;
  assigned_to?: number;
  created_at: string;
  updated_at: string;
}

export interface CommentDbFields {
  id: number;
  content: string;
  task_id: number;
  parent_comment_id?: number;
  created_by: number;
  created_at: string;
  updated_at: string;
}

export interface FileDbFields {
  id: number;
  file_name: string;
  file_size: number;
  mime_type: string;
  file_path: string;
  project_id: number;
  uploaded_by: number;
  uploaded_at: string;
}

export interface TeamMemberDbFields {
  id: number;
  project_id: number;
  user_id: number;
  role: 'owner' | 'admin' | 'member';
  joined_at: string;
}

export interface ActivityLogDbFields {
  id: number;
  activity_type: string;
  entity_type: string;
  entity_id: number;
  old_value?: string;
  new_value?: string;
  project_id: number;
  performed_by: number;
  performed_at: string;
}

/**
 * Strict type definitions for API fields (camelCase)
 */
export interface ProjectApiFields {
  id: number;
  title: string;
  description?: string;
  fullDescription?: string;
  eventLog?: string;
  status: 'todo' | 'in_progress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  progress: number;
  dueDate?: string;
  completedDate?: string;
  createdBy: number;
  assignedTo?: number;
  createdAt: string;
  updatedAt: string;
  createdByUsername?: string;
  assignedToUsername?: string;
}

export interface TaskApiFields {
  id: number;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  progress: number;
  dueDate?: string;
  completedDate?: string;
  projectId: number;
  parentTaskId?: number;
  createdBy: number;
  assignedTo?: number;
  createdAt: string;
  updatedAt: string;
}

export interface CommentApiFields {
  id: number;
  content: string;
  taskId: number;
  parentCommentId?: number;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
}

export interface FileApiFields {
  id: number;
  fileName: string;
  fileSize: number;
  mimeType: string;
  projectId: number;
  uploadedBy: number;
  uploadedAt: string;
}

export interface TeamMemberApiFields {
  id: number;
  projectId: number;
  userId: number;
  role: 'owner' | 'admin' | 'member';
  joinedAt: string;
}

export interface ActivityLogApiFields {
  id: number;
  activityType: string;
  entityType: string;
  entityId: number;
  oldValue?: string;
  newValue?: string;
  projectId: number;
  performedBy: number;
  performedAt: string;
}

/**
 * Zod schemas for runtime validation of field consistency
 */
export const ValidationSchemas = {
  // Database field validation schemas (partial for flexible validation)
  projectDbFields: z.object({
    id: z.number().optional(),
    title: z.string().optional(),
    description: z.string().optional(),
    full_description: z.string().optional(),
    event_log: z.string().optional(),
    status: z.enum(['todo', 'in_progress', 'completed']).optional(),
    priority: z.enum(['low', 'medium', 'high']).optional(),
    progress: z.number().min(0).max(100).optional(),
    due_date: z.string().optional(),
    completed_date: z.string().optional(),
    created_by: z.number().optional(),
    assigned_to: z.number().optional(),
    created_at: z.string().optional(),
    updated_at: z.string().optional(),
  }).partial(),

  // API field validation schemas (partial for flexible validation)
  projectApiFields: z.object({
    id: z.number().optional(),
    title: z.string().optional(),
    description: z.string().optional(),
    fullDescription: z.string().optional(),
    eventLog: z.string().optional(),
    status: z.enum(['todo', 'in_progress', 'completed']).optional(),
    priority: z.enum(['low', 'medium', 'high']).optional(),
    progress: z.number().min(0).max(100).optional(),
    dueDate: z.string().optional(),
    completedDate: z.string().optional(),
    createdBy: z.number().optional(),
    assignedTo: z.number().optional(),
    createdAt: z.string().optional(),
    updatedAt: z.string().optional(),
    createdByUsername: z.string().optional(),
    assignedToUsername: z.string().optional(),
  }).partial(),

  taskDbFields: z.object({
    id: z.number().optional(),
    title: z.string().optional(),
    description: z.string().optional(),
    status: z.enum(['todo', 'in_progress', 'completed']).optional(),
    priority: z.enum(['low', 'medium', 'high']).optional(),
    progress: z.number().min(0).max(100).optional(),
    due_date: z.string().optional(),
    completed_date: z.string().optional(),
    project_id: z.number().optional(),
    parent_task_id: z.number().optional(),
    created_by: z.number().optional(),
    assigned_to: z.number().optional(),
    created_at: z.string().optional(),
    updated_at: z.string().optional(),
  }).partial(),

  taskApiFields: z.object({
    id: z.number().optional(),
    title: z.string().optional(),
    description: z.string().optional(),
    status: z.enum(['todo', 'in_progress', 'completed']).optional(),
    priority: z.enum(['low', 'medium', 'high']).optional(),
    progress: z.number().min(0).max(100).optional(),
    dueDate: z.string().optional(),
    completedDate: z.string().optional(),
    projectId: z.number().optional(),
    parentTaskId: z.number().optional(),
    createdBy: z.number().optional(),
    assignedTo: z.number().optional(),
    createdAt: z.string().optional(),
    updatedAt: z.string().optional(),
  }).partial(),
};

/**
 * Type guards for runtime type checking
 */
export const TypeGuards = {
  isDbProjectFields: (obj: unknown): obj is ProjectDbFields => {
    if (!obj || typeof obj !== 'object') return false;
    const keys = Object.keys(obj);

    // Check for snake_case fields that indicate DB format
    const dbIndicators = ['created_by', 'created_at', 'updated_at', 'full_description', 'event_log', 'due_date'];
    const hasDbIndicators = dbIndicators.some(field => keys.includes(field));

    // Check for camelCase fields that indicate API format
    const apiIndicators = ['createdBy', 'createdAt', 'updatedAt', 'fullDescription', 'eventLog', 'dueDate'];
    const hasApiIndicators = apiIndicators.some(field => keys.includes(field));

    // If it has API indicators, it's not DB format
    if (hasApiIndicators) return false;

    // Return true if it has DB indicators
    return hasDbIndicators;

    return ValidationSchemas.projectDbFields.safeParse(obj).success;
  },

  isApiProjectFields: (obj: unknown): obj is ProjectApiFields => {
    if (!obj || typeof obj !== 'object') return false;
    const keys = Object.keys(obj);

    // Check for camelCase fields that indicate API format
    const apiIndicators = ['createdBy', 'createdAt', 'updatedAt', 'fullDescription', 'eventLog', 'dueDate'];
    const hasApiIndicators = apiIndicators.some(field => keys.includes(field));

    // Check for snake_case fields that indicate DB format
    const dbIndicators = ['created_by', 'created_at', 'updated_at', 'full_description', 'event_log', 'due_date'];
    const hasDbIndicators = dbIndicators.some(field => keys.includes(field));

    // If it has DB indicators, it's not API format
    if (hasDbIndicators) return false;

    // Return true if it has API indicators and passes schema validation
    return hasApiIndicators && ValidationSchemas.projectApiFields.safeParse(obj).success;
  },

  isDbTaskFields: (obj: unknown): obj is TaskDbFields => {
    if (!obj || typeof obj !== 'object') return false;
    const keys = Object.keys(obj);

    // Check for snake_case fields that indicate DB format
    const dbIndicators = ['created_by', 'created_at', 'updated_at', 'project_id', 'parent_task_id', 'due_date'];
    const hasDbIndicators = dbIndicators.some(field => keys.includes(field));

    // Check for camelCase fields that indicate API format
    const apiIndicators = ['createdBy', 'createdAt', 'updatedAt', 'projectId', 'parentTaskId', 'dueDate'];
    const hasApiIndicators = apiIndicators.some(field => keys.includes(field));

    // If it has API indicators, it's not DB format
    if (hasApiIndicators) return false;

    // Return true if it has DB indicators and passes schema validation
    return hasDbIndicators && ValidationSchemas.taskDbFields.safeParse(obj).success;
  },

  isApiTaskFields: (obj: unknown): obj is TaskApiFields => {
    if (!obj || typeof obj !== 'object') return false;
    const keys = Object.keys(obj);

    // Check for camelCase fields that indicate API format
    const apiIndicators = ['createdBy', 'createdAt', 'updatedAt', 'projectId', 'parentTaskId', 'dueDate'];
    const hasApiIndicators = apiIndicators.some(field => keys.includes(field));

    // Check for snake_case fields that indicate DB format
    const dbIndicators = ['created_by', 'created_at', 'updated_at', 'project_id', 'parent_task_id', 'due_date'];
    const hasDbIndicators = dbIndicators.some(field => keys.includes(field));

    // If it has DB indicators, it's not API format
    if (hasDbIndicators) return false;

    // Return true if it has API indicators and passes schema validation
    return hasApiIndicators && ValidationSchemas.taskApiFields.safeParse(obj).success;
  },
};

/**
 * Utility types for field mapping validation
 */
export type DbFieldNames<T> = T extends ProjectDbFields ? keyof ProjectDbFields
  : T extends TaskDbFields ? keyof TaskDbFields
  : T extends CommentDbFields ? keyof CommentDbFields
  : T extends FileDbFields ? keyof FileDbFields
  : T extends TeamMemberDbFields ? keyof TeamMemberDbFields
  : T extends ActivityLogDbFields ? keyof ActivityLogDbFields
  : never;

export type ApiFieldNames<T> = T extends ProjectApiFields ? keyof ProjectApiFields
  : T extends TaskApiFields ? keyof TaskApiFields
  : T extends CommentApiFields ? keyof CommentApiFields
  : T extends FileApiFields ? keyof FileApiFields
  : T extends TeamMemberApiFields ? keyof TeamMemberApiFields
  : T extends ActivityLogApiFields ? keyof ActivityLogApiFields
  : never;

/**
 * Validation configuration
 */
export interface ValidationConfig {
  strictMode: boolean;
  allowExtraFields: boolean;
  requireAllMappings: boolean;
  validateFieldTypes: boolean;
  generateSuggestions: boolean;
}

/**
 * Default validation configuration
 */
export const DEFAULT_VALIDATION_CONFIG: ValidationConfig = {
  strictMode: true,
  allowExtraFields: false,
  requireAllMappings: true,
  validateFieldTypes: true,
  generateSuggestions: true,
};

/**
 * Validation context for tracking validation state
 */
export interface ValidationContext {
  entity: string;
  operation: 'create' | 'read' | 'update' | 'delete';
  source: 'api' | 'database' | 'frontend';
  config: ValidationConfig;
  metadata?: Record<string, unknown>;
}

/**
 * Comprehensive validation result
 */
export interface ComprehensiveValidationResult {
  overall: BaseValidationResult;
  fieldMapping: FieldMappingValidationResult;
  schema: SchemaValidationResult;
  apiContract: ApiContractValidationResult;
  context: ValidationContext;
  recommendations: string[];
  metrics: ValidationMetrics;
}

/**
 * Validation metrics
 */
export interface ValidationMetrics {
  totalFields: number;
  validFields: number;
  invalidFields: number;
  warningFields: number;
  mappingCoverage: number;
  validationTime: number;
}

/**
 * Strict type enforcement utilities
 */

/**
 * Utility type to enforce field name consistency
 * Currently unused but kept for future implementation
 */
// type EnforceFieldConsistency<T, TExpected> = {
//   [K in keyof T]: K extends keyof TExpected
//     ? T[K] extends TExpected[K]
//       ? T[K]
//       : never
//     : never;
// };

/**
 * Type guard to ensure object conforms to expected field structure
 */
export function enforceFieldStructure<T extends Record<string, unknown>>(
  obj: unknown,
  expectedFields: (keyof T)[],
  validator?: (obj: unknown) => obj is T
): obj is T {
  if (!obj || typeof obj !== 'object') {
    return false;
  }

  const objKeys = Object.keys(obj);

  // Check for unexpected fields
  for (const key of objKeys) {
    if (!expectedFields.includes(key as keyof T)) {
      return false;
    }
  }

  // Use custom validator if provided
  if (validator) {
    return validator(obj);
  }

  return true;
}

/**
 * Strict field name validator
 */
export function validateFieldNames<T extends Record<string, any>>(
  obj: T,
  allowedPattern: RegExp = /^[a-z][a-zA-Z0-9]*$/
): { valid: boolean; invalidFields: string[]; suggestions: Record<string, string> } {
  const invalidFields: string[] = [];
  const suggestions: Record<string, string> = {};

  for (const field of Object.keys(obj)) {
    if (!allowedPattern.test(field)) {
      invalidFields.push(field);

      // Suggest camelCase conversion for snake_case
      if (field.includes('_')) {
        suggestions[field] = field.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      }
      // Suggest snake_case conversion for camelCase
      else if (/[A-Z]/.test(field)) {
        suggestions[field] = field.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
      }
    }
  }

  return {
    valid: invalidFields.length === 0,
    invalidFields,
    suggestions,
  };
}

/**
 * Type-safe field mapper with compile-time validation
 */
export function createTypeSafeMapper<TSource extends Record<string, unknown>, TTarget>(
  mapping: Record<keyof TSource, keyof TTarget>
) {
  return function mapFields(source: TSource): Partial<TTarget> {
    const target = {} as Partial<TTarget>;

    for (const [sourceKey, targetKey] of Object.entries(mapping) as [keyof TSource, keyof TTarget][]) {
      if (sourceKey in source) {
        target[targetKey] = source[sourceKey] as any;
      }
    }

    return target;
  };
}

/**
 * Runtime type assertion with detailed error reporting
 */
export function assertType<T>(
  value: unknown,
  typeName: string,
  validator: (value: unknown) => value is T
): asserts value is T {
  if (!validator(value)) {
    throw new Error(`Type assertion failed: expected ${typeName}, got ${typeof value}`);
  }
}

/**
 * Compile-time field name consistency checker
 */
export type CheckFieldConsistency<TApi, TDb> = {
  [K in keyof TApi]: K extends string
    ? K extends keyof TDb
      ? TApi[K] extends TDb[K]
        ? true
        : string // Simplified to avoid template literal type issues
      : string
    : never;
} & {
  [K in keyof TDb]: K extends keyof TApi
    ? true
    : string;
};

/**
 * Utility to create validated field mappings
 */
export function createValidatedMapping<TApi, TDb>(
  apiFields: (keyof TApi)[],
  dbFields: (keyof TDb)[],
  mapping: Partial<Record<keyof TApi, keyof TDb>>
): {
  mapping: Record<keyof TApi, keyof TDb>;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const validatedMapping = {} as Record<keyof TApi, keyof TDb>;

  for (const apiField of apiFields) {
    const dbField = mapping[apiField];

    if (!dbField) {
      // Try to find a matching field
      const matchingDbField = dbFields.find(field =>
        String(field).toLowerCase() === String(apiField).toLowerCase()
      );

      if (matchingDbField) {
        validatedMapping[apiField] = matchingDbField;
        warnings.push(`Auto-mapped ${String(apiField)} to ${String(matchingDbField)}`);
      } else {
        errors.push(`No mapping found for API field: ${String(apiField)}`);
      }
    } else if (!dbFields.includes(dbField)) {
      errors.push(`Mapped DB field ${String(dbField)} does not exist`);
    } else {
      validatedMapping[apiField] = dbField;
    }
  }

  return { mapping: validatedMapping, errors, warnings };
}

/**
 * Advanced validation decorators and utilities
 */

/**
 * Decorator to validate field names on class properties
 */
export function ValidateFieldNames(pattern: RegExp = /^[a-z][a-zA-Z0-9]*$/) {
  return function <T extends { new (...args: any[]): Record<string, unknown> }>(constructor: T) {
    return class extends constructor {
      constructor(...args: any[]) {
        super(...args);

        const fieldValidation = validateFieldNames(this, pattern);
        if (!fieldValidation.valid) {
          console.warn(`Field name validation failed for ${constructor.name}:`, fieldValidation.invalidFields);
        }
      }
    };
  };
}

/**
 * Property decorator to enforce field naming conventions
 */
export function FieldName(expectedPattern: RegExp) {
  return function (target: unknown, propertyKey: string) {
    if (!expectedPattern.test(propertyKey)) {
      throw new Error(`Property ${propertyKey} does not match expected pattern ${expectedPattern}`);
    }
  };
}
