"use client";

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useDebounce } from '@/hooks/use-debounce';
import { FilterState } from '@/components/task-master/filter-dropdown';

export interface UseTaskFiltersReturn {
  filters: FilterState;
  setFilters: (filters: FilterState) => void;
  clearFilters: () => void;
  isLoading: boolean;
  activeFilterCount: number;
  buildApiParams: () => URLSearchParams;
  error: string | null;
  clearError: () => void;
}

export function useTaskFilters(): UseTaskFiltersReturn {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize filters from URL parameters with validation
  const initialFilters = useMemo(() => {
    const filters: FilterState = {};

    try {
      const priority = searchParams.get('priority');
      if (priority && ['high', 'medium', 'low'].includes(priority)) {
        filters.priority = priority as 'high' | 'medium' | 'low';
      } else if (priority) {
        // Invalid priority value - log warning but continue
        console.warn(`Invalid priority filter value: ${priority}. Expected: high, medium, or low`);
      }

      // Validate boolean parameters
      const dueDate = searchParams.get('dueDate');
      if (dueDate === 'true') {
        filters.dueDate = true;
      } else if (dueDate && dueDate !== 'false') {
        console.warn(`Invalid dueDate filter value: ${dueDate}. Expected: true or false`);
      }

      const lastUpdated = searchParams.get('lastUpdated');
      if (lastUpdated === 'true') {
        filters.lastUpdated = true;
      } else if (lastUpdated && lastUpdated !== 'false') {
        console.warn(`Invalid lastUpdated filter value: ${lastUpdated}. Expected: true or false`);
      }

      const completion = searchParams.get('completion');
      if (completion === 'true') {
        filters.completion = true;
      } else if (completion && completion !== 'false') {
        console.warn(`Invalid completion filter value: ${completion}. Expected: true or false`);
      }
    } catch (error) {
      console.error('Error parsing URL filter parameters:', error);
      // Return empty filters on error
      return {};
    }

    return filters;
  }, [searchParams]);

  const [filters, setFiltersState] = useState<FilterState>(initialFilters);

  // Debounce filter changes to avoid excessive API calls
  const debouncedFilters = useDebounce(filters, 300);

  // Update URL when filters change
  const updateUrl = useCallback((newFilters: FilterState) => {
    try {
      const params = new URLSearchParams(searchParams);

      // Clear existing filter parameters
      params.delete('priority');
      params.delete('dueDate');
      params.delete('lastUpdated');
      params.delete('completion');

      // Validate and add new filter parameters
      if (newFilters.priority && ['high', 'medium', 'low'].includes(newFilters.priority)) {
        params.set('priority', newFilters.priority);
      }

      if (newFilters.dueDate === true) {
        params.set('dueDate', 'true');
      }

      if (newFilters.lastUpdated === true) {
        params.set('lastUpdated', 'true');
      }

      if (newFilters.completion === true) {
        params.set('completion', 'true');
      }

      // Update URL without triggering a page reload
      const newUrl = `${pathname}?${params.toString()}`;
      router.replace(newUrl, { scroll: false });
    } catch (error) {
      console.error('Error updating URL with filter parameters:', error);
      setError('Failed to update URL with filter settings');
    }
  }, [searchParams, pathname, router]);

  // Sanitize and validate filter state
  const sanitizeFilters = useCallback((filters: FilterState): FilterState => {
    const sanitized: FilterState = {};

    // Validate priority
    if (filters.priority && ['high', 'medium', 'low'].includes(filters.priority)) {
      sanitized.priority = filters.priority;
    }

    // Validate boolean filters
    if (filters.dueDate === true) {
      sanitized.dueDate = true;
    }

    if (filters.lastUpdated === true) {
      sanitized.lastUpdated = true;
    }

    if (filters.completion === true) {
      sanitized.completion = true;
    }

    return sanitized;
  }, []);

  // Set filters with URL persistence
  const setFilters = useCallback((newFilters: FilterState) => {
    try {
      setError(null);
      setIsLoading(true);

      // Sanitize filters before applying
      const sanitizedFilters = sanitizeFilters(newFilters);

      // Always update state and URL - let React handle optimization
      setFiltersState(sanitizedFilters);
      updateUrl(sanitizedFilters);

      // Clear loading state after a short delay to show visual feedback
      setTimeout(() => setIsLoading(false), 150);
    } catch (err) {
      console.error('Error applying filters:', err);
      setError('Failed to apply filters. Please try again.');
      setIsLoading(false);
    }
  }, [updateUrl, sanitizeFilters]); // Removed 'filters' dependency to prevent infinite loop

  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters({});
  }, [setFilters]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Count active filters
  const activeFilterCount = useMemo(() => {
    return Object.values(filters).filter(Boolean).length;
  }, [filters]);

  // Build API parameters for backend requests
  const buildApiParams = useCallback(() => {
    try {
      const params = new URLSearchParams();

      // Sanitize filters before building API params
      const sanitizedFilters = sanitizeFilters(filters);

      // Build sortBy parameter based on active filters
      const sortFields = [];

      if (sanitizedFilters.priority) {
        sortFields.push('priority');
      }

      if (sanitizedFilters.dueDate) {
        sortFields.push('dueDate');
      }

      if (sanitizedFilters.lastUpdated) {
        sortFields.push('lastUpdated');
      }

      if (sanitizedFilters.completion) {
        sortFields.push('completion');
      }

      // Validate sort fields
      const validSortFields = ['priority', 'dueDate', 'lastUpdated', 'completion'];
      const filteredSortFields = sortFields.filter(field => validSortFields.includes(field));

      if (filteredSortFields.length > 0) {
        params.set('sortBy', filteredSortFields.join(','));
        params.set('order', 'asc'); // Default to ascending order
      }

      return params;
    } catch (err) {
      console.error('Error building API parameters:', err);
      setError('Failed to build filter parameters');
      return new URLSearchParams(); // Return empty params on error
    }
  }, [filters, sanitizeFilters]);

  // Sync filters with URL parameters when they change externally
  useEffect(() => {
    try {
      const urlFilters: FilterState = {};

      // Parse and validate URL parameters
      const priority = searchParams.get('priority');
      if (priority && ['high', 'medium', 'low'].includes(priority)) {
        urlFilters.priority = priority as 'high' | 'medium' | 'low';
      }

      if (searchParams.get('dueDate') === 'true') {
        urlFilters.dueDate = true;
      }

      if (searchParams.get('lastUpdated') === 'true') {
        urlFilters.lastUpdated = true;
      }

      if (searchParams.get('completion') === 'true') {
        urlFilters.completion = true;
      }

      // Sanitize the URL filters
      const sanitizedUrlFilters = sanitizeFilters(urlFilters);

      // Only update if filters actually changed to avoid infinite loops
      const hasChanged = JSON.stringify(filters) !== JSON.stringify(sanitizedUrlFilters);
      if (hasChanged) {
        setFiltersState(sanitizedUrlFilters);
      }
    } catch (error) {
      console.error('Error syncing filters with URL:', error);
      // Reset to empty filters on error
      setFiltersState({});
      setError('Invalid filter parameters in URL. Filters have been reset.');
    }
  }, [searchParams, sanitizeFilters]); // Removed 'filters' from dependencies to prevent infinite loop

  return {
    filters,
    setFilters,
    clearFilters,
    isLoading,
    activeFilterCount,
    buildApiParams,
    error,
    clearError
  };
}
