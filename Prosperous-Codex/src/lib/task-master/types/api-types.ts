/**
 * API Types
 * 
 * Type definitions for API interfaces
 * Extracted from validation-types.ts for better organization
 */

// Re-export API types from validation-types
export type {
  ProjectApiFields,
  TaskApiFields,
  CommentApiFields,
  FileApiFields,
  TeamMemberApiFields,
  ActivityLogApiFields
} from '../validation-types';

/**
 * Additional API-specific types
 */

/**
 * HTTP methods
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

/**
 * API response wrapper
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: {
    timestamp: string;
    version: string;
    requestId?: string;
  };
}

/**
 * Paginated API response
 */
export interface PaginatedApiResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * API error response
 */
export interface ApiErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: Array<{
    field: string;
    message: string;
    code?: string;
  }>;
  metadata?: {
    timestamp: string;
    requestId?: string;
  };
}

/**
 * API endpoint configuration
 */
export interface ApiEndpoint {
  path: string;
  method: HttpMethod;
  description: string;
  requestSchema?: string;
  responseSchema?: string;
  authentication: boolean;
  authorization?: string[];
  rateLimit?: {
    requests: number;
    window: number; // in seconds
  };
}

/**
 * API versioning
 */
export interface ApiVersion {
  version: string;
  deprecated: boolean;
  sunset?: string;
  endpoints: ApiEndpoint[];
}

/**
 * Request context
 */
export interface RequestContext {
  userId?: number;
  userRole?: string;
  requestId: string;
  timestamp: string;
  userAgent?: string;
  ip?: string;
}

/**
 * File upload types
 */
export interface FileUploadRequest {
  file: File;
  description?: string;
  tags?: string[];
}

export interface FileUploadResponse {
  id: number;
  fileName: string;
  fileSize: number;
  mimeType: string;
  url: string;
  uploadedAt: string;
}

/**
 * Bulk operation types
 */
export interface BulkOperationRequest {
  ids: number[];
  operation: 'delete' | 'update' | 'archive';
  data?: Record<string, any>;
}

export interface BulkOperationResponse {
  successful: number[];
  failed: Array<{
    id: number;
    error: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

/**
 * Search and filter types
 */
export interface SearchFilters {
  query?: string;
  status?: string[];
  priority?: string[];
  assignedTo?: number[];
  createdBy?: number[];
  dateRange?: {
    start: string;
    end: string;
  };
  tags?: string[];
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

/**
 * API rate limiting
 */
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number; // Unix timestamp
  retryAfter?: number; // seconds
}

/**
 * API health check
 */
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  services: Record<string, {
    status: 'up' | 'down';
    responseTime?: number;
    error?: string;
  }>;
}
