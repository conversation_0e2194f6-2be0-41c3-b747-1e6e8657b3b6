/**
 * Partial Response Support for Task Master API
 * Allows clients to request only specific fields to reduce payload size
 *
 * Usage Examples:
 * - ?fields=basic - Returns only basic fields
 * - ?fields=id,title,status - Returns specific fields
 * - ?fields=withRelations - Returns all fields including relations
 */

/**
 * Supported field sets for different resources
 */
export const FIELD_SETS = {
  project: {
    basic: ['id', 'title', 'status', 'priority', 'progress', 'createdAt', 'updatedAt'],
    summary: ['id', 'title', 'description', 'status', 'priority', 'progress', 'dueDate', 'createdAt', 'updatedAt', 'createdBy', 'assignedTo'],
    full: ['id', 'title', 'description', 'fullDescription', 'eventLog', 'status', 'priority', 'progress', 'dueDate', 'createdAt', 'updatedAt', 'createdBy', 'assignedTo', 'tags'],
    withRelations: ['id', 'title', 'description', 'status', 'priority', 'progress', 'dueDate', 'createdAt', 'updatedAt', 'createdBy', 'assignedTo', 'tags', 'teamMembers', 'tasks', 'files', 'comments', 'activity']
  },
  task: {
    basic: ['id', 'title', 'status', 'priority', 'createdAt', 'updatedAt'],
    summary: ['id', 'projectId', 'parentTaskId', 'title', 'description', 'status', 'priority', 'dueDate', 'createdAt', 'updatedAt', 'createdBy', 'assignedTo'],
    full: ['id', 'projectId', 'parentTaskId', 'title', 'description', 'status', 'priority', 'dueDate', 'createdAt', 'updatedAt', 'createdBy', 'assignedTo', 'subtasks']
  },
  comment: {
    basic: ['id', 'content', 'createdAt', 'authorId'],
    full: ['id', 'projectId', 'parentCommentId', 'content', 'createdAt', 'authorId', 'authorUsername', 'authorEmail']
  },
  file: {
    basic: ['id', 'fileName', 'fileSize', 'uploadedAt'],
    full: ['id', 'projectId', 'fileName', 'fileType', 'fileSize', 'filePath', 'thumbnailPath', 'uploadedAt', 'uploadedBy', 'uploadedByUsername']
  },
  activity: {
    basic: ['id', 'activityType', 'description', 'createdAt'],
    full: ['id', 'projectId', 'taskId', 'userId', 'activityType', 'description', 'createdAt', 'username']
  }
};

/**
 * Parse fields parameter from query string
 */
export function parseFields(fieldsParam: string | null, resourceType: keyof typeof FIELD_SETS): string[] {
  if (!fieldsParam) {
    return FIELD_SETS[resourceType].summary;
  }

  // Handle predefined field sets
  if (fieldsParam in FIELD_SETS[resourceType]) {
    return FIELD_SETS[resourceType][fieldsParam as keyof typeof FIELD_SETS[typeof resourceType]];
  }

  // Handle comma-separated field list
  const requestedFields = fieldsParam.split(',').map(field => field.trim());
  const availableFields = FIELD_SETS[resourceType].withRelations || FIELD_SETS[resourceType].full;
  
  // Filter to only include valid fields
  return requestedFields.filter(field => availableFields.includes(field));
}

/**
 * Filter object to include only specified fields
 */
export function filterFields<T extends Record<string, any>>(obj: T, fields: string[]): Partial<T> {
  if (!obj || !fields.length) {
    return obj;
  }

  const filtered: Partial<T> = {};
  
  for (const field of fields) {
    if (field in obj) {
      filtered[field as keyof T] = obj[field];
    }
  }
  
  return filtered;
}

/**
 * Filter array of objects to include only specified fields
 */
export function filterFieldsArray<T extends Record<string, any>>(array: T[], fields: string[]): Partial<T>[] {
  return array.map(obj => filterFields(obj, fields));
}

/**
 * Determine what related data to load based on requested fields
 */
export function getLoadingOptions(fields: string[]): {
  includeTasks: boolean;
  includeActivity: boolean;
  includeFiles: boolean;
  includeComments: boolean;
  includeTeamMembers: boolean;
} {
  return {
    includeTasks: fields.includes('tasks'),
    includeActivity: fields.includes('activity'),
    includeFiles: fields.includes('files'),
    includeComments: fields.includes('comments'),
    includeTeamMembers: fields.includes('teamMembers')
  };
}

/**
 * Calculate estimated response size reduction
 */
export function calculateSizeReduction(originalFields: string[], requestedFields: string[]): {
  originalFieldCount: number;
  requestedFieldCount: number;
  reductionPercentage: number;
} {
  const originalFieldCount = originalFields.length;
  const requestedFieldCount = requestedFields.length;
  const reductionPercentage = Math.round(((originalFieldCount - requestedFieldCount) / originalFieldCount) * 100);

  return {
    originalFieldCount,
    requestedFieldCount,
    reductionPercentage: Math.max(0, reductionPercentage)
  };
}

/**
 * Middleware to add partial response support to API endpoints
 */
export function withPartialResponse<T extends Record<string, any>>(
  data: T | T[],
  resourceType: keyof typeof FIELD_SETS,
  fieldsParam: string | null
): {
  data: Partial<T> | Partial<T>[];
  meta?: {
    fields: string[];
    sizeReduction: ReturnType<typeof calculateSizeReduction>;
  };
} {
  const requestedFields = parseFields(fieldsParam, resourceType);
  const allFields = FIELD_SETS[resourceType].withRelations || FIELD_SETS[resourceType].full;
  
  let filteredData: Partial<T> | Partial<T>[];
  
  if (Array.isArray(data)) {
    filteredData = filterFieldsArray(data, requestedFields);
  } else {
    filteredData = filterFields(data, requestedFields);
  }

  const sizeReduction = calculateSizeReduction(allFields, requestedFields);

  return {
    data: filteredData,
    meta: fieldsParam ? {
      fields: requestedFields,
      sizeReduction
    } : undefined
  };
}

/**
 * Validate field parameter against available fields
 */
export function validateFields(fieldsParam: string, resourceType: keyof typeof FIELD_SETS): {
  valid: boolean;
  invalidFields: string[];
  suggestions: string[];
} {
  const requestedFields = fieldsParam.split(',').map(field => field.trim());
  const availableFields = FIELD_SETS[resourceType].withRelations || FIELD_SETS[resourceType].full;
  
  const invalidFields = requestedFields.filter(field => !availableFields.includes(field));
  const valid = invalidFields.length === 0;
  
  // Suggest similar fields for invalid ones
  const suggestions = invalidFields.map(invalidField => {
    const similar = availableFields.find(field => 
      field.toLowerCase().includes(invalidField.toLowerCase()) ||
      invalidField.toLowerCase().includes(field.toLowerCase())
    );
    return similar || '';
  }).filter(Boolean);

  return {
    valid,
    invalidFields,
    suggestions
  };
}

/**
 * Generate field documentation for API responses
 */
export function generateFieldDocs(resourceType: keyof typeof FIELD_SETS): {
  availableFields: string[];
  predefinedSets: Record<string, string[]>;
  examples: string[];
} {
  const fieldSets = FIELD_SETS[resourceType];
  const availableFields = fieldSets.withRelations || fieldSets.full;
  
  return {
    availableFields,
    predefinedSets: fieldSets,
    examples: [
      'basic',
      'summary', 
      'full',
      'id,title,status',
      'title,description,createdAt,updatedAt'
    ]
  };
}

/**
 * Performance metrics for partial response
 */
export interface PartialResponseMetrics {
  originalSize: number;
  filteredSize: number;
  reductionBytes: number;
  reductionPercentage: number;
  processingTimeMs: number;
}

/**
 * Measure performance impact of partial response filtering
 */
export function measurePartialResponsePerformance<T>(
  data: T,
  filterFunction: () => Partial<T>
): PartialResponseMetrics {
  const startTime = performance.now();
  
  const originalSize = JSON.stringify(data).length;
  const filteredData = filterFunction();
  const filteredSize = JSON.stringify(filteredData).length;
  
  const endTime = performance.now();
  
  return {
    originalSize,
    filteredSize,
    reductionBytes: originalSize - filteredSize,
    reductionPercentage: Math.round(((originalSize - filteredSize) / originalSize) * 100),
    processingTimeMs: endTime - startTime
  };
}
