"use client";

import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { X, Copy, Calculator, Ruler, Scale, Grid3X3, Container, Thermometer } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ExpandedUnitConverterProps {
  onClose: () => void;
}

// Unit configuration matching the Modern Unit Converter
const UNIT_CATEGORIES = {
  length: {
    name: 'Length',
    baseUnit: 'm',
    units: [
      { id: 'mm', name: 'Millimeters', symbol: 'mm', factor: 0.001 },
      { id: 'cm', name: 'Centimeters', symbol: 'cm', factor: 0.01 },
      { id: 'm', name: 'Meters', symbol: 'm', factor: 1 },
      { id: 'km', name: 'Kilometers', symbol: 'km', factor: 1000 },
      { id: 'in', name: 'Inches', symbol: 'in', factor: 0.0254 },
      { id: 'ft', name: 'Feet', symbol: 'ft', factor: 0.3048 },
      { id: 'yd', name: 'Yards', symbol: 'yd', factor: 0.9144 },
      { id: 'mi', name: 'Miles', symbol: 'mi', factor: 1609.34 }
    ]
  },
  weight: {
    name: 'Weight',
    baseUnit: 'kg',
    units: [
      { id: 'mg', name: 'Milligrams', symbol: 'mg', factor: 0.000001 },
      { id: 'g', name: 'Grams', symbol: 'g', factor: 0.001 },
      { id: 'kg', name: 'Kilograms', symbol: 'kg', factor: 1 },
      { id: 'tonne', name: 'Metric Tons', symbol: 't', factor: 1000 },
      { id: 'oz', name: 'Ounces', symbol: 'oz', factor: 0.0283495 },
      { id: 'lb', name: 'Pounds', symbol: 'lb', factor: 0.453592 },
      { id: 'st', name: 'Stone', symbol: 'st', factor: 6.35029 }
    ]
  },
  area: {
    name: 'Area',
    baseUnit: 'm2',
    units: [
      { id: 'mm2', name: 'Square mm', symbol: 'mm²', factor: 0.000001 },
      { id: 'cm2', name: 'Square cm', symbol: 'cm²', factor: 0.0001 },
      { id: 'm2', name: 'Square m', symbol: 'm²', factor: 1 },
      { id: 'ha', name: 'Hectares', symbol: 'ha', factor: 10000 },
      { id: 'km2', name: 'Square km', symbol: 'km²', factor: 1000000 },
      { id: 'in2', name: 'Square inches', symbol: 'in²', factor: 0.00064516 },
      { id: 'ft2', name: 'Square feet', symbol: 'ft²', factor: 0.092903 },
      { id: 'yd2', name: 'Square yards', symbol: 'yd²', factor: 0.836127 },
      { id: 'acre', name: 'Acres', symbol: 'acre', factor: 4046.86 }
    ]
  },
  volume: {
    name: 'Volume',
    baseUnit: 'l',
    units: [
      { id: 'ml', name: 'Milliliters', symbol: 'ml', factor: 0.001 },
      { id: 'l', name: 'Liters', symbol: 'l', factor: 1 },
      { id: 'm3', name: 'Cubic meters', symbol: 'm³', factor: 1000 },
      { id: 'gal', name: 'Gallons (US)', symbol: 'gal', factor: 3.78541 },
      { id: 'pt', name: 'Pints (US)', symbol: 'pt', factor: 0.473176 },
      { id: 'qt', name: 'Quarts (US)', symbol: 'qt', factor: 0.946353 },
      { id: 'fl_oz', name: 'Fluid Ounces (US)', symbol: 'fl oz', factor: 0.0295735 },
      { id: 'cup', name: 'Cups (US)', symbol: 'cup', factor: 0.24 }
    ]
  },
  temperature: {
    name: 'Temperature',
    baseUnit: 'c',
    units: [
      { id: 'c', name: 'Celsius', symbol: '°C', factor: 1 },
      { id: 'f', name: 'Fahrenheit', symbol: '°F', factor: 1 },
      { id: 'k', name: 'Kelvin', symbol: 'K', factor: 1 }
    ]
  }
};

export default function ExpandedUnitConverter({ onClose }: ExpandedUnitConverterProps) {
  const { toast } = useToast();
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [activeCategory, setActiveCategory] = useState('length');
  const [inputValue, setInputValue] = useState('');
  const [fromUnit, setFromUnit] = useState('mm');
  const [results, setResults] = useState<Array<{ unit: string; value: string }>>([]);

  // Category icon mapping
  const getCategoryIcon = (categoryKey: string) => {
    const iconMap = {
      length: Ruler,
      weight: Scale,
      area: Grid3X3,
      volume: Container,
      temperature: Thermometer
    };
    return iconMap[categoryKey as keyof typeof iconMap] || Ruler;
  };

  // All hooks must be called before any early returns
  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle keyboard shortcuts
  useEffect(() => {
    if (!mounted) return; // Guard clause instead of early return

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose, mounted]);

  // Update from unit when category changes
  useEffect(() => {
    if (!mounted) return; // Guard clause instead of early return

    const category = UNIT_CATEGORIES[activeCategory as keyof typeof UNIT_CATEGORIES];
    if (category) {
      setFromUnit(category.units[0].id);
    }
  }, [activeCategory, mounted]);

  // Calculate conversions
  useEffect(() => {
    if (!mounted) return; // Guard clause instead of early return

    if (!inputValue || isNaN(Number(inputValue))) {
      setResults([]);
      return;
    }

    const category = UNIT_CATEGORIES[activeCategory as keyof typeof UNIT_CATEGORIES];
    if (!category) return;

    const fromUnitData = category.units.find(u => u.id === fromUnit);
    if (!fromUnitData) return;

    const inputNum = Number(inputValue);

    // Convert to base unit first
    let baseValue: number;
    if (activeCategory === 'temperature') {
      // Special handling for temperature
      if (fromUnit === 'c') baseValue = inputNum;
      else if (fromUnit === 'f') baseValue = (inputNum - 32) * 5/9;
      else if (fromUnit === 'k') baseValue = inputNum - 273.15;
      else baseValue = inputNum;
    } else {
      baseValue = inputNum * fromUnitData.factor;
    }

    // Convert from base to all other units
    const newResults = category.units
      .filter(unit => unit.id !== fromUnit)
      .map(unit => {
        let convertedValue: number;

        if (activeCategory === 'temperature') {
          // Special handling for temperature
          if (unit.id === 'c') convertedValue = baseValue;
          else if (unit.id === 'f') convertedValue = (baseValue * 9/5) + 32;
          else if (unit.id === 'k') convertedValue = baseValue + 273.15;
          else convertedValue = baseValue;
        } else {
          convertedValue = baseValue / unit.factor;
        }

        return {
          unit,
          value: formatNumber(convertedValue)
        };
      });

    setResults(newResults);
  }, [inputValue, fromUnit, activeCategory, mounted]);

  // Define formatNumber function before it's used in useEffect
  const formatNumber = (num: number): string => {
    if (num === 0) return '0.00';
    if (isNaN(num)) return '0.00';

    // Handle very small numbers
    if (Math.abs(num) < 0.00001 && Math.abs(num) > 0) {
      return num.toExponential(4);
    }

    // Handle very large or very small numbers
    if (Math.abs(num) >= 1000000 || (Math.abs(num) > 0 && Math.abs(num) < 0.001)) {
      let fixed = 6;
      if (Math.abs(num) >= 1) fixed = 4;
      if (Math.abs(num) >= 100) fixed = 2;
      if (Math.abs(num) >= 10000) fixed = 0;
      if (Math.abs(num) < 0.000001) fixed = 8;

      let stringNum = num.toFixed(fixed);
      if (stringNum.includes('.')) {
        stringNum = stringNum.replace(/0+$/, '');
        if (stringNum.endsWith('.')) stringNum = stringNum.slice(0, -1);
      }

      const numericVal = parseFloat(stringNum);
      if (!Number.isInteger(numericVal) && (!stringNum.includes('.') || (stringNum.split('.')[1] || "").length < 2)) {
        return numericVal.toFixed(2);
      }
      return stringNum;
    }

    // Normal numbers
    return num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 6 });
  };

  // Early return after all hooks and function definitions
  if (!mounted) {
    return null;
  }

  const isDark = resolvedTheme === 'dark';

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: "Copied to clipboard!",
        description: `Value ${text} has been copied to your clipboard.`,
      });
    }).catch(() => {
      toast({
        title: "Copy failed",
        description: "Unable to copy to clipboard. Please try again.",
        variant: "destructive",
      });
    });
  };

  const currentCategory = UNIT_CATEGORIES[activeCategory as keyof typeof UNIT_CATEGORIES];

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[300] flex items-center justify-center p-4"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        className={`overflow-hidden rounded-2xl max-w-6xl w-full max-h-[90vh] flex flex-col animate-in fade-in-0 zoom-in-95 duration-300 no-hover-transform ${
          isDark
            ? 'beautiful-shadow-dark bg-slate-900 border-slate-700'
            : 'beautiful-shadow bg-white border-slate-200'
        }`}
        data-glow
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header - Sidebar Style */}
        <div className={`flex items-center justify-between border-b pt-5 pr-5 pb-5 pl-5 ${isDark ? 'border-slate-800' : 'border-slate-100'}`}>
          <div className={`flex items-center gap-3 transition-all text-sm font-semibold rounded-xl pt-2.5 pr-4 pb-2.5 pl-4 ${
            isDark
              ? 'beautiful-shadow-dark bg-slate-800 border-slate-700 border'
              : 'beautiful-shadow bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200 border'
          }`}>
            <Calculator className={`w-5 h-5 ${isDark ? 'text-cyan-400' : 'text-indigo-600'}`} />
            <div>
              <span className={`text-lg font-bold ${isDark ? 'text-white' : 'text-slate-800'}`}>Unit Converter</span>
              {inputValue && results.length > 0 && (
                <div className={`text-xs mt-0.5 ${isDark ? 'text-cyan-400' : 'text-indigo-600'}`}>
                  Converting {inputValue} {currentCategory?.units.find(u => u.id === fromUnit)?.symbol} → {results.length} units
                </div>
              )}
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className={`rounded-xl ${isDark ? 'text-white hover:bg-slate-800' : 'text-gray-600 hover:bg-gray-100'}`}
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <div className="flex flex-col lg:flex-row h-full">
            {/* Left Sidebar - Category Navigation */}
            <div className="w-full lg:w-1/4 lg:min-w-[250px] border-b lg:border-b-0 lg:border-r p-5 overflow-y-auto scroll-hide" style={{
              borderColor: isDark ? 'rgb(51 65 85)' : 'rgb(226 232 240)'
            }}>
              <div className="mb-6">
                <div className="mb-3 px-2">
                  <h3 className={`text-sm font-semibold ${isDark ? 'text-slate-400' : 'text-slate-600'}`}>
                    CONVERSION CATEGORIES
                  </h3>
                </div>
                <div className="space-y-1">
                  {Object.entries(UNIT_CATEGORIES).map(([key, category]) => {
                    const isActive = activeCategory === key;
                    const IconComponent = getCategoryIcon(key);
                    return (
                      <button
                        key={key}
                        onClick={() => setActiveCategory(key)}
                        className={`w-full flex items-center gap-3 px-4 py-3 rounded-xl transition-colors text-left relative ${
                          isActive
                            ? isDark
                              ? 'bg-gradient-to-r from-cyan-600 to-blue-600 beautiful-shadow-dark text-white'
                              : 'bg-gradient-to-r from-blue-500 to-purple-600 beautiful-shadow text-white'
                            : isDark
                              ? 'text-slate-300 hover:text-white hover:bg-slate-800'
                              : 'text-slate-700 hover:bg-slate-100'
                        }`}
                      >
                        <IconComponent className={`w-4 h-4 ${
                          isActive
                            ? 'text-white'
                            : isDark
                              ? 'text-slate-400'
                              : 'text-slate-500'
                        }`} />
                        <span className={isActive ? 'font-medium' : ''}>{category.name}</span>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Right Content Area - Input and Results */}
            <div className="flex-1 p-5 overflow-y-auto scroll-hide">
              <div className="space-y-6">

                {/* Input Section - Sidebar Card Style */}
                <div className={`rounded-xl border p-5 ${
                  isDark
                    ? 'bg-slate-800 border-slate-700 beautiful-shadow-dark'
                    : 'bg-gradient-to-br from-slate-50 to-indigo-50 beautiful-shadow border-slate-200'
                }`} data-glow>
                  <h4 className={`font-semibold mb-4 ${isDark ? 'text-white' : 'text-slate-800'}`}>
                    Conversion Input
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="expanded-input" className={`text-sm font-medium mb-2 block ${
                        isDark ? 'text-slate-300' : 'text-slate-600'
                      }`}>
                        Enter Value:
                      </Label>
                      <div className="relative">
                        <Input
                          id="expanded-input"
                          type="text"
                          placeholder="0.00"
                          value={inputValue}
                          onChange={(e) => {
                            const value = e.target.value;
                            // Allow empty string, numbers, decimal points, and negative signs
                            if (value === '' || /^-?\d*\.?\d*$/.test(value)) {
                              setInputValue(value);
                            }
                          }}
                          className={`text-lg pr-12 ${
                            isDark
                              ? 'bg-slate-700 border-slate-600 text-white'
                              : 'bg-white border-slate-300'
                          }`}
                        />
                        {inputValue && (
                          <button
                            onClick={() => setInputValue('')}
                            className={`absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors ${
                              isDark
                                ? 'text-slate-400 hover:text-slate-200'
                                : 'text-gray-400 hover:text-gray-600'
                            }`}
                          >
                            <X className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="expanded-from-unit" className={`text-sm font-medium mb-2 block ${
                        isDark ? 'text-slate-300' : 'text-slate-600'
                      }`}>
                        From Unit:
                      </Label>
                      <Select value={fromUnit} onValueChange={setFromUnit}>
                        <SelectTrigger className={`text-sm ${
                          isDark
                            ? 'bg-slate-700 border-slate-600 text-white'
                            : 'bg-white border-slate-300'
                        }`}>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {currentCategory?.units.map((unit) => (
                            <SelectItem key={unit.id} value={unit.id}>
                              {unit.name} ({unit.symbol})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* Results Section - Sidebar Card Style */}
                <div className={`rounded-xl border p-5 ${
                  isDark
                    ? 'bg-slate-800 border-slate-700 beautiful-shadow-dark'
                    : 'bg-gradient-to-br from-slate-50 to-indigo-50 beautiful-shadow border-slate-200'
                }`} data-glow>
                  <div className={`mb-4 pb-4 border-b ${isDark ? 'border-slate-700' : 'border-slate-200'}`}>
                    <h3 className={`font-semibold ${isDark ? 'text-white' : 'text-slate-800'}`}>
                      Conversion Results
                    </h3>
                    <p className={`text-sm mt-1 ${isDark ? 'text-slate-400' : 'text-slate-500'}`}>
                      {results.length > 0 ? `${results.length} conversions available` : 'Enter a value to see conversions'}
                    </p>
                  </div>

                  {results.length === 0 ? (
                    <div className={`text-center py-12 ${isDark ? 'text-slate-400' : 'text-slate-500'}`}>
                      <div className={`rounded-xl w-20 h-20 flex items-center justify-center mx-auto mb-4 ${
                        isDark ? 'bg-slate-700' : 'bg-slate-200'
                      }`}>
                        <Calculator className="h-10 w-10 opacity-50" />
                      </div>
                      <p className="font-medium text-lg mb-2">Enter a value to see conversions</p>
                      <p className="text-sm">All conversions will appear here</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                      {results.map((result, index) => (
                        <div
                          key={result.unit.id}
                          className={`rounded-xl p-4 transition-all duration-200 group hover:scale-105 animate-in fade-in-0 slide-in-from-bottom-4 ${
                            isDark
                              ? 'bg-slate-700 border border-slate-600 hover:bg-slate-600 beautiful-shadow-dark'
                              : 'bg-white border border-slate-200 hover:shadow-md beautiful-shadow hover:border-indigo-300'
                          }`}
                          data-glow
                          style={{ animationDelay: `${index * 50}ms` }}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className={`text-sm font-medium mb-1 ${
                                isDark ? 'text-slate-300' : 'text-slate-600'
                              }`}>
                                {result.unit.name}
                              </div>
                              <div className={`text-lg font-semibold break-all ${
                                isDark ? 'text-white' : 'text-slate-900'
                              }`}>
                                {result.value} <span className={`${
                                  isDark ? 'text-cyan-400' : 'text-indigo-600'
                                }`}>{result.unit.symbol}</span>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(result.value)}
                              className={`opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0 ${
                                isDark
                                  ? 'hover:bg-slate-600'
                                  : 'hover:bg-indigo-100'
                              }`}
                              title="Copy value"
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
