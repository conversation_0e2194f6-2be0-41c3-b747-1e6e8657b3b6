"use client";

import { useState, useCallback } from 'react';

export function useDragState() {
  const [isTaskDragging, setIsTaskDragging] = useState(false);
  const [draggedItem, setDraggedItem] = useState<{
    id: string;
    title: string;
    createdBy: number;
  } | null>(null);

  const startDrag = useCallback((item: { id: string; title: string; createdBy: number }) => {
    setIsTaskDragging(true);
    setDraggedItem(item);
  }, []);

  const endDrag = useCallback(() => {
    setIsTaskDragging(false);
    setDraggedItem(null);
  }, []);

  return {
    isDragging: isTaskDragging,
    draggedItem,
    startDrag,
    endDrag
  };
}
