import { getDatabase } from './database';
import { UserService } from './user-service';
import { User } from '@/lib/types/auth';
import crypto from 'crypto';

export interface Session {
  id: number;
  userId: number;
  sessionToken: string;
  expiresAt: string;
  createdAt: string;
}

export interface CreateSessionData {
  userId: number;
  rememberMe?: boolean;
}

export class SessionService {
  private db = getDatabase();
  private userService = new UserService();

  // Session duration constants
  private readonly SESSION_DURATION = {
    default: 24 * 60 * 60 * 1000, // 24 hours
    rememberMe: 7 * 24 * 60 * 60 * 1000, // 7 days
  };

  /**
   * Create a new session for a user
   */
  async createSession(data: CreateSessionData): Promise<string> {
    const { userId, rememberMe = false } = data;
    
    try {
      // Generate secure session token
      const sessionToken = this.generateSessionToken();
      
      // Calculate expiration time
      const duration = rememberMe ? this.SESSION_DURATION.rememberMe : this.SESSION_DURATION.default;
      const expiresAt = new Date(Date.now() + duration).toISOString();
      
      // Clean up old sessions for this user (keep only 5 most recent)
      await this.cleanupUserSessions(userId);
      
      // Insert new session
      const stmt = this.db.prepare(`
        INSERT INTO user_sessions (user_id, session_token, expires_at)
        VALUES (?, ?, ?)
      `);
      
      stmt.run(userId, sessionToken, expiresAt);
      
      return sessionToken;
    } catch (error) {
      console.error('Error creating session:', error);
      throw new Error('Failed to create session');
    }
  }

  /**
   * Validate a session token and return the associated user
   */
  async validateSession(sessionToken: string): Promise<User | null> {
    try {
      // Get session from database
      const stmt = this.db.prepare(`
        SELECT s.*, u.id as user_id, u.email, u.username, u.role, u.created_at, u.is_active
        FROM user_sessions s
        JOIN users u ON s.user_id = u.id
        WHERE s.session_token = ? AND s.expires_at > datetime('now') AND u.is_active = 1
      `);
      
      const result = stmt.get(sessionToken) as any;
      
      if (!result) {
        return null;
      }
      
      // Update last access time (optional - for session tracking)
      this.updateSessionAccess(sessionToken);
      
      return {
        id: result.user_id.toString(),
        email: result.email,
        username: result.username,
        role: result.role,
        createdAt: result.created_at
      };
    } catch (error) {
      console.error('Error validating session:', error);
      return null;
    }
  }

  /**
   * Invalidate a session (logout)
   */
  async invalidateSession(sessionToken: string): Promise<boolean> {
    try {
      const stmt = this.db.prepare(`
        DELETE FROM user_sessions WHERE session_token = ?
      `);
      
      const result = stmt.run(sessionToken);
      return result.changes > 0;
    } catch (error) {
      console.error('Error invalidating session:', error);
      return false;
    }
  }

  /**
   * Invalidate all sessions for a user
   */
  async invalidateAllUserSessions(userId: number): Promise<boolean> {
    try {
      const stmt = this.db.prepare(`
        DELETE FROM user_sessions WHERE user_id = ?
      `);
      
      const result = stmt.run(userId);
      return result.changes >= 0; // Could be 0 if no sessions exist
    } catch (error) {
      console.error('Error invalidating user sessions:', error);
      return false;
    }
  }

  /**
   * Get all active sessions for a user
   */
  getUserSessions(userId: number): Session[] {
    try {
      const stmt = this.db.prepare(`
        SELECT id, user_id, session_token, expires_at, created_at
        FROM user_sessions 
        WHERE user_id = ? AND expires_at > datetime('now')
        ORDER BY created_at DESC
      `);
      
      return stmt.all(userId) as Session[];
    } catch (error) {
      console.error('Error getting user sessions:', error);
      return [];
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const stmt = this.db.prepare(`
        DELETE FROM user_sessions WHERE expires_at <= datetime('now')
      `);
      
      const result = stmt.run();
      return result.changes;
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error);
      return 0;
    }
  }

  /**
   * Generate a secure session token
   */
  private generateSessionToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Clean up old sessions for a user (keep only 5 most recent)
   */
  private async cleanupUserSessions(userId: number): Promise<void> {
    try {
      const stmt = this.db.prepare(`
        DELETE FROM user_sessions 
        WHERE user_id = ? AND id NOT IN (
          SELECT id FROM user_sessions 
          WHERE user_id = ? 
          ORDER BY created_at DESC 
          LIMIT 5
        )
      `);
      
      stmt.run(userId, userId);
    } catch (error) {
      console.error('Error cleaning up user sessions:', error);
    }
  }

  /**
   * Update session last access time (optional feature)
   */
  private updateSessionAccess(_sessionToken: string): void {
    try {
      // Note: This would require adding a last_accessed column to the schema
      // For now, we'll skip this to keep the current schema intact
      // In a future update, we could add this column and implement this feature
    } catch (error) {
      console.error('Error updating session access:', error);
    }
  }

  /**
   * Get session statistics
   */
  getSessionStats() {
    try {
      const activeSessionsStmt = this.db.prepare(`
        SELECT COUNT(*) as count FROM user_sessions WHERE expires_at > datetime('now')
      `);
      
      const expiredSessionsStmt = this.db.prepare(`
        SELECT COUNT(*) as count FROM user_sessions WHERE expires_at <= datetime('now')
      `);
      
      const activeResult = activeSessionsStmt.get() as { count: number };
      const expiredResult = expiredSessionsStmt.get() as { count: number };
      
      return {
        active: activeResult.count,
        expired: expiredResult.count,
        total: activeResult.count + expiredResult.count
      };
    } catch (error) {
      console.error('Error getting session stats:', error);
      return {
        active: 0,
        expired: 0,
        total: 0
      };
    }
  }
}
