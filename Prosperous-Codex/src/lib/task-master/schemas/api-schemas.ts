/**
 * API Schemas
 * 
 * Schemas specifically for API request/response validation
 */

import { z } from 'zod';

/**
 * Common API response schema
 */
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * Pagination query schema
 */
export const PaginationQuerySchema = z.object({
  page: z.string().transform(val => parseInt(val)).pipe(z.number().min(1)).default('1'),
  limit: z.string().transform(val => parseInt(val)).pipe(z.number().min(1).max(100)).default('10'),
  sort: z.string().optional(),
  order: z.enum(['asc', 'desc']).default('desc'),
});

/**
 * Fields query schema for partial responses
 */
export const FieldsQuerySchema = z.object({
  fields: z.string().optional(),
});

/**
 * Search query schema
 */
export const SearchQuerySchema = z.object({
  q: z.string().min(1).optional(),
  status: z.enum(['todo', 'in_progress', 'completed']).optional(),
  priority: z.enum(['low', 'medium', 'high']).optional(),
  assignedTo: z.string().transform(val => parseInt(val)).pipe(z.number()).optional(),
  createdBy: z.string().transform(val => parseInt(val)).pipe(z.number()).optional(),
  dueDate: z.string().datetime().optional(),
});

/**
 * File upload schema
 */
export const FileUploadSchema = z.object({
  file: z.instanceof(File),
  description: z.string().optional(),
});

/**
 * Bulk operation schema
 */
export const BulkOperationSchema = z.object({
  ids: z.array(z.number().positive()),
  operation: z.enum(['delete', 'update', 'archive']),
  data: z.record(z.any()).optional(),
});

/**
 * API error response schema
 */
export const ApiErrorSchema = z.object({
  error: z.string(),
  code: z.string().optional(),
  details: z.array(z.object({
    field: z.string(),
    message: z.string(),
    code: z.string().optional(),
  })).optional(),
  timestamp: z.string().datetime().optional(),
});

/**
 * Health check response schema
 */
export const HealthCheckSchema = z.object({
  status: z.enum(['healthy', 'degraded', 'unhealthy']),
  timestamp: z.string().datetime(),
  services: z.record(z.object({
    status: z.enum(['up', 'down']),
    responseTime: z.number().optional(),
    error: z.string().optional(),
  })).optional(),
});

/**
 * API versioning schema
 */
export const ApiVersionSchema = z.object({
  version: z.string().regex(/^v\d+(\.\d+)?$/),
  deprecated: z.boolean().default(false),
  sunset: z.string().datetime().optional(),
});

export type ApiResponse<T = any> = z.infer<typeof ApiResponseSchema> & { data?: T };
export type PaginationQuery = z.infer<typeof PaginationQuerySchema>;
export type FieldsQuery = z.infer<typeof FieldsQuerySchema>;
export type SearchQuery = z.infer<typeof SearchQuerySchema>;
export type FileUpload = z.infer<typeof FileUploadSchema>;
export type BulkOperation = z.infer<typeof BulkOperationSchema>;
export type ApiError = z.infer<typeof ApiErrorSchema>;
export type HealthCheck = z.infer<typeof HealthCheckSchema>;
export type ApiVersion = z.infer<typeof ApiVersionSchema>;
