"use client";

import React from 'react';
import { useUnit } from '@/lib/context/unit-context';

interface UnitToggleProps {
  className?: string;
}

export function UnitToggle({ className = "" }: UnitToggleProps) {
  const { unit, setUnit } = useUnit();

  const baseStyle = "px-3 py-1.5 text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900";
  const activeStyle = "bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-500 dark:hover:bg-blue-600";
  const inactiveStyle = "bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-neutral-700 dark:hover:bg-neutral-600 dark:text-neutral-300";

  return (
    <div className={`flex rounded-md shadow-sm ${className}`} role="group" data-glow>
      <button
        type="button"
        onClick={() => setUnit('mm')}
        className={`${baseStyle} rounded-l-md border border-r-0 border-gray-300 dark:border-neutral-600 ${unit === 'mm' ? activeStyle : inactiveStyle}`}
      >
        mm
      </button>
      <button
        type="button"
        onClick={() => setUnit('in')}
        className={`${baseStyle} rounded-r-md border border-gray-300 dark:border-neutral-600 ${unit === 'in' ? activeStyle : inactiveStyle}`}
      >
        in
      </button>
    </div>
  );
}
