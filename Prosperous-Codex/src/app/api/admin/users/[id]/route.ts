import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/database/user-service';
import { SessionService } from '@/lib/database/session-service';
import { requireRole } from '@/lib/auth/middleware';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require moderator role
    const authResult = await requireRole(request, 'moderator');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const userId = parseInt(params.id);
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      );
    }
    
    const userService = new UserService();
    const user = userService.getUserById(userId);
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Get user sessions
    const sessionService = new SessionService();
    const sessions = sessionService.getUserSessions(userId);
    
    return NextResponse.json({ 
      user,
      sessions: sessions.map(session => ({
        id: session.id,
        createdAt: session.createdAt,
        expiresAt: session.expiresAt
      }))
    });
    
  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require admin role for user updates
    const authResult = await requireRole(request, 'admin');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const userId = parseInt(params.id);
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      );
    }
    
    const { username, role, is_active } = await request.json();
    
    // Validate role if provided
    if (role && !['user', 'moderator', 'admin'].includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role. Must be user, moderator, or admin' },
        { status: 400 }
      );
    }
    
    // Validate username if provided
    if (username !== undefined) {
      if (typeof username !== 'string' || username.length < 2) {
        return NextResponse.json(
          { error: 'Username must be at least 2 characters long' },
          { status: 400 }
        );
      }
    }
    
    // Update user
    const userService = new UserService();
    const success = userService.updateUser(userId, {
      username: username || undefined,
      role: role || undefined,
      is_active: is_active !== undefined ? is_active : undefined
    });
    
    if (success) {
      const updatedUser = userService.getUserById(userId);
      return NextResponse.json({ 
        message: 'User updated successfully',
        user: updatedUser
      });
    } else {
      return NextResponse.json(
        { error: 'User not found or update failed' },
        { status: 404 }
      );
    }
    
  } catch (error) {
    console.error('Update user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require admin role for user deletion
    const authResult = await requireRole(request, 'admin');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const userId = parseInt(params.id);
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      );
    }
    
    // Prevent self-deletion
    if (authResult.user.id === userId.toString()) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }
    
    const userService = new UserService();
    const sessionService = new SessionService();
    
    // Invalidate all user sessions first
    await sessionService.invalidateAllUserSessions(userId);
    
    // Soft delete user
    const success = userService.deleteUser(userId);
    
    if (success) {
      return NextResponse.json({ message: 'User deleted successfully' });
    } else {
      return NextResponse.json(
        { error: 'User not found or deletion failed' },
        { status: 404 }
      );
    }
    
  } catch (error) {
    console.error('Delete user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
