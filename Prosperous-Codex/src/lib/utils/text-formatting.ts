/**
 * Text formatting utilities for preserving line breaks and basic formatting
 * in task descriptions and other user-generated content.
 */

import DOMPurify from 'isomorphic-dompurify';



/**
 * Convert plain text with line breaks to formatted HTML
 * Preserves line breaks, paragraphs, and basic list formatting
 */
export function formatTextForDisplay(text: string): string {
  if (!text || typeof text !== 'string') {
    return '';
  }

  // Start with the original text
  const formatted = text.trim();

  // Preserve line breaks by temporarily replacing them with placeholders
  // This prevents DOMPurify from removing them during sanitization
  const withPlaceholders = formatted
    .replace(/\r\n/g, '___CRLF___')
    .replace(/\n/g, '___LF___')
    .replace(/\r/g, '___CR___');

  // Sanitize the HTML to prevent XSS attacks with expanded tag allowlist
  const sanitized = DOMPurify.sanitize(withPlaceholders, {
    ALLOWED_TAGS: ['br', 'p', 'ul', 'ol', 'li', 'strong', 'b', 'em', 'i'],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true
  });

  // Restore line breaks and convert them to <br> tags
  const withLineBreaks = sanitized
    .replace(/___CRLF___/g, '<br>')
    .replace(/___LF___/g, '<br>')
    .replace(/___CR___/g, '<br>');

  return withLineBreaks;
}

/**
 * Convert formatted HTML back to plain text for editing
 * This reverses the formatting process for textarea inputs
 */
export function formatTextForEditing(html: string): string {
  if (!html || typeof html !== 'string') {
    return '';
  }

  let text = html;

  // Convert <br> tags back to line breaks
  text = text.replace(/<br\s*\/?>/gi, '\n');
  
  // Convert paragraph breaks back to double line breaks
  text = text.replace(/<\/p>\s*<p>/gi, '\n\n');
  
  // Remove paragraph tags
  text = text.replace(/<\/?p>/gi, '');
  
  // Convert list items back to bullet points
  text = text.replace(/<li>(.*?)<\/li>/gi, '• $1\n');
  
  // Remove list container tags
  text = text.replace(/<\/?[uo]l>/gi, '');
  
  // Remove any remaining HTML tags
  text = text.replace(/<[^>]*>/g, '');
  
  // Clean up extra whitespace
  text = text.replace(/\n\s*\n\s*\n/g, '\n\n'); // Max 2 consecutive line breaks
  text = text.trim();

  return text;
}

/**
 * Check if text contains formatting that should be preserved
 */
export function hasFormatting(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }

  // Check for line breaks (including different line break types)
  if (text.includes('\n') || text.includes('\r')) {
    return true;
  }

  // Check for bullet points
  if (/^[\s]*[•\-\*][\s]+/m.test(text)) {
    return true;
  }

  // Check for numbered lists
  if (/^[\s]*\d+\.[\s]+/m.test(text)) {
    return true;
  }

  // Check for basic HTML tags that might already be in the text
  if (/<(br|p|ul|ol|li|strong|b|em|i)\b[^>]*>/i.test(text)) {
    return true;
  }

  return false;
}

/**
 * Truncate formatted text for display in cards while preserving formatting
 */
export function truncateFormattedText(text: string, maxLength: number = 100): string {
  if (!text || text.length <= maxLength) {
    return text;
  }

  // Find a good breaking point (end of sentence, line break, etc.)
  const truncated = text.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');
  const lastLineBreak = truncated.lastIndexOf('\n');
  
  const breakPoint = Math.max(lastSpace, lastLineBreak);
  
  if (breakPoint > maxLength * 0.7) {
    return text.substring(0, breakPoint) + '...';
  }
  
  return truncated + '...';
}
