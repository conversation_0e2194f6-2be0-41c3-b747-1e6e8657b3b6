#!/usr/bin/env node

/**
 * Task Master Optimization Test Runner
 * Executes comprehensive tests for all optimization features
 */

const {
  TestRunner,
  DatabaseOptimizationTests,
  PerformanceOptimizationTests,
  TEST_CONFIG
} = require('./task-master-optimization.test.js');

class SecurityAndAuthorizationTests {
  constructor(testRunner) {
    this.testRunner = testRunner;
  }

  async testAuthorizationIntegration() {
    console.log('\n🔒 Testing Authorization Integration...');
    
    // Test creating a project (should succeed with valid auth)
    const { response, responseTime } = await this.testRunner.makeRequest(
      '/api/task-master/projects',
      {
        method: 'POST',
        body: JSON.stringify({
          title: 'Authorization Test Project',
          description: 'Testing authorization integration',
          status: 'todo',
          priority: 'medium'
        })
      }
    );

    const passed = response.ok;
    
    this.testRunner.recordResult('Authorization - Project Creation', passed, {
      responseTime,
      error: !passed ? `HTTP ${response.status}` : null
    });
  }

  async testInputSanitization() {
    console.log('\n🧹 Testing Input Sanitization...');
    
    // Test creating a project with potentially malicious input
    const maliciousData = {
      title: '<script>alert("xss")</script>Test Project',
      description: '<img src="x" onerror="alert(1)">Description with XSS attempt',
      status: 'todo',
      priority: 'medium'
    };

    const { response, responseTime, data } = await this.testRunner.makeRequest(
      '/api/task-master/projects',
      {
        method: 'POST',
        body: JSON.stringify(maliciousData)
      }
    );

    let passed = response.ok;
    
    if (passed && data?.project) {
      // Verify that malicious content was sanitized
      const sanitizedTitle = data.project.title;
      const sanitizedDescription = data.project.description;
      
      passed = !sanitizedTitle.includes('<script>') && 
               !sanitizedDescription.includes('<img') &&
               !sanitizedDescription.includes('onerror');
    }

    this.testRunner.recordResult('Input Sanitization - XSS Prevention', passed, {
      responseTime,
      error: !passed ? 'Malicious input not properly sanitized' : null,
      details: data?.project ? `Title: "${data.project.title}"` : null
    });
  }

  async testFieldValidation() {
    console.log('\n✅ Testing Field Validation...');
    
    // Test invalid field names in partial response
    const { response, responseTime, data } = await this.testRunner.makeRequest(
      '/api/task-master/projects?fields=invalidField,anotherInvalid'
    );

    const passed = response.status === 400 && 
                   data?.error?.includes('Invalid fields') &&
                   Array.isArray(data?.invalidFields);

    this.testRunner.recordResult('Field Validation - Invalid Fields', passed, {
      responseTime,
      error: !passed ? 'Invalid fields not properly rejected' : null,
      details: data?.invalidFields ? `Invalid: ${data.invalidFields.join(', ')}` : null
    });
  }
}

class ErrorHandlingTests {
  constructor(testRunner) {
    this.testRunner = testRunner;
  }

  async testStandardizedErrors() {
    console.log('\n🚨 Testing Standardized Error Handling...');
    
    // Test accessing non-existent project
    const { response, responseTime, data } = await this.testRunner.makeRequest(
      '/api/task-master/projects/99999'
    );

    const passed = response.status === 404 && 
                   data?.error &&
                   data?.timestamp;

    this.testRunner.recordResult('Error Handling - Not Found', passed, {
      responseTime,
      error: !passed ? 'Error response format not standardized' : null,
      details: data?.error || null
    });
  }

  async testValidationErrors() {
    console.log('\n📝 Testing Validation Error Responses...');
    
    // Test creating project with invalid data
    const { response, responseTime, data } = await this.testRunner.makeRequest(
      '/api/task-master/projects',
      {
        method: 'POST',
        body: JSON.stringify({
          title: '', // Empty title should fail
          status: 'invalid_status', // Invalid status
          priority: 'invalid_priority' // Invalid priority
        })
      }
    );

    const passed = response.status === 400 && 
                   data?.error;

    this.testRunner.recordResult('Error Handling - Validation Errors', passed, {
      responseTime,
      error: !passed ? 'Validation errors not properly handled' : null,
      details: data?.error || null
    });
  }
}

class IntegrationTests {
  constructor(testRunner) {
    this.testRunner = testRunner;
  }

  async testCompleteWorkflow() {
    console.log('\n🔄 Testing Complete Project Workflow...');
    
    let projectId = null;
    let taskId = null;
    let passed = true;
    let error = null;

    try {
      // 1. Create project
      const createResponse = await this.testRunner.makeRequest(
        '/api/task-master/projects',
        {
          method: 'POST',
          body: JSON.stringify({
            title: 'Integration Test Project',
            description: 'Testing complete workflow',
            status: 'todo',
            priority: 'high',
            tags: ['integration', 'test']
          })
        }
      );

      if (!createResponse.response.ok) {
        throw new Error('Project creation failed');
      }

      projectId = createResponse.data.project.id;

      // 2. Create task
      const taskResponse = await this.testRunner.makeRequest(
        '/api/task-master/tasks',
        {
          method: 'POST',
          body: JSON.stringify({
            projectId,
            title: 'Integration Test Task',
            description: 'Testing task creation',
            status: 'todo',
            priority: 'medium'
          })
        }
      );

      if (!taskResponse.response.ok) {
        throw new Error('Task creation failed');
      }

      taskId = taskResponse.data.task.id;

      // 3. Update task status
      const updateResponse = await this.testRunner.makeRequest(
        `/api/task-master/tasks/${taskId}`,
        {
          method: 'PUT',
          body: JSON.stringify({
            status: 'completed'
          })
        }
      );

      if (!updateResponse.response.ok) {
        throw new Error('Task update failed');
      }

      // 4. Verify project progress updated
      const projectResponse = await this.testRunner.makeRequest(
        `/api/task-master/projects/${projectId}?includeTasks=true`
      );

      if (!projectResponse.response.ok || projectResponse.data.project.progress === 0) {
        throw new Error('Project progress not updated');
      }

    } catch (err) {
      passed = false;
      error = err.message;
    }

    this.testRunner.recordResult('Integration - Complete Workflow', passed, {
      error,
      details: projectId ? `Project ID: ${projectId}, Task ID: ${taskId}` : null
    });
  }
}

// Main test execution
async function runAllTests() {
  console.log('🧪 TASK MASTER OPTIMIZATION TEST SUITE');
  console.log('=====================================');
  console.log(`Testing against: ${TEST_CONFIG.baseUrl}`);
  console.log(`Test user: ${TEST_CONFIG.testUser.email}`);
  console.log('');

  const testRunner = new TestRunner();

  try {
    // Authenticate
    await testRunner.authenticate();

    // Initialize test suites
    const dbTests = new DatabaseOptimizationTests(testRunner);
    const perfTests = new PerformanceOptimizationTests(testRunner);
    const securityTests = new SecurityAndAuthorizationTests(testRunner);
    const errorTests = new ErrorHandlingTests(testRunner);
    const integrationTests = new IntegrationTests(testRunner);

    // Run all test suites
    console.log('\n🏃‍♂️ Running test suites...\n');

    // Database optimization tests
    await dbTests.testDatabaseIndexes();
    await dbTests.testTransactionIntegrity();
    await dbTests.testConcurrentOperations();

    // Performance optimization tests
    await perfTests.testLazyLoading();
    await perfTests.testPagination();
    await perfTests.testPartialResponse();

    // Security and authorization tests
    await securityTests.testAuthorizationIntegration();
    await securityTests.testInputSanitization();
    await securityTests.testFieldValidation();

    // Error handling tests
    await errorTests.testStandardizedErrors();
    await errorTests.testValidationErrors();

    // Integration tests
    await integrationTests.testCompleteWorkflow();

    // Generate final report
    const report = testRunner.generateReport();
    
    // Save detailed results
    const fs = require('fs');
    const path = require('path');
    
    const reportPath = path.join(__dirname, 'optimization-test-results.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 Detailed results saved to: ${reportPath}`);
    
    // Exit with appropriate code
    process.exit(report.failedTests > 0 ? 1 : 0);

  } catch (error) {
    console.error('\n❌ Test execution failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  SecurityAndAuthorizationTests,
  ErrorHandlingTests,
  IntegrationTests
};
