/**
 * Tests for text formatting utilities
 * Verifies that line breaks and formatting are preserved correctly
 */

import { formatTextForDisplay, hasFormatting, formatTextForEditing, truncateFormattedText } from '@/lib/utils/text-formatting';

describe('Text Formatting Utilities', () => {
  describe('formatTextForDisplay', () => {
    it('should preserve simple line breaks', () => {
      const input = 'Line 1\nLine 2\nLine 3';
      const result = formatTextForDisplay(input);
      expect(result).toBe('Line 1<br>Line 2<br>Line 3');
    });

    it('should handle different line break types', () => {
      const input = 'Line 1\r\nLine 2\rLine 3\nLine 4';
      const result = formatTextForDisplay(input);
      expect(result).toBe('Line 1<br>Line 2<br>Line 3<br>Line 4');
    });

    it('should preserve multiple consecutive line breaks', () => {
      const input = 'Paragraph 1\n\nParagraph 2\n\n\nParagraph 3';
      const result = formatTextForDisplay(input);
      expect(result).toBe('Paragraph 1<br><br>Paragraph 2<br><br><br>Paragraph 3');
    });

    it('should handle complex multi-line text with bullet points', () => {
      const input = `Project Overview:
• Phase 1: Planning
• Phase 2: Development

Important Notes:
- Client meeting next week
- Budget pending approval`;
      
      const result = formatTextForDisplay(input);
      expect(result).toContain('<br>');
      expect(result).toContain('Project Overview:<br>• Phase 1: Planning<br>• Phase 2: Development<br><br>Important Notes:<br>- Client meeting next week<br>- Budget pending approval');
    });

    it('should handle empty or null input', () => {
      expect(formatTextForDisplay('')).toBe('');
      expect(formatTextForDisplay(null as any)).toBe('');
      expect(formatTextForDisplay(undefined as any)).toBe('');
    });

    it('should sanitize potentially dangerous HTML while preserving line breaks', () => {
      const input = 'Line 1\n<script>alert("xss")</script>\nLine 2';
      const result = formatTextForDisplay(input);
      expect(result).toBe('Line 1<br><br>Line 2');
      expect(result).not.toContain('<script>');
      expect(result).not.toContain('alert');
    });

    it('should preserve allowed HTML tags', () => {
      const input = 'Line 1\n<strong>Bold text</strong>\nLine 2';
      const result = formatTextForDisplay(input);
      expect(result).toBe('Line 1<br><strong>Bold text</strong><br>Line 2');
    });
  });

  describe('hasFormatting', () => {
    it('should detect line breaks', () => {
      expect(hasFormatting('Line 1\nLine 2')).toBe(true);
      expect(hasFormatting('Line 1\r\nLine 2')).toBe(true);
      expect(hasFormatting('Line 1\rLine 2')).toBe(true);
    });

    it('should detect bullet points', () => {
      expect(hasFormatting('• Item 1')).toBe(true);
      expect(hasFormatting('- Item 1')).toBe(true);
      expect(hasFormatting('* Item 1')).toBe(true);
    });

    it('should detect numbered lists', () => {
      expect(hasFormatting('1. Item 1')).toBe(true);
      expect(hasFormatting('10. Item 10')).toBe(true);
    });

    it('should detect HTML tags', () => {
      expect(hasFormatting('<br>')).toBe(true);
      expect(hasFormatting('<p>Paragraph</p>')).toBe(true);
      expect(hasFormatting('<strong>Bold</strong>')).toBe(true);
    });

    it('should return false for plain text', () => {
      expect(hasFormatting('Just plain text')).toBe(false);
      expect(hasFormatting('Simple sentence without formatting.')).toBe(false);
    });

    it('should handle empty or null input', () => {
      expect(hasFormatting('')).toBe(false);
      expect(hasFormatting(null as any)).toBe(false);
      expect(hasFormatting(undefined as any)).toBe(false);
    });
  });

  describe('formatTextForEditing', () => {
    it('should convert <br> tags back to line breaks', () => {
      const input = 'Line 1<br>Line 2<br>Line 3';
      const result = formatTextForEditing(input);
      expect(result).toBe('Line 1\nLine 2\nLine 3');
    });

    it('should handle self-closing br tags', () => {
      const input = 'Line 1<br/>Line 2<br />Line 3';
      const result = formatTextForEditing(input);
      expect(result).toBe('Line 1\nLine 2\nLine 3');
    });

    it('should convert paragraphs to double line breaks', () => {
      const input = '<p>Paragraph 1</p><p>Paragraph 2</p>';
      const result = formatTextForEditing(input);
      expect(result).toBe('Paragraph 1\n\nParagraph 2');
    });

    it('should handle empty or null input', () => {
      expect(formatTextForEditing('')).toBe('');
      expect(formatTextForEditing(null as any)).toBe('');
      expect(formatTextForEditing(undefined as any)).toBe('');
    });
  });

  describe('truncateFormattedText', () => {
    it('should truncate long text at word boundaries', () => {
      const input = 'This is a very long text that should be truncated at some point to prevent it from being too long';
      const result = truncateFormattedText(input, 50);
      expect(result.length).toBeLessThanOrEqual(53); // 50 + '...'
      expect(result).toContain('...');
    });

    it('should preserve line breaks in truncated text', () => {
      const input = 'Line 1\nLine 2\nThis is a very long line that should be truncated';
      const result = truncateFormattedText(input, 30);
      expect(result).toContain('\n');
      expect(result).toContain('...');
    });

    it('should not truncate short text', () => {
      const input = 'Short text';
      const result = truncateFormattedText(input, 50);
      expect(result).toBe(input);
    });
  });

  describe('Integration test - Real world scenario', () => {
    it('should handle the exact scenario described in the issue', () => {
      const userInput = `Project Overview:
• Phase 1: Planning
• Phase 2: Development

Important Notes:
- Client meeting next week
- Budget pending approval`;

      // Test that hasFormatting detects the formatting
      expect(hasFormatting(userInput)).toBe(true);

      // Test that formatTextForDisplay preserves line breaks
      const formatted = formatTextForDisplay(userInput);
      expect(formatted).toContain('<br>');
      
      // Verify the structure is preserved
      const lines = formatted.split('<br>');
      expect(lines[0]).toBe('Project Overview:');
      expect(lines[1]).toBe('• Phase 1: Planning');
      expect(lines[2]).toBe('• Phase 2: Development');
      expect(lines[3]).toBe('');
      expect(lines[4]).toBe('Important Notes:');
      expect(lines[5]).toBe('- Client meeting next week');
      expect(lines[6]).toBe('- Budget pending approval');

      // Test round-trip conversion
      const backToText = formatTextForEditing(formatted);
      expect(backToText).toBe(userInput);
    });
  });
});
