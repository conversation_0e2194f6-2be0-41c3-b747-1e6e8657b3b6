// Final Metrics and Cost Calculation Functions
// Handles roll optimization, cost calculations, and final result processing

import { 
  PaperCandidate, 
  JobInputs,
  InnerTextJobInputs,
  CoverJobInputs,
  EndpaperJobInputs,
  ERROR_KEYS 
} from '../types/calculation';
import { ceil } from './core';

// Helper function to get required alignment mode from job inputs
function getRequiredAlignmentMode(
  tabId: 'innerText' | 'cover' | 'endpapers',
  jobInputs: JobInputs | InnerTextJobInputs | CoverJobInputs | EndpaperJobInputs
): 'Aligned' | 'Misaligned' {
  return jobInputs.alignmentMode;
}

// Roll dimension finalization and optimization
export function finalizeRollDimensions(
  candidate: PaperCandidate,
  gripper: number,
  colorBar: number,
  lip: number
): PaperCandidate {
  // Only trim if it's a roll AND one of its dimensions was maximized for the initial fitting.
  if (candidate.source !== 'Roll' || !candidate.rollDimensionOptimizedForFitting || candidate.error) {
    return candidate;
  }

  let finalPressH = parseFloat(String(candidate.pressH)) || 0;
  let finalPressW = parseFloat(String(candidate.pressW)) || 0;
  const occupiedH = parseFloat(String(candidate.occupiedHeight)) || 0;
  const occupiedW = parseFloat(String(candidate.occupiedWidth)) || 0;
  const totalVerticalMargins = gripper + colorBar;
  const totalHorizontalMargins = lip;

  if (occupiedH <= 0 || occupiedW <= 0) {
    return candidate;
  }

  // Determine which press dimension was the one initially maximized (the cut-off length)
  if (candidate.inputNoteKey === "resNoteHeightInferred") {
    // Original cut-off length was sheetH_for_initial_fit, which was maximized.
    // This dimension is now either pressH (if not rotated) or pressW (if rotated).
    if (candidate.paperWasRotated) { // Maximized initial H is now pressW
      finalPressW = occupiedW + totalHorizontalMargins;
    } else { // Maximized initial H is still pressH
      finalPressH = occupiedH + totalVerticalMargins;
    }
  } else if (candidate.inputNoteKey === "resNoteWidthInferred") {
    // Original cut-off length was sheetW_for_initial_fit, which was maximized.
    // This dimension is now either pressW (if not rotated) or pressH (if rotated).
    if (candidate.paperWasRotated) { // Maximized initial W is now pressH
      finalPressH = occupiedH + totalVerticalMargins;
    } else { // Maximized initial W is still pressW
      finalPressW = occupiedW + totalHorizontalMargins;
    }
  } else {
    // Should not reach here if rollDimensionOptimizedForFitting is true
    return candidate;
  }

  const result = { ...candidate };
  result.pressH = parseFloat(finalPressH.toFixed(1));
  result.pressW = parseFloat(finalPressW.toFixed(1));
  result.usableH = Math.max(0, parseFloat((result.pressH - totalVerticalMargins).toFixed(1)));
  result.usableW = Math.max(0, parseFloat((result.pressW - totalHorizontalMargins).toFixed(1)));
  result.rollOptimizationNoteKey = "rollDimensionsOptimized";

  return result;
}

// Calculate final metrics including costs and efficiency
export function calculateFinalMetrics_ComponentSpecific(
  tabId: 'innerText' | 'cover' | 'endpapers',
  candidate: PaperCandidate,
  jobInputs: JobInputs | InnerTextJobInputs | CoverJobInputs | EndpaperJobInputs,
  benchmarkValue: number
): PaperCandidate {
  const result = { ...candidate };
  
  try {
    if ((result.maxItemsPerSide || 0) <= 0) {
      throw new Error("No items fit per sheet.");
    }

    // Calculate pages/items per sheet output based on component type
    if (tabId === 'innerText') {
      // For inner text, maxItemsPerSide is effectively pages per signature side.
      // A signature (one item) produces maxItemsPerSide * 2 pages when folded.
      result.pagesPerSheetOutput = (result.maxItemsPerSide || 0) * 2;
      if ((result.pagesPerSheetOutput || 0) <= 0) {
        throw new Error("Invalid pages per sheet output for Inner Text.");
      }
      
      const innerInputs = jobInputs as InnerTextJobInputs;
      result.totalSheetsNeeded = ceil(
        ceil(innerInputs.totalPages / (result.pagesPerSheetOutput || 1)) * 
        innerInputs.quantity * 
        (1 + innerInputs.spoilagePct)
      );
    } else if (tabId === 'cover') {
      // For covers, maxItemsPerSide is number of flat cover spreads N-up on one side of sheet.
      const coversPerFullSheet = result.maxItemsPerSide || 0;
      if (coversPerFullSheet <= 0) {
        throw new Error("Invalid covers per sheet output.");
      }
      
      result.pagesPerSheetOutput = coversPerFullSheet;
      result.totalSheetsNeeded = ceil(
        jobInputs.quantity / coversPerFullSheet * (1 + jobInputs.spoilagePct)
      );
    } else if (tabId === 'endpapers') {
      // maxItemsPerSide is number of endpaper SPREADS that fit on one side of sheet.
      const leavesPerEndpaperSpread = 2;
      const totalLeavesFromOneSheetPass = (result.maxItemsPerSide || 0) * leavesPerEndpaperSpread;
      
      result.pagesPerSheetOutput = totalLeavesFromOneSheetPass;
      
      const endpaperInputs = jobInputs as EndpaperJobInputs;
      const leavesNeededPerBook = endpaperInputs.endpaperType === 'double' ? 8 : 4;
      
      if ((result.pagesPerSheetOutput || 0) <= 0) {
        throw new Error("Invalid endpaper leaves per sheet output.");
      }
      
      const booksWorthOfEndpapersPerSheet = (result.pagesPerSheetOutput || 1) / leavesNeededPerBook;
      if (booksWorthOfEndpapersPerSheet <= 0) {
        throw new Error("Cannot get a full book's endpapers from a sheet.");
      }
      
      result.totalSheetsNeeded = ceil(
        jobInputs.quantity / booksWorthOfEndpapersPerSheet * (1 + jobInputs.spoilagePct)
      );
    }

    // Cost per sheet calculation
    if (result.source === "Pre-Cut") {
      if ((result.costReam || 0) <= 0) {
        throw new Error("Invalid Cost/Ream.");
      }
      result.costPerSheet = (result.costReam || 0) / 500;


    } else { // Roll
      if ((result.gsm || 0) <= 0 || (result.costTonne || 0) < 0) {
        throw new Error("Invalid GSM or Cost/Tonne for Roll.");
      }

      const areaM2 = ((result.pressW || 0) / 1000) * ((result.pressH || 0) / 1000);
      if (areaM2 <= 0) {
        throw new Error("Invalid press area for roll cost calculation.");
      }

      const weightKg = areaM2 * (result.gsm || 0) / 1000;
      result.costPerSheet = (weightKg * (result.costTonne || 0) / 1000);


    }
    
    result.totalCost = (result.totalSheetsNeeded || 0) * (result.costPerSheet || 0);

    // Cost per book/unit and thickness calculations
    if (jobInputs.quantity > 0) {
      result.costPerBook = (result.totalCost || 0) / jobInputs.quantity;


      
      if (tabId === 'innerText' && 
          typeof result.caliperMicrons === 'number' && 
          result.caliperMicrons > 0 && 
          (jobInputs as InnerTextJobInputs).totalPages > 0) {
        result.bookBlockThickness_mm = 
          ((jobInputs as InnerTextJobInputs).totalPages / 2) * (result.caliperMicrons / 1000);
      } else {
        result.bookBlockThickness_mm = NaN;
      }
    } else {
      result.costPerBook = NaN;
      result.bookBlockThickness_mm = NaN;
    }

    // Validate final cost calculations
    if ((result.costPerSheet || 0) <= 0 || 
        isNaN(result.costPerSheet || 0) || 
        (result.totalCost || 0) === Infinity || 
        isNaN(result.totalCost || 0)) {
      throw new Error("Invalid final cost calculation.");
    }

    // Calculate waste and utilization based on final usable area (after roll optimization)
    // This ensures the utilization reflects the actual efficiency against the final sheet size
    const finalUsableArea = (result.usableW || 0) * (result.usableH || 0);
    const occupiedArea = (result.occupiedWidth || 0) * (result.occupiedHeight || 0);

    if (finalUsableArea > 0 && occupiedArea <= finalUsableArea) {
      result.wastePercent = Math.max(0, (finalUsableArea - occupiedArea)) / finalUsableArea;
      result.utilisationRate = 1.0 - result.wastePercent;
    } else {
      result.wastePercent = 1.0;
      result.utilisationRate = 0.0;
    }
    // Optimal classification: must achieve benchmark value AND have correct grain alignment
    // This matches the reference implementation logic
    const hasCorrectAlignment = result.grainAlignmentStatus === getRequiredAlignmentMode(tabId, jobInputs);
    result.isOptimalCandidate = (
      (result.maxItemsPerSide || 0) === benchmarkValue &&
      !result.error &&
      hasCorrectAlignment
    );

  } catch (error) {
    console.error(`Final Metrics error for ${tabId}, row ${result.id}:`, error);
    result.error = true;
    
    if ((result.costPerSheet || 0) <= 0) {
      result.errorMessageKey = ERROR_KEYS.COST_SHEET_NEGATIVE;
    } else if (error instanceof Error && error.message.includes("Roll")) {
      result.errorMessageKey = ERROR_KEYS.INVALID_ROLL_COST;
    } else if (error instanceof Error && error.message.includes("Pre-Cut")) {
      result.errorMessageKey = ERROR_KEYS.MISSING_COST_REAM;
    } else {
      result.errorMessageKey = ERROR_KEYS.COST_CALC_ERROR;
    }
    
    result.totalCost = Infinity;
    result.costPerSheet = NaN;
    result.costPerBook = NaN;
    result.bookBlockThickness_mm = NaN;
    result.wastePercent = 1.1;
    result.utilisationRate = -0.1;
  }

  return result;
}

// Sort and categorize results
export function sortAndCategorizeResults(results: PaperCandidate[]): {
  optimal: PaperCandidate[];
  good: PaperCandidate[];
  errors: PaperCandidate[];
} {
  const optimal: PaperCandidate[] = [];
  const good: PaperCandidate[] = [];
  const errors: PaperCandidate[] = [];

  results.forEach(result => {
    if (result.error) {
      errors.push(result);
    } else if (result.isOptimalCandidate) {
      optimal.push(result);
    } else {
      good.push(result);
    }
  });

  // Sort each category
  // CRITICAL FIX C: Use wastePercent and totalCost as primary sorting criteria
  const sortByEfficiency = (a: PaperCandidate, b: PaperCandidate) => {
    // Primary: waste percent (ascending - less waste is better)
    if ((a.wastePercent || 1) !== (b.wastePercent || 1)) {
      return (a.wastePercent || 1) - (b.wastePercent || 1);
    }
    // Secondary: total cost (ascending - lower cost is better)
    if ((a.totalCost || Infinity) !== (b.totalCost || Infinity)) {
      return (a.totalCost || Infinity) - (b.totalCost || Infinity);
    }
    // Tertiary: items per side (descending - more items is better as tie-breaker)
    return (b.maxItemsPerSide || 0) - (a.maxItemsPerSide || 0);
  };

  optimal.sort(sortByEfficiency);
  good.sort(sortByEfficiency);

  return { optimal, good, errors };
}
