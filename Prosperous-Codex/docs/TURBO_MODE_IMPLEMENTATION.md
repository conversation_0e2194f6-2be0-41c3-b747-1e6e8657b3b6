# Turbo Mode Implementation

## Overview

Turbo Mode is a feature that combines AI enhancement and parsing into a single operation for improved efficiency. It creates a real project card first, then populates it with AI-enhanced content, following the "Real Card + Update" approach.

## Architecture

### Backend Components

#### 1. AI Turbo Service (`src/lib/services/ai-turbo-service.ts`)
- **Purpose**: Combines enhancement and parsing in a single LLM call
- **Key Features**:
  - Uses reasoning models with thinking_budget parameter
  - Supports both OpenRouter and OpenAI APIs
  - Implements fallback model strategy
  - Extracts thinking content for display
  - Validates and structures response data

#### 2. API Route (`src/app/api/task-master/ai-turbo/route.ts`)
- **Endpoint**: `POST /api/task-master/ai-turbo`
- **Authentication**: Required
- **Input Validation**: Length limits, content validation
- **Field Mapping**: Automatic camelCase ↔ snake_case conversion
- **Error Handling**: Comprehensive error classification

#### 3. Type Definitions (`src/lib/types/ai-turbo.ts`)
- **Z<PERSON>**: Input validation and type safety
- **TypeScript Interfaces**: Frontend/backend compatibility
- **Type Guards**: Runtime validation helpers

#### 4. Field Mappings (`src/lib/task-master/field-mapping.ts`)
- **New Mappings**:
  - `modelUsed: 'model_used'`
  - `tokensUsed: 'tokens_used'`
  - `reasoningContent: 'reasoning_content'`

### Frontend Components

#### 1. Main Page Integration (`src/app/[locale]/(protected)/task-master/page.tsx`)
- **Turbo Button**: Positioned next to Enhance button
- **State Management**: Processing states, progress tracking
- **Error Handling**: Retry logic, user-friendly messages
- **Input Validation**: Length limits, content sanitization
- **Memory Management**: AbortController, cleanup on unmount

#### 2. Card Loading Overlay (`src/components/task-master/task-master-card.tsx`)
- **Visual Feedback**: Blur effect with spinner
- **Progress Display**: Dynamic progress text
- **Z-index Management**: Proper layering
- **Accessibility**: Screen reader support

#### 3. Column Integration (`src/components/task-master/task-master-column.tsx`)
- **Prop Drilling**: Passes loading states to cards
- **Performance**: Minimal re-renders

## API Specification

### Request Format
```typescript
POST /api/task-master/ai-turbo
Content-Type: application/json

{
  "inputText": "string (10-65536 chars)",
  "context": "task_description",
  "tone": "professional",
  "maxTokens": 65536
}
```

### Response Format
```typescript
{
  "success": true,
  "data": {
    "enhancedText": "string",
    "originalText": "string",
    "enhancementApplied": true,
    "modelUsed": "string",
    "tokensUsed": 1234,
    "reasoningContent": "string",
    "title": "string",
    "eventLog": "string",
    "description": "string",
    "tasks": [
      {
        "title": "string",
        "description": "string",
        "subtasks": [
          {
            "id": "string",
            "description": "string"
          }
        ]
      }
    ]
  }
}
```

### Error Responses
```typescript
{
  "success": false,
  "error": "string"
}
```

## User Flow

1. **Input Validation**: User input is validated and sanitized
2. **Project Creation**: Real project card is created immediately
3. **AI Processing**: Enhanced content generation with retry logic
4. **Card Update**: Project is updated with AI-generated content
5. **Success Feedback**: Visual confirmation and thinking content display

## Error Handling

### Retryable Errors
- Rate limits (429)
- Network timeouts
- Service unavailable (503, 502, 504)

### Non-Retryable Errors
- Authentication errors (401, 403)
- Content policy violations (400 with content_filter)
- Invalid input format
- Parse errors

### Retry Strategy
- Maximum 2 attempts
- Exponential backoff (1s, 2s)
- Progress text updates during retries
- Graceful degradation on failure

## Configuration

### Environment Variables
```bash
# AI Service Configuration
OPENROUTER_API_KEY=your_openrouter_key
OPENAI_API_KEY=your_openai_key
AI_TIMEOUT=30000
AI_MAX_RETRIES=2
AI_MAX_TOKENS=65536
AI_MAX_INPUT_LENGTH=65536
AI_THINKING_BUDGET=20000

# OpenRouter Specific
OPENROUTER_REFERER=http://localhost:3000
OPENROUTER_APP_NAME=Prosperous Codex
```

### Model Configuration
- **Primary Model**: `google/gemini-2.5-flash-lite-preview-06-17`
- **Fallback Model**: `deepseek/deepseek-chat-v3-0324`
- **Temperature**: 0.65 (reasoning models)
- **Top-P**: 0.65 (reasoning models)

## Performance Considerations

### Frontend Optimizations
- **Optimistic UI**: Real card creation before AI processing
- **Minimal Re-renders**: Efficient state management
- **Memory Management**: AbortController for cleanup
- **Loading States**: Visual feedback during processing

### Backend Optimizations
- **Model Fallback**: Automatic failover on errors
- **Request Validation**: Early rejection of invalid inputs
- **Field Mapping**: Efficient data transformation
- **Error Classification**: Smart retry decisions

## Security

### Input Sanitization
- Length validation (10-65536 characters)
- Content pattern detection
- Whitespace normalization
- XSS prevention through proper escaping

### API Security
- Authentication required for all endpoints
- Rate limiting through AI service
- Input validation with Zod schemas
- Error message sanitization

## Troubleshooting

### Common Issues

#### 1. "AI turbo service is not available"
- **Cause**: Missing API keys
- **Solution**: Configure OPENROUTER_API_KEY or OPENAI_API_KEY

#### 2. "Rate limit exceeded"
- **Cause**: Too many requests
- **Solution**: Wait and retry, check API quotas

#### 3. "Content cannot be processed due to safety guidelines"
- **Cause**: Content policy violation
- **Solution**: Modify input text, avoid sensitive content

#### 4. "Input too short/long"
- **Cause**: Input length validation
- **Solution**: Adjust input to 10-65536 characters

#### 5. "Network timeout"
- **Cause**: Slow AI service response
- **Solution**: Automatic retry, check network connection

### Debug Information
- Console logs for API attempts and responses
- Error classification for troubleshooting
- Progress tracking for user feedback
- Abort signal handling for cleanup

## Future Enhancements

### Planned Features
1. **Custom Model Selection**: User-configurable AI models
2. **Batch Processing**: Multiple inputs at once
3. **Template System**: Predefined enhancement templates
4. **Analytics**: Usage tracking and performance metrics
5. **Caching**: Response caching for similar inputs

### Performance Improvements
1. **Streaming Responses**: Real-time content updates
2. **Progressive Enhancement**: Partial results display
3. **Background Processing**: Queue-based processing
4. **Smart Retries**: Adaptive retry strategies

## Testing

### Unit Tests
- AI service functionality
- Input validation logic
- Error handling scenarios
- Field mapping accuracy

### Integration Tests
- Complete turbo flow
- API endpoint validation
- Error recovery testing
- UI state management

### Manual Testing Scenarios
- Various input lengths and complexity
- Network failure simulation
- Rate limiting scenarios
- Content policy violations
- Concurrent operations
