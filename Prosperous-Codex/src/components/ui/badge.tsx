import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2 py-1 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        brand: "border-[#5E6AD2]/20 bg-[#5E6AD2]/10 text-[#5E6AD2] dark:border-[#6E56CF]/30 dark:bg-[#6E56CF]/20 dark:text-[#9E8CFC]",
        success: "border-green-200 bg-green-100 text-green-800 dark:border-green-800 dark:bg-emerald-900 dark:text-emerald-200",
        warning: "border-yellow-200 bg-yellow-100 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
        error: "border-red-200 bg-red-100 text-red-800 dark:border-red-800 dark:bg-red-900 dark:text-red-200",
        neutral: "border-gray-200 bg-gray-100 text-gray-800 dark:border-neutral-700 dark:bg-neutral-800 dark:text-neutral-200",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  icon?: React.ReactNode
  iconRight?: React.ReactNode
}

function Badge({ className, variant, icon, iconRight, children, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props}>
      {icon && <span className="mr-1 h-3 w-3">{icon}</span>}
      {children}
      {iconRight && <span className="ml-1 h-3 w-3">{iconRight}</span>}
    </div>
  )
}

export { Badge, badgeVariants }
