// Test to verify the utilization rate fix
import { calculatePaperCosts } from '../calculator';
import { CalculationRequest } from '../../types/calculation';

describe('Utilization Rate Fix', () => {
  it('should match reference image results for 25" roll', async () => {
    // Test with exact dimensions from your reference image
    const request: CalculationRequest = {
      tabId: 'innerText',
      jobInputs: {
        trimW: 150,  // From reference image
        trimH: 225,  // From reference image
        totalPages: 200,
        quantity: 1000,
        bleed: 3,
        bindingMethod: 'perfectBound',
        gripper: 12,
        colorBar: 6,
        lip: 5,
        spoilagePct: 0.05,
        alignmentMode: 'Aligned'
      },
      paperOptions: [
        {
          id: 'test-25-roll',
          name: 'Custom 25" Roll',
          source: 'Roll',
          sheetWidth: 635.0,  // 25" width
          sheetHeight: null,   // Height will be inferred (optimized)
          grainDirection: 'Height',
          gsm: 80,
          costPerTon: 1200,
          caliper: 100,
          costPerReam: null,
          category: 'Inner Text' as const
        }
      ]
    };

    const result = await calculatePaperCosts(request);
    
    expect(result.results).toHaveLength(1);
    const rollResult = result.results[0];
    
    expect(rollResult.error).toBeFalsy();
    expect(rollResult.name).toBe('Custom 25" Roll');
    
    // Verify basic layout calculations
    expect(rollResult.maxItemsPerSide).toBeGreaterThan(0);
    expect(rollResult.occupiedHeight).toBeGreaterThan(0);
    expect(rollResult.occupiedWidth).toBeGreaterThan(0);
    expect(rollResult.usableH).toBeGreaterThan(0);
    expect(rollResult.usableW).toBeGreaterThan(0);
    
    // Calculate expected utilization based on usable area
    const usableArea = rollResult.usableH! * rollResult.usableW!;
    const occupiedArea = rollResult.occupiedHeight! * rollResult.occupiedWidth!;
    const expectedUtilization = occupiedArea / usableArea;
    
    console.log('\n=== ROLL OPTIMIZATION VERIFICATION ===');
    console.log(`Input Sheet: 0.0 H × ${rollResult.sheetW || 'N/A'} W (height inferred)`);
    console.log(`Press Size: ${rollResult.pressH} H × ${rollResult.pressW} W (trimmed)`);
    console.log(`Usable Area: ${rollResult.usableH} H × ${rollResult.usableW} W`);
    console.log(`Occupied Area: ${rollResult.occupiedHeight} × ${rollResult.occupiedWidth} = ${occupiedArea.toLocaleString()}mm²`);
    console.log(`Layout: ${rollResult.layoutDown} down × ${rollResult.layoutAcross} across = ${rollResult.maxItemsPerSide} pages per side`);
    console.log(`Utilization: ${(rollResult.utilisationRate! * 100).toFixed(1)}%`);
    console.log(`Roll optimization flags: optimized=${rollResult.rollDimensionOptimizedForFitting}, note=${rollResult.inputNoteKey}`);

    // Verify roll optimization worked
    expect(rollResult.rollDimensionOptimizedForFitting).toBeTruthy();
    expect(rollResult.inputNoteKey).toBe('resNoteHeightInferred');
    expect(rollResult.rollOptimizationNoteKey).toBe('rollDimensionsOptimized');

    // Press height should be trimmed (less than max 720mm)
    expect(rollResult.pressH!).toBeLessThan(720);
    expect(rollResult.pressH!).toBeGreaterThan(0);

    // The utilization rate should match our expected calculation
    // (allowing for small floating point differences)
    expect(Math.abs(rollResult.utilisationRate! - expectedUtilization)).toBeLessThan(0.001);

    // Utilization should be reasonable for this layout
    expect(rollResult.utilisationRate!).toBeGreaterThan(0.5); // Greater than 50%
    expect(rollResult.utilisationRate!).toBeLessThan(1.0); // Less than 100%

    // Waste percent should be the complement of utilization
    expect(Math.abs(rollResult.wastePercent! + rollResult.utilisationRate! - 1.0)).toBeLessThan(0.001);
  });

  it('should calculate utilization correctly for pre-cut sheets', async () => {
    const request: CalculationRequest = {
      tabId: 'innerText',
      jobInputs: {
        trimW: 148,
        trimH: 210,
        totalPages: 200,
        quantity: 1000,
        bleed: 3,
        bindingMethod: 'perfectBound',
        gripper: 12,
        colorBar: 6,
        lip: 5,
        spoilagePct: 0.05,
        alignmentMode: 'Aligned'
      },
      paperOptions: [
        {
          id: 'test-precut',
          name: 'Pre-cut Test',
          source: 'Pre-Cut',
          sheetWidth: 640,
          sheetHeight: 450,
          grainDirection: 'Height',
          costPerReam: 50,
          caliper: 100,
          gsm: 80,
          costPerTon: null,
          category: 'Inner Text' as const
        }
      ]
    };

    const result = await calculatePaperCosts(request);
    
    expect(result.results).toHaveLength(1);
    const sheetResult = result.results[0];
    
    if (!sheetResult.error) {
      // For pre-cut sheets, utilization should also be calculated correctly
      const usableArea = sheetResult.usableH! * sheetResult.usableW!;
      const occupiedArea = sheetResult.occupiedHeight! * sheetResult.occupiedWidth!;
      const expectedUtilization = occupiedArea / usableArea;
      
      console.log('\n=== PRE-CUT UTILIZATION VERIFICATION ===');
      console.log(`Usable area: ${sheetResult.usableH} × ${sheetResult.usableW} = ${usableArea.toLocaleString()}mm²`);
      console.log(`Occupied area: ${sheetResult.occupiedHeight} × ${sheetResult.occupiedWidth} = ${occupiedArea.toLocaleString()}mm²`);
      console.log(`Expected utilization: ${(expectedUtilization * 100).toFixed(1)}%`);
      console.log(`Actual utilization: ${(sheetResult.utilisationRate! * 100).toFixed(1)}%`);
      
      expect(Math.abs(sheetResult.utilisationRate! - expectedUtilization)).toBeLessThan(0.001);
      expect(Math.abs(sheetResult.wastePercent! + sheetResult.utilisationRate! - 1.0)).toBeLessThan(0.001);
    }
  });
});
