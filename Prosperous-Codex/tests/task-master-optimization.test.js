/**
 * Comprehensive Test Suite for Task Master Optimization Features
 * Tests performance improvements, database optimizations, and new features
 */

const { performance } = require('perf_hooks');

// Test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3001',
  testUser: {
    email: '<EMAIL>',
    password: 'moderator123'
  },
  performance: {
    maxResponseTime: 500, // ms
    maxDatabaseQueryTime: 100, // ms
    minSizeReduction: 30 // percentage
  }
};

// Test utilities
class TestRunner {
  constructor() {
    this.results = [];
    this.authToken = null;
  }

  async authenticate() {
    console.log('🔐 Authenticating test user...');
    
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/auth/signin`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(TEST_CONFIG.testUser)
    });

    if (!response.ok) {
      throw new Error(`Authentication failed: ${response.status}`);
    }

    // Extract session token from response headers or cookies
    const setCookieHeader = response.headers.get('set-cookie');
    if (setCookieHeader) {
      this.authToken = setCookieHeader;
    }

    console.log('✅ Authentication successful');
  }

  async makeRequest(endpoint, options = {}) {
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    if (this.authToken) {
      headers['Cookie'] = this.authToken;
    }

    const startTime = performance.now();
    
    const response = await fetch(`${TEST_CONFIG.baseUrl}${endpoint}`, {
      ...options,
      headers
    });

    const endTime = performance.now();
    const responseTime = endTime - startTime;

    return {
      response,
      responseTime,
      data: response.ok ? await response.json() : null
    };
  }

  recordResult(testName, passed, details = {}) {
    const result = {
      test: testName,
      passed,
      timestamp: new Date().toISOString(),
      ...details
    };
    
    this.results.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}`);
    
    if (details.responseTime) {
      console.log(`   Response time: ${details.responseTime.toFixed(2)}ms`);
    }
    
    if (details.sizeReduction) {
      console.log(`   Size reduction: ${details.sizeReduction}%`);
    }
    
    if (!passed && details.error) {
      console.log(`   Error: ${details.error}`);
    }
  }

  generateReport() {
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('========================');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results.filter(r => !r.passed).forEach(result => {
        console.log(`- ${result.test}: ${result.error || 'Unknown error'}`);
      });
    }

    // Performance summary
    const performanceTests = this.results.filter(r => r.responseTime);
    if (performanceTests.length > 0) {
      const avgResponseTime = performanceTests.reduce((sum, r) => sum + r.responseTime, 0) / performanceTests.length;
      console.log(`\n⚡ PERFORMANCE SUMMARY:`);
      console.log(`Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
      console.log(`Max Response Time: ${Math.max(...performanceTests.map(r => r.responseTime)).toFixed(2)}ms`);
    }

    return {
      totalTests,
      passedTests,
      failedTests,
      successRate: (passedTests / totalTests) * 100,
      results: this.results
    };
  }
}

// Test suites
class DatabaseOptimizationTests {
  constructor(testRunner) {
    this.testRunner = testRunner;
  }

  async testDatabaseIndexes() {
    console.log('\n🗄️  Testing Database Indexes...');
    
    // Test project queries with status filter (should use idx_projects_status)
    const { response, responseTime } = await this.testRunner.makeRequest(
      '/api/task-master/projects?status=inProgress&limit=50'
    );

    const passed = response.ok && responseTime < TEST_CONFIG.performance.maxDatabaseQueryTime;
    
    this.testRunner.recordResult('Database Indexes - Project Status Query', passed, {
      responseTime,
      error: !response.ok ? `HTTP ${response.status}` : responseTime >= TEST_CONFIG.performance.maxDatabaseQueryTime ? 'Query too slow' : null
    });
  }

  async testTransactionIntegrity() {
    console.log('\n🔄 Testing Transaction Integrity...');
    
    // Create a project (should be atomic with tags and activity log)
    const projectData = {
      title: 'Test Transaction Project',
      description: 'Testing atomic operations',
      tags: ['test', 'transaction', 'optimization'],
      status: 'todo',
      priority: 'medium'
    };

    const { response, responseTime, data } = await this.testRunner.makeRequest(
      '/api/task-master/projects',
      {
        method: 'POST',
        body: JSON.stringify(projectData)
      }
    );

    let passed = response.ok && data?.project?.id;
    
    if (passed) {
      // Verify all related data was created atomically
      const projectId = data.project.id;
      const { response: detailResponse, data: detailData } = await this.testRunner.makeRequest(
        `/api/task-master/projects/${projectId}?includeActivity=true&fields=withRelations`
      );
      
      passed = detailResponse.ok && 
               detailData?.project?.tags?.length === 3 &&
               detailData?.activity?.length > 0;
    }

    this.testRunner.recordResult('Transaction Integrity - Project Creation', passed, {
      responseTime,
      error: !passed ? 'Transaction rollback or incomplete data' : null
    });
  }

  async testConcurrentOperations() {
    console.log('\n⚡ Testing Concurrent Operations...');
    
    // Create multiple projects concurrently to test database locking
    const concurrentRequests = Array.from({ length: 5 }, (_, i) => 
      this.testRunner.makeRequest('/api/task-master/projects', {
        method: 'POST',
        body: JSON.stringify({
          title: `Concurrent Project ${i + 1}`,
          description: 'Testing concurrent database operations',
          status: 'todo',
          priority: 'low'
        })
      })
    );

    const startTime = performance.now();
    const results = await Promise.all(concurrentRequests);
    const endTime = performance.now();
    
    const allSuccessful = results.every(r => r.response.ok);
    const totalTime = endTime - startTime;
    
    this.testRunner.recordResult('Concurrent Operations', allSuccessful, {
      responseTime: totalTime,
      error: !allSuccessful ? 'Some concurrent operations failed' : null,
      details: `${results.filter(r => r.response.ok).length}/5 requests succeeded`
    });
  }
}

class PerformanceOptimizationTests {
  constructor(testRunner) {
    this.testRunner = testRunner;
  }

  async testLazyLoading() {
    console.log('\n🚀 Testing Lazy Loading...');
    
    // Test basic project load (should be fast)
    const { response: basicResponse, responseTime: basicTime } = await this.testRunner.makeRequest(
      '/api/task-master/projects?limit=10'
    );

    // Test full project load (should be slower but still reasonable)
    const { response: fullResponse, responseTime: fullTime } = await this.testRunner.makeRequest(
      '/api/task-master/projects?includeDetails=true&limit=10'
    );

    const basicPassed = basicResponse.ok && basicTime < TEST_CONFIG.performance.maxResponseTime / 2;
    const fullPassed = fullResponse.ok && fullTime < TEST_CONFIG.performance.maxResponseTime;
    const improvementPassed = basicTime < fullTime; // Basic should be faster than full

    this.testRunner.recordResult('Lazy Loading - Basic Load', basicPassed, {
      responseTime: basicTime,
      error: !basicPassed ? 'Basic load too slow' : null
    });

    this.testRunner.recordResult('Lazy Loading - Full Load', fullPassed, {
      responseTime: fullTime,
      error: !fullPassed ? 'Full load too slow' : null
    });

    this.testRunner.recordResult('Lazy Loading - Performance Improvement', improvementPassed, {
      details: `Basic: ${basicTime.toFixed(2)}ms, Full: ${fullTime.toFixed(2)}ms`,
      error: !improvementPassed ? 'No performance improvement detected' : null
    });
  }

  async testPagination() {
    console.log('\n📄 Testing Pagination...');
    
    // Test paginated project list
    const { response, responseTime, data } = await this.testRunner.makeRequest(
      '/api/task-master/projects?page=1&limit=5'
    );

    const passed = response.ok && 
                   data?.pagination?.page === 1 &&
                   data?.pagination?.limit === 5 &&
                   data?.projects?.length <= 5 &&
                   typeof data?.pagination?.totalCount === 'number';

    this.testRunner.recordResult('Pagination - Project List', passed, {
      responseTime,
      error: !passed ? 'Pagination metadata missing or incorrect' : null,
      details: data?.pagination ? `Total: ${data.pagination.totalCount}, Pages: ${data.pagination.totalPages}` : null
    });
  }

  async testPartialResponse() {
    console.log('\n🎯 Testing Partial Response...');
    
    // Get project with all fields
    const { response: fullResponse, data: fullData } = await this.testRunner.makeRequest(
      '/api/task-master/projects'
    );

    // Get project with basic fields only
    const { response: partialResponse, responseTime, data: partialData } = await this.testRunner.makeRequest(
      '/api/task-master/projects?fields=basic'
    );

    if (!fullResponse.ok || !partialResponse.ok) {
      this.testRunner.recordResult('Partial Response', false, {
        error: 'Failed to fetch comparison data'
      });
      return;
    }

    // Calculate size reduction
    const fullSize = JSON.stringify(fullData).length;
    const partialSize = JSON.stringify(partialData).length;
    const sizeReduction = Math.round(((fullSize - partialSize) / fullSize) * 100);

    const passed = sizeReduction >= TEST_CONFIG.performance.minSizeReduction;

    this.testRunner.recordResult('Partial Response - Size Reduction', passed, {
      responseTime,
      sizeReduction,
      error: !passed ? `Size reduction ${sizeReduction}% below minimum ${TEST_CONFIG.performance.minSizeReduction}%` : null,
      details: `Full: ${fullSize} bytes, Partial: ${partialSize} bytes`
    });
  }
}

// Export test runner for use in other files
module.exports = {
  TestRunner,
  DatabaseOptimizationTests,
  PerformanceOptimizationTests,
  TEST_CONFIG
};
