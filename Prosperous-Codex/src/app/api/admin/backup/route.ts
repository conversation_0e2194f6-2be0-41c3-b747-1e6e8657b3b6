import { NextRequest, NextResponse } from 'next/server';
import { requireRole } from '@/lib/auth/middleware';
import { copyFileSync, existsSync, mkdirSync, readdirSync, statSync, writeFileSync, rmSync } from 'fs';
import { join } from 'path';
import { getDatabase } from '@/lib/database/database';
import Database from 'better-sqlite3';

// Type definitions for backup data structures
interface ProjectWithUsernames {
  id: number;
  title: string;
  description?: string;
  full_description?: string;
  event_log?: string;
  status: string;
  priority: string;
  progress: number;
  due_date?: string;
  visibility: string;
  created_by: number;
  assigned_to?: number;
  created_at: string;
  updated_at: string;
  creator_username?: string;
  assignee_username?: string;
}

interface CommentWithUsernames {
  id: number;
  project_id: number;
  author_id: number;
  content: string;
  created_at: string;
  updated_at: string;
  author_username?: string;
  project_title?: string;
}

interface ActivityWithUsernames {
  id: number;
  project_id: number;
  user_id: number;
  activity_type: string;
  description: string;
  metadata?: string;
  created_at: string;
  user_username?: string;
  project_title?: string;
}

interface TaskMasterExportData {
  projects: ProjectWithUsernames[];
  comments: CommentWithUsernames[];
  activities: ActivityWithUsernames[];
}

interface SavedCalculationWithUsername {
  id: number;
  user_id: number;
  calculation_name: string;
  calculation_data: string;
  created_at: string;
  updated_at: string;
  user_username?: string;
}

interface CalculationHistoryWithUsername {
  id: number;
  user_id: number;
  calculation_type: string;
  input_data: string;
  result_data: string;
  created_at: string;
  user_username?: string;
}

interface PaperCalculatorExportData {
  savedCalculations: SavedCalculationWithUsername[];
  calculationHistory: CalculationHistoryWithUsername[];
}

interface UserExportData {
  id: number;
  email: string;
  username: string;
  role: string;
  created_at: string;
  last_login?: string;
  is_active: boolean;
}

/**
 * Create database backup
 */
export async function POST(request: NextRequest) {
  try {
    // Require admin or moderator role
    const authResult = await requireRole(request, 'moderator');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    // User authentication verified, but user object not needed for backup operation
    
    // Create backup
    const backupResult = await createDatabaseBackup('manual');
    
    if (backupResult.success) {
      return NextResponse.json({
        message: 'Database backup created successfully',
        backupName: backupResult.backupName,
        backupPath: backupResult.backupPath,
        size: backupResult.size
      });
    } else {
      return NextResponse.json(
        { error: backupResult.error || 'Failed to create backup' },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Backup creation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * List available backups
 */
export async function GET(request: NextRequest) {
  try {
    // Require admin or moderator role
    const authResult = await requireRole(request, 'moderator');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const backups = await listDatabaseBackups();
    
    return NextResponse.json({
      backups,
      count: backups.length
    });
    
  } catch (error) {
    console.error('List backups error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Create a comprehensive global database backup
 */
export async function createDatabaseBackup(backupType: 'manual' | 'auto-reset' = 'manual') {
  try {
    const dbPath = process.env.DATABASE_PATH || join(process.cwd(), 'data', 'prosperous-codex.db');
    const backupDir = join(process.cwd(), 'data', 'backups', 'global');

    // Ensure backup directory exists
    if (!existsSync(backupDir)) {
      mkdirSync(backupDir, { recursive: true });
    }

    // Check if database exists
    if (!existsSync(dbPath)) {
      return {
        success: false,
        error: 'Database file not found'
      };
    }

    // Generate backup folder with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFolderName = `global-backup-${backupType}-${timestamp}`;
    const backupFolderPath = join(backupDir, backupFolderName);

    // Create backup folder
    mkdirSync(backupFolderPath, { recursive: true });

    // Copy database file
    const dbBackupPath = join(backupFolderPath, 'prosperous-codex.db');
    copyFileSync(dbPath, dbBackupPath);

    // Create comprehensive metadata
    const metadata = await createGlobalBackupMetadata();
    const metadataPath = join(backupFolderPath, 'backup-manifest.json');
    writeFileSync(metadataPath, JSON.stringify(metadata, null, 2), 'utf-8');

    // Create module-specific data exports
    await createModuleDataExports(backupFolderPath);

    // Get total backup size
    const totalSize = await calculateFolderSize(backupFolderPath);

    console.log(`✅ Global database backup created: ${backupFolderName} (${(totalSize / 1024).toFixed(2)} KB)`);

    return {
      success: true,
      backupName: backupFolderName,
      backupPath: backupFolderPath,
      size: totalSize
    };

  } catch (error) {
    console.error('❌ Global database backup failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * List all available global backups
 */
export async function listDatabaseBackups() {
  try {
    const backupDir = join(process.cwd(), 'data', 'backups', 'global');

    if (!existsSync(backupDir)) {
      return [];
    }

    const items = readdirSync(backupDir);
    const backups = [];

    for (const item of items) {
      const itemPath = join(backupDir, item);
      const stats = statSync(itemPath);

      if (stats.isDirectory() && item.startsWith('global-backup-')) {
        // Parse backup info from folder name
        const parts = item.split('-');
        const backupTypeFromName = parts.includes('manual') ? 'manual' :
                    parts.includes('auto') ? 'auto-reset' : 'unknown';

        const size = await calculateFolderSize(itemPath);

        backups.push({
          name: item,
          path: itemPath,
          size,
          created: stats.birthtime,
          type: 'global-comprehensive',
          backupType: backupTypeFromName,
          sizeFormatted: `${(size / 1024).toFixed(2)} KB`
        });
      }
    }

    return backups.sort((a, b) => b.created.getTime() - a.created.getTime()); // Most recent first

  } catch (error) {
    console.error('Error listing global backups:', error);
    return [];
  }
}

/**
 * Clean up old global backups (keep last 5)
 */
export async function cleanupOldBackups() {
  try {
    const backups = await listDatabaseBackups();

    // Keep last 5 global backups, delete older ones
    if (backups.length > 5) {
      const toDelete = backups.slice(5);
      let deletedCount = 0;

      for (const backup of toDelete) {
        try {
          rmSync(backup.path, { recursive: true, force: true });
          deletedCount++;
          console.log(`🗑️ Deleted old global backup: ${backup.name}`);
        } catch (error) {
          console.error(`Failed to delete global backup ${backup.name}:`, error);
        }
      }

      console.log(`✅ Cleaned up ${deletedCount} old global backups`);
      return deletedCount;
    }

    return 0;

  } catch (error) {
    console.error('Error cleaning up global backups:', error);
    return 0;
  }
}

/**
 * Create comprehensive backup metadata
 */
async function createGlobalBackupMetadata() {
  try {
    const db = getDatabase();

    // Get statistics for each module
    const userStats = db.prepare('SELECT COUNT(*) as count FROM users').get() as { count: number };
    const projectStats = db.prepare('SELECT COUNT(*) as count FROM projects').get() as { count: number };
    const sessionStats = db.prepare('SELECT COUNT(*) as count FROM user_sessions').get() as { count: number };
    const calculationStats = db.prepare('SELECT COUNT(*) as count FROM saved_calculations').get() as { count: number };
    const historyStats = db.prepare('SELECT COUNT(*) as count FROM calculation_history').get() as { count: number };
    const commentStats = db.prepare('SELECT COUNT(*) as count FROM project_comments').get() as { count: number };
    const activityStats = db.prepare('SELECT COUNT(*) as count FROM activity_log').get() as { count: number };

    return {
      metadata: {
        backupType: 'global-comprehensive',
        createdAt: new Date().toISOString(),
        version: '1.0.0',
        description: 'Comprehensive global backup including all application data and modules'
      },
      modules: {
        userManagement: {
          description: 'User accounts, authentication, and session data',
          tables: ['users', 'user_sessions', 'user_settings'],
          recordCounts: {
            users: userStats.count,
            sessions: sessionStats.count
          }
        },
        taskMaster: {
          description: 'Project management, tasks, comments, and activity tracking',
          tables: ['projects', 'project_comments', 'activity_log', 'project_team_members', 'project_files'],
          recordCounts: {
            projects: projectStats.count,
            comments: commentStats.count,
            activities: activityStats.count
          }
        },
        paperCostEstimator: {
          description: 'Paper cost calculations, saved estimates, and calculation history',
          tables: ['saved_calculations', 'calculation_history'],
          recordCounts: {
            savedCalculations: calculationStats.count,
            calculationHistory: historyStats.count
          }
        },
        systemData: {
          description: 'Access requests and system configuration',
          tables: ['access_requests'],
          recordCounts: {}
        }
      },
      totalRecords: userStats.count + projectStats.count + sessionStats.count +
                   calculationStats.count + historyStats.count + commentStats.count + activityStats.count,
      backupContents: [
        'prosperous-codex.db - Complete SQLite database file',
        'backup-manifest.json - This metadata file',
        'modules/ - Module-specific data exports',
        'modules/task-master-data.json - Task Master data export',
        'modules/paper-calculator-data.json - Paper Cost Estimator data export',
        'modules/user-data.json - User and session data export'
      ]
    };

  } catch (error) {
    console.error('Error creating backup metadata:', error);
    return {
      metadata: {
        backupType: 'global-comprehensive',
        createdAt: new Date().toISOString(),
        version: '1.0.0',
        description: 'Comprehensive global backup (metadata generation failed)'
      },
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Create module-specific data exports
 */
async function createModuleDataExports(backupFolderPath: string) {
  try {
    const db = getDatabase();
    const modulesDir = join(backupFolderPath, 'modules');
    mkdirSync(modulesDir, { recursive: true });

    // Export Task Master data
    const taskMasterData = await exportTaskMasterData(db);
    writeFileSync(
      join(modulesDir, 'task-master-data.json'),
      JSON.stringify(taskMasterData, null, 2),
      'utf-8'
    );

    // Export Paper Calculator data
    const paperCalculatorData = await exportPaperCalculatorData(db);
    writeFileSync(
      join(modulesDir, 'paper-calculator-data.json'),
      JSON.stringify(paperCalculatorData, null, 2),
      'utf-8'
    );

    // Export User data
    const userData = await exportUserData(db);
    writeFileSync(
      join(modulesDir, 'user-data.json'),
      JSON.stringify(userData, null, 2),
      'utf-8'
    );

    console.log('✅ Module data exports created successfully');

  } catch (error) {
    console.error('Error creating module data exports:', error);
  }
}

/**
 * Export Task Master data with relationships
 */
async function exportTaskMasterData(db: Database.Database): Promise<TaskMasterExportData> {
  const projects = db.prepare(`
    SELECT p.*, u1.username as creator_username, u2.username as assignee_username
    FROM projects p
    LEFT JOIN users u1 ON p.created_by = u1.id
    LEFT JOIN users u2 ON p.assigned_to = u2.id
  `).all();

  const comments = db.prepare(`
    SELECT pc.*, u.username as author_username, p.title as project_title
    FROM project_comments pc
    LEFT JOIN users u ON pc.author_id = u.id
    LEFT JOIN projects p ON pc.project_id = p.id
  `).all();

  const activities = db.prepare(`
    SELECT al.*, u.username as user_username, p.title as project_title
    FROM activity_log al
    LEFT JOIN users u ON al.user_id = u.id
    LEFT JOIN projects p ON al.project_id = p.id
  `).all();

  return { projects, comments, activities };
}

/**
 * Export Paper Calculator data
 */
async function exportPaperCalculatorData(db: Database.Database): Promise<PaperCalculatorExportData> {
  const savedCalculations = db.prepare(`
    SELECT sc.*, u.username as user_username
    FROM saved_calculations sc
    LEFT JOIN users u ON sc.user_id = u.id
  `).all();

  const calculationHistory = db.prepare(`
    SELECT ch.*, u.username as user_username
    FROM calculation_history ch
    LEFT JOIN users u ON ch.user_id = u.id
  `).all();

  return { savedCalculations, calculationHistory };
}

/**
 * Export User data
 */
async function exportUserData(db: Database.Database): Promise<UserExportData[]> {
  const users = db.prepare(`
    SELECT id, email, username, role, created_at, last_login, is_active
    FROM users
  `).all();

  const sessions = db.prepare(`
    SELECT us.*, u.username as user_username
    FROM user_sessions us
    LEFT JOIN users u ON us.user_id = u.id
  `).all();

  return { users, sessions };
}

/**
 * Calculate total size of a folder
 */
async function calculateFolderSize(folderPath: string): Promise<number> {
  try {
    let totalSize = 0;
    const items = readdirSync(folderPath);

    for (const item of items) {
      const itemPath = join(folderPath, item);
      const stats = statSync(itemPath);

      if (stats.isDirectory()) {
        totalSize += await calculateFolderSize(itemPath);
      } else {
        totalSize += stats.size;
      }
    }

    return totalSize;
  } catch (error) {
    console.error('Error calculating folder size:', error);
    return 0;
  }
}
