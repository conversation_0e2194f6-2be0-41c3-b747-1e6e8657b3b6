/**
 * Service Utilities
 * 
 * Common utilities used across services
 */

import { ValidationError, DatabaseError, AuthorizationError } from '../errors';

/**
 * Service response wrapper
 */
export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Create a successful service response
 */
export function createSuccessResponse<T>(data: T, metadata?: Record<string, any>): ServiceResponse<T> {
  return {
    success: true,
    data,
    metadata,
  };
}

/**
 * Create an error service response
 */
export function createErrorResponse(error: string | Error): ServiceResponse<never> {
  return {
    success: false,
    error: error instanceof Error ? error.message : error,
  };
}

/**
 * Service method wrapper for consistent error handling
 */
export function withServiceErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<ServiceResponse<R>> => {
    try {
      const result = await fn(...args);
      return createSuccessResponse(result);
    } catch (error) {
      if (error instanceof ValidationError || 
          error instanceof DatabaseError || 
          error instanceof AuthorizationError) {
        return createErrorResponse(error);
      }
      
      // Log unexpected errors
      console.error('Unexpected service error:', error);
      return createErrorResponse('An unexpected error occurred');
    }
  };
}

/**
 * Pagination utilities
 */
export interface PaginationOptions {
  page: number;
  limit: number;
  offset?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export function calculatePagination(options: PaginationOptions): { offset: number; limit: number } {
  const { page, limit } = options;
  const offset = (page - 1) * limit;
  return { offset, limit };
}

export function createPaginatedResponse<T>(
  data: T[],
  total: number,
  options: PaginationOptions
): PaginatedResponse<T> {
  const { page, limit } = options;
  const totalPages = Math.ceil(total / limit);
  
  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

/**
 * Service method timing decorator
 */
export function withTiming<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  methodName: string
) {
  return async (...args: T): Promise<R> => {
    const startTime = Date.now();
    try {
      const result = await fn(...args);
      const duration = Date.now() - startTime;
      // Service method completed (debug logging removed for production)
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`Service method ${methodName} failed after ${duration}ms:`, error);
      throw error;
    }
  };
}

/**
 * Service method caching utilities
 */
export class ServiceCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set<T>(key: string, data: T, ttlMs: number = 300000): void { // 5 minutes default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs,
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) {
      return null;
    }

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  clear(): void {
    this.cache.clear();
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }
}

/**
 * Global service cache instance
 */
export const serviceCache = new ServiceCache();
