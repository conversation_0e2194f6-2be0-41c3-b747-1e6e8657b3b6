import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FilterDropdown, FilterState } from '../filter-dropdown';

// Mock the UI components
jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} {...props}>
      {children}
    </button>
  ),
}));

jest.mock('@/components/ui/popover', () => ({
  Popover: ({ children, open, onOpenChange }: any) => (
    <div data-testid="popover" data-open={open}>
      {children}
    </div>
  ),
  PopoverTrigger: ({ children }: any) => <div data-testid="popover-trigger">{children}</div>,
  PopoverContent: ({ children }: any) => <div data-testid="popover-content">{children}</div>,
}));

jest.mock('@/components/ui/radio-group', () => ({
  RadioGroup: ({ children, value, onValueChange }: any) => (
    <div data-testid="radio-group" data-value={value} onChange={onValueChange}>
      {children}
    </div>
  ),
  RadioGroupItem: ({ value, id }: any) => (
    <input
      type="radio"
      value={value}
      id={id}
      data-testid={`radio-${value}`}
      onChange={(e) => e.target.form?.dispatchEvent(new Event('change', { bubbles: true }))}
    />
  ),
}));

jest.mock('@/components/ui/checkbox', () => ({
  Checkbox: ({ id, checked, onCheckedChange }: any) => (
    <input
      type="checkbox"
      id={id}
      checked={checked}
      data-testid={`checkbox-${id}`}
      onChange={(e) => onCheckedChange?.(e.target.checked)}
    />
  ),
}));

jest.mock('@/components/ui/label', () => ({
  Label: ({ children, htmlFor }: any) => <label htmlFor={htmlFor}>{children}</label>,
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, className }: any) => (
    <span className={className} data-testid="badge">
      {children}
    </span>
  ),
}));

describe('FilterDropdown', () => {
  const mockOnFiltersChange = jest.fn();
  
  const defaultProps = {
    filters: {} as FilterState,
    onFiltersChange: mockOnFiltersChange,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders filter button with no active filters', () => {
    render(<FilterDropdown {...defaultProps} />);
    
    const filterButton = screen.getByRole('button');
    expect(filterButton).toBeInTheDocument();
    expect(filterButton).toHaveTextContent('Filter');
  });

  it('shows active filter count when filters are applied', () => {
    const filtersWithPriority: FilterState = { priority: 'high' };
    
    render(
      <FilterDropdown 
        {...defaultProps} 
        filters={filtersWithPriority} 
      />
    );
    
    expect(screen.getByTestId('badge')).toHaveTextContent('1');
  });

  it('handles priority filter selection', async () => {
    const user = userEvent.setup();
    
    render(<FilterDropdown {...defaultProps} />);
    
    // Find and click the high priority radio button
    const highPriorityRadio = screen.getByTestId('radio-high');
    await user.click(highPriorityRadio);
    
    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      priority: 'high'
    });
  });

  it('handles checkbox filter toggles', async () => {
    const user = userEvent.setup();
    
    render(<FilterDropdown {...defaultProps} />);
    
    // Find and click the due date checkbox
    const dueDateCheckbox = screen.getByTestId('checkbox-due-date');
    await user.click(dueDateCheckbox);
    
    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      dueDate: true
    });
  });

  it('clears all filters when clear all button is clicked', async () => {
    const user = userEvent.setup();
    const filtersWithMultiple: FilterState = { 
      priority: 'high', 
      dueDate: true 
    };
    
    render(
      <FilterDropdown 
        {...defaultProps} 
        filters={filtersWithMultiple} 
      />
    );
    
    const clearAllButton = screen.getByText('Clear All');
    await user.click(clearAllButton);
    
    expect(mockOnFiltersChange).toHaveBeenCalledWith({});
  });

  it('disables button when loading', () => {
    render(<FilterDropdown {...defaultProps} isLoading={true} />);
    
    const filterButton = screen.getByRole('button');
    expect(filterButton).toBeDisabled();
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(<FilterDropdown {...defaultProps} />);
    
    const filterButton = screen.getByRole('button');
    
    // Test Enter key to open dropdown
    await user.click(filterButton);
    await user.keyboard('{Enter}');
    
    // Test Escape key to close dropdown
    await user.keyboard('{Escape}');
    
    // Test Arrow keys for navigation
    await user.keyboard('{ArrowDown}');
    await user.keyboard('{ArrowUp}');
  });

  it('maintains filter state correctly', () => {
    const complexFilters: FilterState = {
      priority: 'medium',
      dueDate: true,
      lastUpdated: false,
      completion: true
    };
    
    render(
      <FilterDropdown 
        {...defaultProps} 
        filters={complexFilters} 
      />
    );
    
    // Check that the priority radio is selected
    const mediumPriorityRadio = screen.getByTestId('radio-medium');
    expect(mediumPriorityRadio).toBeChecked();
    
    // Check that checkboxes reflect the state
    const dueDateCheckbox = screen.getByTestId('checkbox-due-date');
    const completionCheckbox = screen.getByTestId('checkbox-completion');
    
    expect(dueDateCheckbox).toBeChecked();
    expect(completionCheckbox).toBeChecked();
  });

  it('handles accessibility attributes correctly', () => {
    render(<FilterDropdown {...defaultProps} />);
    
    const filterButton = screen.getByRole('button');
    
    expect(filterButton).toHaveAttribute('aria-expanded');
    expect(filterButton).toHaveAttribute('aria-haspopup', 'menu');
    expect(filterButton).toHaveAttribute('aria-label');
  });

  it('shows correct filter count in aria-label', () => {
    const filtersWithTwo: FilterState = { 
      priority: 'high', 
      dueDate: true 
    };
    
    render(
      <FilterDropdown 
        {...defaultProps} 
        filters={filtersWithTwo} 
      />
    );
    
    const filterButton = screen.getByRole('button');
    expect(filterButton).toHaveAttribute('aria-label', 'Filter tasks (2 active)');
  });
});
