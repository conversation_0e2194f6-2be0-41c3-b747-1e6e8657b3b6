"use client";

import React from 'react';
import { Trash2 } from 'lucide-react';
import { useDrop } from 'react-dnd';
import { useSession } from 'next-auth/react';

interface TrashBinProps {
  isVisible: boolean;
  onDeleteTask: (taskId: string) => void;
}

export function TrashBin({ isVisible, onDeleteTask }: TrashBinProps) {
  const { data: session } = useSession();
  const currentUser = session?.user;

  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'TASK',
    drop: (item: { id: string; fromColumnId: string; createdBy: number; title: string }) => {
      // Only allow deletion if current user is the creator
      if (currentUser && parseInt(currentUser.id) === item.createdBy) {
        onDeleteTask(item.id);
      }
    },
    canDrop: (item: { id: string; fromColumnId: string; createdBy: number; title: string }) => {
      // Only allow drop if current user is the creator
      return currentUser && parseInt(currentUser.id) === item.createdBy;
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop(),
    }),
  }), [currentUser, onDeleteTask]);

  if (!isVisible) {
    return null;
  }

  return (
    <div
      ref={drop}
      className={`fixed bottom-6 right-6 z-[300] w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 ${
        isOver && canDrop
          ? 'bg-red-500 scale-110 shadow-lg'
          : canDrop
          ? 'bg-red-400 hover:bg-red-500'
          : 'bg-gray-400'
      } ${
        isOver && !canDrop
          ? 'bg-gray-500 cursor-not-allowed'
          : 'cursor-pointer'
      }`}
      title={
        canDrop
          ? isOver
            ? 'Release to delete'
            : 'Drag task here to delete'
          : 'You can only delete tasks you created'
      }
    >
      <Trash2 
        className={`h-6 w-6 text-white transition-transform duration-200 ${
          isOver && canDrop ? 'scale-110' : ''
        }`} 
      />
      
      {/* Visual feedback ring */}
      {isOver && canDrop && (
        <div className="absolute inset-0 rounded-full border-4 border-white border-opacity-50 animate-pulse" />
      )}
    </div>
  );
}
