import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/database/user-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    
    // Return user profile (already sanitized by auth middleware)
    return NextResponse.json({ user });
    
  } catch (error) {
    console.error('Get profile error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { username } = await request.json();
    
    // Validate input
    if (username !== undefined) {
      if (typeof username !== 'string') {
        return NextResponse.json(
          { error: 'Username must be a string' },
          { status: 400 }
        );
      }
      
      if (username.length < 2) {
        return NextResponse.json(
          { error: 'Username must be at least 2 characters long' },
          { status: 400 }
        );
      }
      
      if (username.length > 50) {
        return NextResponse.json(
          { error: 'Username must be less than 50 characters' },
          { status: 400 }
        );
      }
      
      // Check for valid characters (alphanumeric, underscore, hyphen)
      if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
        return NextResponse.json(
          { error: 'Username can only contain letters, numbers, underscores, and hyphens' },
          { status: 400 }
        );
      }
    }
    
    // Update user profile
    const userService = new UserService();
    const success = userService.updateUser(parseInt(user.id), {
      username: username || undefined
    });
    
    if (success) {
      // Get updated user data
      const updatedUser = userService.getUserById(parseInt(user.id));
      
      return NextResponse.json({ 
        message: 'Profile updated successfully',
        user: updatedUser
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to update profile' },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Update profile error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
