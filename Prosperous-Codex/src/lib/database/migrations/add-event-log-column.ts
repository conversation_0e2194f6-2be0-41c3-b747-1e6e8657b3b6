import Database from 'better-sqlite3';

/**
 * Migration to add event_log column to projects table
 */
export function addEventLogColumn(db: Database.Database) {
  
  try {
    // Check if column already exists
    const tableInfo = db.prepare("PRAGMA table_info(projects)").all() as any[];
    const hasEventLog = tableInfo.some(column => column.name === 'event_log');
    
    if (!hasEventLog) {
      console.log('Adding event_log column to projects table...');
      db.prepare("ALTER TABLE projects ADD COLUMN event_log TEXT").run();
      console.log('Successfully added event_log column to projects table');
    } else {
      console.log('event_log column already exists in projects table');
    }
  } catch (error) {
    console.error('Error adding event_log column:', error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  addEventLogColumn();
}
