/**
 * API Contract Validator for Task Master
 * 
 * Validates API request/response field consistency and generates contract tests
 * to ensure proper field name mapping between frontend and backend.
 */

// Unused imports removed - this file appears to be a placeholder

/**
 * API validation result
 */
export interface ApiValidationResult {
  valid: boolean;
  endpoint: string;
  errors: string[];
  warnings: string[];
  fieldIssues: FieldIssue[];
  suggestions: string[];
}

/**
 * Field issue details
 */
export interface FieldIssue {
  field: string;
  issue: string;
  severity: 'error' | 'warning';
  suggestion?: string;
  expectedMapping?: string;
}

/**
 * Test suite for API contracts
 */
export interface TestSuite {
  name: string;
  tests: ContractTest[];
  setup: string[];
  teardown: string[];
}

/**
 * Individual contract test
 */
export interface ContractTest {
  name: string;
  description: string;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  requestBody?: Record<string, unknown>;
  expectedFields: string[];
  testCode: string;
}

/**
 * Endpoint configuration for validation
 */
export interface EndpointConfig {
  path: string;
  method: string;
  requestFields?: string[];
  responseFields?: string[];
  entity: 'project' | 'task' | 'comment' | 'file' | 'team_member' | 'activity_log';
}

/**
 * API Contract Validator Class
 */
export class ApiValidator {
  private static readonly ENDPOINT_CONFIGS: EndpointConfig[] = [
    // Project endpoints
    { path: '/api/task-master/projects', method: 'GET', responseFields: ['id', 'title', 'description', 'fullDescription', 'eventLog', 'status', 'priority', 'progress', 'dueDate', 'createdBy', 'assignedTo', 'createdAt', 'updatedAt'], entity: 'project' },
    { path: '/api/task-master/projects', method: 'POST', requestFields: ['title', 'description', 'fullDescription', 'eventLog', 'status', 'priority', 'dueDate', 'assignedTo'], responseFields: ['id', 'title', 'description', 'fullDescription', 'eventLog', 'status', 'priority', 'progress', 'dueDate', 'createdBy', 'assignedTo', 'createdAt', 'updatedAt'], entity: 'project' },
    { path: '/api/task-master/projects/[id]', method: 'GET', responseFields: ['id', 'title', 'description', 'fullDescription', 'eventLog', 'status', 'priority', 'progress', 'dueDate', 'createdBy', 'assignedTo', 'createdAt', 'updatedAt'], entity: 'project' },
    { path: '/api/task-master/projects/[id]', method: 'PUT', requestFields: ['title', 'description', 'fullDescription', 'eventLog', 'status', 'priority', 'dueDate', 'assignedTo'], responseFields: ['id', 'title', 'description', 'fullDescription', 'eventLog', 'status', 'priority', 'progress', 'dueDate', 'createdBy', 'assignedTo', 'createdAt', 'updatedAt'], entity: 'project' },
    { path: '/api/task-master/projects/[id]', method: 'DELETE', entity: 'project' },

    // Task endpoints
    { path: '/api/task-master/projects/[id]/tasks', method: 'GET', responseFields: ['id', 'title', 'description', 'status', 'priority', 'progress', 'dueDate', 'projectId', 'parentTaskId', 'createdBy', 'assignedTo', 'createdAt', 'updatedAt'], entity: 'task' },
    { path: '/api/task-master/projects/[id]/tasks', method: 'POST', requestFields: ['title', 'description', 'status', 'priority', 'dueDate', 'assignedTo', 'parentTaskId'], responseFields: ['id', 'title', 'description', 'status', 'priority', 'progress', 'dueDate', 'projectId', 'parentTaskId', 'createdBy', 'assignedTo', 'createdAt', 'updatedAt'], entity: 'task' },
    { path: '/api/task-master/tasks/[id]', method: 'GET', responseFields: ['id', 'title', 'description', 'status', 'priority', 'progress', 'dueDate', 'projectId', 'parentTaskId', 'createdBy', 'assignedTo', 'createdAt', 'updatedAt'], entity: 'task' },
    { path: '/api/task-master/tasks/[id]', method: 'PUT', requestFields: ['title', 'description', 'status', 'priority', 'dueDate', 'assignedTo', 'parentTaskId'], responseFields: ['id', 'title', 'description', 'status', 'priority', 'progress', 'dueDate', 'projectId', 'parentTaskId', 'createdBy', 'assignedTo', 'createdAt', 'updatedAt'], entity: 'task' },
    { path: '/api/task-master/tasks/[id]', method: 'DELETE', entity: 'task' },

    // Comment endpoints
    { path: '/api/task-master/tasks/[id]/comments', method: 'GET', responseFields: ['id', 'content', 'parentCommentId', 'createdBy', 'createdAt', 'updatedAt'], entity: 'comment' },
    { path: '/api/task-master/tasks/[id]/comments', method: 'POST', requestFields: ['content', 'parentCommentId'], responseFields: ['id', 'content', 'parentCommentId', 'createdBy', 'createdAt', 'updatedAt'], entity: 'comment' },

    // File endpoints
    { path: '/api/task-master/projects/[id]/files', method: 'GET', responseFields: ['id', 'fileName', 'fileSize', 'mimeType', 'uploadedBy', 'uploadedAt'], entity: 'file' },
    { path: '/api/task-master/projects/[id]/files', method: 'POST', requestFields: ['file'], responseFields: ['id', 'fileName', 'fileSize', 'mimeType', 'uploadedBy', 'uploadedAt'], entity: 'file' },

    // Team member endpoints
    { path: '/api/task-master/projects/[id]/team', method: 'GET', responseFields: ['id', 'userId', 'role', 'joinedAt'], entity: 'team_member' },
    { path: '/api/task-master/projects/[id]/team', method: 'POST', requestFields: ['email', 'role'], responseFields: ['id', 'userId', 'role', 'joinedAt'], entity: 'team_member' },
  ];

  /**
   * Validate request fields for an endpoint
   */
  static validateRequestFields(endpoint: string, fields: object, method: string = 'POST'): ApiValidationResult {
    const config = this.findEndpointConfig(endpoint, method);
    const fieldNames = Object.keys(fields);
    const errors: string[] = [];
    const warnings: string[] = [];
    const fieldIssues: FieldIssue[] = [];
    const suggestions: string[] = [];

    if (!config) {
      errors.push(`Unknown endpoint: ${endpoint}`);
      return { valid: false, endpoint, errors, warnings, fieldIssues, suggestions };
    }

    if (!config.requestFields) {
      warnings.push(`No request field validation configured for ${endpoint}`);
      return { valid: true, endpoint, errors, warnings, fieldIssues, suggestions };
    }

    // Check for missing required fields
    for (const requiredField of config.requestFields) {
      if (!fieldNames.includes(requiredField)) {
        fieldIssues.push({
          field: requiredField,
          issue: `Required field missing from request`,
          severity: 'error',
        });
        errors.push(`Missing required field: ${requiredField}`);
      }
    }

    // Check for unexpected fields
    for (const fieldName of fieldNames) {
      if (!config.requestFields.includes(fieldName)) {
        fieldIssues.push({
          field: fieldName,
          issue: `Unexpected field in request`,
          severity: 'warning',
          suggestion: `Remove field or add to endpoint configuration`,
        });
        warnings.push(`Unexpected field: ${fieldName}`);
      }
    }

    // Check field name consistency
    const mappingValidation = this.validateFieldMappings(fieldNames, 'request');
    errors.push(...mappingValidation.errors);
    warnings.push(...mappingValidation.warnings);
    fieldIssues.push(...mappingValidation.fieldIssues);
    suggestions.push(...mappingValidation.suggestions);

    return {
      valid: errors.length === 0,
      endpoint,
      errors,
      warnings,
      fieldIssues,
      suggestions,
    };
  }

  /**
   * Validate response fields for an endpoint
   */
  static validateResponseFields(endpoint: string, fields: object, method: string = 'GET'): ApiValidationResult {
    const config = this.findEndpointConfig(endpoint, method);
    const fieldNames = Object.keys(fields);
    const errors: string[] = [];
    const warnings: string[] = [];
    const fieldIssues: FieldIssue[] = [];
    const suggestions: string[] = [];

    if (!config) {
      errors.push(`Unknown endpoint: ${endpoint}`);
      return { valid: false, endpoint, errors, warnings, fieldIssues, suggestions };
    }

    if (!config.responseFields) {
      warnings.push(`No response field validation configured for ${endpoint}`);
      return { valid: true, endpoint, errors, warnings, fieldIssues, suggestions };
    }

    // Check for missing expected fields
    for (const expectedField of config.responseFields) {
      if (!fieldNames.includes(expectedField)) {
        fieldIssues.push({
          field: expectedField,
          issue: `Expected field missing from response`,
          severity: 'warning',
        });
        warnings.push(`Missing expected field: ${expectedField}`);
      }
    }

    // Check for unexpected fields (less strict for responses)
    for (const fieldName of fieldNames) {
      if (!config.responseFields.includes(fieldName)) {
        fieldIssues.push({
          field: fieldName,
          issue: `Unexpected field in response`,
          severity: 'warning',
          suggestion: `Add to endpoint configuration if intentional`,
        });
        warnings.push(`Unexpected field: ${fieldName}`);
      }
    }

    // Check field name consistency
    const mappingValidation = this.validateFieldMappings(fieldNames, 'response');
    errors.push(...mappingValidation.errors);
    warnings.push(...mappingValidation.warnings);
    fieldIssues.push(...mappingValidation.fieldIssues);
    suggestions.push(...mappingValidation.suggestions);

    return {
      valid: errors.length === 0,
      endpoint,
      errors,
      warnings,
      fieldIssues,
      suggestions,
    };
  }

  /**
   * Generate contract tests for all endpoints
   */
  static generateContractTests(): TestSuite {
    const tests: ContractTest[] = [];

    for (const config of this.ENDPOINT_CONFIGS) {
      // Generate request validation test
      if (config.requestFields) {
        tests.push({
          name: `${config.method} ${config.path} - Request Validation`,
          description: `Validates request field consistency for ${config.path}`,
          endpoint: config.path,
          method: config.method as any,
          requestBody: this.generateSampleRequestBody(config.requestFields),
          expectedFields: config.requestFields,
          testCode: this.generateRequestTestCode(config),
        });
      }

      // Generate response validation test
      if (config.responseFields) {
        tests.push({
          name: `${config.method} ${config.path} - Response Validation`,
          description: `Validates response field consistency for ${config.path}`,
          endpoint: config.path,
          method: config.method as any,
          expectedFields: config.responseFields,
          testCode: this.generateResponseTestCode(config),
        });
      }
    }

    return {
      name: 'Task Master API Contract Tests',
      tests,
      setup: [
        'import { ApiValidator } from "@/lib/task-master/api-validator";',
        'import { describe, it, expect } from "vitest";',
      ],
      teardown: [],
    };
  }

  /**
   * Find endpoint configuration by path and method
   */
  private static findEndpointConfig(endpoint: string, method?: string): EndpointConfig | undefined {
    return this.ENDPOINT_CONFIGS.find(config => {
      // Handle dynamic routes like [id]
      const pattern = config.path.replace(/\[([^\]]+)\]/g, '[^/]+');
      const regex = new RegExp(`^${pattern}$`);
      const pathMatches = regex.test(endpoint);
      const methodMatches = !method || config.method === method;
      return pathMatches && methodMatches;
    });
  }

  /**
   * Validate field name mappings
   */
  private static validateFieldMappings(fieldNames: string[], context: 'request' | 'response'): {
    errors: string[];
    warnings: string[];
    fieldIssues: FieldIssue[];
    suggestions: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const fieldIssues: FieldIssue[] = [];
    const suggestions: string[] = [];

    for (const fieldName of fieldNames) {
      // Check if field should be mapped
      if (this.shouldBeMapped(fieldName)) {
        const hasMapping = fieldName in FIELD_MAPPINGS;
        
        if (!hasMapping) {
          fieldIssues.push({
            field: fieldName,
            issue: `Field appears to need mapping but no mapping defined`,
            severity: 'warning',
            suggestion: `Add mapping for ${fieldName}`,
            expectedMapping: this.suggestDbFieldName(fieldName),
          });
          warnings.push(`No mapping defined for ${fieldName}`);
          suggestions.push(`Add mapping: ${fieldName} -> ${this.suggestDbFieldName(fieldName)}`);
        }
      }

      // Check for common naming issues
      if (fieldName.includes('_') && context === 'request') {
        fieldIssues.push({
          field: fieldName,
          issue: `API field uses snake_case instead of camelCase`,
          severity: 'error',
          suggestion: `Use camelCase: ${this.suggestApiFieldName(fieldName)}`,
        });
        errors.push(`API field ${fieldName} should use camelCase`);
      }
    }

    return { errors, warnings, fieldIssues, suggestions };
  }

  /**
   * Check if field should be mapped
   */
  private static shouldBeMapped(fieldName: string): boolean {
    // Skip common fields that don't need mapping
    const skipFields = ['id', 'title', 'description', 'status', 'priority', 'progress', 'role', 'content'];
    if (skipFields.includes(fieldName)) {
      return false;
    }

    // Check for camelCase that should map to snake_case
    return /^[a-z][a-zA-Z]*[A-Z]/.test(fieldName);
  }

  /**
   * Suggest database field name
   */
  private static suggestDbFieldName(apiField: string): string {
    return apiField.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * Suggest API field name
   */
  private static suggestApiFieldName(dbField: string): string {
    return dbField.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  /**
   * Generate sample request body for testing
   */
  private static generateSampleRequestBody(fields: string[]): Record<string, unknown> {
    const body: Record<string, unknown> = {};
    
    for (const field of fields) {
      switch (field) {
        case 'title':
          body[field] = 'Sample Title';
          break;
        case 'description':
          body[field] = 'Sample description';
          break;
        case 'status':
          body[field] = 'todo';
          break;
        case 'priority':
          body[field] = 'medium';
          break;
        case 'email':
          body[field] = '<EMAIL>';
          break;
        case 'role':
          body[field] = 'member';
          break;
        case 'content':
          body[field] = 'Sample content';
          break;
        default:
          body[field] = `sample_${field}`;
      }
    }
    
    return body;
  }

  /**
   * Generate test code for request validation
   */
  private static generateRequestTestCode(config: EndpointConfig): string {
    return `
it('validates request fields for ${config.method} ${config.path}', () => {
  const sampleRequest = ${JSON.stringify(this.generateSampleRequestBody(config.requestFields || []), null, 2)};
  const result = ApiValidator.validateRequestFields('${config.path}', sampleRequest);
  
  expect(result.valid).toBe(true);
  expect(result.errors).toHaveLength(0);
  
  // Check that all required fields are present
  ${config.requestFields?.map(field => `expect(sampleRequest).toHaveProperty('${field}');`).join('\n  ') || ''}
});`;
  }

  /**
   * Generate test code for response validation
   */
  private static generateResponseTestCode(config: EndpointConfig): string {
    return `
it('validates response fields for ${config.method} ${config.path}', async () => {
  // This test would need to be implemented with actual API calls
  // or mock responses based on your testing setup
  
  const mockResponse = {
    ${config.responseFields?.map(field => `${field}: 'mock_value'`).join(',\n    ') || ''}
  };
  
  const result = ApiValidator.validateResponseFields('${config.path}', mockResponse);
  
  expect(result.valid).toBe(true);
  expect(result.errors).toHaveLength(0);
});`;
  }
}

/**
 * Utility function to validate API contract
 */
export function validateApiContract(endpoint: string, data: object, type: 'request' | 'response', method?: string): ApiValidationResult {
  if (type === 'request') {
    return ApiValidator.validateRequestFields(endpoint, data, method || 'POST');
  } else {
    return ApiValidator.validateResponseFields(endpoint, data, method || 'GET');
  }
}

/**
 * Middleware to add API validation to routes
 */
export function withApiValidation(endpoint: string) {
  return {
    validateRequest: (data: object) => ApiValidator.validateRequestFields(endpoint, data),
    validateResponse: (data: object) => ApiValidator.validateResponseFields(endpoint, data),
  };
}
