<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Features Section</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500&display=swap" rel="stylesheet">
</head>
<body class="bg-[#fafafa] text-gray-900 font-['Inter']">
  <div class="container mx-auto px-4 py-12 max-w-[800px]">
    <h2 class="text-2xl font-light tracking-tight text-center mb-10">Key Features</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Feature 1 (2/3 width) -->
      <div class="md:col-span-2 bg-white border border-gray-100 rounded-lg overflow-hidden transition-all hover:border-[#5E6AD2]/30 shadow-[0_2px_8px_rgba(0,0,0,0.04)] hover:shadow-[0_4px_12px_rgba(0,0,0,0.06)]">
        <div class="aspect-w-16 aspect-h-9 bg-[#F8F9FE] p-4">
          <div class="w-full h-full">
            <!-- Placeholder UI -->
            <div class="flex space-x-2">
              <div class="w-24 h-6 bg-[#5E6AD2]/10 rounded"></div>
              <div class="w-16 h-6 bg-[#5E6AD2]/10 rounded"></div>
            </div>
            <div class="mt-3 flex space-x-1">
              <div class="w-3 h-3 bg-[#5E6AD2]/30 rounded-full"></div>
              <div class="w-3 h-3 bg-[#5E6AD2]/20 rounded-full"></div>
              <div class="w-3 h-3 bg-[#5E6AD2]/10 rounded-full"></div>
            </div>
            <div class="mt-3 flex flex-col space-y-1">
              <div class="w-full h-2 bg-[#5E6AD2]/10 rounded"></div>
              <div class="w-5/6 h-2 bg-[#5E6AD2]/10 rounded"></div>
              <div class="w-4/6 h-2 bg-[#5E6AD2]/10 rounded"></div>
            </div>
          </div>
        </div>
        <div class="p-5 flex flex-col">
          <div class="flex items-center space-x-2 mb-3">
            <svg class="w-4 h-4 text-[#5E6AD2]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            <h3 class="text-base font-medium tracking-tight">Lightning Fast Performance</h3>
          </div>
          <p class="text-gray-600 mb-3 text-xs leading-relaxed">
            Experience sub-second load times with our optimized architecture. Our platform uses edge caching, lazy loading, and intelligent prefetching to deliver content instantly.
          </p>
          <div class="flex text-[10px] gap-1 mt-auto">
            <span class="px-1.5 py-0.5 rounded-full border border-[#5E6AD2]/20 text-[#5E6AD2]">99.9% Uptime</span>
            <span class="px-1.5 py-0.5 rounded-full border border-[#5E6AD2]/20 text-[#5E6AD2]">Global CDN</span>
          </div>
        </div>
      </div>
      
      <!-- Feature 2 (1/3 width) -->
      <div class="bg-white border border-gray-100 rounded-lg overflow-hidden transition-all hover:border-[#5E6AD2]/30 shadow-[0_2px_8px_rgba(0,0,0,0.04)] hover:shadow-[0_4px_12px_rgba(0,0,0,0.06)]">
        <div class="aspect-w-16 aspect-h-9 bg-[#F8F9FE] p-4">
          <div class="w-full h-full">
            <!-- Placeholder UI -->
            <div class="flex justify-center items-center h-full">
              <div class="w-12 h-12 bg-[#5E6AD2]/20 rounded-full flex items-center justify-center">
                <div class="w-6 h-6 bg-[#5E6AD2]/40 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="p-5 flex flex-col">
          <div class="flex items-center space-x-2 mb-3">
            <svg class="w-4 h-4 text-[#5E6AD2]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
            <h3 class="text-base font-medium tracking-tight">Enterprise Security</h3>
          </div>
          <p class="text-gray-600 mb-3 text-xs leading-relaxed">
            SOC 2 Type II certified with AES-256 encryption at rest and in transit.
          </p>
          <div class="flex text-[10px] gap-1 mt-auto">
            <span class="px-1.5 py-0.5 rounded-full border border-[#5E6AD2]/20 text-[#5E6AD2]">GDPR</span>
          </div>
        </div>
      </div>
      
      <!-- Feature 3 (1/3 width) -->
      <div class="bg-white border border-gray-100 rounded-lg overflow-hidden transition-all hover:border-[#5E6AD2]/30 shadow-[0_2px_8px_rgba(0,0,0,0.04)] hover:shadow-[0_4px_12px_rgba(0,0,0,0.06)]">
        <div class="aspect-w-16 aspect-h-9 bg-[#F8F9FE] p-4">
          <div class="w-full h-full">
            <!-- Placeholder UI -->
            <div class="flex flex-col space-y-2">
              <div class="w-full h-3 bg-[#5E6AD2]/10 rounded"></div>
              <div class="grid grid-cols-3 gap-1">
                <div class="h-6 bg-[#5E6AD2]/20 rounded"></div>
                <div class="h-6 bg-[#5E6AD2]/15 rounded"></div>
                <div class="h-6 bg-[#5E6AD2]/10 rounded"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="p-5 flex flex-col">
          <div class="flex items-center space-x-2 mb-3">
            <svg class="w-4 h-4 text-[#5E6AD2]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <h3 class="text-base font-medium tracking-tight">Customizable</h3>
          </div>
          <p class="text-gray-600 mb-3 text-xs leading-relaxed">
            Build your own workflows with our visual editor. Create custom fields and templates.
          </p>
          <div class="flex text-[10px] gap-1 mt-auto">
            <span class="px-1.5 py-0.5 rounded-full border border-[#5E6AD2]/20 text-[#5E6AD2]">No-code</span>
          </div>
        </div>
      </div>
      
      <!-- Feature 4 (2/3 width) -->
      <div class="md:col-span-2 bg-white border border-gray-100 rounded-lg overflow-hidden transition-all hover:border-[#5E6AD2]/30 shadow-[0_2px_8px_rgba(0,0,0,0.04)] hover:shadow-[0_4px_12px_rgba(0,0,0,0.06)]">
        <div class="aspect-w-16 aspect-h-9 bg-[#F8F9FE] p-4">
          <div class="w-full h-full">
            <!-- Placeholder UI -->
            <div class="grid grid-cols-3 gap-2 h-full">
              <div class="col-span-2 bg-[#5E6AD2]/10 rounded p-2">
                <div class="w-1/2 h-2 bg-[#5E6AD2]/20 rounded mb-1"></div>
                <div class="w-full h-1 bg-[#5E6AD2]/20 rounded mb-1"></div>
                <div class="w-3/4 h-1 bg-[#5E6AD2]/20 rounded"></div>
              </div>
              <div class="flex flex-col space-y-2">
                <div class="flex-1 bg-[#5E6AD2]/20 rounded"></div>
                <div class="flex-1 bg-[#5E6AD2]/15 rounded"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="p-5 flex flex-col">
          <div class="flex items-center space-x-2 mb-3">
            <svg class="w-4 h-4 text-[#5E6AD2]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <h3 class="text-base font-medium tracking-tight">Collaborative Workspace</h3>
          </div>
          <p class="text-gray-600 mb-3 text-xs leading-relaxed">
            Work together in real-time with presence indicators, cursor tracking, and live updates. Comments and @mentions keep everyone in the loop.
          </p>
          <div class="flex flex-wrap text-[10px] gap-1 mt-auto">
            <span class="px-1.5 py-0.5 rounded-full border border-[#5E6AD2]/20 text-[#5E6AD2]">Real-time</span>
            <span class="px-1.5 py-0.5 rounded-full border border-[#5E6AD2]/20 text-[#5E6AD2]">Version History</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>



