import { NextRequest, NextResponse } from 'next/server';
import { TaskMasterService } from '@/lib/database/task-master-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('project_id');
    
    const taskMasterService = new TaskMasterService();
    
    let activity;
    
    if (projectId) {
      // Get activity for specific project
      const projectIdNum = parseInt(projectId);
      
      if (isNaN(projectIdNum)) {
        return NextResponse.json(
          { error: 'Invalid project ID' },
          { status: 400 }
        );
      }
      
      // Check if user has access to this project
      const userProjects = taskMasterService.getUserProjects(parseInt(user.id));
      const hasAccess = userProjects.some(p => p.id === projectIdNum);
      
      if (!hasAccess) {
        return NextResponse.json(
          { error: 'Access denied' },
          { status: 403 }
        );
      }
      
      activity = taskMasterService.getProjectActivity(projectIdNum);
    } else {
      // Get all activity for user's projects
      activity = taskMasterService.getUserActivity(parseInt(user.id));
    }
    
    return NextResponse.json({ activity });
    
  } catch (error) {
    console.error('Get activity error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
