/**
 * End-to-End Test for Team Member Addition Workflow
 * 
 * This test verifies the complete team member addition workflow that previously
 * had bugs related to field mapping between snake_case and camelCase.
 * 
 * Tests cover:
 * - User dropdown population
 * - Role selection
 * - Member addition with proper field mapping
 * - Team list refresh after addition
 * - Field mapping accuracy (team<PERSON><PERSON><PERSON> vs team_members)
 * - Proper display of member names (no 'member.name is undefined')
 * - 'No team members yet' message only when team is actually empty
 */

import { test, expect } from '@playwright/test';

// Test data
const TEST_PROJECT = {
  title: 'Team Member Test Project',
  description: 'Project for testing team member functionality'
};

const TEST_USER = {
  email: '<EMAIL>',
  username: 'testuser',
  role: 'member'
};

const MODERATOR_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'moderator123'
};

test.describe('Team Member Addition Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Login as moderator
    await page.goto('/auth/signin');
    await page.fill('input[name="email"]', MODERATOR_CREDENTIALS.email);
    await page.fill('input[name="password"]', MODERATOR_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    
    // Wait for dashboard
    await page.waitForURL('/dashboard');
    
    // Navigate to Task Master
    await page.goto('/task-master');
    await page.waitForLoadState('networkidle');
  });

  test('should complete full team member addition workflow', async ({ page }) => {
    // Step 1: Create a test project
    await page.click('button:has-text("Create Project")');
    await page.fill('input[placeholder*="title"]', TEST_PROJECT.title);
    await page.fill('textarea[placeholder*="description"]', TEST_PROJECT.description);
    await page.click('button:has-text("Create")');
    
    // Wait for project to be created and visible
    await page.waitForSelector(`text=${TEST_PROJECT.title}`);
    
    // Step 2: Open project details
    await page.click(`text=${TEST_PROJECT.title}`);
    await page.waitForSelector('[data-testid="project-flow-board"]', { timeout: 10000 });
    
    // Step 3: Navigate to team tab
    await page.click('button:has-text("Team")');
    await page.waitForSelector('text=Team Members');
    
    // Step 4: Verify initial state shows "No team members yet"
    await expect(page.locator('text=No team members yet')).toBeVisible();
    
    // Step 5: Click "Add Member" button
    await page.click('button:has-text("Add Member")');
    await page.waitForSelector('text=Select User');
    
    // Step 6: Test user dropdown population
    await page.click('[data-testid="user-select-trigger"]');
    
    // Verify dropdown loads users
    await page.waitForSelector('[data-testid="user-select-content"]');
    const userOptions = await page.locator('[data-testid="user-select-item"]').count();
    expect(userOptions).toBeGreaterThan(0);
    
    // Step 7: Select a test user
    await page.click(`[data-testid="user-select-item"]:has-text("${TEST_USER.email}")`);
    
    // Step 8: Test role selection
    await page.click('[data-testid="role-select-trigger"]');
    await page.click(`[data-testid="role-select-item"]:has-text("${TEST_USER.role}")`);
    
    // Step 9: Add the team member
    await page.click('button:has-text("Add Member")');
    
    // Step 10: Verify API call uses camelCase fields
    const addMemberRequest = page.waitForRequest(request => 
      request.url().includes('/api/task-master/projects/') && 
      request.url().includes('/team') &&
      request.method() === 'POST'
    );
    
    await addMemberRequest;
    const requestBody = await (await addMemberRequest).postDataJSON();
    
    // Verify request uses camelCase (userEmail, not user_email)
    expect(requestBody).toHaveProperty('userEmail', TEST_USER.email);
    expect(requestBody).toHaveProperty('role', TEST_USER.role);
    expect(requestBody).not.toHaveProperty('user_email');
    
    // Step 11: Wait for team list to refresh
    await page.waitForSelector('text=Team member added successfully');
    await page.waitForSelector(`text=${TEST_USER.email}`);
    
    // Step 12: Verify "No team members yet" message is gone
    await expect(page.locator('text=No team members yet')).not.toBeVisible();
    
    // Step 13: Verify team member is displayed correctly
    const memberCard = page.locator(`[data-testid="team-member-card"]:has-text("${TEST_USER.email}")`);
    await expect(memberCard).toBeVisible();
    
    // Step 14: Verify member name is displayed (not undefined)
    const memberName = memberCard.locator('[data-testid="member-name"]');
    const nameText = await memberName.textContent();
    expect(nameText).toBeTruthy();
    expect(nameText).not.toBe('undefined');
    expect(nameText).not.toBe('');
    
    // Step 15: Verify member role is displayed
    await expect(memberCard.locator(`text=${TEST_USER.role}`)).toBeVisible();
    
    // Step 16: Verify member email is displayed
    await expect(memberCard.locator(`text=${TEST_USER.email}`)).toBeVisible();
    
    // Step 17: Verify "Added" date is displayed
    await expect(memberCard.locator('text=Added')).toBeVisible();
  });

  test('should handle field mapping correctly in API responses', async ({ page }) => {
    // Create project and add team member (abbreviated setup)
    await page.click('button:has-text("Create Project")');
    await page.fill('input[placeholder*="title"]', 'API Test Project');
    await page.click('button:has-text("Create")');
    await page.click('text=API Test Project');
    await page.click('button:has-text("Team")');
    
    // Monitor API response for team members
    const teamMembersResponse = page.waitForResponse(response => 
      response.url().includes('/api/task-master/projects/') && 
      response.url().includes('includeTeamMembers=true')
    );
    
    await page.reload();
    const response = await teamMembersResponse;
    const responseData = await response.json();
    
    // Verify response uses camelCase fields
    if (responseData.data && responseData.data.teamMembers) {
      const teamMembers = responseData.data.teamMembers;
      if (teamMembers.length > 0) {
        const member = teamMembers[0];
        
        // Verify camelCase fields are present
        expect(member).toHaveProperty('userId');
        expect(member).toHaveProperty('projectId');
        expect(member).toHaveProperty('addedAt');
        expect(member).toHaveProperty('name');
        
        // Verify snake_case fields are NOT present
        expect(member).not.toHaveProperty('user_id');
        expect(member).not.toHaveProperty('project_id');
        expect(member).not.toHaveProperty('added_at');
      }
    }
  });

  test('should refresh team list after member addition', async ({ page }) => {
    // Setup project
    await page.click('button:has-text("Create Project")');
    await page.fill('input[placeholder*="title"]', 'Refresh Test Project');
    await page.click('button:has-text("Create")');
    await page.click('text=Refresh Test Project');
    await page.click('button:has-text("Team")');
    
    // Count initial team members
    const initialCount = await page.locator('[data-testid="team-member-card"]').count();
    
    // Add a team member
    await page.click('button:has-text("Add Member")');
    await page.click('[data-testid="user-select-trigger"]');
    await page.click('[data-testid="user-select-item"]').first();
    await page.click('button:has-text("Add Member")');
    
    // Wait for success message
    await page.waitForSelector('text=Team member added successfully');
    
    // Verify team list has refreshed with new member
    const newCount = await page.locator('[data-testid="team-member-card"]').count();
    expect(newCount).toBe(initialCount + 1);
  });

  test('should handle team member removal correctly', async ({ page }) => {
    // Setup project with team member
    await page.click('button:has-text("Create Project")');
    await page.fill('input[placeholder*="title"]', 'Removal Test Project');
    await page.click('button:has-text("Create")');
    await page.click('text=Removal Test Project');
    await page.click('button:has-text("Team")');
    
    // Add a team member first
    await page.click('button:has-text("Add Member")');
    await page.click('[data-testid="user-select-trigger"]');
    await page.click('[data-testid="user-select-item"]').first();
    await page.click('button:has-text("Add Member")');
    await page.waitForSelector('text=Team member added successfully');
    
    // Remove the team member
    await page.click('[data-testid="remove-member-button"]').first();
    await page.click('button:has-text("OK")'); // Confirm removal
    
    // Verify member is removed and "No team members yet" appears
    await page.waitForSelector('text=Team member removed successfully');
    await expect(page.locator('text=No team members yet')).toBeVisible();
  });

  test('should validate field mapping consistency', async ({ page }) => {
    // This test ensures that the field mapping between frontend and backend is consistent
    
    // Monitor all API calls during team member operations
    const apiCalls: any[] = [];
    
    page.on('request', request => {
      if (request.url().includes('/api/task-master/')) {
        apiCalls.push({
          url: request.url(),
          method: request.method(),
          postData: request.postData()
        });
      }
    });
    
    page.on('response', async response => {
      if (response.url().includes('/api/task-master/')) {
        try {
          const data = await response.json();
          apiCalls.push({
            url: response.url(),
            method: 'RESPONSE',
            data: data
          });
        } catch (e) {
          // Non-JSON response
        }
      }
    });
    
    // Perform team member operations
    await page.click('button:has-text("Create Project")');
    await page.fill('input[placeholder*="title"]', 'Field Mapping Test');
    await page.click('button:has-text("Create")');
    await page.click('text=Field Mapping Test');
    await page.click('button:has-text("Team")');
    
    // Add team member
    await page.click('button:has-text("Add Member")');
    await page.click('[data-testid="user-select-trigger"]');
    await page.click('[data-testid="user-select-item"]').first();
    await page.click('button:has-text("Add Member")');
    await page.waitForSelector('text=Team member added successfully');
    
    // Analyze API calls for field naming consistency
    const teamApiCalls = apiCalls.filter(call => 
      call.url.includes('/team') || call.url.includes('includeTeamMembers')
    );
    
    expect(teamApiCalls.length).toBeGreaterThan(0);
    
    // Verify no snake_case fields in requests/responses
    for (const call of teamApiCalls) {
      if (call.postData) {
        const postData = JSON.parse(call.postData);
        expect(postData).not.toHaveProperty('user_email');
        expect(postData).not.toHaveProperty('user_id');
        expect(postData).not.toHaveProperty('project_id');
      }
      
      if (call.data && call.data.teamMembers) {
        for (const member of call.data.teamMembers) {
          expect(member).not.toHaveProperty('user_id');
          expect(member).not.toHaveProperty('project_id');
          expect(member).not.toHaveProperty('added_at');
        }
      }
    }
  });
});
