"use client";

import React, { useState, useEffect } from 'react';
import { UserPlus, X, Mail, User, Crown, Shield, Calendar } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { TeamMember } from '@/lib/task-master/types';

interface User {
  id: string;
  email: string;
  username?: string;
  role: string;
  isActive?: boolean;
}

interface TemporaryTeamMember {
  tempId: string;
  projectId: number;
  userId: number;
  role: string;
  addedBy?: number;
  username?: string;
  email?: string;
  name: string;
  userEmail: string;
  isTemporary: true;
}

interface TeamManagementProps {
  projectId: string;
  teamMembers: TeamMember[];
  pendingTeamMembers?: TemporaryTeamMember[];
  isProjectOwner: boolean;
  onTeamUpdated?: () => void;
  onAddTemporaryMember?: (userEmail: string, role: string, userData: { username?: string; email: string; id: string }) => void;
}

export default function TeamManagement({
  projectId,
  teamMembers,
  pendingTeamMembers = [],
  isProjectOwner,
  onTeamUpdated,
  onAddTemporaryMember
}: TeamManagementProps) {
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState('');
  const [newMemberRole, setNewMemberRole] = useState('member');
  const [isLoading, setIsLoading] = useState(false);
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const { toast } = useToast();

  // Fetch available users when component mounts or team members change
  useEffect(() => {
    fetchAvailableUsers();
  }, [teamMembers, pendingTeamMembers]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchAvailableUsers = async () => {
    setIsLoadingUsers(true);
    try {
      const response = await fetch('/api/users');
      const data = await response.json();

      if (response.ok) {
        // Filter out users who are already team members or pending team members
        const existingMemberIds = teamMembers.map(member => member.userId?.toString());
        const pendingMemberIds = pendingTeamMembers.map(member => member.userId?.toString());
        const allExistingIds = [...existingMemberIds, ...pendingMemberIds];

        const filteredUsers = data.users.filter((user: User) =>
          !allExistingIds.includes(user.id)
        );

        // Sort users alphabetically by username or email
        const sortedUsers = filteredUsers.sort((a: User, b: User) => {
          const nameA = (a.username || a.email).toLowerCase();
          const nameB = (b.username || b.email).toLowerCase();
          return nameA.localeCompare(nameB);
        });

        setAvailableUsers(sortedUsers);
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch available users",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoadingUsers(false);
    }
  };

  const handleAddMember = async () => {
    if (!selectedUserId) {
      toast({
        title: "Error",
        description: "Please select a user",
        variant: "destructive"
      });
      return;
    }

    const selectedUser = availableUsers.find(user => user.id === selectedUserId);
    if (!selectedUser) {
      toast({
        title: "Error",
        description: "Selected user not found",
        variant: "destructive"
      });
      return;
    }

    // Check if we're in temporary mode (Project Flow Board) or immediate mode
    if (onAddTemporaryMember) {
      // Temporary mode: Add to temporary state only
      onAddTemporaryMember(selectedUser.email, newMemberRole, selectedUser);

      // Reset form
      setSelectedUserId('');
      setNewMemberRole('member');
      setIsAddingMember(false);

      // Refresh available users list to remove the added user
      fetchAvailableUsers();

      console.log('✅ Team member added to temporary state:', selectedUser.email);
      return;
    }

    // Immediate mode: Make API call directly (backward compatibility)
    setIsLoading(true);

    try {
      const response = await fetch(`/api/task-master/projects/${projectId}/team`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userEmail: selectedUser.email,
          role: newMemberRole
        })
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Success",
          description: "Team member added successfully"
        });
        setSelectedUserId('');
        setNewMemberRole('member');
        setIsAddingMember(false);
        if (onTeamUpdated) {
          onTeamUpdated();
        }
        // Refresh available users list
        fetchAvailableUsers();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to add team member",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveMember = async (memberId: number, memberEmail: string) => {
    if (!confirm(`Are you sure you want to remove ${memberEmail} from the team?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/task-master/projects/${projectId}/team/${memberId}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Success",
          description: "Team member removed successfully"
        });
        onTeamUpdated();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to remove team member",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return <Crown className="h-3 w-3" />;
      case 'admin':
        return <Shield className="h-3 w-3" />;
      default:
        return <User className="h-3 w-3" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'admin':
        return 'bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-base font-semibold text-black dark:text-white">
          Team Members
        </h3>
        {isProjectOwner && (
          <Button
            size="sm"
            onClick={() => setIsAddingMember(true)}
            className="bg-[#5E6AD2] hover:bg-[#5E6AD2]/90 dark:bg-[#6E56CF] dark:hover:bg-[#6E56CF]/90"
          >
            <UserPlus className="h-3 w-3 mr-1" />
            Add Member
          </Button>
        )}
      </div>

      {/* Add member form */}
      {isAddingMember && (
        <div className="p-3 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 block">
                Select User
              </label>
              <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                <SelectTrigger className="text-sm w-full">
                  <SelectValue placeholder={isLoadingUsers ? "Loading users..." : "Select a user"} />
                </SelectTrigger>
                <SelectContent className="z-[10000]">
                  {availableUsers.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      <div className="flex items-center gap-2">
                        <span>{user.username || user.email}</span>
                        <span className="text-xs text-gray-500">({user.email})</span>
                      </div>
                    </SelectItem>
                  ))}
                  {availableUsers.length === 0 && !isLoadingUsers && (
                    <SelectItem value="" disabled>
                      No available users
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 block">
                Role
              </label>
              <Select value={newMemberRole} onValueChange={setNewMemberRole}>
                <SelectTrigger className="text-sm w-full">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent className="z-[10000]">
                  <SelectItem value="member">Member</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={handleAddMember}
                disabled={isLoading || isLoadingUsers || !selectedUserId}
              >
                {isLoading ? 'Adding...' : 'Add Member'}
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setIsAddingMember(false);
                  setSelectedUserId('');
                }}
                disabled={isLoading}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Team members list */}
      <div className="space-y-3">
        {(() => {
          // Combine real and pending team members for display
          const allMembers = [
            ...teamMembers.map(member => ({ ...member, isTemporary: false })),
            ...pendingTeamMembers.map(member => ({ ...member, isTemporary: true }))
          ];

          if (allMembers.length === 0) {
            return (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                <p className="text-sm">No team members yet</p>
              </div>
            );
          }

          return allMembers.map((member) => (
            <div
              key={member.isTemporary ? member.tempId : member.id}
              className={`relative flex items-center gap-3 p-3 rounded-lg border bg-white dark:bg-[#1A1A1A] ${
                member.isTemporary
                  ? 'border-dashed border-[#5E6AD2] dark:border-[#6E56CF] bg-[#5E6AD2]/5 dark:bg-[#6E56CF]/5'
                  : 'border-gray-200 dark:border-[#2A2A2A]'
              }`}
            >
              <Avatar className="h-8 w-8">
                <div className="h-full w-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                  <User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                </div>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center">
                  <p className="text-sm font-medium text-black dark:text-white truncate">
                    {member.username || member.email}
                  </p>
                </div>
                <div className="flex flex-col gap-1 text-xs text-gray-600 dark:text-gray-400">
                  <div className="flex items-center gap-2">
                    <Mail className="h-3 w-3" />
                    <span>{member.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-3 w-3" />
                    <span>
                      {member.isTemporary
                        ? 'Pending addition'
                        : `Added ${formatDate(member.addedAt || '')}`
                      }
                    </span>
                  </div>
                </div>
              </div>
              <div className="absolute left-[75%] transform -translate-x-1/2">
                <div className="flex flex-col items-center gap-1">
                  {member.isTemporary && (
                    <Badge
                      variant="outline"
                      className="text-xs border-[#5E6AD2] dark:border-[#6E56CF] text-[#5E6AD2] dark:text-[#6E56CF]"
                    >
                      Pending
                    </Badge>
                  )}
                  <Badge
                    variant="secondary"
                    className={`text-xs ${getRoleColor(member.role)}`}
                  >
                    {getRoleIcon(member.role)}
                    <span className="ml-1 capitalize">{member.role}</span>
                  </Badge>
                </div>
              </div>
              {isProjectOwner && member.role !== 'owner' && !member.isTemporary && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                  onClick={() => handleRemoveMember(member.id, member.email || '')}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          ));
        })()}
      </div>
    </div>
  );
}
