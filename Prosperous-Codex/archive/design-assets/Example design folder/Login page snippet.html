<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Network Onboarding</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.rings.min.js"></script>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
  <style>
    body { font-family: 'Inter', sans-serif; }
    #vanta-canvas { overflow: hidden; }
  </style>
</head>
<body class="bg-gray-950 min-h-screen flex items-center justify-center p-4">
  <div class="max-w-5xl w-full bg-gray-900 rounded-xl overflow-hidden shadow-2xl border border-gray-800 flex flex-col md:flex-row">
    <!-- Visualization Side -->
    <div class="md:w-1/2 h-64 md:h-auto relative" id="vanta-canvas">
      <div class="absolute inset-0 bg-gradient-to-r from-transparent to-gray-900/90 md:via-transparent md:to-gray-900"></div>
      <div class="absolute top-8 left-8 z-10">
        <span class="px-2 py-1 bg-gray-800/80 rounded-full text-xs text-gray-400 mb-2 inline-block">WELCOME</span>
        <h2 class="text-3xl font-bold text-white">Join Our<br>Network</h2>
        <div class="h-1 w-16 bg-gray-400 mt-3 rounded-full"></div>
        <p class="mt-4 text-gray-300 max-w-xs">Sign in to your account to continue your journey with Prosperous.</p>
      </div>
      <div class="absolute bottom-8 left-8 bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2 z-10">
        <div class="flex space-x-4 text-xs text-gray-300">
          <div class="flex items-center">
            <span class="w-2 h-2 rounded-full bg-green-400 mr-1.5"></span>
            <span>System Online</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Form Side -->
    <div class="md:w-1/2 p-8 flex flex-col justify-center">
      <div>
        <h3 class="text-xl font-semibold text-gray-200 mb-2">Sign In</h3>
        <p class="text-sm text-gray-400 mb-6">Enter your internal credentials to access your tools.</p>
        
        <form class="space-y-5">
          <div class="space-y-2">
            <label for="email" class="block text-sm font-medium text-gray-300">EMAIL</label>
            <input type="email" id="email" name="email" placeholder="Enter your email" class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-gray-500">
          </div>
          
          <div class="space-y-2">
            <div class="flex justify-between items-center">
              <label for="password" class="block text-sm font-medium text-gray-300">PASSWORD</label>
              <a href="#" class="text-xs text-gray-400 hover:text-gray-300">Forgot?</a>
            </div>
            <input type="password" id="password" name="password" placeholder="Enter your password" class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-gray-500">
          </div>
          
          <div class="flex items-center">
            <input id="remember" name="remember" type="checkbox" class="h-4 w-4 bg-gray-800 border-gray-700 rounded text-gray-500 focus:ring-gray-600">
            <label for="remember" class="ml-2 block text-sm text-gray-400">
              Remember this device
            </label>
          </div>
          
          <div class="pt-2">
            <button type="submit" class="w-full px-4 py-2.5 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition flex items-center justify-center font-medium">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              Login
            </button>
          </div>
        </form>
        
        <div class="relative flex py-5 items-center">
          <div class="flex-grow border-t border-gray-700"></div>
          <span class="flex-shrink mx-4 text-gray-500 text-xs">OR</span>
          <div class="flex-grow border-t border-gray-700"></div>
        </div>
        <div class="text-center">
          <p class="text-xs text-gray-400">
            Don't have an account? <a href="#" class="text-gray-300 hover:text-white">Request Access</a>
          </p>
        </div>
        
        <div class="mt-4 text-center">
          <div class="flex items-center justify-center text-xs text-gray-400">
            <span class="w-2 h-2 rounded-full bg-green-400 mr-2"></span>
            <span>System Status: Operational</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      VANTA.RINGS({
        el: "#vanta-canvas",
        mouseControls: true,
        touchControls: true,
        gyroControls: false,
        minHeight: 200,
        minWidth: 200,
        scale: 1.00,
        scaleMobile: 1.00,
        backgroundColor: 0x030712,
        color: 0xd1d5db
      });

      // Login form functionality
      const loginForm = document.querySelector('form');
      const emailInput = document.getElementById('email');
      const passwordInput = document.getElementById('password');
      const rememberCheckbox = document.getElementById('remember');
      const loginButton = document.querySelector('button[type="submit"]');

      // Create error and success message elements
      const errorMessage = document.createElement('div');
      errorMessage.id = 'errorMessage';
      errorMessage.style.display = 'none';
      loginForm.appendChild(errorMessage);

      const successMessage = document.createElement('div');
      successMessage.id = 'successMessage';
      successMessage.style.display = 'none';
      loginForm.appendChild(successMessage);

      // Show/hide messages
      function showMessage(element, message, isError = false) {
        element.textContent = message;
        element.className = `mt-4 p-3 rounded-lg text-sm ${isError ? 'bg-red-900/50 text-red-300 border border-red-700' : 'bg-green-900/50 text-green-300 border border-green-700'}`;
        element.style.display = 'block';
      }

      function hideMessages() {
        errorMessage.style.display = 'none';
        successMessage.style.display = 'none';
      }

      // Handle form submission
      loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        hideMessages();

        const email = emailInput.value.trim();
        const password = passwordInput.value;
        const rememberDevice = rememberCheckbox.checked;

        // Basic validation
        if (!email || !password) {
          showMessage(errorMessage, 'Please enter both email and password.', true);
          return;
        }

        // Disable button and show loading
        loginButton.disabled = true;
        loginButton.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Signing in...';

        try {
          const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: email,
              password: password,
              rememberDevice: rememberDevice
            })
          });

          const data = await response.json();

          if (data.success) {
            // Store token in localStorage or sessionStorage
            if (rememberDevice) {
              localStorage.setItem('authToken', data.token);
            } else {
              sessionStorage.setItem('authToken', data.token);
            }
            
            // Store user info
            localStorage.setItem('userInfo', JSON.stringify(data.user));
            
            showMessage(successMessage, `Welcome back, ${data.user.fullName || data.user.email}!`);
            
            // Redirect after 2 seconds (you can change this to your dashboard URL)
            setTimeout(() => {
              window.location.href = '/dashboard.html'; // Change this to your dashboard page
            }, 2000);
          } else {
            showMessage(errorMessage, data.message || 'Login failed. Please try again.', true);
          }
        } catch (error) {
          console.error('Login error:', error);
          showMessage(errorMessage, 'Network error. Please check your connection and try again.', true);
        } finally {
          // Re-enable button
          loginButton.disabled = false;
          loginButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>Login';
        }
      });

      // Handle "Request Access" link
      const requestAccessLink = document.querySelector('a[href="#"]');
      if (requestAccessLink && requestAccessLink.textContent.includes('Request Access')) {
        requestAccessLink.addEventListener('click', function(e) {
          e.preventDefault();
          alert('Please contact your system administrator to request access.\n\nEmail: <EMAIL>\nPhone: (*************');
        });
      }
    });
  </script>
</body>
</html>
