import { NetworkAuthWrapper } from '@/components/auth/network-auth-wrapper';
import { LoginForm } from '@/components/auth/login-form';
import { LoginLanguageSwitcher } from '@/components/auth/login-language-switcher';
import { getTranslations } from 'next-intl/server';
import type { Metadata } from 'next';

export async function generateMetadata({params}: {params: Promise<{locale: string}>}): Promise<Metadata> {
  const {locale} = await params;
  const t = await getTranslations({locale, namespace: 'auth.login'});

  return {
    title: `Prosperous Codex - ${t('title')}`,
    description: t('subtitle'),
  };
}

export default async function LoginPage({params}: {params: Promise<{locale: string}>}) {
  const {locale} = await params;
  const t = await getTranslations({locale, namespace: 'auth.login'});

  return (
    <>
      <NetworkAuthWrapper
        welcomeMessage={t('welcome')}
        title={t.raw('brandTitle')}
        subtitle={t('subtitle')}
      >
        <LoginForm />
      </NetworkAuthWrapper>
      <LoginLanguageSwitcher currentLocale={locale} />
    </>
  );
}
