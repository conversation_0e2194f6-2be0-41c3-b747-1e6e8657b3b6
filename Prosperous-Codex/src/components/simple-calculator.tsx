'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Calculator, Delete } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SimpleCalculatorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Floating drawer overlay component with clean animations
const CalculatorOverlay = ({ children, open, onOpenChange, position, onPositionChange }: {
  children: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  position: { x: number; y: number };
  onPositionChange: (position: { x: number; y: number }) => void;
}) => {
  const [shouldRender, setShouldRender] = React.useState(false);

  React.useEffect(() => {
    if (open) {
      setShouldRender(true);
    } else {
      // Wait for exit animation to complete
      const timer = setTimeout(() => setShouldRender(false), 300);
      return () => clearTimeout(timer);
    }
  }, [open]);

  if (!shouldRender) return null;

  return (
    <div
      className={`fixed inset-0 z-[300] p-0 ${
        open
          ? 'animate-in fade-in-0 duration-300'
          : 'animate-out fade-out-0 duration-300'
      }`}
      onClick={() => onOpenChange(false)}
    >
      <div
        className={`absolute bg-white dark:bg-[#1A1A1A] border border-gray-100 dark:border-[#2A2A2A] shadow-xl rounded-lg ${
          open
            ? 'animate-in slide-in-from-bottom-4 fade-in-0 zoom-in-95 duration-300'
            : 'animate-out slide-out-to-bottom-4 fade-out-0 zoom-out-95 duration-300'
        }`}
        style={{ top: `${position.y}px`, left: `${position.x}px`, width: '300px' }}
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>
  );
};

export default function SimpleCalculator({ open, onOpenChange }: SimpleCalculatorProps) {
  const [display, setDisplay] = useState('0');
  const [inputBuffer, setInputBuffer] = useState('');
  const [previousValue, setPreviousValue] = useState<number | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForOperand, setWaitingForOperand] = useState(false);
  const [position, setPosition] = useState({ x: window.innerWidth / 2 - 150, y: window.innerHeight / 2 - 200 });
  const [dragging, setDragging] = useState(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number } | null>(null);

  React.useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (dragging && dragStart) {
        const newX = position.x + (e.clientX - dragStart.x);
        const newY = position.y + (e.clientY - dragStart.y);
        setPosition({
          x: Math.max(0, Math.min(newX, window.innerWidth - 300)),
          y: Math.max(0, Math.min(newY, window.innerHeight - 400))
        });
        setDragStart({ x: e.clientX, y: e.clientY });
      }
    };

    const handleMouseUp = () => {
      setDragging(false);
      setDragStart(null);
    };

    if (dragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [dragging, dragStart, position]);

  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open) return;

      const key = e.key;
      if (/^[0-9]$/.test(key)) {
        inputNumber(key);
        setInputBuffer(prev => prev + key);
      } else if (key === '.') {
        inputDecimal();
        setInputBuffer(prev => prev + key);
      } else if (key === '+' || key === '-' || key === '*' || key === '/') {
        const op = key === '*' ? '×' : key === '/' ? '÷' : key;
        performOperation(op);
        setInputBuffer('');
      } else if (key === 'Enter' || key === '=') {
        handleOperationClick('=');
        setInputBuffer('');
      } else if (key === 'Backspace') {
        backspace();
        setInputBuffer(prev => prev.slice(0, -1));
      } else if (key === 'Escape') {
        clear();
        setInputBuffer('');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]); // Only depend on open state

  const inputNumber = useCallback((num: string) => {
    if (waitingForOperand) {
      setDisplay(num);
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? num : display + num);
    }
  }, [display, waitingForOperand]);

  const inputDecimal = useCallback(() => {
    if (waitingForOperand) {
      setDisplay('0.');
      setWaitingForOperand(false);
    } else if (display.indexOf('.') === -1) {
      setDisplay(display + '.');
    }
  }, [display, waitingForOperand]);

  const clear = useCallback(() => {
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForOperand(false);
  }, []);

  const performOperation = useCallback((nextOperation: string) => {
    const inputValue = parseFloat(display);

    if (previousValue === null) {
      setPreviousValue(inputValue);
    } else if (operation) {
      const currentValue = previousValue || 0;
      const newValue = calculate(currentValue, inputValue, operation);

      setDisplay(String(newValue));
      setPreviousValue(newValue);
    }

    setWaitingForOperand(true);
    setOperation(nextOperation);
  }, [display, previousValue, operation]);

  const calculate = (firstValue: number, secondValue: number, operation: string): number => {
    switch (operation) {
      case '+':
        return firstValue + secondValue;
      case '-':
        return firstValue - secondValue;
      case '×':
        return firstValue * secondValue;
      case '÷':
        return firstValue / secondValue;
      case '=':
        return secondValue;
      default:
        return secondValue;
    }
  };

  const handleOperationClick = useCallback((op: string) => {
    if (op === '=') {
      if (operation && previousValue !== null) {
        const inputValue = parseFloat(display);
        const newValue = calculate(previousValue, inputValue, operation);
        setDisplay(String(newValue));
        setPreviousValue(null);
        setOperation(null);
        setWaitingForOperand(true);
      }
    } else {
      performOperation(op);
    }
  }, [operation, previousValue, display, performOperation]);

  const backspace = useCallback(() => {
    if (display.length > 1) {
      setDisplay(display.slice(0, -1));
    } else {
      setDisplay('0');
    }
  }, [display]);

  const buttonClass = "h-10 text-base font-medium transition-all hover:scale-105 active:scale-95";
  const numberButtonClass = `${buttonClass} bg-white dark:bg-neutral-800 text-gray-700 dark:text-neutral-300 border border-gray-200 dark:border-neutral-700 hover:bg-gray-50 dark:hover:bg-neutral-700`;
  const operatorButtonClass = `${buttonClass} bg-[#5E6AD2] dark:bg-[#6E56CF] text-white hover:bg-[#5E6AD2]/90 dark:hover:bg-[#9E8CFC]`;
  const specialButtonClass = `${buttonClass} bg-gray-100 dark:bg-neutral-700 text-gray-700 dark:text-neutral-300 hover:bg-gray-200 dark:hover:bg-neutral-600`;

  return (
    <CalculatorOverlay open={open} onOpenChange={onOpenChange} position={position} onPositionChange={setPosition}>
      <div className="flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-2 bg-[#F5F7FF] dark:bg-[#2A2740] rounded-t-lg cursor-move"
          onMouseDown={(e) => {
            setDragging(true);
            setDragStart({ x: e.clientX, y: e.clientY });
          }}
        >
          <div className="flex items-center gap-2">
            <Calculator className="h-4 w-4 text-[#5E6AD2] dark:text-[#6E56CF]" />
            <h2 className="text-base font-semibold text-gray-900 dark:text-white">Calculator</h2>
          </div>
        </div>

        {/* Calculator Body */}
        <div className="p-4 bg-white dark:bg-[#1A1A1A] rounded-b-lg">
          {/* Display */}
          <div className="mb-3 p-3 bg-gray-50 dark:bg-neutral-900 rounded-lg border border-gray-200 dark:border-neutral-700">
            {operation && previousValue !== null && (
              <div className="text-right text-xs text-gray-500 dark:text-neutral-400 mb-1">
                {previousValue} {operation}
              </div>
            )}
            <div className="text-right text-xl font-mono text-gray-900 dark:text-white overflow-hidden">
              {display}
            </div>
          </div>

          {/* Button Grid */}
          <div className="grid grid-cols-4 gap-3">
            {/* Row 1 */}
            <Button
              className={`${specialButtonClass} col-span-2`}
              onClick={clear}
            >
              Clear
            </Button>
            <Button
              className={specialButtonClass}
              onClick={backspace}
            >
              <Delete className="h-4 w-4" />
            </Button>
            <Button
              className={operatorButtonClass}
              onClick={() => handleOperationClick('÷')}
            >
              ÷
            </Button>

            {/* Row 2 */}
            <Button
              className={numberButtonClass}
              onClick={() => inputNumber('7')}
            >
              7
            </Button>
            <Button
              className={numberButtonClass}
              onClick={() => inputNumber('8')}
            >
              8
            </Button>
            <Button
              className={numberButtonClass}
              onClick={() => inputNumber('9')}
            >
              9
            </Button>
            <Button
              className={operatorButtonClass}
              onClick={() => handleOperationClick('×')}
            >
              ×
            </Button>

            {/* Row 3 */}
            <Button
              className={numberButtonClass}
              onClick={() => inputNumber('4')}
            >
              4
            </Button>
            <Button
              className={numberButtonClass}
              onClick={() => inputNumber('5')}
            >
              5
            </Button>
            <Button
              className={numberButtonClass}
              onClick={() => inputNumber('6')}
            >
              6
            </Button>
            <Button
              className={operatorButtonClass}
              onClick={() => handleOperationClick('-')}
            >
              -
            </Button>

            {/* Row 4 */}
            <Button
              className={numberButtonClass}
              onClick={() => inputNumber('1')}
            >
              1
            </Button>
            <Button
              className={numberButtonClass}
              onClick={() => inputNumber('2')}
            >
              2
            </Button>
            <Button
              className={numberButtonClass}
              onClick={() => inputNumber('3')}
            >
              3
            </Button>
            <Button
              className={operatorButtonClass}
              onClick={() => handleOperationClick('+')}
            >
              +
            </Button>

            {/* Row 5 */}
            <Button
              className={`${numberButtonClass} col-span-2`}
              onClick={() => inputNumber('0')}
            >
              0
            </Button>
            <Button
              className={numberButtonClass}
              onClick={inputDecimal}
            >
              .
            </Button>
            <Button
              className={`${operatorButtonClass} bg-[#4C5ED4] dark:bg-[#5E4BCF] hover:bg-[#4C5ED4]/90 dark:hover:bg-[#8B7CFC]`}
              onClick={() => handleOperationClick('=')}
            >
              =
            </Button>
          </div>
        </div>
      </div>
    </CalculatorOverlay>
  );
}
