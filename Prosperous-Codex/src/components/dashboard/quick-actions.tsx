"use client";

import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useTranslations } from 'next-intl';
import { Plus, UserPlus, Calendar } from 'lucide-react';

export function QuickActions() {
  const t = useTranslations('dashboard.actions');
  const projects = [
    { name: 'Book 1', progress: 75 },
    { name: 'Title 2', progress: 90 },
  ];

  return (
    <div className="flex w-full flex-col items-start gap-4">
      <span className="text-base font-semibold text-foreground">
        {t('title')}
      </span>
      <div className="flex w-full flex-col items-start gap-2">
        <Button className="h-8 w-full flex-none" variant="brand-primary">
          <Plus className="h-4 w-4" />
          {t('createProject')}
        </Button>
        <Button className="h-8 w-full flex-none" variant="neutral-secondary">
          <UserPlus className="h-4 w-4" />
          {t('inviteTeam')}
        </Button>
        <Button className="h-8 w-full flex-none" variant="neutral-secondary">
          <Calendar className="h-4 w-4" />
          {t('scheduleMeeting')}
        </Button>
      </div>
      <div className="flex w-full flex-col items-start gap-4 rounded-md border border-border bg-card/50 backdrop-blur-sm px-4 py-4">
        <span className="text-sm font-medium text-foreground">
          Project Progress
        </span>
        {projects.map((project, index) => (
          <div key={index} className="flex w-full items-center gap-3">
            <span className="flex-1 text-sm text-foreground min-w-0">
              {project.name}
            </span>
            <div className="flex flex-1 items-center gap-2">
              <Progress
                value={project.progress}
                variant="brand"
                className="flex-1"
                showPercentage={false}
              />
              <span className="text-sm font-medium text-[#5E6AD2] dark:text-[#9E8CFC] min-w-[3rem] text-right">
                {project.progress}%
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
